"use strict";(self.webpack<PERSON>hunkapp=self.webpackChunkapp||[]).push([[7438],{17438:(v,p,n)=>{n.d(p,{Ei:()=>S,Gg:()=>T,Y9:()=>d,wi:()=>E});var r=n(2978),u=n(56610);const o=["*"];let d=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275cmp=r.VBU({type:i,selectors:[["p-header"]],ngContentSelectors:o,decls:1,vars:0,template:function(t,a){1&t&&(r.NAR(),r.SdG(0))},encapsulation:2})}return i})(),E=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275cmp=r.VBU({type:i,selectors:[["p-footer"]],ngContentSelectors:o,decls:1,vars:0,template:function(t,a){1&t&&(r.NAR(),r.SdG(0))},encapsulation:2})}return i})(),S=(()=>{class i{template;type;name;constructor(e){this.template=e}getType(){return this.name}static \u0275fac=function(t){return new(t||i)(r.rXU(r.C4Q))};static \u0275dir=r.FsC({type:i,selectors:[["","pTemplate",""]],inputs:{type:"type",name:["pTemplate","name"]}})}return i})(),T=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=r.$C({type:i});static \u0275inj=r.G2t({imports:[u.MD]})}return i})()}}]);