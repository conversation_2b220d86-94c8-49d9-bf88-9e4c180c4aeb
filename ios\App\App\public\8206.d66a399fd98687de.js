"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8206],{68206:(f,g,r)=>{r.r(g),r.d(g,{UserDetailPageModule:()=>c});var d=r(56610),s=r(37222),l=r(77897),h=r(77575),p=r(73308),n=r(2978),m=r(23985),b=r(44444),u=r(74657);const M=[{path:"",component:(()=>{class i{constructor(){this.location=(0,n.WQX)(d.aZ),this.formBuilder=(0,n.WQX)(s.ok),this.userSrv=(0,n.WQX)(m.D),this.route=(0,n.WQX)(h.nX)}ngOnInit(){var o=this;return(0,p.A)(function*(){o.route.snapshot.params.id&&(o.userId=o.route.snapshot.params.id,yield o.getUser(o.userId)),o.form=o.formBuilder.group({name:[o.user.firstName,s.k0.required],phone:[o.user.tel??6e8,s.k0.required],commercial:[o.user.socialReason??"N/A",s.k0.required],localisation:[o.user.address.district??"N/A",s.k0.required],seller:[o.user.associatedCompanies??["N/A","N/A"],s.k0.required]})})()}getCategory(o){switch(o){case b.iL.BHB:return"BHB";case b.iL.BS:return"BS";case b.iL.BPI:return"BPI";default:return"Unknown Category"}}getUser(o){var a=this;return(0,p.A)(function*(){a.user=yield a.userSrv.find(o)})()}back(){this.location.back()}static{this.\u0275fac=function(a){return new(a||i)}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-user-detail"]],decls:77,vars:52,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"user-info-container"],[1,"user-info-item"],[1,"user-info-label"],[1,"user-info-value"]],template:function(a,e){1&a&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return e.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content")(7,"div",3)(8,"div",4)(9,"label",5),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.j41(12,"div",6),n.EFF(13),n.k0s()(),n.j41(14,"div",4)(15,"label",5),n.EFF(16,"Type de client"),n.k0s(),n.j41(17,"div",6),n.EFF(18),n.k0s()(),n.j41(19,"div",4)(20,"label",5),n.EFF(21),n.nI1(22,"translate"),n.k0s(),n.j41(23,"div",6),n.EFF(24),n.k0s()(),n.j41(25,"div",4)(26,"label",5),n.EFF(27),n.nI1(28,"translate"),n.k0s(),n.j41(29,"div",6),n.EFF(30),n.k0s()(),n.j41(31,"div",4)(32,"label",5),n.EFF(33),n.nI1(34,"translate"),n.k0s(),n.j41(35,"div",6),n.EFF(36),n.k0s()(),n.j41(37,"div",4)(38,"label",5),n.EFF(39,"Quartier"),n.k0s(),n.j41(40,"div",6),n.EFF(41),n.k0s()(),n.j41(42,"div",4)(43,"label",5),n.EFF(44),n.nI1(45,"translate"),n.k0s(),n.j41(46,"div",6),n.EFF(47),n.k0s()(),n.j41(48,"div",4)(49,"label",5),n.EFF(50),n.nI1(51,"translate"),n.k0s(),n.j41(52,"div",6),n.EFF(53),n.nI1(54,"translate"),n.nI1(55,"translate"),n.k0s()(),n.j41(56,"div",4)(57,"label",5),n.EFF(58),n.nI1(59,"translate"),n.k0s(),n.j41(60,"div",6),n.EFF(61),n.nI1(62,"date"),n.k0s()(),n.j41(63,"div",4)(64,"label",5),n.EFF(65),n.nI1(66,"translate"),n.k0s(),n.j41(67,"div",6),n.EFF(68),n.nI1(69,"translate"),n.nI1(70,"translate"),n.k0s()(),n.j41(71,"div",4)(72,"label",5),n.EFF(73),n.nI1(74,"translate"),n.k0s(),n.j41(75,"div",6),n.EFF(76),n.k0s()()()()),2&a&&(n.R7$(4),n.SpI(" ",n.bMT(5,21,"user-info.title")," "),n.R7$(6),n.JRh(n.bMT(11,23,"user-info.full-name")),n.R7$(3),n.SpI(" ",null==e.user?null:e.user.firstName,""),n.R7$(5),n.JRh(e.getCategory(null==e.user?null:e.user.categoryType)),n.R7$(3),n.JRh(n.bMT(22,25,"user-info.phone")),n.R7$(3),n.JRh(null==e.user?null:e.user.tel),n.R7$(3),n.JRh(n.bMT(28,27,"user-info.region")),n.R7$(3),n.JRh(null==e.user?null:e.user.address.region),n.R7$(3),n.JRh(n.bMT(34,29,"indirect-clients.ville")),n.R7$(3),n.JRh(null==e.user?null:e.user.address.city),n.R7$(5),n.JRh(null==e.user||null==e.user.address?null:e.user.address.neighborhood),n.R7$(3),n.JRh(n.bMT(45,31,"user-info.location")),n.R7$(3),n.JRh(null==e.user?null:e.user.address.district),n.R7$(3),n.JRh(n.bMT(51,33,"user-info.category")),n.R7$(3),n.JRh(0===(null==e.user?null:e.user.category)?n.bMT(54,35,"user-info.particular"):n.bMT(55,37,"user-info.other")),n.R7$(5),n.JRh(n.bMT(59,39,"user-info.created-at")),n.R7$(3),n.JRh(n.i5U(62,41,null==e.user?null:e.user.created_at,"dd/MM/yyyy HH:mm")),n.R7$(4),n.JRh(n.bMT(66,44,"user-info.enabled")),n.R7$(3),n.JRh(null!=e.user&&e.user.enable?n.bMT(69,46,"user-info.yes"):n.bMT(70,48,"user-info.no")),n.R7$(5),n.JRh(n.bMT(74,50,"user-info.company")),n.R7$(3),n.SpI(" ",(null==e.user?null:e.user.socialReason)||"N/A",""))},dependencies:[l.W9,l.eU,l.KW,l.BC,l.ai,d.vh,u.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: #f1f2f4;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}ion-content[_ngcontent-%COMP%]{--background: #f1f2f4;--overflow: auto;margin:20px;padding:10px}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;gap:20px;height:auto;--overflow: auto}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;border:1px solid rgba(65,156,251,.168627451);border-radius:9px;width:83%;font-weight:400;height:2em;padding:16px}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}ion-content[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   .user-info-value[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}"]})}}return i})()}];let t=(()=>{class i{static{this.\u0275fac=function(a){return new(a||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[h.iI.forChild(M),h.iI]})}}return i})(),c=(()=>{class i{static{this.\u0275fac=function(a){return new(a||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[d.MD,s.YN,s.X1,l.bv,u.h,t]})}}return i})()},44444:(f,g,r)=>{r.d(g,{PB:()=>u,cs:()=>l,iL:()=>M}),r(68953);class s{constructor(c){this.email="",this.firstName="",this.lastName="",this.tel="",this.password="",this.cni="",this.nui="",this.address={region:"",city:"",district:""},this.category=c}}class l extends s{}var u=function(t){return t[t.NORMAL=100]="NORMAL",t[t.CORDO_RH=101]="CORDO_RH",t[t.DRH=102]="DRH",t}(u||{}),M=function(t){return t[t.BHB=101]="BHB",t[t.BS=102]="BS",t[t.BPI=103]="BPI",t}(M||{})}}]);