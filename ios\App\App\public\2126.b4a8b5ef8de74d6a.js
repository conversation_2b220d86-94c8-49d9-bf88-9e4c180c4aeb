"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2126],{22126:(X,R,T)=>{T.d(R,{$c:()=>_,E_:()=>x,F3:()=>$,I9:()=>C,Ii:()=>E});var c=T(73308),_=function(r){return r.Unimplemented="UNIMPLEMENTED",r.<PERSON>vailable="UNAVAILABLE",r}(_||{});class C extends Error{constructor(e,t,i){super(e),this.message=e,this.code=t,this.data=i}}const B=r=>{const e=r.CapacitorCustomPlatform||null,t=r.Capacitor||{},i=t.Plugins=t.Plugins||{},s=()=>null!==e?e.name:(r=>{var e,t;return r?.androidBridge?"android":null!==(t=null===(e=r?.webkit)||void 0===e?void 0:e.messageHandlers)&&void 0!==t&&t.bridge?"ios":"web"})(r),l=a=>{var d;return null===(d=t.PluginHeaders)||void 0===d?void 0:d.find(k=>k.name===a)},g=new Map;return t.convertFileSrc||(t.convertFileSrc=a=>a),t.getPlatform=s,t.handleError=a=>r.console.error(a),t.isNativePlatform=()=>"web"!==s(),t.isPluginAvailable=a=>{const d=g.get(a);return!(!d?.platforms.has(s())&&!l(a))},t.registerPlugin=(a,d={})=>{const k=g.get(a);if(k)return console.warn(`Capacitor plugin "${a}" already registered. Cannot register plugins twice.`),k.proxy;const y=s(),A=l(a);let p;const z=function(){var u=(0,c.A)(function*(){return!p&&y in d?p=p="function"==typeof d[y]?yield d[y]():d[y]:null!==e&&!p&&"web"in d&&(p=p="function"==typeof d.web?yield d.web():d.web),p});return function(){return u.apply(this,arguments)}}(),U=u=>{let h;const m=(...w)=>{const v=z().then(b=>{const P=((u,h)=>{var m,w;if(!A){if(u)return null===(w=u[h])||void 0===w?void 0:w.bind(u);throw new C(`"${a}" plugin is not implemented on ${y}`,_.Unimplemented)}{const v=A?.methods.find(b=>h===b.name);if(v)return"promise"===v.rtype?b=>t.nativePromise(a,h.toString(),b):(b,P)=>t.nativeCallback(a,h.toString(),b,P);if(u)return null===(m=u[h])||void 0===m?void 0:m.bind(u)}})(b,u);if(P){const O=P(...w);return h=O?.remove,O}throw new C(`"${a}.${u}()" is not implemented on ${y}`,_.Unimplemented)});return"addListener"===u&&(v.remove=(0,c.A)(function*(){return h()})),v};return m.toString=()=>`${u.toString()}() { [capacitor code] }`,Object.defineProperty(m,"name",{value:u,writable:!1,configurable:!1}),m},H=U("addListener"),W=U("removeListener"),Q=(u,h)=>{const m=H({eventName:u},h),w=function(){var b=(0,c.A)(function*(){const P=yield m;W({eventName:u,callbackId:P},h)});return function(){return b.apply(this,arguments)}}(),v=new Promise(b=>m.then(()=>b({remove:w})));return v.remove=(0,c.A)(function*(){console.warn("Using addListener() without 'await' is deprecated."),yield w()}),v},j=new Proxy({},{get(u,h){switch(h){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return A?Q:H;case"removeListener":return W;default:return U(h)}}});return i[a]=j,g.set(a,{name:a,proxy:j,platforms:new Set([...Object.keys(d),...A?[y]:[]])}),j},t.Exception=C,t.DEBUG=!!t.DEBUG,t.isLoggingEnabled=!!t.isLoggingEnabled,t},E=(r=>r.Capacitor=B(r))(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),$=E.registerPlugin;class x{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){var i=this;let s=!1;this.listeners[e]||(this.listeners[e]=[],s=!0),this.listeners[e].push(t);const o=this.windowListeners[e];o&&!o.registered&&this.addWindowListener(o),s&&this.sendRetainedArgumentsForEvent(e);const l=function(){var g=(0,c.A)(function*(){return i.removeListener(e,t)});return function(){return g.apply(this,arguments)}}();return Promise.resolve({remove:l})}removeAllListeners(){var e=this;return(0,c.A)(function*(){e.listeners={};for(const t in e.windowListeners)e.removeWindowListener(e.windowListeners[t]);e.windowListeners={}})()}notifyListeners(e,t,i){const s=this.listeners[e];if(s)s.forEach(n=>n(t));else if(i){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:i=>{this.notifyListeners(t,i)}}}unimplemented(e="not implemented"){return new E.Exception(e,_.Unimplemented)}unavailable(e="not available"){return new E.Exception(e,_.Unavailable)}removeListener(e,t){var i=this;return(0,c.A)(function*(){const s=i.listeners[e];if(!s)return;const n=s.indexOf(t);i.listeners[e].splice(n,1),i.listeners[e].length||i.removeWindowListener(i.windowListeners[e])})()}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(i=>{this.notifyListeners(e,i)}))}}const S=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),D=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class F extends x{getCookies(){return(0,c.A)(function*(){const e=document.cookie,t={};return e.split(";").forEach(i=>{if(i.length<=0)return;let[s,n]=i.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");s=D(s).trim(),n=D(n).trim(),t[s]=n}),t})()}setCookie(e){return(0,c.A)(function*(){try{const t=S(e.key),i=S(e.value),s=`; expires=${(e.expires||"").replace("expires=","")}`,n=(e.path||"/").replace("path=",""),o=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${i||""}${s}; path=${n}; ${o};`}catch(t){return Promise.reject(t)}})()}deleteCookie(e){return(0,c.A)(function*(){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}})()}clearCookies(){return(0,c.A)(function*(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}})()}clearAllCookies(){var e=this;return(0,c.A)(function*(){try{yield e.clearCookies()}catch(t){return Promise.reject(t)}})()}}$("CapacitorCookies",{web:()=>new F});const M=function(){var r=(0,c.A)(function*(e){return new Promise((t,i)=>{const s=new FileReader;s.onload=()=>{const n=s.result;t(n.indexOf(",")>=0?n.split(",")[1]:n)},s.onerror=n=>i(n),s.readAsDataURL(e)})});return function(t){return r.apply(this,arguments)}}();class V extends x{request(e){return(0,c.A)(function*(){const t=((r,e={})=>{const t=Object.assign({method:r.method||"GET",headers:r.headers},e),s=((r={})=>{const e=Object.keys(r);return Object.keys(r).map(s=>s.toLocaleLowerCase()).reduce((s,n,o)=>(s[n]=r[e[o]],s),{})})(r.headers)["content-type"]||"";if("string"==typeof r.data)t.body=r.data;else if(s.includes("application/x-www-form-urlencoded")){const n=new URLSearchParams;for(const[o,l]of Object.entries(r.data||{}))n.set(o,l);t.body=n.toString()}else if(s.includes("multipart/form-data")||r.data instanceof FormData){const n=new FormData;if(r.data instanceof FormData)r.data.forEach((l,f)=>{n.append(f,l)});else for(const l of Object.keys(r.data))n.append(l,r.data[l]);t.body=n;const o=new Headers(t.headers);o.delete("content-type"),t.headers=o}else(s.includes("application/json")||"object"==typeof r.data)&&(t.body=JSON.stringify(r.data));return t})(e,e.webFetchExtra),i=((r,e=!0)=>r?Object.entries(r).reduce((i,s)=>{const[n,o]=s;let l,f;return Array.isArray(o)?(f="",o.forEach(g=>{l=e?encodeURIComponent(g):g,f+=`${n}=${l}&`}),f.slice(0,-1)):(l=e?encodeURIComponent(o):o,f=`${n}=${l}`),`${i}&${f}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),s=i?`${e.url}?${i}`:e.url,n=yield fetch(s,t),o=n.headers.get("content-type")||"";let f,g,{responseType:l="text"}=n.ok?e:{};switch(o.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":g=yield n.blob(),f=yield M(g);break;case"json":f=yield n.json();break;default:f=yield n.text()}const L={};return n.headers.forEach((a,d)=>{L[d]=a}),{data:f,headers:L,status:n.status,url:n.url}})()}get(e){var t=this;return(0,c.A)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"GET"}))})()}post(e){var t=this;return(0,c.A)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"POST"}))})()}put(e){var t=this;return(0,c.A)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"PUT"}))})()}patch(e){var t=this;return(0,c.A)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"PATCH"}))})()}delete(e){var t=this;return(0,c.A)(function*(){return t.request(Object.assign(Object.assign({},e),{method:"DELETE"}))})()}}$("CapacitorHttp",{web:()=>new V})}}]);