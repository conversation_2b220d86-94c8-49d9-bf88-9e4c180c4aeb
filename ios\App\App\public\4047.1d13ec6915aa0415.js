"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4047],{44047:(y,C,o)=>{o.r(C),o.d(C,{ReportingPurchasesQuantityPageModule:()=>p});var M=o(56610),u=o(37222),d=o(71604),O=o(60787),i=o(77897),m=o(77575),b=o(73308),n=o(2978),v=o(32327),_=o(82571),D=o(94440),s=o(74657);function a(r,P){if(1&r&&(n.j41(0,"ion-row")(1,"ion-col",11),n.EFF(2),n.nI1(3,"truncateString"),n.k0s(),n.j41(4,"ion-col",12),n.EFF(5),n.k0s()()),2&r){const e=P.$implicit;n.R7$(2),n.SpI("",n.i5U(3,2,null==e?null:e.label,30),"T"),n.R7$(3),n.JRh(null==e?null:e.totalTone)}}const c=[{path:"",component:(()=>{class r{constructor(e,h){this.reportingSrv=e,this.commonSrv=h}ngOnInit(){return(0,b.A)(function*(){})()}ionViewWillEnter(){var e=this;return(0,b.A)(function*(){yield e.geTotalOrderQdty(),e.chartData()})()}geTotalOrderQdty(){var e=this;return(0,b.A)(function*(){e.dataStateOrderQdty=e.reportingSrv.dataStateOrderQdty,e.totalOrder=e.reportingSrv.totalOrder})()}chartData(){var e=this;return(0,b.A)(function*(){e.isLoading=!0,e.dataStateProductBar={labels:e.dataStateOrderQdty?.dataYear?.labels,datasets:[{label:"quantity",data:e.dataStateOrderQdty?.dataYear?.data,backgroundColor:[],borderColor:[]}]},e.dataStateOrderQdty?.dataYear?.data?.forEach(h=>{const f=e.generateHexColor();e.dataStateProductBar.datasets[0].backgroundColor?.push(f)}),e.isLoading=!1})()}generateHexColor(){const e=Math.floor(16777215*Math.random()).toString(16);return"#"+"0".repeat(6-e.length)+e}static{this.\u0275fac=function(h){return new(h||r)(n.rXU(v.k),n.rXU(_.h))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-reporting-purchases-quantity"]],decls:22,vars:13,consts:[[1,"header"],["mode","md","slot","start","text",""],["defaultHref","./"],[1,"title"],[1,"ion-content"],[1,"p-card"],[1,"p-card-header"],["type","bar",1,"p-card",3,"data","height"],["size","6",1,"row-title"],["size","4",1,"row-title"],[4,"ngFor","ngForOf"],["size","6"],["size","4"]],template:function(h,f){1&h&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-buttons",1),n.nrm(3,"ion-back-button",2),n.k0s(),n.j41(4,"h1",3),n.EFF(5),n.nI1(6,"translate"),n.k0s()()(),n.j41(7,"ion-content")(8,"div",4)(9,"p-card",5)(10,"div",6),n.EFF(11),n.k0s(),n.nrm(12,"p-chart",7),n.k0s(),n.j41(13,"ion-grid")(14,"ion-row")(15,"ion-col",8),n.EFF(16),n.nI1(17,"translate"),n.k0s(),n.j41(18,"ion-col",9),n.EFF(19),n.nI1(20,"translate"),n.k0s()(),n.DNE(21,a,6,5,"ion-row",10),n.k0s()()()),2&h&&(n.R7$(5),n.JRh(n.bMT(6,7,"reporting.quantities-purchased")),n.R7$(6),n.SpI("Total : ",f.reportingSrv.totalOrder,""),n.R7$(1),n.Y8G("data",f.dataStateProductBar)("height","10rem"),n.R7$(4),n.SpI(" ",n.bMT(17,9,"reporting.products"),""),n.R7$(3),n.SpI(" ",n.bMT(20,11,"reporting.quantity"),""),n.R7$(2),n.Y8G("ngForOf",f.reportingSrv.dataStateProduct))},dependencies:[M.Sq,i.el,i.QW,i.hU,i.W9,i.lO,i.eU,i.ln,i.ai,i.tY,d.Z,O.X,D.c,s.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--border-color: transparent;display:flex;align-items:center;padding:calc(41 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{width:4rem}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-size:calc(55 * var(--res));text-align:start}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]{padding:0 calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{width:50%;height:10rem;margin:20px 20px .5em;background-color:#d9d9d9}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-size:calc(42 * var(--res));color:#143c5d}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{padding-bottom:10px;margin-bottom:10px;border-bottom:1px solid hsl(0,0%,85%)}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-title[_ngcontent-%COMP%]{font-size:calc(42 * var(--res));font-weight:700}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{font-size:1.25rem}"]})}}return r})()}];let l=(()=>{class r{static{this.\u0275fac=function(h){return new(h||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[m.iI.forChild(c),m.iI]})}}return r})();var g=o(93887);let p=(()=>{class r{static{this.\u0275fac=function(h){return new(h||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[M.MD,u.YN,i.bv,d.D,O.F,l,g.G,s.h]})}}return r})()},32327:(y,C,o)=>{o.d(C,{k:()=>v});var M=o(73308),u=o(56610),d=o(26409),O=o(94934),i=o(45312),m=o(2978),b=o(33607),n=o(82571);let v=(()=>{class _{constructor(s,a,t){this.http=s,this.baseUrl=a,this.commonSrv=t,this.base_url=`${this.baseUrl.getOrigin()}${i.c.basePath}`}geTotalQuantityOrder(s){var a=this;return(0,M.A)(function*(){try{let t=new d.Nl;const{status:c,startDate:l,endDate:g,enable:p=!0,userId:r}=s;return c&&(t=t.append("status",c)),l&&g&&(t=t.append("startDate",new u.vh("fr").transform(l,"YYYY-MM-dd")),t=t.append("endDate",new u.vh("fr").transform(g,"YYYY-MM-dd"))),t=t.append("enable",p),r&&(t=t.append("user",r)),yield(0,O.s)(a.http.get(`${a.base_url}reporting/total-quantity-evolution`,{params:t}))}catch(t){return a.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}geTotalProduct(s){var a=this;return(0,M.A)(function*(){try{let t=new d.Nl;const{status:c,startDate:l,endDate:g,product:p,enable:r=!0,userId:P}=s;return l&&g&&(t=t.append("startDate",new u.vh("fr").transform(l,"YYYY-MM-dd")),t=t.append("endDate",new u.vh("fr").transform(g,"YYYY-MM-dd"))),c&&(t=t.append("status",c)),p&&(t=t.append("items.product._id",p)),P&&(t=t.append("user",P)),t=t.append("enable",r),yield(0,O.s)(a.http.get(`${a.base_url}reporting/product-quantity`,{params:t}))}catch(t){return yield a.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}getTotalEarnPoint(s){var a=this;return(0,M.A)(function*(){try{let t=new d.Nl;const{startDate:c,endDate:l,region:g,enable:p=!0}=s;return g&&(t=t.append("region",g)),c&&l&&(t=t.append("startDate",new u.vh("fr").transform(c,"YYYY-MM-dd")),t=t.append("endDate",new u.vh("fr").transform(l,"YYYY-MM-dd"))),t=t.append("enable",p),yield(0,O.s)(a.http.get(`${a.base_url}reporting/points-evolution`,{params:t}))}catch(t){return yield a.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}getDistributorVolume(s){var a=this;return(0,M.A)(function*(){try{let t=new d.Nl;const{status:c,startDate:l,endDate:g,product:p,enable:r=!0,userId:P}=s;return c&&(t=t.append("status",c)),p&&(t=t.append("items.product._id",p)),P&&(t=t.append("user",P)),l&&g&&(t=t.append("startDate",new u.vh("fr").transform(l,"YYYY-MM-dd")),t=t.append("endDate",new u.vh("fr").transform(g,"YYYY-MM-dd"))),t=t.append("enable",r),yield(0,O.s)(a.http.get(`${a.base_url}reporting/distributor-volume`,{params:t}))}catch(t){return yield a.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}getTotalPoint(s){var a=this;return(0,M.A)(function*(){try{let t=new d.Nl;const{status:c,product:p,enable:r=!0,region:P}=s;return c&&(t=t.append("status",c)),p&&(t=t.append("items.product._id",p)),P&&(t=t.append("payment.mode.id",P)),t=t.append("enable",r),yield(0,O.s)(a.http.get(`${a.base_url}reporting/retail-points`,{params:t}))}catch(t){return yield a.commonSrv.showToast({message:t.error.message,color:"danger"}),t}})()}static{this.\u0275fac=function(a){return new(a||_)(m.KVO(d.Qq),m.KVO(b.K),m.KVO(n.h))}}static{this.\u0275prov=m.jDH({token:_,factory:_.\u0275fac,providedIn:"root"})}}return _})()},94440:(y,C,o)=>{o.d(C,{c:()=>u});var M=o(2978);let u=(()=>{class d{transform(i,...m){return i?i.length>m[0]?`${i.substring(0,m[0]-3)}...`:i:""}static{this.\u0275fac=function(m){return new(m||d)}}static{this.\u0275pipe=M.EJ8({name:"truncateString",type:d,pure:!0})}}return d})()}}]);