"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8110],{58110:(G,C,i)=>{i.r(C),i.d(C,{SchedulePageModule:()=>z});var r=i(77897),O=i(24608),g=i(37222),p=i(74657),u=i(56610),v=i(93887),M=i(77575),m=i(73308),l=i(99987),h=i(73014),b=i(58133),n=i(2978),f=i(82571),y=i(14599),_=i(61095),P=i(62049);function S(o,s){if(1&o){const e=n.RV6();n.j41(0,"ion-button",7),n.bIt("click",function(){n.eBV(e);const c=n.XpG();return n.Njj(c.scheduleSrv.editSchedule())}),n.EFF(1),n.nI1(2,"translate"),n.k0s()}if(2&o){const e=n.XpG();n.Y8G("disabled",0===e.scheduleSrv.remainingQtyTonne),n.R7$(1),n.SpI(" ",n.bMT(2,2,"order-new-page.schedule-list.new-line")," ")}}function T(o,s){1&o&&(n.j41(0,"ion-label",16),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&o&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,"order-new-page.schedule-list.camions"),""))}function k(o,s){if(1&o&&(n.j41(0,"ion-label",11),n.EFF(1),n.k0s()),2&o){const e=n.XpG().$implicit;n.R7$(1),n.SpI("",null==e?null:e.hourTime," ")}}function w(o,s){if(1&o&&(n.j41(0,"ion-label",16),n.EFF(1),n.nI1(2,"number"),n.k0s()),2&o){const e=n.XpG().$implicit;n.R7$(1),n.SpI("",n.brH(2,1,e.nbrTruck,"","fr")," ")}}function F(o,s){if(1&o){const e=n.RV6();n.j41(0,"div",17)(1,"ion-label",10),n.EFF(2),n.nI1(3,"date"),n.k0s(),n.DNE(4,k,2,1,"ion-label",18),n.DNE(5,w,3,5,"ion-label",12),n.j41(6,"ion-label",13),n.EFF(7),n.nI1(8,"number"),n.k0s(),n.j41(9,"div",19)(10,"div",20),n.bIt("click",function(){const a=n.eBV(e).index,d=n.XpG(2);return n.Njj(d.removeLine(a))}),n.nrm(11,"img",21),n.k0s()()()}if(2&o){const e=s.$implicit;n.R7$(2),n.JRh(n.i5U(3,4,e.removalDate,"dd/MM/yyyy")),n.R7$(2),n.Y8G("ngIf",null==e?null:e.hourTime),n.R7$(1),n.Y8G("ngIf",e.nbrTruck),n.R7$(2),n.SpI("",n.brH(8,7,e.nbrTonnage,"","fr")," T")}}function x(o,s){if(1&o&&(n.j41(0,"div",8)(1,"ion-list")(2,"div",9)(3,"ion-label",10),n.EFF(4,"Date"),n.k0s(),n.j41(5,"ion-label",11),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.DNE(8,T,3,3,"ion-label",12),n.j41(9,"ion-label",13),n.EFF(10,"Tonnage"),n.k0s(),n.nrm(11,"ion-label",14),n.k0s(),n.DNE(12,F,12,11,"div",15),n.k0s()()),2&o){const e=n.XpG();n.R7$(6),n.SpI(" ",n.bMT(7,3,"order-new-page.schedule-list.heure"),""),n.R7$(2),n.Y8G("ngIf",1===e.scheduleSrv.schedulesTypeSelected),n.R7$(4),n.Y8G("ngForOf",e.scheduleSrv.schedules)}}let I=(()=>{class o{constructor(e,t,c,a,d){this.modalCtrl=e,this.scheduleSrv=t,this.alertController=c,this.translateService=a,this.navController=d}ngOnInit(){}closeModal(e){this.modalCtrl.dismiss(e)}removeLine(e){var t=this;return(0,m.A)(function*(){yield(yield t.alertController.create({header:t.translateService.currentLang===l.T.French?"Supprimer la ligne":"Delete the line",message:t.translateService.currentLang===l.T.French?" Vous \xeates sur le point de supprimer cette ligne de vos enl\xe8vements, cela ne peut \xeatre d\xe9fait. ":"You are about to delete this line from your removals, it cannot be undone",buttons:[{text:t.translateService.currentLang===l.T.French?"Annuler":"Cancel",role:"annuler",handler:()=>{}},{text:t.translateService.currentLang===l.T.French?"Supprimer":"Delete",role:"changer",handler:()=>{t.removeTonne(e)}}]})).present()})()}removeTonne(e){this.scheduleSrv.schedules.splice(e,1);let t=this.scheduleSrv.getTotalTonnage();return this.scheduleSrv.remainingQtyTonne=this.tonneQuantity-t,this.navController.navigateRoot("/order/new/second-step"),Math.round(100*this.scheduleSrv.remainingQtyTonne)/100}modalReset(){var e=this;return(0,m.A)(function*(){e.scheduleSrv?.schedules.length>0&&(yield(yield e.alertController.create({header:e.translateService.currentLang===l.T.French?"R\xe9initialisation":"Reset",message:e.translateService.currentLang===l.T.French?"Vous \xeates sur le point r\xe9initialiser vos planifications, cela effacera toutes vos lignes d'enl\xe8vement. ":"You are about to reset your plans, this will erase all your removal lines.",buttons:[{text:e.translateService.currentLang===l.T.French?"Annuler":"Cancel",role:"annuler",handler:()=>{}},{text:e.translateService.currentLang===l.T.French?"R\xe9initialiser":"Reset",role:"changer",handler:()=>{e.resetSchedules()}}]})).present())})()}resetSchedules(){this.scheduleSrv.schedules=[],this.scheduleSrv.remainingQtyTonne=this.tonneQuantity?this.tonneQuantity:0,this.navController.navigateRoot("/order/new/second-step")}static{this.\u0275fac=function(t){return new(t||o)(n.rXU(r.W3),n.rXU(_.l),n.rXU(r.hG),n.rXU(P.E),n.rXU(r.q9))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-schedule-list"]],inputs:{tonneQuantity:"tonneQuantity",unit:"unit"},decls:12,vars:5,consts:[[1,"scheudle-list"],[1,"scheudle-list-container","bottom-sheet-content"],[1,"header-cart","ion-padding"],[1,"left-col"],["src","assets/icons/refresh-outline.svg",3,"click"],["class","btn add-line","color","primary",3,"disabled","click",4,"ngIf"],["class","wrapper",4,"ngIf"],["color","primary",1,"btn","add-line",3,"disabled","click"],[1,"wrapper"],[1,"item-schedule"],[1,"item","date"],[1,"item","hour"],["class","item truck",4,"ngIf"],[1,"item","qautity"],[1,"item","action"],["class","item-schedule","detail","false",4,"ngFor","ngForOf"],[1,"item","truck"],["detail","false",1,"item-schedule"],["class","item hour",4,"ngIf"],[1,"action"],[1,"item","btn--action",3,"click"],["src","/assets/icons/moins-white.svg"]],template:function(t,c){1&t&&(n.j41(0,"div",0)(1,"div",1)(2,"ion-header")(3,"div",2)(4,"ion-label"),n.EFF(5),n.nI1(6,"translate"),n.k0s(),n.j41(7,"div",3)(8,"ion-img",4),n.bIt("click",function(){return c.modalReset()}),n.k0s(),n.DNE(9,S,3,4,"ion-button",5),n.k0s()()(),n.j41(10,"ion-content"),n.DNE(11,x,13,5,"div",6),n.k0s()()()),2&t&&(n.R7$(5),n.JRh(n.bMT(6,3,"order-new-page.schedule-list.title")),n.R7$(4),n.Y8G("ngIf",(null==c.scheduleSrv.schedules?null:c.scheduleSrv.schedules.length)>0),n.R7$(2),n.Y8G("ngIf",(null==c.scheduleSrv.schedules?null:c.scheduleSrv.schedules.length)>0))},dependencies:[u.Sq,u.bT,r.Jm,r.W9,r.eU,r.KW,r.he,r.nf,u.QX,u.vh,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.scheudle-list[_ngcontent-%COMP%]{margin-bottom:1em;padding:0 calc(41 * var(--res));flex:1;height:100%}.scheudle-list[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]{height:82%}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]{padding:unset}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header-cart[_ngcontent-%COMP%]{background:#f5f5f4;display:flex;align-items:center;justify-content:space-between;padding:.5em}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header-cart[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:AquawaxPro-Bold;font-size:calc(40 * var(--res))}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header-cart[_ngcontent-%COMP%]   .left-col[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:14px}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header-cart[_ngcontent-%COMP%]   .left-col[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:20px}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header-cart[_ngcontent-%COMP%]   .left-col[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{font-size:10px}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:13px;padding:8px 0;font-family:Mont Light}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .item-schedule[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid var(--ion-color-medium)}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .item-schedule[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{width:22%}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .item-schedule[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{width:24%}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .item-schedule[_ngcontent-%COMP%]   .action[_ngcontent-%COMP%]{width:10%;display:flex;justify-content:center;align-items:center}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .item-schedule[_ngcontent-%COMP%]   .action[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{margin-bottom:10px}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .item-schedule[_ngcontent-%COMP%]   .btn--action[_ngcontent-%COMP%]{background-color:var(--ion-color-secondary);width:20px;height:20px;border-radius:50%;justify-content:center;align-items:center}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column;gap:1em;height:75%}.scheudle-list[_ngcontent-%COMP%]   .scheudle-list-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:35%;padding:1rem 0}"]})}}return o})();function R(o,s){if(1&o&&(n.j41(0,"div",16)(1,"ion-label",7),n.EFF(2),n.nI1(3,"translate"),n.j41(4,"span",20),n.EFF(5),n.k0s()()()),2&o){const e=n.XpG();n.R7$(2),n.SpI(" ",n.bMT(3,2,"order-new-page.planning-modal.remainning-quantity")," : "),n.R7$(3),n.SpI(" ",null==e.scheduleSrv?null:e.scheduleSrv.remainingQtyTonneInStock,"T")}}const j=function(o,s){return{value1:o,value2:s}},Q=[{path:"",component:(()=>{class o{constructor(e,t,c,a,d){this.commonSrv=e,this.modalCtrl=t,this.storageSrv=c,this.scheduleSrv=a,this.translateService=d,this.currentDate=new Date,this.totalTrucks=0,this.statusForFrench={equal:{message:"Votre planification a atteint la quantit\xe9 totale \xe0 retirer",isGoodPlannify:!0},superior:{message:"Votre planification a d\xe9pass\xe9 la quantit\xe9 totale \xe0 retirer veuillez r\xe9duire votre quantit\xe9",isGoodPlannify:!1},inferior:{message:"Votre planification n'a pas atteint la quantit\xe9 totale \xe0 retirer veuillez augmenter votre quantit\xe9",isGoodPlannify:!1}},this.statusForEnglish={equal:{message:"Your planning has reached the total amount to be removed",isGoodPlannify:!0},superior:{message:"Your planning has exceeded the total quantity to be removed please reduce your quantity",isGoodPlannify:!1},inferior:{message:"Your planning has not reached the total quantity to be withdrawn please increase your quantity",isGoodPlannify:!1}}}ngOnInit(){var e=this;return(0,m.A)(function*(){e.resetData(),e.scheduleSrv.removals=JSON.parse(e.storageSrv.load("removals"))??[],e.scheduleSrv.getRemovals(),yield e.scheduleSrv.getRemovalsObject(),e.isUpdate?(e.scheduleSrv.scheduleForm?.patchValue({tonneQuantity:e.scheduleSrv?.currentRemoval?.tonneQuantity}),e.scheduleSrv.remainingQtyTonne=0):e.scheduleSrv.getRemainingQuantityStock()})()}ngOnDestroy(){this.scheduleSrv.remainingQtyTonne=0}trackByFn(e,t){return e}resetData(){this.scheduleSrv.remainingQtyTonneInStock=0,this.scheduleSrv.currentRemoval=new h.J7,this.scheduleSrv.removalObject=new h.iB,this.scheduleSrv.remainingQtyTonne=0,this.scheduleSrv.scheduleForm.reset(),this.scheduleSrv.isGoodPlannify=!1}closeModal(){this.resetData(),this.modalCtrl.dismiss()}addQuantity(e){e?.detail?.value<0&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===l.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),this.scheduleSrv.scheduleForm.patchValue({tonneQuantity:null})),this.scheduleSrv.verifyCanRemoval(e.detail.value/this.ratio)?this.scheduleSrv.scheduleForm.patchValue({tonneQuantity:null}):(this.scheduleSrv.remainingQtyTonne=this.isUpdate&&0===this.scheduleSrv.remainingQtyTonne?this.scheduleSrv.remainingQtyTonne:parseFloat((e.detail.value/this.ratio).toFixed(3)),this.scheduleSrv.scheduleForm.patchValue({tonneQuantity:e.detail.value/this.ratio}),!this.scheduleSrv.verifyCanRemoval(this.scheduleSrv.scheduleForm.get("tonneQuantity").value)&&!this.isUpdate&&0===this.scheduleSrv.schedules?.length&&(this.scheduleSrv.schedulesTypeSelected=this.scheduleSrv.remainingQtyTonne>this.scheduleSrv.dailyCeiling?h.JN.perDate:h.JN.PerQuantity))}addTonneQuantity(e){var t=this;return(0,m.A)(function*(){return e?.detail?.value<0?(yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===l.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),t.scheduleSrv.scheduleForm.patchValue({quantity:null})):t.commonSrv?.user?.category===b.s.EmployeeLapasta&&e?.detail?.value>t.commonSrv?.user?.tonnage?.capacityPerYear?(yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===l.T.French?`Vous avez exceder votre capacit\xe9 de tonnage annuelle restante (${t.commonSrv?.user?.tonnage?.capacityPerYear} T)`:`You have exceded your remaining annual tonnage capacity (${t.commonSrv?.user?.tonnage?.capacityPerYear} T)`}),t.scheduleSrv.scheduleForm.patchValue({quantity:null})):t.scheduleSrv.verifyCanRemoval(e.detail.value)?t.scheduleSrv.scheduleForm.patchValue({quantity:null}):(t.scheduleSrv.remainingQtyTonne=t.isUpdate&&0===t.scheduleSrv.remainingQtyTonne?t.scheduleSrv.remainingQtyTonne:e.detail.value,t.scheduleSrv.scheduleForm.patchValue({quantity:e.detail.value*t.ratio}),void(!t.isUpdate&&0===t.scheduleSrv.schedules?.length&&(t.scheduleSrv.schedulesTypeSelected=t.scheduleSrv.remainingQtyTonne>t.scheduleSrv.dailyCeiling?h.JN.perDate:h.JN.PerQuantity)))})()}saveSchedule(){var e=this;return(0,m.A)(function*(){e.schedule={schedules:e.scheduleSrv.schedules,itemId:e.scheduleSrv.cartItem.product._id,removalType:e.scheduleSrv.schedulesTypeSelected,unitPrice:e.scheduleSrv.cartItem.unitPrice,...e.scheduleSrv.scheduleForm.value};const t=yield e.calculateTotalTrucks(e.scheduleSrv.removals);if((yield e.calculateTotalTrucks([e.schedule]))+t>10){const a=e.translateService.currentLang===l.T.French?"Le nombre total de camions d\xe9passe 10. Veuillez r\xe9duire le nombre camions.":"The total number of trucks exceeds 10. Please decrease the trucks.";return yield e.commonSrv.showToast({color:"warning",message:a})}e.scheduleSrv.removals.push(e.schedule),e.scheduleSrv.cartItem.quantity=e.scheduleSrv.scheduleForm.get("quantity").value,e.storageSrv.store("removals",JSON.stringify(e.scheduleSrv.removals)),e.scheduleSrv.currentRemoval=new h.J7,e.resetData(),e.modalCtrl.dismiss(e.scheduleSrv.cartItem)})()}calculateTotalTrucks(e){var t=this;return(0,m.A)(function*(){t.totalTrucks=0;for(const c of e)for(const a of c.schedules)t.totalTrucks+=a.nbrTruck;return t.totalTrucks})()}static{this.\u0275fac=function(t){return new(t||o)(n.rXU(f.h),n.rXU(r.W3),n.rXU(y.n),n.rXU(_.l),n.rXU(P.E))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-schedule"]],inputs:{ratio:"ratio",unit:"unit",isUpdate:"isUpdate"},decls:36,vars:27,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],["color","primary",1,"title"],[1,"fMedium","color-dark"],["id","content",3,"formGroup","ngSubmit"],["class","form-group padding-horizontal last-form",4,"ngIf"],[1,"form-group","padding-horizontal"],[1,"title"],[1,"mbottom","quantities"],[1,"ion-justify-content-between"],[1,"unit"],[1,"fMedium"],["locale","fr-FR","min","0","formControlName","quantity","type","number","pattern","[0-9]*","step","1","placeholder","0","clearInput","",3,"readonly","ionChange"],[1,"tonne"],["locale","fr-FR","min","0","formControlName","tonneQuantity","type","number","clearInput","","placeholder","0",3,"readonly","ionChange"],[1,"schedule-list",3,"tonneQuantity","unit"],[1,"form-group","padding-horizontal","last-form"],["color","primary",1,"fMedium","mbottom"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper"],[1,"primary-text","fMedium","mbottom"]],template:function(t,c){if(1&t&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return c.closeModal()}),n.k0s(),n.j41(3,"ion-title",2)(4,"span",3),n.EFF(5),n.nI1(6,"translate"),n.k0s(),n.EFF(7),n.k0s()()(),n.j41(8,"ion-content")(9,"form",4),n.bIt("ngSubmit",function(){return c.saveSchedule()}),n.DNE(10,R,6,4,"div",5),n.j41(11,"div",6)(12,"ion-label",7),n.EFF(13),n.nI1(14,"translate"),n.k0s(),n.j41(15,"ion-grid",8)(16,"ion-row",9)(17,"ion-col",10)(18,"ion-label",11),n.EFF(19),n.k0s(),n.j41(20,"ion-input",12),n.bIt("ionChange",function(d){return c.addQuantity(d)}),n.k0s()(),n.j41(21,"ion-col",13)(22,"ion-label",11),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"ion-input",14),n.bIt("ionChange",function(d){return c.addTonneQuantity(d)}),n.k0s()()()()(),n.nrm(26,"app-schedule-list",15),n.j41(27,"div",16)(28,"ion-label",17),n.EFF(29),n.nI1(30,"translate"),n.k0s()(),n.j41(31,"div",18)(32,"ion-button",19)(33,"ion-label"),n.EFF(34),n.nI1(35,"translate"),n.k0s()()()()()),2&t){let a;n.R7$(5),n.SpI(" ",n.bMT(6,13,"order-new-page.planning-modal.title"),""),n.R7$(2),n.SpI(" ",null==c.scheduleSrv.cartItem||null==c.scheduleSrv.cartItem.product?null:c.scheduleSrv.cartItem.product.label,""),n.R7$(2),n.Y8G("formGroup",c.scheduleSrv.scheduleForm),n.R7$(1),n.Y8G("ngIf",null==c.scheduleSrv?null:c.scheduleSrv.remainingQtyTonneInStock),n.R7$(3),n.SpI(" ",n.bMT(14,15,"order-new-page.planning-modal.quantity")," "),n.R7$(6),n.SpI(" ",null==c.scheduleSrv||null==c.scheduleSrv.cartItem||null==c.scheduleSrv.cartItem.packaging?null:c.scheduleSrv.cartItem.packaging.label," "),n.R7$(1),n.Y8G("readonly",(null==c.scheduleSrv||null==c.scheduleSrv.schedules?null:c.scheduleSrv.schedules.length)>0),n.R7$(3),n.SpI(" ",n.bMT(24,17,"order-new-page.planning-modal.tons"),""),n.R7$(2),n.Y8G("readonly",(null==c.scheduleSrv.schedules?null:c.scheduleSrv.schedules.length)>0),n.R7$(1),n.Y8G("tonneQuantity",null==c.scheduleSrv.scheduleForm||null==(a=c.scheduleSrv.scheduleForm.get("tonneQuantity"))?null:a.value)("unit",c.unit),n.R7$(3),n.SpI(" ",n.i5U(30,19,"order-new-page.planning-modal.remains",n.l_i(24,j,c.scheduleSrv.remainingQtyTonne,c.scheduleSrv.scheduleForm.get("tonneQuantity").value||0))," "),n.R7$(5),n.SpI(" ",n.bMT(35,22,"order-new-page.planning-modal.save-button-label")," ")}},dependencies:[u.bT,r.Jm,r.hU,r.W9,r.lO,r.eU,r.KW,r.$w,r.he,r.ln,r.BC,r.ai,r.su,g.qT,g.BC,g.cb,g.R_,g.j4,g.JD,I,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--border-width: 0;--border-width: 0px;padding-right:0;padding-left:0;--background: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;color:var(--ion-color-primary)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .color-dark[_ngcontent-%COMP%]{color:#000!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;color:#000;display:flex;flex-direction:column;height:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .primary-text[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;--padding-start: 0;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: var(--space-4) !important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .unit[_ngcontent-%COMP%]{margin-right:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .tonne[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{margin-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .full-input[_ngcontent-%COMP%]{margin:.5em 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .btn-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-schedule[_ngcontent-%COMP%]{margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{font-size:10px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]   .ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#1e1e1e!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fMedium[_ngcontent-%COMP%]{font-family:Mont Light}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{padding:calc(25 * var(--res)) 0 calc(75 * var(--res)) 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(30 * var(--res));line-height:initial}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-reduce[_ngcontent-%COMP%]{width:calc(50 * var(--res));height:calc(50 * var(--res));--padding-top: 0;--padding-bottom: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-reduce[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1em}ion-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));width:100%}.schedule-list[_ngcontent-%COMP%]{height:100%}ion-modal#modal-schedules[_ngcontent-%COMP%]{--width: fit-content;--min-width: 90%;--height: fit-content;--border-radius: 6px;--box-shadow: 0 28px 48px rgba(0, 0, 0, .4)}ion-modal#modal-schedules[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:20px 20px 10px}ion-modal#modal-schedules[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:6px;width:48px;height:48px;padding:4px 0;color:#aaa}ion-modal#modal-schedules[_ngcontent-%COMP%]   .wrapper[_ngcontent-%COMP%]{margin-bottom:10px;padding:.5rem}.item-native[_ngcontent-%COMP%]{padding-inline-start:0}"]})}}return o})()}];let $=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[M.iI.forChild(Q),M.iI]})}}return o})(),z=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({providers:[u.vh],imports:[u.MD,r.bv,O.vj,v.G,p.h,g.X1,$]})}}return o})()}}]);