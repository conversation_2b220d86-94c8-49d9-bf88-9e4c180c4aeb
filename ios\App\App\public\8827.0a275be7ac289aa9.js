"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8827],{48827:(y,b,l)=>{l.r(b),l.d(b,{CompanyAccountDetailPageModule:()=>en});var m=l(56610),_=l(37222),c=l(77897),P=l(77575),p=l(73308),n=l(2978),h=l(43556),v=l(82571),C=l(61955),O=l(74657);function s(o,g){if(1&o&&(n.j41(0,"ion-card")(1,"ion-card-content"),n.nrm(2,"ion-icon",1),n.j41(3,"div",2)(4,"div",3)(5,"ion-label",4),n.EFF(6),n.k0s()(),n.j41(7,"div",5)(8,"ion-label",6),n.<PERSON><PERSON>(9),n.nI1(10,"translate"),n.k0s(),n.j41(11,"ion-text",7),n.EFF(12),n.k0s()(),n.j41(13,"div",5)(14,"ion-label",6),n.EFF(15),n.nI1(16,"translate"),n.k0s(),n.j41(17,"ion-text",7),n.EFF(18),n.k0s()()()()()),2&o){const t=g.$implicit;n.R7$(6),n.JRh((null==t?null:t.firstName)||"N/A"),n.R7$(3),n.JRh(n.bMT(10,5,"company-detail-page.account.phone-field")),n.R7$(3),n.JRh((null==t?null:t.tel)||"N/A"),n.R7$(3),n.JRh(n.bMT(16,7,"company-detail-page.account.email-field")),n.R7$(3),n.JRh((null==t?null:t.email)||"N/A")}}let d=(()=>{class o{constructor(){}ngOnInit(){}static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-account"]],inputs:{usersCompany:"usersCompany"},decls:1,vars:1,consts:[[4,"ngFor","ngForOf"],["color","primary","size","large","name","person-circle",1,"account"],[1,"card-content-line"],[1,"card-line","label-name"],[1,"label"],[1,"card-line","label-info"],[1,"mx-width","value"],[1,"value"]],template:function(a,e){1&a&&n.DNE(0,s,19,9,"ion-card",0),2&a&&n.Y8G("ngForOf",e.usersCompany)},dependencies:[m.Sq,c.b_,c.I9,c.iq,c.he,c.IO,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-card-content[_ngcontent-%COMP%]{padding-right:calc(41 * var(--res));padding-left:calc(25 * var(--res));padding-block:calc(25 * var(--res));display:flex;align-items:center;gap:calc(17 * var(--res))}ion-card-content[_ngcontent-%COMP%]   .account[_ngcontent-%COMP%]{height:calc(150 * var(--res));width:calc(150 * var(--res));color:#143c5d}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]{width:calc(100% - 150 * var(--res))}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:calc(25 * var(--res));margin-block:calc(15 * var(--res))}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#143c5d}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{color:#000;text-align:right;text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:calc(35 * var(--res));font-weight:500}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]   .label-info[_ngcontent-%COMP%]{font-size:small}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]   .label-name[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:700}ion-card-content[_ngcontent-%COMP%]   .card-content-line[_ngcontent-%COMP%]   .mx-width[_ngcontent-%COMP%]{min-width:max-content}"]})}}return o})();function r(o,g){if(1&o&&(n.j41(0,"ion-card")(1,"ion-card-content")(2,"div",1)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-text"),n.EFF(7),n.k0s()(),n.j41(8,"div",1)(9,"ion-label"),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.j41(12,"ion-text"),n.EFF(13),n.k0s()(),n.j41(14,"div",1)(15,"ion-label"),n.EFF(16),n.nI1(17,"translate"),n.k0s(),n.j41(18,"ion-text"),n.EFF(19),n.k0s()()()()),2&o){const t=g.$implicit;n.R7$(4),n.JRh(n.bMT(5,6,"company-detail-page.address.description")),n.R7$(3),n.JRh((null==t?null:t.label)||""),n.R7$(3),n.JRh(n.bMT(11,8,"company-detail-page.address.usine")),n.R7$(3),n.JRh((null==t?null:t.startRef.label)||""),n.R7$(3),n.JRh(n.bMT(17,10,"company-detail-page.address.shipTo")),n.R7$(3),n.JRh((null==t?null:t.erpShipToId)||"")}}let f=(()=>{class o{constructor(){}ngOnInit(){}static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-address"]],inputs:{companyShippings:"companyShippings"},decls:1,vars:1,consts:[[4,"ngFor","ngForOf"],[1,"card-line"]],template:function(a,e){1&a&&n.DNE(0,r,20,12,"ion-card",0),2&a&&n.Y8G("ngForOf",e.companyShippings)},dependencies:[m.Sq,c.b_,c.I9,c.he,c.IO,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-card-content[_ngcontent-%COMP%]{padding-inline:calc(50 * var(--res));padding-block:calc(25 * var(--res))}ion-card-content[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:space-between;gap:1rem;margin-block:calc(15 * var(--res));color:#000;font-size:calc(42.5 * var(--res))}ion-card-content[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:700;min-width:max-content}ion-card-content[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{text-align:right}"]})}}return o})();var M=l(35025),k=l.n(M),R=l(94440);const w=["popover"],F=["popover1"],I=["popover2"],x=["popover3"],T=["popover4"];function E(o,g){if(1&o&&(n.j41(0,"ion-label",29),n.EFF(1),n.nI1(2,"number"),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.SpI(" ",n.brH(2,1,null==t.balance?null:t.balance.availableBalance,"","fr-FR")," XAF")}}function $(o,g){if(1&o&&(n.j41(0,"ion-label",30),n.EFF(1),n.nI1(2,"translate"),n.nI1(3,"translate"),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.LHq("",n.bMT(2,4,"account-balance-page.description-balance")," ",t.getDate(null==t.balance?null:t.balance.date,"day")," ",n.bMT(3,6,"account-balance-page.at")," ",t.getDate(null==t.balance?null:t.balance.date,"time"),"")}}function j(o,g){1&o&&(n.j41(0,"ion-content",31),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&o&&(n.R7$(1),n.JRh(n.bMT(2,1,"account-balance-page.limit-credit-desc")))}function D(o,g){1&o&&(n.j41(0,"ion-content",31),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&o&&(n.R7$(1),n.JRh(n.bMT(2,1,"account-balance-page.amountAvailable")))}function S(o,g){1&o&&(n.j41(0,"ion-content",31),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&o&&(n.R7$(1),n.JRh(n.bMT(2,1,"account-balance-page.open-order-desc")))}function A(o,g){1&o&&(n.j41(0,"ion-content",31),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&o&&(n.R7$(1),n.JRh(n.bMT(2,1,"account-balance-page.others-bill-desc")))}function B(o,g){1&o&&(n.j41(0,"ion-content",31),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&o&&(n.R7$(1),n.JRh(n.bMT(2,1,"account-balance-page.bill-fail-desc")))}function G(o,g){if(1&o&&n.nrm(0,"p-chart",32),2&o){const t=n.XpG();n.Y8G("data",t.dataSaleEvolutionsBar)("options",t.chartOptions)("height","9rem")}}const z=function(o){return{"background-color":o}};function N(o,g){if(1&o&&(n.j41(0,"div",33),n.nrm(1,"span",34),n.j41(2,"ion-label"),n.EFF(3),n.k0s()()),2&o){const t=g.$implicit,a=g.index,e=n.XpG();n.R7$(1),n.Y8G("ngStyle",n.eq3(2,z,null==e.dataSaleEvolutionsBar?null:e.dataSaleEvolutionsBar.datasets[0].backgroundColor[a])),n.R7$(2),n.JRh(t)}}function Y(o,g){if(1&o){const t=n.RV6();n.j41(0,"div",9)(1,"ion-list",35)(2,"ion-item")(3,"div",11)(4,"ion-label",12),n.EFF(5),n.nI1(6,"translate"),n.k0s(),n.j41(7,"ion-img",13),n.bIt("click",function(e){n.eBV(t);const i=n.XpG();return n.Njj(i.presentInvoiceFalied(e))}),n.k0s()(),n.j41(8,"ion-label",14),n.EFF(9),n.nI1(10,"number"),n.k0s()()()()}if(2&o){const t=n.XpG();n.R7$(5),n.JRh(n.bMT(6,3,"account-balance-page.bill-fail")),n.R7$(4),n.Lme("",n.bMT(10,5,null==t.balance?null:t.balance.invoicedDelayed)||"Non renseign\xe9",""," ","XAF")}}let L=(()=>{class o{constructor(){this.dataSaleEvolutionsBar={},this.chartOptions={plugins:{legend:{display:!0}}}}ngOnInit(){console.log(this.balance),this.balance&&this.chartData()}presentCreditLimit(t){this.popover.event=t,this.isLimitCredit=!0}prsentAccountBalance(t){this.popover1.event=t,this.isAccountBalance=!0}presentOpenedOrder(t){this.popover2.event=t,this.isOpenedOrder=!0}presentOtherNotes(t){this.popover3.event=t,this.isOtherNotes=!0}presentInvoiceFalied(t){this.popover4.event=t,this.isInvoiceFalied=!0}printPrice(t){return t?`${Number(t).toLocaleString("fr-FR",{style:"currency",currency:"XAF"})}`:"N/A"}getDate(t,a){return"day"===a?k()(t).format("DD-MM-YYYY"):k()(t).format("HH:mm")}chartData(){var t=this;return(0,p.A)(function*(){t.isLoading=!0,t.dataSaleEvolutionsBar={labels:["Solde disponible","Solde Comptable","Commandes ouvertes","Autres notes de d\xe9bit"],datasets:[{data:[t.balance?.othersAmount,t.balance?.creditLimit,t.balance?.openOrderAmount,t.balance?.invoiceInProgress],backgroundColor:["#4CD964","#007bff","#E30613","#173A64"]}]},t.chartOptions={plugins:{legend:{display:!1}}},t.dataSaleEvolutionsBar?.labels?.forEach((a,e)=>{const i=t.generateHexColor();t.dataSaleEvolutionsBar.datasets[0].backgroundColor?.push(i)}),t.isLoading=!1})()}generateHexColor(){const t=Math.floor(16777215*Math.random()).toString(16);return"#"+"0".repeat(6-t.length)+t}static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-balance"]],viewQuery:function(a,e){if(1&a&&(n.GBs(w,5),n.GBs(F,5),n.GBs(I,5),n.GBs(x,5),n.GBs(T,5)),2&a){let i;n.mGM(i=n.lsd())&&(e.popover=i.first),n.mGM(i=n.lsd())&&(e.popover1=i.first),n.mGM(i=n.lsd())&&(e.popover2=i.first),n.mGM(i=n.lsd())&&(e.popover3=i.first),n.mGM(i=n.lsd())&&(e.popover4=i.first)}},inputs:{balance:"balance"},decls:76,vars:46,consts:[[1,"contener-balance"],[1,"head-balance"],[1,"illustration-container"],[1,"user-info"],["src","../../assets/icons/avatar.svg","name","person-circle-outline"],[1,"details-balance"],[1,"value"],["class","balance","color","primary",4,"ngIf"],["class","value date",4,"ngIf"],[1,"amount-card"],[1,"custom-list"],[1,"item-label"],[1,"title"],["src","../../../assets/icons/DangerCircle.svg",3,"click"],["slot","end",1,"value"],["trigger","top-center","side","top","alignment","center",1,"account-pop",3,"isOpen","didDismiss"],["popover",""],["popover1",""],["popover2",""],["popover3",""],["popover4",""],[1,"ion-content"],[1,"p-card"],[1,"p-card-header"],[1,"chart-container"],["type","doughnut",3,"data","options","height",4,"ngIf"],[1,"labels-container"],["class","label-item",4,"ngFor","ngForOf"],["class","amount-card",4,"ngIf"],["color","primary",1,"balance"],[1,"value","date"],[1,"ion-padding","content-pop"],["type","doughnut",3,"data","options","height"],[1,"label-item"],[1,"label-color",3,"ngStyle"],[1,"danger-car"]],template:function(a,e){1&a&&(n.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),n.nrm(4,"ion-img",4),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"truncateString"),n.k0s()(),n.j41(8,"div",5)(9,"ion-label",6),n.EFF(10),n.k0s(),n.DNE(11,E,3,5,"ion-label",7),n.DNE(12,$,4,8,"ion-label",8),n.k0s()()(),n.j41(13,"div",9)(14,"ion-list",10)(15,"ion-item")(16,"div",11)(17,"ion-label",12),n.EFF(18),n.nI1(19,"translate"),n.k0s(),n.j41(20,"ion-img",13),n.bIt("click",function(u){return e.presentCreditLimit(u)}),n.k0s()(),n.j41(21,"ion-label",14),n.EFF(22),n.nI1(23,"number"),n.k0s()(),n.j41(24,"ion-item")(25,"div",11)(26,"ion-label",12),n.EFF(27),n.nI1(28,"translate"),n.k0s(),n.j41(29,"ion-img",13),n.bIt("click",function(u){return e.prsentAccountBalance(u)}),n.k0s()(),n.j41(30,"ion-label",14),n.EFF(31),n.nI1(32,"number"),n.k0s()(),n.j41(33,"ion-item")(34,"div",11)(35,"ion-label",12),n.EFF(36),n.nI1(37,"translate"),n.k0s(),n.j41(38,"ion-img",13),n.bIt("click",function(u){return e.presentOpenedOrder(u)}),n.k0s()(),n.j41(39,"ion-label",14),n.EFF(40),n.nI1(41,"number"),n.k0s()(),n.j41(42,"ion-item")(43,"div",11)(44,"ion-label",12),n.EFF(45),n.nI1(46,"translate"),n.k0s(),n.j41(47,"ion-img",13),n.bIt("click",function(u){return e.presentOtherNotes(u)}),n.k0s()(),n.j41(48,"ion-label",14),n.EFF(49),n.nI1(50,"number"),n.k0s()()()(),n.j41(51,"ion-popover",15,16),n.bIt("didDismiss",function(){return e.isLimitCredit=!1}),n.DNE(53,j,3,3,"ng-template"),n.k0s(),n.j41(54,"ion-popover",15,17),n.bIt("didDismiss",function(){return e.isAccountBalance=!1}),n.DNE(56,D,3,3,"ng-template"),n.k0s(),n.j41(57,"ion-popover",15,18),n.bIt("didDismiss",function(){return e.isOpenedOrder=!1}),n.DNE(59,S,3,3,"ng-template"),n.k0s(),n.j41(60,"ion-popover",15,19),n.bIt("didDismiss",function(){return e.isOtherNotes=!1}),n.DNE(62,A,3,3,"ng-template"),n.k0s(),n.j41(63,"ion-popover",15,20),n.bIt("didDismiss",function(){return e.isInvoiceFalied=!1}),n.DNE(65,B,3,3,"ng-template"),n.k0s(),n.j41(66,"div",21)(67,"div",22)(68,"div",23),n.EFF(69),n.nI1(70,"translate"),n.k0s(),n.j41(71,"div",24),n.DNE(72,G,1,3,"p-chart",25),n.j41(73,"div",26),n.DNE(74,N,4,4,"div",27),n.k0s()()()(),n.DNE(75,Y,11,7,"div",28),n.k0s()),2&a&&(n.R7$(6),n.JRh(n.i5U(7,25,(null==e.balance||null==e.balance.company?null:e.balance.company.name)||"N/A",20)),n.R7$(4),n.JRh("Solde"),n.R7$(1),n.Y8G("ngIf",null==e.balance?null:e.balance.availableBalance),n.R7$(1),n.Y8G("ngIf",null==e.balance?null:e.balance.date),n.R7$(6),n.JRh(n.bMT(19,28,"account-balance-page.creditLimit")),n.R7$(4),n.Lme("",n.bMT(23,30,null==e.balance?null:e.balance.creditLimit)||"Non renseign\xe9e"," "," ","XAF"),n.R7$(5),n.JRh(n.bMT(28,32,"account-balance-page.outstanding-bill")),n.R7$(4),n.Lme("",n.bMT(32,34,null==e.balance?null:e.balance.openOrderAmount)||"Non renseign\xe9e",""," ","XAF"),n.R7$(5),n.JRh(n.bMT(37,36,"account-balance-page.open-order")),n.R7$(4),n.Lme("",n.bMT(41,38,null==e.balance?null:e.balance.invoiceInProgress)||"Non renseign\xe9e",""," ","XAF"),n.R7$(5),n.JRh(n.bMT(46,40,"account-balance-page.others-bill")),n.R7$(4),n.Lme("",n.bMT(50,42,null==e.balance?null:e.balance.othersAmount)||"Non renseign\xe9e",""," ","XAF"),n.R7$(2),n.Y8G("isOpen",e.isLimitCredit),n.R7$(3),n.Y8G("isOpen",e.isAccountBalance),n.R7$(3),n.Y8G("isOpen",e.isOpenedOrder),n.R7$(3),n.Y8G("isOpen",e.isOtherNotes),n.R7$(3),n.Y8G("isOpen",e.isInvoiceFalied),n.R7$(6),n.JRh(n.bMT(70,44,"account-balance-page.histogram")),n.R7$(3),n.Y8G("ngIf",null==e.dataSaleEvolutionsBar||null==e.dataSaleEvolutionsBar.datasets?null:e.dataSaleEvolutionsBar.datasets.length),n.R7$(2),n.Y8G("ngForOf",null==e.dataSaleEvolutionsBar?null:e.dataSaleEvolutionsBar.labels),n.R7$(1),n.Y8G("ngIf",null==e.balance?null:e.balance.invoicedDelayed))},dependencies:[m.Sq,m.bT,m.B3,c.W9,c.KW,c.uz,c.he,c.nf,c.CF,m.QX,O.D9,R.c],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.contener-balance[_ngcontent-%COMP%]{padding-block:calc(25 * var(--res));height:100%}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]{display:flex;align-items:flex-start;flex-direction:column;gap:1em;height:8em;padding:var(--container-padding);background-position:center;background-size:cover;margin-bottom:1em;border-radius:16px;width:100%;background-image:url(/assets/images/amount-card.png)}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;font-size:1em;gap:.5em;font-family:Mont Regular}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:13%}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff;width:100%;font-size:var(--fs-14-px)}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]{display:flex;flex-direction:column;font-size:1em;font-family:Mont Regular}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%]{font-size:var(--fs-32-px);color:var(--clr-tertiary-600);font-family:Mont Bold}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:var(--fs-15-px);color:var(--clr-tertiary-600)}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-size:var(--fs-10-px)}.contener-balance[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .icon-home[_ngcontent-%COMP%]{height:48px;width:48px}.amount-card[_ngcontent-%COMP%]{margin-bottom:1em;max-width:calc(100% + -0px)}.amount-card[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]{padding:0 var(--container-padding);padding-bottom:16px}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]{background:#EEF6FF}.amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]{background:#FFEDED}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]{border-radius:10px}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   .last-item[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   .last-item[_ngcontent-%COMP%]{border-bottom:none}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: transparent;--inner-padding-end: 0 border-radius: 8px;width:calc(100% + 17px);display:flex;justify-content:center}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]::part(native), .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]::part(native){padding:0!important}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:last-of-type, .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:last-of-type{border-bottom:none}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:var(--fs-12-px);color:var(--clr-primary-900);font-family:Mont Regular}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-weight:600;font-size:15px;color:var(--clr-primary-900);font-family:Mont Bold}@media (max-width: 350px){.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:13px}}@media (min-width: 700px){.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:14px}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-weight:600;font-size:16px}}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]{display:flex;align-items:baseline}.amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%], .amount-card[_ngcontent-%COMP%]   .danger-car[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:7%}.ion-content[_ngcontent-%COMP%]{background:#EEF6FF;border-radius:10px}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{width:100%;height:13rem;padding-top:1rem!important;padding:var(--container-padding);margin-bottom:1em}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(42 * var(--res));color:#143c5d}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]{display:flex;align-items:center}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   p-chart[_ngcontent-%COMP%]{width:8rem}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .labels-container[_ngcontent-%COMP%]{display:flex;padding-left:.7em;flex-direction:column}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .label-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.5em}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .label-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:var(--fs-10-px);margin-left:12px;color:var(--clr-primary-900)}@media (max-width: 350px){.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .label-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:var(--fs-8-px)}}.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .label-color[_ngcontent-%COMP%]{width:.5rem;height:.5rem;display:inline-block;border-radius:50%}@media (max-width: 350px){.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .label-color[_ngcontent-%COMP%]{width:.3rem;height:.3rem}}@media (min-width: 520px){.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{height:17rem}}@media (max-width: 350px){.ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{height:13rem}}ion-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-around;gap:calc(25 * var(--res))}ion-list[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%], ion-list[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{width:100%;margin-block:calc(15 * var(--res));color:#000;font-size:calc(42.5 * var(--res))}ion-list[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], ion-list[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Light;font-weight:500;text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap}ion-list[_ngcontent-%COMP%]   .card-line[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%], ion-list[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:900}"]})}}return o})(),J=(()=>{class o{constructor(){this.precompteRate=[{value:5,label:"R\xe9el"},{value:2,label:"Simplifier"},{value:10,label:"Imp\xf4t lib\xe9ratoire"}],this.precompteRateEn=[{value:5,label:"Real"},{value:2,label:"Simplify"},{value:10,label:"Final tax"}],this.isEdit=!1}ngOnInit(){return(0,p.A)(function*(){})()}getPrecompteRate(t){return this.precompteRate.find(e=>e.value===t)?.label||"N/A"}static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-informations"]],inputs:{company:"company"},decls:61,vars:54,consts:[[1,"mbottom250"],[1,"input-group"],[1,"form-group"],[1,"field-info"],["position","floating"],[3,"readonly","value"]],template:function(a,e){1&a&&(n.j41(0,"form",0)(1,"div",1)(2,"ion-list",2)(3,"ion-item",3)(4,"ion-label",4),n.EFF(5),n.nI1(6,"translate"),n.k0s(),n.nrm(7,"ion-input",5),n.k0s(),n.j41(8,"ion-item",3)(9,"ion-label",4),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.nrm(12,"ion-input",5),n.k0s(),n.j41(13,"ion-item",3)(14,"ion-label",4),n.EFF(15),n.nI1(16,"translate"),n.k0s(),n.nrm(17,"ion-input",5),n.k0s(),n.j41(18,"ion-item",3)(19,"ion-label",4),n.EFF(20),n.nI1(21,"translate"),n.k0s(),n.nrm(22,"ion-input",5),n.k0s(),n.j41(23,"ion-item",3)(24,"ion-label",4),n.EFF(25),n.nI1(26,"translate"),n.k0s(),n.nrm(27,"ion-input",5),n.k0s(),n.j41(28,"ion-item",3)(29,"ion-label",4),n.EFF(30,"SolTo ID"),n.k0s(),n.nrm(31,"ion-input",5),n.k0s(),n.j41(32,"ion-item",3)(33,"ion-label",4),n.EFF(34,"ShipTo ID"),n.k0s(),n.nrm(35,"ion-input",5),n.k0s(),n.j41(36,"ion-item",3)(37,"ion-label",4),n.EFF(38),n.nI1(39,"translate"),n.k0s(),n.nrm(40,"ion-input",5),n.k0s(),n.j41(41,"ion-item",3)(42,"ion-label",4),n.EFF(43),n.nI1(44,"translate"),n.k0s(),n.nrm(45,"ion-input",5),n.k0s(),n.j41(46,"ion-item",3)(47,"ion-label",4),n.EFF(48),n.nI1(49,"translate"),n.k0s(),n.nrm(50,"ion-input",5),n.k0s(),n.j41(51,"ion-item",3)(52,"ion-label",4),n.EFF(53),n.nI1(54,"translate"),n.k0s(),n.nrm(55,"ion-input",5),n.k0s(),n.j41(56,"ion-item",3)(57,"ion-label",4),n.EFF(58),n.nI1(59,"translate"),n.k0s(),n.nrm(60,"ion-input",5),n.k0s()()()()),2&a&&(n.R7$(5),n.SpI(" ",n.bMT(6,34,"company-detail-page.information.name-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.name)||"N/A"),n.R7$(3),n.SpI(" ",n.bMT(11,36,"company-detail-page.information.status-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",e.getPrecompteRate(null==e.company?null:e.company.precompteRate)),n.R7$(3),n.SpI(" ",n.bMT(16,38,"company-detail-page.information.niu-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.nui)||"N/A"),n.R7$(3),n.SpI(" ",n.bMT(21,40,"company-detail-page.information.rccm-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.rccm)||"N/A"),n.R7$(3),n.SpI(" ",n.bMT(26,42,"company-detail-page.information.phone-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.tel)||"N/A"),n.R7$(4),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.erpSoldToId)||"N/A"),n.R7$(4),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.erpShipToId)||"N/A"),n.R7$(3),n.SpI(" ",n.bMT(39,44,"company-detail-page.information.commercial-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value","N/A"),n.R7$(3),n.SpI(" ",n.bMT(44,46,"company-detail-page.information.regionCom-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.address.region)||"N/A"),n.R7$(3),n.SpI(" ",n.bMT(49,48,"company-detail-page.information.region-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.address.region)||"N/A"),n.R7$(3),n.SpI(" ",n.bMT(54,50,"company-detail-page.information.city-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.address.city)||"N/A"),n.R7$(3),n.SpI(" ",n.bMT(59,52,"company-detail-page.information.district-label")," "),n.R7$(2),n.Y8G("readonly",!0)("value",(null==e.company?null:e.company.address.district)||"N/A"))},dependencies:[_.qT,_.cb,_.cV,c.$w,c.uz,c.he,c.nf,c.Gw,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{padding-top:0}form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Light;font-weight:500;font-size:calc(42 * var(--res));color:#1e1e1e}form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-right:calc(37.5 * var(--res));font-family:Mont Bold;color:#143c5d}form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .field-info[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{color:#1e1e1e}.mbottom250[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}"]})}}return o})();function U(o,g){1&o&&n.nrm(0,"ion-progress-bar",21)}function X(o,g){1&o&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",22),n.nrm(2,"ion-skeleton-text",23),n.k0s()()),2&o&&(n.R7$(2),n.Y8G("animated",!0))}function K(o,g){if(1&o&&(n.j41(0,"div"),n.nrm(1,"app-informations",24),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("company",t.company)}}function W(o,g){1&o&&(n.j41(0,"div",27),n.nrm(1,"ion-img",28),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&o&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"company-detail-page.empty-account")," "))}function V(o,g){if(1&o&&(n.j41(0,"div"),n.nrm(1,"app-account",25),n.DNE(2,W,5,3,"div",26),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("usersCompany",t.usersCompany),n.R7$(1),n.Y8G("ngIf",(null==t.usersCompany?null:t.usersCompany.length)<=0&&!t.isLoading)}}function H(o,g){1&o&&(n.j41(0,"div",27),n.nrm(1,"ion-img",28),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&o&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"company-detail-page.empty-address")," "))}function Q(o,g){if(1&o&&(n.j41(0,"div"),n.nrm(1,"app-address",29),n.DNE(2,H,5,3,"div",26),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("companyShippings",t.companyShippings),n.R7$(1),n.Y8G("ngIf",(null==t.companyShippings?null:t.companyShippings.length)<=0&&!t.isLoading)}}function Z(o,g){if(1&o&&(n.j41(0,"div"),n.nrm(1,"app-balance",30),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("balance",t.balance)}}const q=[{path:"",component:(()=>{class o{constructor(t,a,e,i,u){this.companySrv=t,this.commonSrv=a,this.route=e,this.shipmentSrv=i,this.location=u,this.usersCompany=[],this.skeletons=[1,2,3,4,5,6]}ngOnInit(){var t=this;return(0,p.A)(function*(){t.commonSrv.showNav=!1,t.commonSrv.showNav=!1,t.isLoading=!0,t.skeletons=[1,2,3,4,5,6],t.currentTab=1,t.route.snapshot.params.id&&(t.companyId=t.route.snapshot.params.id,t.company=yield t.companySrv.find(t.companyId),yield t.getUsersCompanies()),yield t.getCompanyShipping(),yield t.getCompanyBalance(),t.isLoading=!1,t.skeletons=[]})()}getUsersCompanies(){var t=this;return(0,p.A)(function*(){const e=yield t.companySrv.getUsersCompany(t.companyId,{email:"",enable:!0});t.usersCompany=e.data.sort()})()}getCompanyShipping(){var t=this;return(0,p.A)(function*(){const a={companyId:t.companyId};t.companyShippings=(yield t.shipmentSrv?.getShippingAddress(a))?.data?.sort()})()}getCompanyBalance(){var t=this;return(0,p.A)(function*(){const a={companyId:t.companyId};t.balance=yield t.companySrv.getBalance(a)})()}doRefresh(t){var a=this;return(0,p.A)(function*(){a.ngOnInit(),t.target.complete()})()}onChangeTab(t){this.currentTab=t}back(){this.location.back()}static{this.\u0275fac=function(a){return new(a||o)(n.rXU(h.B),n.rXU(v.h),n.rXU(P.nX),n.rXU(C.o),n.rXU(m.aZ))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-company-account-detail"]],decls:38,vars:33,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"ion-padding","ion-content-padding",3,"fullscreen"],["type","indeterminate",4,"ngIf"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],[1,"container-status"],[1,"tabs-status"],["type","radio","id","radio-1","name","tabs-status",3,"checked"],["for","radio-1",1,"tab-status","tab-status-1",3,"click"],["type","radio","id","radio-2","name","tabs-status",3,"checked"],["for","radio-2",1,"tab-status","tab-status-2",3,"click"],["type","radio","id","radio-3","name","tabs-status",3,"checked"],["for","radio-3",1,"tab-status","tab-status-3",3,"click"],["type","radio","id","radio-4","name","tabs-status",3,"checked"],["for","radio-4",1,"tab-status","tab-status-4",3,"click"],[1,"glider"],[4,"ngFor","ngForOf"],[1,"content-tab"],[4,"ngIf"],["type","indeterminate"],[1,"skeleton"],[3,"animated"],[3,"company"],[1,"list-card",3,"usersCompany"],["class","empty-list",4,"ngIf"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"],[1,"list-card",3,"companyShippings"],[3,"balance"]],template:function(a,e){1&a&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return e.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content",3),n.DNE(7,U,1,0,"ion-progress-bar",4),n.j41(8,"ion-refresher",5),n.bIt("ionRefresh",function(u){return e.doRefresh(u)}),n.nrm(9,"ion-refresher-content",6),n.nI1(10,"translate"),n.nI1(11,"translate"),n.k0s(),n.j41(12,"div")(13,"div",7)(14,"div",8),n.nrm(15,"input",9),n.j41(16,"label",10),n.bIt("click",function(){return e.onChangeTab(1)}),n.EFF(17),n.nI1(18,"translate"),n.k0s(),n.nrm(19,"input",11),n.j41(20,"label",12),n.bIt("click",function(){return e.onChangeTab(2)}),n.EFF(21),n.nI1(22,"translate"),n.k0s(),n.nrm(23,"input",13),n.j41(24,"label",14),n.bIt("click",function(){return e.onChangeTab(3)}),n.EFF(25),n.nI1(26,"translate"),n.k0s(),n.nrm(27,"input",15),n.j41(28,"label",16),n.bIt("click",function(){return e.onChangeTab(4)}),n.EFF(29),n.nI1(30,"translate"),n.k0s(),n.nrm(31,"span",17),n.k0s()(),n.DNE(32,X,3,1,"ion-cart",18),n.j41(33,"div",19),n.DNE(34,K,2,1,"div",20),n.DNE(35,V,3,2,"div",20),n.DNE(36,Q,3,2,"div",20),n.DNE(37,Z,2,1,"div",20),n.k0s()()()),2&a&&(n.R7$(4),n.Lme(" ",n.bMT(5,19,"company-detail-page.title")," ",(null==e.company?null:e.company.name)||""," "),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",e.isLoading),n.R7$(2),n.FS9("pullingText",n.bMT(10,21,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(11,23,"refresher.refreshing"),"..."),n.R7$(6),n.Y8G("checked",1===e.currentTab),n.R7$(2),n.SpI(" ",n.bMT(18,25,"company-detail-page.tabs-menu.information")," "),n.R7$(2),n.Y8G("checked",2===e.currentTab),n.R7$(2),n.SpI(" ",n.bMT(22,27,"company-detail-page.tabs-menu.account")," "),n.R7$(2),n.Y8G("checked",3===e.currentTab),n.R7$(2),n.SpI(" ",n.bMT(26,29,"company-detail-page.tabs-menu.address")," "),n.R7$(2),n.Y8G("checked",4===e.currentTab),n.R7$(2),n.SpI(" ",n.bMT(30,31,"company-detail-page.tabs-menu.balance")," "),n.R7$(3),n.Y8G("ngForOf",e.skeletons),n.R7$(2),n.Y8G("ngIf",1===e.currentTab&&!e.isLoading),n.R7$(1),n.Y8G("ngIf",2===e.currentTab&&!e.isLoading),n.R7$(1),n.Y8G("ngIf",3===e.currentTab&&!e.isLoading),n.R7$(1),n.Y8G("ngIf",4===e.currentTab&&!e.isLoading))},dependencies:[m.Sq,m.bT,c.W9,c.eU,c.KW,c.he,c.FH,c.To,c.Ki,c.ds,c.Zx,c.BC,c.ai,d,f,L,J,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}ion-content[_ngcontent-%COMP%]{--padding-top: 4px}ion-content[_ngcontent-%COMP%]   ion-progress-bar[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]{display:flex;position:relative;padding:.35rem;border-radius:99px}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .tab-status[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:calc(75 * var(--res));min-height:3rem;max-height:5rem;color:#143c5d;font-size:calc(37.5 * var(--res));font-weight:500;z-index:2;transition:.25s ease-out}@media (max-width: 400px){ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .tab-status[_ngcontent-%COMP%]{min-height:auto}}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .tab-status-1[_ngcontent-%COMP%]{width:calc(300 * var(--res))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .tab-status-2[_ngcontent-%COMP%]{width:calc(200 * var(--res))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .tab-status-3[_ngcontent-%COMP%]{width:calc(200 * var(--res))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .tab-status-4[_ngcontent-%COMP%]{width:calc(200 * var(--res))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{display:none}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   input[id=radio-1][_ngcontent-%COMP%]:checked ~ .glider[_ngcontent-%COMP%]{transform:translate(0)}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   input[id=radio-2][_ngcontent-%COMP%]:checked ~ .glider[_ngcontent-%COMP%]{width:calc(200 * var(--res));transform:translate(calc(300 * var(--res)))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   input[id=radio-3][_ngcontent-%COMP%]:checked ~ .glider[_ngcontent-%COMP%]{width:calc(200 * var(--res));transform:translate(calc(500 * var(--res)))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   input[id=radio-4][_ngcontent-%COMP%]:checked ~ .glider[_ngcontent-%COMP%]{width:calc(200 * var(--res));transform:translate(calc(700 * var(--res)))}ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .glider[_ngcontent-%COMP%]{position:absolute;display:flex;height:calc(75 * var(--res));min-height:3rem;max-height:5rem;width:calc(300 * var(--res));background-color:#143c5d;z-index:1;border-radius:99px;transition:.25s ease-out}@media (max-width: 400px){ion-content[_ngcontent-%COMP%]   .container-status[_ngcontent-%COMP%]   .tabs-status[_ngcontent-%COMP%]   .glider[_ngcontent-%COMP%]{min-height:auto}}ion-content[_ngcontent-%COMP%]   .content-tab[_ngcontent-%COMP%]{width:100%;height:100%}ion-content[_ngcontent-%COMP%]   .content-tab[_ngcontent-%COMP%]   .list-card[_ngcontent-%COMP%]{margin-top:calc(50 * var(--res));display:flex;flex-direction:column;justify-content:center;gap:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   .content-tab[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:10vh;display:flex;flex-direction:column;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .content-tab[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-content[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem;margin-top:1rem}ion-content[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}"]})}}return o})()}];let nn=(()=>{class o{static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[P.iI.forChild(q),P.iI]})}}return o})();var tn=l(93887);let en=(()=>{class o{static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[m.MD,_.YN,c.bv,O.h,nn,tn.G]})}}return o})()},61955:(y,b,l)=>{l.d(b,{o:()=>v});var m=l(73308),_=l(26409),c=l(94934),P=l(45312),p=l(2978),n=l(82571),h=l(33607);let v=(()=>{class C{constructor(s,d,r){this.http=s,this.commonSrv=d,this.baseUrlService=r,this.url=this.baseUrlService.getOrigin()+P.c.basePath+"shippings"}getShippingAddress(s){var d=this;return(0,m.A)(function*(){try{let r=new _.Nl;return s?.startRef&&(r=r.append("startRef.storeRef",s?.startRef)),void 0!==s?.category&&(r=r.append("category",s?.category)),void 0!==s?.companyId&&(r=r.append("companyId",s?.companyId)),yield(0,c.s)(d.http.get(`${d.url}`,{params:r}))}catch(r){const M={message:d.commonSrv.getError("",r).message,color:"danger"};return yield d.commonSrv.showToast(M),r}})()}getDefaultShippingAddress(s){var d=this;return(0,m.A)(function*(){try{let r=new _.Nl;return s?.startRef&&(r=r.append("startRef.storeRef",s?.startRef)),void 0!==s?.category&&(r=r.append("category",s?.category)),void 0!==s?.commercialRegion&&(r=r.append("address.commercialRegion",s?.commercialRegion)),yield(0,c.s)(d.http.get(`${d.url}/default-shipping`,{params:r}))}catch(r){const M={message:d.commonSrv.getError("",r).message,color:"danger"};return yield d.commonSrv.showToast(M),r}})()}getCategoriesShippingAddress(s){var d=this;return(0,m.A)(function*(){try{let r=new _.Nl;return s?.startRef&&(r=r.append("startRef.storeRef",s?.startRef)),void 0!==s?.category&&(r=r.append("category",s?.category)),yield(0,c.s)(d.http.get(`${d.url}/categories`,{params:r}))}catch(r){const M={message:d.commonSrv.getError("",r).message,color:"danger"};return yield d.commonSrv.showToast(M),r}})()}static{this.\u0275fac=function(d){return new(d||C)(p.KVO(_.Qq),p.KVO(n.h),p.KVO(h.K))}}static{this.\u0275prov=p.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})}}return C})()},94440:(y,b,l)=>{l.d(b,{c:()=>_});var m=l(2978);let _=(()=>{class c{transform(p,...n){return p?p.length>n[0]?`${p.substring(0,n[0]-3)}...`:p:""}static{this.\u0275fac=function(n){return new(n||c)}}static{this.\u0275pipe=m.EJ8({name:"truncateString",type:c,pure:!0})}}return c})()}}]);