"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6084],{66084:(x,l,o)=>{o.r(l),o.d(l,{UserListPageModule:()=>v});var c=o(56610),m=o(37222),e=o(77897),a=o(77575),n=o(2978),u=o(62049),p=o(99987),s=o(74657),d=o(838);const M=function(){return["/navigation/companies-account"]};function P(t,_){1&t&&(n.j41(0,"a",6),n.nrm(1,"ion-img",7),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.nrm(5,"ion-icon",8),n.k0s()),2&t&&(n.Y8G("routerLink",n.lJ4(4,M)),n.R7$(3),n.SpI(" ",n.bMT(4,2,"user-list.direct")," "))}const C=function(){return["/navigation/indirect-user"]};function b(t,_){1&t&&(n.j41(0,"a",6),n.nrm(1,"ion-img",7),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.nrm(5,"ion-icon",8),n.k0s()),2&t&&(n.Y8G("routerLink",n.lJ4(4,C)),n.R7$(3),n.SpI(" ",n.bMT(4,2,"user-list.indirect")," "))}const O=[{path:"",component:(()=>{class t{constructor(){this.location=(0,n.WQX)(c.aZ),this.translateService=(0,n.WQX)(u.E),this.translateSrv=(0,n.WQX)(s.c$),this.clients=[],this.companyAction=d.Kr,this.userAction=d.ay}ngOnInit(){this.updateLanguage()}updateLanguage(){const i=this.translateService.currentLang===p.T.French;this.clients=[{title:i?"Clients directs":"Directs clients",link:"/navigation/companies-account"},{title:i?"Clients indirects":"Indirects clients",link:"/navigation/indirect-user"}]}back(){this.location.back()}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275cmp=n.VBU({type:t,selectors:[["app-user-list"]],decls:9,vars:4,consts:[[3,"translucent"],[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[3,"fullscreen"],[1,"container"],["class","item",3,"routerLink",4,"ngIf"],[1,"item",3,"routerLink"],["src","/assets/images/man.png"],["slot","end","name","chevron-forward"]],template:function(r,g){1&r&&(n.j41(0,"ion-header",0)(1,"div",1)(2,"ion-img",2),n.bIt("click",function(){return g.back()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4," Liste des utilisateurs "),n.k0s()()(),n.j41(5,"ion-content",3)(6,"div",4),n.DNE(7,P,6,5,"a",5),n.DNE(8,b,6,5,"a",5),n.k0s()()),2&r&&(n.Y8G("translucent",!0),n.R7$(5),n.Y8G("fullscreen",!0),n.R7$(2),n.Y8G("ngIf",g.companyAction.VIEW),n.R7$(1),n.Y8G("ngIf",g.userAction.VIEW))},dependencies:[c.bT,e.W9,e.eU,e.iq,e.KW,e.he,e.BC,e.oY,a.Wk,s.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-button[_ngcontent-%COMP%]{margin:1em 0}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:1em 0}.header[_ngcontent-%COMP%]{--background: #F1F2F4;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]{--background: #F1F2F4}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:white;border-radius:5px;box-shadow:0 4px 4px #00000017;width:calc(100% - 48px);margin:1em;padding:1em;display:flex;justify-content:space-between}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-semibold);text-align:center;margin:auto}ion-content[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-weight:700}ion-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{padding:8px;border-radius:50px;border:1px solid var(--clr-primary-750)}ion-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}"]})}}return t})()}];let h=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[a.iI.forChild(O),a.iI]})}}return t})();var f=o(17438);let v=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[c.MD,m.YN,e.bv,f.Gg,s.h,h]})}}return t})()}}]);