"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1605],{71605:(j,S,s)=>{s.r(S),s.d(S,{HistoryPageModule:()=>H});var i=s(77897),h=s(56610),O=s(74657),u=s(37222),p=s(77575),C=s(73308),M=s(88233),P=s(58133),F=s(79898),t=s(2978),U=s(81559),b=s(82571),m=s(39316),f=s(14599),a=s(2611),r=s(94440);function n(o,v){1&o&&t.nrm(0,"ion-progress-bar",19)}const l=function(o){return{active:o}};function d(o,v){if(1&o){const e=t.RV6();t.j41(0,"ion-tab-button",10),t.bIt("click",function(){t.eBV(e);const g=t.XpG();return g.filterStatus.status=g.orderStatus.CREATED,g.orders=[],t.Njj(g.getOrdersCreditByUser())}),t.j41(1,"ion-title"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}if(2&o){const e=t.XpG();t.Y8G("ngClass",t.eq3(4,l,e.filterStatus.status!==e.orderStatus.PAID&&e.filterStatus.status!==e.orderStatus.VALIDATED)),t.R7$(2),t.JRh(t.bMT(3,2,"history-page.tabs.credit"))}}function _(o,v){if(1&o&&(t.j41(0,"ion-label"),t.EFF(1),t.nI1(2,"date"),t.k0s()),2&o){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" Date: ",t.i5U(2,1,null==e?null:e.created_at,"dd/MM/YYYY \xe0 HH:mm"),"")}}function R(o,v){if(1&o&&(t.j41(0,"ion-label"),t.EFF(1,"Statut: "),t.j41(2,"strong",25),t.nI1(3,"colorStatusOrder"),t.EFF(4),t.nI1(5,"statusOrder"),t.k0s()()),2&o){const e=t.XpG().$implicit;t.R7$(2),t.Y8G("ngClass",t.bMT(3,2,e.status)),t.R7$(2),t.JRh(t.bMT(5,4,null==e?null:e.status))}}function I(o,v){if(1&o&&(t.j41(0,"ion-label"),t.EFF(1,"Motif du rejet: "),t.j41(2,"strong",25),t.nI1(3,"colorStatusOrder"),t.EFF(4),t.nI1(5,"truncateString"),t.k0s()()),2&o){const e=t.XpG().$implicit;t.R7$(2),t.Y8G("ngClass",t.bMT(3,2,e.status)),t.R7$(2),t.JRh(t.i5U(5,4,(null==e?null:e.rejectReason)||"N/A",20))}}function D(o,v){if(1&o&&(t.j41(0,"ion-label"),t.EFF(1),t.nI1(2,"translate"),t.j41(3,"strong",26),t.nI1(4,"colorStatusOrder"),t.EFF(5),t.k0s()()),2&o){const e=t.XpG().$implicit;t.R7$(1),t.SpI("",t.bMT(2,3,"history-page.update-nber")," : "),t.R7$(2),t.Y8G("ngClass",t.bMT(4,5,e.status)),t.R7$(2),t.JRh((null==e?null:e.nberModif)||"N/A")}}function T(o,v){if(1&o&&(t.j41(0,"ion-label"),t.EFF(1),t.nI1(2,"translate"),t.j41(3,"button",27),t.EFF(4),t.nI1(5,"statusCancelled"),t.k0s()()),2&o){const e=t.XpG().$implicit,c=t.XpG();t.R7$(1),t.SpI(" ",t.bMT(2,3,"history-page.status-cancelled")," :"),t.R7$(2),t.Y8G("ngStyle",c.getCancellationStatusStyle(e)),t.R7$(1),t.JRh(t.bMT(5,5,null==e?null:e.cancellationStatus))}}function A(o,v){if(1&o&&(t.j41(0,"ion-label"),t.EFF(1),t.nI1(2,"translate"),t.k0s()),2&o){const e=t.XpG().$implicit;t.R7$(1),t.E5c(" ",t.bMT(2,3,"history-page.client-name"),": ",null==e||null==e.company?null:e.company.name," - ",((null==e||null==e.user?null:e.user.firstName)||"")+" "+((null==e||null==e.user?null:e.user.lastName)||"")||(null==e||null==e.user?null:e.user.email)," ")}}const E=function(o,v){return[o,v]};function k(o,v){if(1&o){const e=t.RV6();t.j41(0,"ion-card",20),t.bIt("click",function(){const y=t.eBV(e).$implicit,$=t.XpG();return t.Njj($.orderService.order=y)}),t.j41(1,"ion-card-content")(2,"div",21)(3,"ion-label"),t.EFF(4),t.nI1(5,"translate"),t.k0s(),t.j41(6,"ion-label"),t.EFF(7),t.nI1(8,"translate"),t.nI1(9,"number"),t.k0s(),t.DNE(10,_,3,4,"ion-label",22),t.DNE(11,R,6,6,"ion-label",22),t.DNE(12,I,6,7,"ion-label",22),t.DNE(13,D,6,7,"ion-label",22),t.DNE(14,T,6,7,"ion-label",22),t.DNE(15,A,3,5,"ion-label",22),t.k0s(),t.j41(16,"div",23),t.nrm(17,"ion-icon",24),t.k0s()()()}if(2&o){const e=v.$implicit,c=t.XpG();t.Mz_("routerLink","/order/detail/",null==e?null:e._id,""),t.R7$(4),t.Lme("",t.bMT(5,11,"history-page.reference"),": ",(null==e?null:e.customerReference)||(null==e?null:e.appReference),""),t.R7$(3),t.Lme("",t.bMT(8,13,"history-page.amount"),": ",t.bMT(9,15,null==e||null==e.cart||null==e.cart.amount?null:e.cart.amount.TTC)," FCFA"),t.R7$(3),t.Y8G("ngIf",!t.l_i(17,E,c.userCategory.Commercial,c.userCategory.DonutAnimator).includes(null==c.commonService.user?null:c.commonService.user.category)),t.R7$(1),t.Y8G("ngIf",t.l_i(20,E,c.userCategory.Commercial,c.userCategory.DonutAnimator).includes(null==c.commonService.user?null:c.commonService.user.category)),t.R7$(1),t.Y8G("ngIf",e.status===c.orderStatus.REJECTED),t.R7$(1),t.Y8G("ngIf",(null==e?null:e.nberModif)>=1),t.R7$(1),t.Y8G("ngIf",e.cancellationStatus),t.R7$(1),t.Y8G("ngIf",t.l_i(23,E,c.userCategory.Commercial,c.userCategory.DonutAnimator).includes(null==c.commonService.user?null:c.commonService.user.category))}}function L(o,v){1&o&&(t.j41(0,"ion-cart")(1,"ion-thumbnail",28),t.nrm(2,"ion-skeleton-text",29),t.k0s()()),2&o&&(t.R7$(2),t.Y8G("animated",!0))}function Y(o,v){1&o&&(t.j41(0,"div",30),t.nrm(1,"ion-img",31),t.j41(2,"ion-label"),t.EFF(3),t.nI1(4,"translate"),t.k0s()()),2&o&&(t.R7$(3),t.SpI(" ",t.bMT(4,1,"history-page.empty-order")," "))}const B=[{path:"",component:(()=>{class o{constructor(e,c,g,y,$,K){this.location=e,this.orderService=c,this.modalCtrl=g,this.commonService=y,this.productService=$,this.storageService=K,this.isLoading=!1,this.tabOption=M.Re.PAID,this.orderStatus=M.Re,this.userCategory=P.s,this.orders=[],this.skeletons=[1,2,3,4,5,6],this.filterStatus={status:M.Re.PAID},this.offset=0,this.limit=20,this.CancellationStatus=M.q}doRefresh(e){var c=this;return(0,C.A)(function*(){c.filterData=null,c.offset=0,c.orders=[],yield c.getOrderByUser(),e.target.complete()})()}ionViewWillEnter(){var e=this;return(0,C.A)(function*(){e.isLoading=!0,e.storageService.getUserConnected(),e.orders=[],yield e.getOrderByUser(),console.log("Hello client commercial")})()}ionViewDidEnter(){this.isLoading=!1}getFlowOrder(e){var c=this;return(0,C.A)(function*(){c.offset=c.offset+c.limit+1,yield c.getOrderByUser(),e.target.complete()})()}getOrderByUser(){var e=this;return(0,C.A)(function*(){e.skeletons=[1,2,3,4,5,6],e.isLoading=!0;const c={status:[P.s.Particular,P.s.Commercial,P.s.DonutAnimator].includes(e.commonService.user.category)&&e.filterStatus.status==M.Re.PAID?JSON.stringify({$in:[M.Re.PAID,M.Re.CREDIT_IN_AWAIT_VALIDATION]}):e.filterStatus.status,limit:e.limit,offset:e.offset,commercialId:e.commonService.user.category===e.userCategory.Commercial&&e.commonService?.user?._id,...e.filterData};[P.s.Commercial,P.s.DonutAnimator].includes(e.commonService.user?.category)&&(c.commercialId=e.commonService?.user?._id),e.commonService?.user?.category===P.s?.CompanyUser&&e.commonService?.user?.associatedCompanies?.length>0&&(c.selectedCompanyId=e.commonService.user?.company?._id);const g=(yield e.orderService.getAllOrder(c)).data;e.orders=e.orders.concat(g),e.isLoading=!1,e.skeletons=[]})()}getOrdersCreditByUser(){var e=this;return(0,C.A)(function*(){e.filterStatus.status=JSON.stringify({$lt:M.Re.PAID}),yield e.getOrderByUser()})()}showFilter(){var e=this;return(0,C.A)(function*(){const c=yield e.modalCtrl.create({component:F.W,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios",componentProps:{filterData:e.filterData}});c.present(),e.filterData=(yield c.onWillDismiss()).data,e.filterData&&(e.orders=[],yield e.getOrderByUser())})()}showModalRemovalDetail(e){this.orders=e,this.orderService.modalDetailRemoval=!0}back(){this.location.back()}getCancellationStatusStyle(e){switch(e.cancellationStatus){case M.q.ISSUE:return{background:"rgb(211, 211, 211)",color:"rgb(70, 70, 70)"};case M.q.REFUSED:return{background:"rgb(255, 77, 77)",color:"rgb(255, 255, 255)"};case M.q.ACCEPTED:return{background:"rgb(76, 175, 80)",color:"rgb(255, 255, 255)"};default:return{backgroundColor:"transparent",color:"inherit"}}}static{this.\u0275fac=function(c){return new(c||o)(t.rXU(h.aZ),t.rXU(U.Q),t.rXU(i.W3),t.rXU(b.h),t.rXU(m.b),t.rXU(f.n))}}static{this.\u0275cmp=t.VBU({type:o,selectors:[["app-history"]],decls:39,vars:36,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/funnel-outline.svg",3,"click"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click"],[3,"ngClass","click",4,"ngIf"],[1,"order-list"],["class","order",3,"routerLink","click",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[3,"ionInfinite"],["class","empty-list",4,"ngIf"],[1,"btn-validate"],["routerLink","/navigation/home","color","primary","expand","block",1,"btn--meduim","btn--upper"],["type","indeterminate"],[1,"order",3,"routerLink","click"],[1,"detail"],[4,"ngIf"],[1,"icon"],["src","/assets/icons/arrow-forward-green.svg"],[3,"ngClass"],["color","primary",3,"ngClass"],["size","small",2,"height","26px","width","7em","margin-left","10px","border-radius","5px",3,"ngStyle"],[1,"skeleton"],[3,"animated"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(c,g){1&c&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),t.bIt("click",function(){return g.back()}),t.k0s(),t.j41(3,"ion-title",2),t.EFF(4),t.nI1(5,"translate"),t.k0s(),t.j41(6,"ion-img",3),t.bIt("click",function(){return g.showFilter()}),t.k0s()()(),t.j41(7,"ion-content",4),t.DNE(8,n,1,0,"ion-progress-bar",5),t.j41(9,"div",6)(10,"ion-refresher",7),t.bIt("ionRefresh",function($){return g.doRefresh($)}),t.nrm(11,"ion-refresher-content",8),t.nI1(12,"translate"),t.nI1(13,"translate"),t.k0s(),t.j41(14,"ion-tab-bar",9)(15,"ion-tab-button",10),t.bIt("click",function(){return g.filterStatus.status=g.orderStatus.PAID,g.orders=[],g.getOrderByUser()}),t.j41(16,"ion-title"),t.EFF(17),t.nI1(18,"translate"),t.k0s()(),t.DNE(19,d,4,6,"ion-tab-button",11),t.j41(20,"ion-tab-button",10),t.bIt("click",function(){return g.filterStatus.status=g.orderStatus.VALIDATED,g.orders=[],g.getOrderByUser()}),t.j41(21,"ion-title"),t.EFF(22),t.nI1(23,"translate"),t.k0s()(),t.j41(24,"ion-tab-button",10),t.bIt("click",function(){return g.filterStatus.status=g.orderStatus.CREDIT_REJECTED,g.orders=[],g.getOrderByUser()}),t.j41(25,"ion-title"),t.EFF(26),t.nI1(27,"translate"),t.k0s()()(),t.j41(28,"div",12),t.DNE(29,k,18,26,"ion-card",13),t.DNE(30,L,3,1,"ion-cart",14),t.j41(31,"ion-infinite-scroll",15),t.bIt("ionInfinite",function($){return g.getFlowOrder($)}),t.nrm(32,"ion-infinite-scroll-content"),t.k0s()(),t.DNE(33,Y,5,3,"div",16),t.k0s()(),t.j41(34,"div",17)(35,"ion-button",18)(36,"ion-label"),t.EFF(37),t.nI1(38,"translate"),t.k0s()()()),2&c&&(t.R7$(4),t.JRh(t.bMT(5,16,"history-page.title")),t.R7$(3),t.Y8G("fullscreen",!0),t.R7$(1),t.Y8G("ngIf",g.isLoading),t.R7$(3),t.FS9("pullingText",t.bMT(12,18,"refresher.pull")),t.Mz_("refreshingText","",t.bMT(13,20,"refresher.refreshing"),"..."),t.R7$(4),t.Y8G("ngClass",t.eq3(30,l,g.filterStatus.status===g.orderStatus.PAID)),t.R7$(2),t.JRh(t.bMT(18,22,"history-page.tabs.in-progres")),t.R7$(2),t.Y8G("ngIf",(null==g.commonService||null==g.commonService.user?null:g.commonService.user.category)===g.userCategory.EmployeeLapasta),t.R7$(1),t.Y8G("ngClass",t.eq3(32,l,g.filterStatus.status===g.orderStatus.VALIDATED)),t.R7$(2),t.JRh(t.bMT(23,24,"history-page.tabs.validate")),t.R7$(2),t.Y8G("ngClass",t.eq3(34,l,g.filterStatus.status===g.orderStatus.CREDIT_REJECTED)),t.R7$(2),t.JRh(t.bMT(27,26,"history-page.tabs.Rejected")),t.R7$(3),t.Y8G("ngForOf",g.orders),t.R7$(1),t.Y8G("ngForOf",g.skeletons),t.R7$(3),t.Y8G("ngIf",(null==g.orders?null:g.orders.length)<=0&&!g.isLoading),t.R7$(4),t.SpI(" ",t.bMT(38,28,"order-new-page.last-step.back-button-label")," "))},dependencies:[h.YU,h.Sq,h.bT,h.B3,i.Jm,i.b_,i.I9,i.W9,i.eU,i.iq,i.KW,i.Ax,i.Hp,i.he,i.FH,i.To,i.Ki,i.ds,i.Jq,i.qW,i.Zx,i.BC,i.ai,i.N7,p.Wk,h.QX,h.vh,a.D3,r.c,a.qZ,a.sk,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background:#cfcfcf;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));padding-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{margin:auto;min-height:35px;gap:1em;margin-bottom:calc(50 * var(--res));border:none}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-tab-button[_ngcontent-%COMP%]{min-width:-moz-fit-content;min-width:fit-content}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:calc(41 * var(--res));color:#6d839d;font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#6d839d;border-bottom:3px solid #419CFB}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#0b305c}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]{border-bottom:1px solid #8597AD;box-shadow:none;border-radius:unset;margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont SemiBold;color:#0b305c;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-primary-400[_ngcontent-%COMP%]{color:#0d7d3d!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-success-200[_ngcontent-%COMP%]{background-color:#b8ddb6!important;padding:3px 6px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-info-500[_ngcontent-%COMP%]{color:#0af!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-100[_ngcontent-%COMP%]{background-color:#cef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-default-400[_ngcontent-%COMP%]{color:#fff!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-500[_ngcontent-%COMP%]{background-color:#0af!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-tertiary-200[_ngcontent-%COMP%]{background-color:#f0efef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:20vh;display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-infinite-scroll-content[_ngcontent-%COMP%]{min-height:15px}.btn-validate[_ngcontent-%COMP%]{margin:calc(41 * var(--res))}"]})}}return o})()},{path:"order-removals-details",loadChildren:()=>Promise.all([s.e(148),s.e(1095)]).then(s.bind(s,80148)).then(o=>o.OrderRemovalsDetailsPageModule)},{path:"list-order",loadChildren:()=>Promise.all([s.e(2076),s.e(2457)]).then(s.bind(s,42457)).then(o=>o.ListOrderPageModule)}];let x=(()=>{class o{static{this.\u0275fac=function(c){return new(c||o)}}static{this.\u0275mod=t.$C({type:o})}static{this.\u0275inj=t.G2t({imports:[p.iI.forChild(B),p.iI]})}}return o})();var G=s(93887);let H=(()=>{class o{static{this.\u0275fac=function(c){return new(c||o)}}static{this.\u0275mod=t.$C({type:o})}static{this.\u0275inj=t.G2t({imports:[h.MD,u.YN,i.bv,G.G,u.X1,x,O.h]})}}return o})()},81559:(j,S,s)=>{s.d(S,{Q:()=>b});var i=s(73308),h=s(35025),O=s.n(h),u=s(94934),p=s(56610),C=s(45312),M=s(26409),P=s(2978),F=s(82571),t=s(33607),U=s(14599);let b=(()=>{class m{constructor(a,r,n,l){this.http=a,this.commonSrv=r,this.baseUrlService=n,this.storageSrv=l,this.url=this.baseUrlService.getOrigin()+C.c.basePath}create(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.post(`${r.url}orders`,a))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}createOrderByCommercialForClient(a,r){var n=this;return(0,i.A)(function*(){try{return yield(0,u.s)(n.http.post(`${n.url}orders/${r}`,a))}catch(l){const _={message:n.commonSrv.getError("",l).message,color:"danger"};return yield n.commonSrv.showToast(_),l}})()}getAllOrder(a){var r=this;return(0,i.A)(function*(){try{let n=new M.Nl;const{num:l,commercialId:d,status:_,offset:R,limit:I,startDate:D,endDate:T,customerReference:A,selectedCompanyId:E}=a;return D&&T&&(n=n.append("startDate",new p.vh("fr").transform(D,"YYYY-MM-dd"))),T&&D&&(n=n.append("endDate",new p.vh("fr").transform(T,"YYYY-MM-dd"))),A&&(n=n.append("appReference",A)),E&&(n=n.append("selectedCompanyId",E)),d&&(n=n.append("commercial",d)),void 0!==R&&(n=n.append("offset",R)),I&&(n=n.append("limit",I)),_&&(n=n.append("status",_)),l&&(n=n.append("appReference",l)),yield(0,u.s)(r.http.get(`${r.url}orders/history`,{params:n}))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}getOrders(a){var r=this;return(0,i.A)(function*(){try{let n=new M.Nl;const{status:l,appReference:d,offset:_,limit:R,userCategory:I,paymentMode:D,validation:T,customer:A,product:E,date:k,enable:L=!0}=a;return _&&(n=n.append("offset",_)),R&&(n=n.append("limit",R)),l&&(n=n.append("status",l)),d&&(n=n.append("appReference",`${d}`)),D&&(n=n.append("payment.mode.id",D)),I&&(n=n.append("user.category",I)),A&&(n=n.append("user.email",A)),E&&(n=n.append("cart.items.product.label",E)),T&&(n=n.append("validation",T)),k.start&&k.end&&(n=n.append("startDate",O()(k.start).format("YYYY-MM-DD")),n=n.append("endDate",O()(k.end).format("YYYY-MM-DD"))),n=n.append("enable",L),yield(0,u.s)(r.http.get(`${r.url}orders`,{params:n}))}catch(n){return n}})()}updateOrders(a,r){var n=this;return(0,i.A)(function*(){try{return yield(0,u.s)(n.http.patch(`${n.url}orders/${a}`,r))}catch(l){const _={message:n.commonSrv.getError("",l).message,color:"danger"};return yield n.commonSrv.showToast(_),l}})()}RhValidatedOrder(a,r){var n=this;return(0,i.A)(function*(){try{return yield(0,u.s)(n.http.patch(`${n.url}orders/${a._id}/validate`,r))}catch(l){const _={message:n.commonSrv.getError("",l).message,color:"danger"};return yield n.commonSrv.showToast(_),l}})()}RhRejectOrder(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.patch(`${r.url}orders/${a._id}/reject`,{}))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}sendOtp(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.post(`${r.url}callback/afriland`,a))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}sendWallet(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.post(`${r.url}orders/verify-Wallet-Nber`,a))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}ubaPayment(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.post(`${r.url}orders/m2u-paymentRequest`,a))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}find(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.get(r.url+"orders/"+a))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}getCardToken(){var a=this;return(0,i.A)(function*(){try{return yield(0,u.s)(a.http.post(`${a.url}orders/order-generate-visa-key`,{}))}catch(r){const l={message:a.commonSrv.getError("",r).message,color:"danger"};return yield a.commonSrv.showToast(l),r}})()}setupPayerAuthentication(a,r){var n=this;return(0,i.A)(function*(){try{return yield(0,u.s)(n.http.post(`${n.url}orders/order-setup-payer-auth`,{transientTokenJwt:a,order:r}))}catch(l){const _={message:n.commonSrv.getError("",l).message,color:"danger"};return yield n.commonSrv.showToast(_),l}})()}authorizationWithPAEnroll(a,r){var n=this;return(0,i.A)(function*(){try{return yield(0,u.s)(n.http.post(`${n.url}orders/order-authorization-pay-enroll`,{order:a,options:r}))}catch(l){const _={message:n.commonSrv.getError("",l).message,color:"danger"};return yield n.commonSrv.showToast(_),l}})()}checkIfOrderExist(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.get(`${r.url}orders/${a}/exist`))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}generatePurchaseOrder(a){var r=this;return(0,i.A)(function*(){try{return yield(0,u.s)(r.http.get(`${r.url}orders/${a}/generate-purchase`))}catch(n){const d={message:r.commonSrv.getError("",n).message,color:"danger"};return yield r.commonSrv.showToast(d),n}})()}cancellationOrder(a,r){var n=this;return(0,i.A)(function*(){try{return yield(0,u.s)(n.http.patch(`${n.url}orders/${a}/cancellation-order`,r))}catch(l){const _={message:n.commonSrv.getError("",l).message,color:"danger"};return yield n.commonSrv.showToast(_),l}})()}updateCarrier(a,r){var n=this;return(0,i.A)(function*(){try{return yield(0,u.s)(n.http.patch(`${n.url}orders/${a}/add-carrier`,{carrier:r}))}catch(l){const _={message:n.commonSrv.getError("",l).message,color:"danger"};return yield n.commonSrv.showToast(_),l}})()}static{this.\u0275fac=function(r){return new(r||m)(P.KVO(M.Qq),P.KVO(F.h),P.KVO(t.K),P.KVO(U.n))}}static{this.\u0275prov=P.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()},39316:(j,S,s)=>{s.d(S,{b:()=>F});var i=s(73308),h=s(26409),O=s(94934),u=s(45312),p=s(2978),C=s(82571),M=s(33607),P=s(77897);let F=(()=>{class t{constructor(b,m,f,a){this.http=b,this.commonSrv=m,this.baseUrlService=f,this.toastController=a,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+u.c.basePath+"products"}getProducts(b){var m=this;return(0,i.A)(function*(){try{let f=new h.Nl;return b?.limit&&(f=f.append("limit",b?.limit)),yield(0,O.s)(m.http.get(m.url,{params:f}))}catch(f){const r={message:m.commonSrv.getError("",f).message,color:"danger"};return yield m.commonSrv.showToast(r),f}})()}getProduct(b){var m=this;return(0,i.A)(function*(){try{return yield(0,O.s)(m.http.get(`${m.url}/${b}`))}catch(f){const r={message:m.commonSrv.getError("",f).message,color:"danger"};return yield m.commonSrv.showToast(r),f}})()}static{this.\u0275fac=function(m){return new(m||t)(p.KVO(h.Qq),p.KVO(C.h),p.KVO(M.K),p.KVO(P.K_))}}static{this.\u0275prov=p.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})()},94440:(j,S,s)=>{s.d(S,{c:()=>h});var i=s(2978);let h=(()=>{class O{transform(p,...C){return p?p.length>C[0]?`${p.substring(0,C[0]-3)}...`:p:""}static{this.\u0275fac=function(C){return new(C||O)}}static{this.\u0275pipe=i.EJ8({name:"truncateString",type:O,pure:!0})}}return O})()}}]);