"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[148],{80148:(f,a,r)=>{r.r(a),r.d(a,{OrderRemovalsDetailsPageModule:()=>b});var l=r(56610),s=r(37222),i=r(77897),d=r(77575),O=r(73014),n=r(2978),P=r(81559),M=r(61095);function C(t,g){1&t&&(n.j41(0,"th",2),n.EFF(1,"nbre de camions"),n.k0s())}function _(t,g){if(1&t&&(n.j41(0,"td",14),n.EFF(1),n.k0s()),2&t){const c=n.XpG().$implicit;n.R7$(1),n.JRh(null==c?null:c.nbr<PERSON><PERSON>ck)}}function p(t,g){if(1&t&&(n.j41(0,"tr")(1,"td",14),n.EFF(2),n.nI1(3,"date"),n.k0s(),n.j41(4,"td",14),n.EFF(5),n.k0s(),n.j41(6,"td",14),n.EFF(7),n.k0s(),n.j41(8,"td",14),n.EFF(9),n.k0s(),n.DNE(10,_,2,1,"td",15),n.k0s()),2&t){const c=g.$implicit,o=n.XpG(2);n.R7$(2),n.JRh(n.i5U(3,5,null==c?null:c.removalDate,"dd/MM/YYYY")),n.R7$(3),n.JRh(null==c?null:c.hourTime),n.R7$(2),n.JRh(null==c?null:c.quartTime),n.R7$(2),n.SpI(" ",null==c?null:c.nbrTonnage,"T"),n.R7$(1),n.Y8G("ngIf",o.scheduleTypes)}}function h(t,g){if(1&t&&(n.j41(0,"div",6)(1,"ion-title",7)(2,"div",8),n.nrm(3,"img",9),n.j41(4,"div",10),n.EFF(5),n.k0s()()(),n.j41(6,"div",11)(7,"table")(8,"thead")(9,"tr")(10,"th",2),n.EFF(11,"Date"),n.k0s(),n.j41(12,"th",2),n.EFF(13,"Heure"),n.k0s(),n.j41(14,"th",2),n.EFF(15,"Quart temps"),n.k0s(),n.j41(16,"th",2),n.EFF(17,"Quantit\xe9"),n.k0s(),n.DNE(18,C,2,0,"th",12),n.k0s()(),n.j41(19,"tbody"),n.DNE(20,p,11,8,"tr",13),n.k0s()()()()),2&t){const c=g.$implicit,o=g.index,e=n.XpG();n.R7$(3),n.Y8G("src",null==e.order||null==e.order.cart||null==e.order.cart.items[o]?null:e.order.cart.items[o].product.image,n.B4B),n.R7$(2),n.JRh((null==e.order||null==e.order.cart||null==e.order.cart.items[o]?null:e.order.cart.items[o].quantity)+" "+(null==e.order||null==e.order.cart||null==e.order.cart.items[o].packaging?null:e.order.cart.items[o].packaging.label)+" "+(null==e.order||null==e.order.cart||null==e.order.cart.items[o].product?null:e.order.cart.items[o].product.label.slice(0,15))),n.R7$(13),n.Y8G("ngIf",e.scheduleTypes),n.R7$(2),n.Y8G("ngForOf",c.schedules)}}const m=[{path:"",component:(()=>{class t{constructor(c,o,e){this.location=c,this.orderService=o,this.scheduleService=e,this.scheduleTypes=!0}ngOnInit(){this.order=this.orderService.order,this.scheduleType(),console.log(this.scheduleService.schedulesTypeSelected)}scheduleType(){this.order?.removals[0]?.removalType==O.JN.perDate&&(this.scheduleTypes=!1)}back(){this.location.back()}static{this.\u0275fac=function(o){return new(o||t)(n.rXU(l.aZ),n.rXU(P.Q),n.rXU(M.l))}}static{this.\u0275cmp=n.VBU({type:t,selectors:[["app-order-removals-details"]],inputs:{order:"order"},decls:9,vars:1,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"order-history-page"],[1,"historic-bill-detail-container"],["class","order-detail",4,"ngFor","ngForOf"],[1,"order-detail"],[1,"h3-title"],[1,"col","item"],[3,"src"],[1,"small-text"],[1,"bill-info"],["class","title",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"value"],["class","value",4,"ngIf"]],template:function(o,e){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return e.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4,"Liste des enl\xe8vements"),n.k0s()()(),n.j41(5,"ion-content")(6,"div",3)(7,"div",4),n.DNE(8,h,21,4,"div",5),n.k0s()()()),2&o&&(n.R7$(8),n.Y8G("ngForOf",null==e.order?null:e.order.removals))},dependencies:[l.Sq,l.bT,i.W9,i.eU,i.KW,i.BC,i.ai,l.vh],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]{background-color:#e5e5e5;height:100%;text-align:-webkit-center;width:100%;display:flex;align-items:center;align-items:baseline}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]{background:var(--clr-default-400);border-radius:1rem;width:100%;padding-bottom:.2rem;margin-top:10px;box-sizing:content-box}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]{width:95%;padding:0 .5em;background:#ffffff;margin:1rem 0rem;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:1.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;width:100%;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%;text-align:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:5px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{border-top:1px solid rgba(0,0,0,.24)}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:6px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(36 * var(--res));margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;font-size:calc(36 * var(--res));overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]{width:95%;padding:0 .5em;background:#ffffff;margin-bottom:1rem!important;border-radius:5px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .form--header[_ngcontent-%COMP%]{text-transform:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{box-shadow:0 4px 25px #00000017;border-radius:1px;overflow-x:hidden;overflow-y:auto;max-height:calc(25 * var(--resH))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{padding:5px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]{font-size:14px;display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{height:100%;padding:5px 0;display:flex;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:calc(100% - 300px)}@media only screen and (max-width: 530px){ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:80px}}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%]{width:80px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%]{width:105px;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%]{width:105px;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:ce}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%]{width:25px;height:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{margin-left:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]{height:30px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#000000a6;height:100%;text-align:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{text-align:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,0,0,.24)}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-bottom:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]{width:95%;padding:0 .5em;background:#ffffff;margin-bottom:1.5rem!important;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-transform:capitalize;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]{width:95%;padding:0 .5em;background:#ffffff;margin-bottom:1rem!important;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-transform:capitalize;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]{margin-top:calc(50 * var(--res));margin-bottom:calc(50 * var(--res));flex:1}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100%;width:100%;padding:calc(25 * var(--res));display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .mWidth[_ngcontent-%COMP%]{width:60%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(8.3 * var(--resH))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{padding:0;height:100%;box-shadow:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:space-between;gap:2px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{align-items:flex-start;width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-around}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(29 * var(--res))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:55%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .form--footer[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}"]})}}return t})()}];let u=(()=>{class t{static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[d.iI.forChild(m),d.iI]})}}return t})(),b=(()=>{class t{static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[l.MD,s.YN,i.bv,u]})}}return t})()}}]);