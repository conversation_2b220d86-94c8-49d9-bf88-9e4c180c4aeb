"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[43],{90043:(x,u,c)=>{c.d(u,{O:()=>R});var g=c(73308),p=c(99987),n=c(2978),M=c(62049),i=c(77897),P=c(63037),C=c(82571),O=c(77717),b=c(77575),l=c(56610),m=c(37222),h=c(74657);function v(a,d){if(1&a){const e=n.RV6();n.j41(0,"div",18)(1,"div",19)(2,"div",20)(3,"ion-icon",21),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.deleteImage())}),n.k0s(),n.j41(4,"ion-icon",22),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showImage=!1)}),n.k0s()(),n.j41(5,"div",23),n.nrm(6,"img",24),n.k0s()()()}if(2&a){const e=n.XpG();n.R7$(6),n.Y8G("src",e.currentImages,n.B4B)}}const f=function(){return{"background-color":" rgb(110, 4, 4)",color:"#ffffff"}};function E(a,d){if(1&a){const e=n.RV6();n.j41(0,"div",25),n.nrm(1,"img",26),n.j41(2,"button",27),n.bIt("click",function(){const t=n.eBV(e),r=t.$implicit,s=t.index,_=n.XpG();return n.Njj(_.openImage(r,s))}),n.EFF(3," Voir"),n.k0s()()}if(2&a){const e=d.$implicit;n.R7$(1),n.Y8G("src",e,n.B4B),n.R7$(1),n.Y8G("ngStyle",n.lJ4(2,f))}}function y(a,d){1&a&&n.nrm(0,"ion-spinner",28)}let R=(()=>{class a{constructor(e,o,t,r,s,_,k){this.translateService=e,this.modalCtrl=o,this.imageCompress=t,this.commonSrv=r,this.othersService=s,this.router=_,this.popoverController=k,this.disabled=!1,this.images=[]}ngOnInit(){}ionViewWillEnter(){}ionViewDidEnter(){}trackByFn(e,o){return e}back(){this.modalCtrl.dismiss(!0)}deleteImage(){this.images.splice(this.index,1),this.showImage=!1}openImage(e,o){this.currentImages=e,this.index=o,this.showImage=!0}addPicture(){var e=this;return(0,g.A)(function*(){try{e.isLoading=!0,e.imageCompress.uploadFile().then(function(){var o=(0,g.A)(function*({image:t}){const r=yield e.imageCompress.compressFile(t,0,70);if(e.imageCompress.byteCount(r)>15e5)return console.warn("Size in bytes was:",e.imageCompress.byteCount(t)),void e.commonSrv.showToast({color:"warning",message:"Veuillez s\xe9lectionner une image d'une taille maximale de 1.5 m\xe9gabite.Image trop volumineuse !"});e.images.push(r)});return function(t){return o.apply(this,arguments)}}())}catch(o){return o}finally{e.isLoading=!1}})()}saveModificationRetrievement(){var e=this;return(0,g.A)(function*(){if(e.isLoading=!0,!e.images)return void e.commonSrv.showToast({color:"warning",message:"Veiller renseigne au moins une image."});if(!e.qtyRecieved)return void e.commonSrv.showToast({color:"warning",message:"Veiller renseigne au moins une quantit\xe9."});if("quantityReceive"in e.curRetriev)return e.commonSrv.showToast({color:"warning",message:"Cette autorisation  a d\xe9j\xe0 une quantit\xe9  re\xe7ue."}),void(e.disabled=!0);const o={dataUrls:e.images,loadNumber:e.curRetriev?.loadNumber},t={quantityReceive:e.qtyRecieved};try{const r=yield e.othersService.postImage(o),s=yield e.othersService.updateRetrievement(e.curRetriev._id,t);e.commonSrv.showToast({color:r.status<400?"success":"danger",message:r?.message}),e.commonSrv.showToast({color:s.status<400?"success":"danger",message:s?.message}),e.qtyRecieved=null,e.images=[],e.router.navigate(["/navigation/retrievements/"]),e.back()}catch{e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===p.T.French?"Echec de cr\xe9ation de l'image":"Failed to create the order"})}finally{e.isLoading=!1}})()}getImgStyle(e){return{"background-image":`url('${e}')`,"background-repeat":"no-repeat","background-position":"top","background-size":"contain"}}showModalRemovalDetail(e){}static{this.\u0275fac=function(o){return new(o||a)(n.rXU(M.E),n.rXU(i.W3),n.rXU(P.ep),n.rXU(C.h),n.rXU(O.J),n.rXU(b.Ix),n.rXU(i.IE))}}static{this.\u0275cmp=n.VBU({type:a,selectors:[["app-retrievement-edit"]],inputs:{curRetriev:"curRetriev"},decls:46,vars:28,consts:[[1,"header"],[1,"title"],[1,"color-primary"],["class","items-img",4,"ngIf"],[1,"form-container"],[1,"elts-form"],[1,"qdty-delivery"],["value","","type","number","placeholder"," Ex: 123",1,"input",3,"ngModel","ngModelChange"],[1,"upload-image"],["class","items",4,"ngFor","ngForOf"],[1,"items",3,"click"],[1,"cart"],["src","/assets/images/camera.png","alt","image",1,"add-image"],[1,"take-picture"],[1,"footer-btn"],[1,"btn","close",3,"click"],[1,"btn","edit",3,"disabled","click"],["name","bubbles",4,"ngIf"],[1,"items-img"],[1,"cont"],[1,"btn-action"],["name","trash-outline",1,"delete",3,"click"],["name","close-outline",3,"click"],[1,"im"],["alt","image",1,"im",3,"src"],[1,"items"],["alt","image",3,"src"],["name","trash-outline",1,"img-delete-icon",3,"ngStyle","click"],["name","bubbles"]],template:function(o,t){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-title",1),n.EFF(3),n.nI1(4,"translate"),n.j41(5,"span",2),n.EFF(6),n.k0s()()()(),n.DNE(7,v,7,1,"div",3),n.j41(8,"ion-content")(9,"div",4)(10,"ion-title",1),n.EFF(11),n.nI1(12,"translate"),n.k0s(),n.j41(13,"div",5)(14,"ion-label",6),n.EFF(15),n.nI1(16,"translate"),n.j41(17,"span"),n.EFF(18),n.k0s()(),n.nrm(19,"br"),n.j41(20,"ion-label",6),n.EFF(21),n.nI1(22,"translate"),n.k0s(),n.j41(23,"ion-input",7),n.bIt("ngModelChange",function(s){return t.qtyRecieved=s}),n.k0s()(),n.j41(24,"ion-title",1),n.EFF(25),n.nI1(26,"translate"),n.k0s(),n.j41(27,"div",8),n.DNE(28,E,4,3,"div",9),n.j41(29,"div",10),n.bIt("click",function(){return t.addPicture()}),n.j41(30,"ion-card",11),n.nrm(31,"img",12),n.j41(32,"p",13),n.EFF(33,"Prendre "),n.nrm(34,"br"),n.EFF(35," une photo"),n.k0s()()()(),n.j41(36,"ion-footer")(37,"div",14)(38,"div",15),n.bIt("click",function(){return t.back()}),n.EFF(39),n.nI1(40,"translate"),n.k0s(),n.j41(41,"ion-button",16),n.bIt("click",function(){return t.saveModificationRetrievement()}),n.j41(42,"ion-label"),n.EFF(43),n.nI1(44,"translate"),n.k0s(),n.DNE(45,y,1,0,"ion-spinner",17),n.k0s()()()()()),2&o&&(n.R7$(3),n.SpI("",n.bMT(4,14,"retrievement-page.editRef")," N\xb0: "),n.R7$(3),n.JRh(null==t.curRetriev?null:t.curRetriev.shipmentNumber),n.R7$(1),n.Y8G("ngIf",t.showImage),n.R7$(4),n.SpI("",n.bMT(12,16,"retrievement-page.addElts")," "),n.R7$(4),n.SpI("",n.bMT(16,18,"retrievement-page.qtyShipped")," :"),n.R7$(3),n.SpI(" (",(null==t.curRetriev||null==t.curRetriev.details?null:t.curRetriev.details.qtyOrdered)+"tonne(s) attendue(s)",")"),n.R7$(3),n.SpI("",n.bMT(22,20,"retrievement-page.Qty-recieved")," "),n.R7$(2),n.Y8G("ngModel",t.qtyRecieved),n.R7$(2),n.SpI("",n.bMT(26,22,"retrievement-page.uploadText")," "),n.R7$(3),n.Y8G("ngForOf",t.images),n.R7$(11),n.JRh(n.bMT(40,24,"retrievement-page.back")),n.R7$(2),n.Y8G("disabled",t.disabled||!t.qtyRecieved||!t.images||t.isLoading),n.R7$(2),n.JRh(n.bMT(44,26,"retrievement-page.save")),n.R7$(2),n.Y8G("ngIf",t.isLoading))},dependencies:[l.Sq,l.bT,l.B3,m.BC,m.vS,i.Jm,i.b_,i.W9,i.M0,i.eU,i.iq,i.$w,i.he,i.w2,i.BC,i.ai,i.su,h.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));display:flex;align-items:center;background:#fff}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}.items-img[_ngcontent-%COMP%]{width:100%;position:absolute;display:flex;justify-content:center;align-items:center;height:85%;background:rgba(149,144,144,.5568627451);z-index:1}.items-img[_ngcontent-%COMP%]   .cont[_ngcontent-%COMP%]{width:80%;height:80%}.items-img[_ngcontent-%COMP%]   .cont[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:5%;margin-bottom:5px}.items-img[_ngcontent-%COMP%]   .cont[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]   .delete[_ngcontent-%COMP%]{color:#e8032f;background:#ebe0e0;border-radius:50%;padding:8px;display:flex}.items-img[_ngcontent-%COMP%]   .cont[_ngcontent-%COMP%]   .im[_ngcontent-%COMP%]{width:100%;height:95%}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]{overflow:auto;padding:.5em;width:100%}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#000;font-style:normal;font-weight:500;line-height:normal}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .elts-form[_ngcontent-%COMP%]{margin:.5em}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .elts-form[_ngcontent-%COMP%]   .qdty-delivery[_ngcontent-%COMP%]{font-size:15px;font-style:normal;font-weight:200;line-height:19px}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .elts-form[_ngcontent-%COMP%]   .qdty-delivery[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#6e0404}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .elts-form[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%]{display:block;height:2em;border-bottom:.5px solid #4e4d4d}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:flex-start;flex-wrap:wrap;gap:5px}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center;flex-shrink:0;border-radius:8px;overflow:hidden;border:1px solid #E1E1E1;transform:scale(1);background:#F3F3F3;height:6em;width:32%}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%]   .img-delete-icon[_ngcontent-%COMP%]{position:absolute;text-align:center;font-size:11px;font-style:normal;font-weight:500;line-height:normal;border-radius:4px;width:96px;height:23px;flex-shrink:0}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:brightness(.7)}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%]   .cart[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:10px}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%]   .cart[_ngcontent-%COMP%]   .take-picture[_ngcontent-%COMP%]{text-align:center!important}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .items[_ngcontent-%COMP%]   .cart[_ngcontent-%COMP%]   .add-image[_ngcontent-%COMP%]{width:35px;border-radius:50%;height:35px}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .btn-container[_ngcontent-%COMP%]{width:93%}ion-content[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]   .text-container[_ngcontent-%COMP%]{height:56px}ion-content[_ngcontent-%COMP%]   .footer-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin-block:2vh;margin-inline:1rem;column-gap:2rem}ion-content[_ngcontent-%COMP%]   .footer-btn[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:1.5vh;font-size:calc(45 * var(--res));border-radius:.4rem;width:10rem;text-align:center}ion-content[_ngcontent-%COMP%]   .footer-btn[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%]{background-color:#dedede;color:#000}ion-content[_ngcontent-%COMP%]   .footer-btn[_ngcontent-%COMP%]   .edit[_ngcontent-%COMP%]{color:#fff;background-color:#143c5d}"]})}}return a})()}}]);