"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1290],{11290:(g,o,t)=>{t.r(o),t.d(o,{RetrievementEditPageModule:()=>u});var r=t(56610),i=t(37222),d=t(77897),e=t(77575),m=t(90043),n=t(2978);const c=[{path:"",component:m.O}];let l=(()=>{class s{static{this.\u0275fac=function(a){return new(a||s)}}static{this.\u0275mod=n.$C({type:s})}static{this.\u0275inj=n.G2t({imports:[e.iI.forChild(c),e.iI]})}}return s})();var h=t(74657),v=t(93887);let u=(()=>{class s{static{this.\u0275fac=function(a){return new(a||s)}}static{this.\u0275mod=n.$C({type:s})}static{this.\u0275inj=n.G2t({imports:[r.MD,i.YN,d.bv,v.G,h.h,i.X1,l]})}}return s})()}}]);