@use "../utils/mixins.scss" as *;

.btn {
    // display: inline-block;
    align-items: center;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: var(--container-padding);
    font-size: 14px;
    font-family: var(--mont-bold);
    border-radius: 5px;
    width: 100%;
    transition: color 0.15s ease-in-out,
        background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out,
        box-shadow 0.15s ease-in-out;
}

.btn-icon-inline {
    @include v-center;

    i {
        margin-right: 5px;
    }
}

.btn-primary {
    ion-button {
        width: 100%;
        height: 100%;
        --background: var(--clr-primary-750);
        --padding-top: 16px;
        --padding-bottom: 16px;
        font-weight: 600;
        font-family: var(--mont-bold);
        text-transform: uppercase;
        transition: none;
        letter-spacing: 0.01rem;

        @media (min-width: 650px) {
            --padding-top: 1em;
            --padding-bottom: 1em;

        }

        &:hover {
            border-radius: 5px;
            background-color: var(--clr-primary-300);
            border-color: var(--clr-secondary-300);
        }
    }
}


.btn-secondary {
    color: #fff;
    background-color: var(--clr-secondary-400);
    border-color: var(--clr-secondary-400);
    font-weight: bold;

    &:hover {
        background-color: var(--clr-secondary-500);
        border-color: var(--clr-secondary-500);
    }
}

.btn-tertiary {
    color: #fff;
    background-color: var(--clr-tertiary-400);
    border-color: var(--clr-tertiary-400);
    font-weight: bold;

    &:hover {
        background-color: var(--clr-tertiary-500);
        border-color: var(--clr-tertiary-500);
    }
}

.btn-success {
    color: #fff;
    background-color: var(--clr-success-400);
    border-color: var(--clr-success-400);

    &:hover {
        background-color: var(--clr-success-500);
        border-color: var(--clr-success-500);
    }
}

.btn-warning {
    background-color: var(--clr-secondary-400);
    border-color: var(--clr-secondary-400);
    font-weight: bold;
    font-family: "Montserrat-ExtraBold";

    &:hover {
        background-color: var(--clr-secondary-500);
        border-color: var(--clr-secondary-500);
    }
}

.btn-danger {
    color: #fff;
    background-color: var(--clr-danger-200);
    border-color: var(--clr-danger-200);

    &:hover {
        background-color: var(--clr-danger-300);
        border-color: var(--clr-danger-300);
    }
}

.btn-danger-dark {
    color: #fff;
    background-color: var(--clr-danger-400);
    border-color: var(--clr-danger-400);

    &:hover {
        background-color: var(--clr-danger-500);
        border-color: var(--clr-danger-500);
    }
}

.btn-outline-success {
    color: var(--clr-success-500);
    background-color: transparent;
    border-color: var(--clr-success-500);

    &:hover,
    &:active,
    &:focus,
    &.active {
        color: #fff;
        background-color: var(--clr-success-500);
    }
}

.btn-big {
    padding-left: 2em;
    padding-right: 2em;
}

.btn-sm {
    font-size: 1rem;
}

.btn-outline-danger {
    color: var(--clr-danger-400);
    background-color: transparent;
    border-color: var(--clr-danger-400);

    &:hover,
    &:active,
    &:focus,
    &.active {
        color: #fff;
        background-color: var(--clr-danger-400);
    }
}

.btn-edit {
    display: flex !important;
    @include vh-center;

    svg {
        width: 21px;
        height: 21px;
    }
}

.btn-delete {
    display: flex !important;
    @include vh-center;

    // background-color: $color-twenty;
    // color: $color-twenty-one;
    svg {
        width: 21px;
        height: 21px;
    }
}

.btn-show-detail {
    display: flex !important;
    @include vh-center;

    // background-color: $color-twenty-seven;
    // color: $color-twenty-eight;
    svg {
        width: 21px;
        height: 21px;
    }
}

.btn-icon {
    @include vh-center;
    padding: 6px;

    i {
        cursor: pointer;

        &:hover {
            scale: 1.1;
            transition: 1s;
        }
    }

    &-edit {
        i {
            color: var(--clr-primary-400);
        }

        &:hover {
            background-color: var(--clr-primary-50);
        }
    }

    &-delete {
        i {
            color: var(--clr-secondary-500);
        }

        &:hover {
            background-color: var(--clr-secondary-200);
        }
    }

    &-view {
        i {
            color: var(--clr-tertiary-500);
        }

        &:hover {
            background-color: var(--clr-tertiary-100);
        }
    }
}

.btn-icon-text {
    i {
        font-size: 12px;
        line-height: 0;
    }
}