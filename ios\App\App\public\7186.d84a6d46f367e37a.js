"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7186],{71604:(xo,ni,wt)=>{wt.d(ni,{D:()=>li,Z:()=>ri});var U=wt(56610),S=wt(2978),lt=wt(17438);function ct(R,gt){1&R&&S.eu8(0)}function Rt(R,gt){if(1&R&&(S.j41(0,"div",8),S.SdG(1,1),S.DNE(2,ct,1,0,"ng-container",6),S.k0s()),2&R){const z=S.XpG();S.R7$(2),S.Y8G("ngTemplateOutlet",z.headerTemplate)}}function Zi(R,gt){1&R&&S.eu8(0)}function ft(R,gt){if(1&R&&(S.j41(0,"div",9),<PERSON><PERSON>(1),<PERSON><PERSON><PERSON>(2,Zi,1,0,"ng-container",6),S.k0s()),2&R){const z=S.XpG();S.R7$(1),S.SpI(" ",z.header," "),S.R7$(1),S.Y8G("ngTemplateOutlet",z.titleTemplate)}}function ht(R,gt){1&R&&S.eu8(0)}function Pe(R,gt){if(1&R&&(S.j41(0,"div",10),S.EFF(1),S.DNE(2,ht,1,0,"ng-container",6),S.k0s()),2&R){const z=S.XpG();S.R7$(1),S.SpI(" ",z.subheader," "),S.R7$(1),S.Y8G("ngTemplateOutlet",z.subtitleTemplate)}}function et(R,gt){1&R&&S.eu8(0)}function se(R,gt){1&R&&S.eu8(0)}function oi(R,gt){if(1&R&&(S.j41(0,"div",11),S.SdG(1,2),S.DNE(2,se,1,0,"ng-container",6),S.k0s()),2&R){const z=S.XpG();S.R7$(2),S.Y8G("ngTemplateOutlet",z.footerTemplate)}}const ai=["*",[["p-header"]],[["p-footer"]]],Xt=["*","p-header","p-footer"];let ri=(()=>{class R{el;header;subheader;style;styleClass;headerFacet;footerFacet;templates;headerTemplate;titleTemplate;subtitleTemplate;contentTemplate;footerTemplate;constructor(z){this.el=z}ngAfterContentInit(){this.templates.forEach(z=>{switch(z.getType()){case"header":this.headerTemplate=z.template;break;case"title":this.titleTemplate=z.template;break;case"subtitle":this.subtitleTemplate=z.template;break;case"content":default:this.contentTemplate=z.template;break;case"footer":this.footerTemplate=z.template}})}getBlockableElement(){return this.el.nativeElement.children[0]}static \u0275fac=function(rt){return new(rt||R)(S.rXU(S.aKT))};static \u0275cmp=S.VBU({type:R,selectors:[["p-card"]],contentQueries:function(rt,K,ne){if(1&rt&&(S.wni(ne,lt.Y9,5),S.wni(ne,lt.wi,5),S.wni(ne,lt.Ei,4)),2&rt){let Ct;S.mGM(Ct=S.lsd())&&(K.headerFacet=Ct.first),S.mGM(Ct=S.lsd())&&(K.footerFacet=Ct.first),S.mGM(Ct=S.lsd())&&(K.templates=Ct)}},hostAttrs:[1,"p-element"],inputs:{header:"header",subheader:"subheader",style:"style",styleClass:"styleClass"},ngContentSelectors:Xt,decls:9,vars:10,consts:[[3,"ngClass","ngStyle"],["class","p-card-header",4,"ngIf"],[1,"p-card-body"],["class","p-card-title",4,"ngIf"],["class","p-card-subtitle",4,"ngIf"],[1,"p-card-content"],[4,"ngTemplateOutlet"],["class","p-card-footer",4,"ngIf"],[1,"p-card-header"],[1,"p-card-title"],[1,"p-card-subtitle"],[1,"p-card-footer"]],template:function(rt,K){1&rt&&(S.NAR(ai),S.j41(0,"div",0),S.DNE(1,Rt,3,1,"div",1),S.j41(2,"div",2),S.DNE(3,ft,3,2,"div",3),S.DNE(4,Pe,3,2,"div",4),S.j41(5,"div",5),S.SdG(6),S.DNE(7,et,1,0,"ng-container",6),S.k0s(),S.DNE(8,oi,3,1,"div",7),S.k0s()()),2&rt&&(S.HbH(K.styleClass),S.Y8G("ngClass","p-card p-component")("ngStyle",K.style),S.BMQ("data-pc-name","card"),S.R7$(1),S.Y8G("ngIf",K.headerFacet||K.headerTemplate),S.R7$(2),S.Y8G("ngIf",K.header||K.titleTemplate),S.R7$(1),S.Y8G("ngIf",K.subheader||K.subtitleTemplate),S.R7$(3),S.Y8G("ngTemplateOutlet",K.contentTemplate),S.R7$(1),S.Y8G("ngIf",K.footerFacet||K.footerTemplate))},dependencies:[U.YU,U.bT,U.T3,U.B3],styles:["@layer primeng{.p-card-header img{width:100%}}\n"],encapsulation:2,changeDetection:0})}return R})(),li=(()=>{class R{static \u0275fac=function(rt){return new(rt||R)};static \u0275mod=S.$C({type:R});static \u0275inj=S.G2t({imports:[U.MD,lt.Gg]})}return R})()},60787:(xo,ni,wt)=>{wt.d(ni,{F:()=>Ah,X:()=>Oh});var U=wt(2978),S=wt(56610);function lt(i){return i+.5|0}const ct=(i,e,t)=>Math.max(Math.min(i,t),e);function Rt(i){return ct(lt(2.55*i),0,255)}function ft(i){return ct(lt(255*i),0,255)}function ht(i){return ct(lt(i/2.55)/100,0,1)}function Pe(i){return ct(lt(100*i),0,100)}const et={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},se=[..."0123456789ABCDEF"],oi=i=>se[15&i],ai=i=>se[(240&i)>>4]+se[15&i],Xt=i=>(240&i)>>4==(15&i);const z=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function rt(i,e,t){const s=e*Math.min(t,1-t),n=(o,a=(o+i/30)%12)=>t-s*Math.max(Math.min(a-3,9-a,1),-1);return[n(0),n(8),n(4)]}function K(i,e,t){const s=(n,o=(n+i/60)%6)=>t-t*e*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function ne(i,e,t){const s=rt(i,1,.5);let n;for(e+t>1&&(n=1/(e+t),e*=n,t*=n),n=0;n<3;n++)s[n]*=1-e-t,s[n]+=e;return s}function ci(i){const t=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(t,s,n),a=Math.min(t,s,n),r=(o+a)/2;let l,c,h;return o!==a&&(h=o-a,c=r>.5?h/(2-o-a):h/(o+a),l=function Ct(i,e,t,s,n){return i===n?(e-t)/s+(e<t?6:0):e===n?(t-i)/s+2:(i-e)/s+4}(t,s,n,h,o),l=60*l+.5),[0|l,c||0,r]}function hi(i,e,t,s){return(Array.isArray(e)?i(e[0],e[1],e[2]):i(e,t,s)).map(ft)}function di(i,e,t){return hi(rt,i,e,t)}function Qi(i){return(i%360+360)%360}const Ji={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ts={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let De;const Po=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,ui=i=>i<=.0031308?12.92*i:1.055*Math.pow(i,1/2.4)-.055,Gt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function Oe(i,e,t){if(i){let s=ci(i);s[e]=Math.max(0,Math.min(s[e]+s[e]*t,0===e?360:1)),s=di(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function es(i,e){return i&&Object.assign(e||{},i)}function is(i){var e={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(e={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(e.a=ft(i[3]))):(e=es(i,{r:0,g:0,b:0,a:1})).a=ft(e.a),e}function To(i){return"r"===i.charAt(0)?function Do(i){const e=Po.exec(i);let s,n,o,t=255;if(e){if(e[7]!==s){const a=+e[7];t=e[8]?Rt(a):ct(255*a,0,255)}return s=+e[1],n=+e[3],o=+e[5],s=255&(e[2]?Rt(s):ct(s,0,255)),n=255&(e[4]?Rt(n):ct(n,0,255)),o=255&(e[6]?Rt(o):ct(o,0,255)),{r:s,g:n,b:o,a:t}}}(i):function Mo(i){const e=z.exec(i);let s,t=255;if(!e)return;e[5]!==s&&(t=e[6]?Rt(+e[5]):ft(+e[5]));const n=Qi(+e[2]),o=+e[3]/100,a=+e[4]/100;return s="hwb"===e[1]?function yo(i,e,t){return hi(ne,i,e,t)}(n,o,a):"hsv"===e[1]?function vo(i,e,t){return hi(K,i,e,t)}(n,o,a):di(n,o,a),{r:s[0],g:s[1],b:s[2],a:t}}(i)}class Kt{constructor(e){if(e instanceof Kt)return e;const t=typeof e;let s;"object"===t?s=is(e):"string"===t&&(s=function li(i){var t,e=i.length;return"#"===i[0]&&(4===e||5===e?t={r:255&17*et[i[1]],g:255&17*et[i[2]],b:255&17*et[i[3]],a:5===e?17*et[i[4]]:255}:(7===e||9===e)&&(t={r:et[i[1]]<<4|et[i[2]],g:et[i[3]]<<4|et[i[4]],b:et[i[5]]<<4|et[i[6]],a:9===e?et[i[7]]<<4|et[i[8]]:255})),t}(e)||function Co(i){De||(De=function wo(){const i={},e=Object.keys(ts),t=Object.keys(Ji);let s,n,o,a,r;for(s=0;s<e.length;s++){for(a=r=e[s],n=0;n<t.length;n++)o=t[n],r=r.replace(o,Ji[o]);o=parseInt(ts[a],16),i[r]=[o>>16&255,o>>8&255,255&o]}return i}(),De.transparent=[0,0,0,0]);const e=De[i.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(e)||To(e)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var e=es(this._rgb);return e&&(e.a=ht(e.a)),e}set rgb(e){this._rgb=is(e)}rgbString(){return this._valid?function Oo(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${ht(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}(this._rgb):void 0}hexString(){return this._valid?function gt(i){var e=(i=>Xt(i.r)&&Xt(i.g)&&Xt(i.b)&&Xt(i.a))(i)?oi:ai;return i?"#"+e(i.r)+e(i.g)+e(i.b)+((i,e)=>i<255?e(i):"")(i.a,e):void 0}(this._rgb):void 0}hslString(){return this._valid?function So(i){if(!i)return;const e=ci(i),t=e[0],s=Pe(e[1]),n=Pe(e[2]);return i.a<255?`hsla(${t}, ${s}%, ${n}%, ${ht(i.a)})`:`hsl(${t}, ${s}%, ${n}%)`}(this._rgb):void 0}mix(e,t){if(e){const s=this.rgb,n=e.rgb;let o;const a=t===o?.5:t,r=2*a-1,l=s.a-n.a,c=((r*l==-1?r:(r+l)/(1+r*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=a*s.a+(1-a)*n.a,this.rgb=s}return this}interpolate(e,t){return e&&(this._rgb=function Ao(i,e,t){const s=Gt(ht(i.r)),n=Gt(ht(i.g)),o=Gt(ht(i.b));return{r:ft(ui(s+t*(Gt(ht(e.r))-s))),g:ft(ui(n+t*(Gt(ht(e.g))-n))),b:ft(ui(o+t*(Gt(ht(e.b))-o))),a:i.a+t*(e.a-i.a)}}(this._rgb,e._rgb,t)),this}clone(){return new Kt(this.rgb)}alpha(e){return this._rgb.a=ft(e),this}clearer(e){return this._rgb.a*=1-e,this}greyscale(){const e=this._rgb,t=lt(.3*e.r+.59*e.g+.11*e.b);return e.r=e.g=e.b=t,this}opaquer(e){return this._rgb.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return Oe(this._rgb,2,e),this}darken(e){return Oe(this._rgb,2,-e),this}saturate(e){return Oe(this._rgb,1,e),this}desaturate(e){return Oe(this._rgb,1,-e),this}rotate(e){return function ko(i,e){var t=ci(i);t[0]=Qi(t[0]+e),t=di(t),i.r=t[0],i.g=t[1],i.b=t[2]}(this._rgb,e),this}}function _t(){}const Lo=(()=>{let i=0;return()=>i++})();function A(i){return null==i}function B(i){if(Array.isArray&&Array.isArray(i))return!0;const e=Object.prototype.toString.call(i);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function T(i){return null!==i&&"[object Object]"===Object.prototype.toString.call(i)}function j(i){return("number"==typeof i||i instanceof Number)&&isFinite(+i)}function nt(i,e){return j(i)?i:e}function P(i,e){return typeof i>"u"?e:i}const ss=(i,e)=>"string"==typeof i&&i.endsWith("%")?parseFloat(i)/100*e:+i;function I(i,e,t){if(i&&"function"==typeof i.call)return i.apply(t,e)}function E(i,e,t,s){let n,o,a;if(B(i))if(o=i.length,s)for(n=o-1;n>=0;n--)e.call(t,i[n],n);else for(n=0;n<o;n++)e.call(t,i[n],n);else if(T(i))for(a=Object.keys(i),o=a.length,n=0;n<o;n++)e.call(t,i[a[n]],a[n])}function Ae(i,e){let t,s,n,o;if(!i||!e||i.length!==e.length)return!1;for(t=0,s=i.length;t<s;++t)if(n=i[t],o=e[t],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Te(i){if(B(i))return i.map(Te);if(T(i)){const e=Object.create(null),t=Object.keys(i),s=t.length;let n=0;for(;n<s;++n)e[t[n]]=Te(i[t[n]]);return e}return i}function ns(i){return-1===["__proto__","prototype","constructor"].indexOf(i)}function Eo(i,e,t,s){if(!ns(i))return;const n=e[i],o=t[i];T(n)&&T(o)?oe(n,o,s):e[i]=Te(o)}function oe(i,e,t){const s=B(e)?e:[e],n=s.length;if(!T(i))return i;const o=(t=t||{}).merger||Eo;let a;for(let r=0;r<n;++r){if(a=s[r],!T(a))continue;const l=Object.keys(a);for(let c=0,h=l.length;c<h;++c)o(l[c],i,a,t)}return i}function ae(i,e){return oe(i,e,{merger:Fo})}function Fo(i,e,t){if(!ns(i))return;const s=e[i],n=t[i];T(s)&&T(n)?ae(s,n):Object.prototype.hasOwnProperty.call(e,i)||(e[i]=Te(n))}const os={"":i=>i,x:i=>i.x,y:i=>i.y};function Pt(i,e){return(os[e]||(os[e]=function zo(i){const e=function Io(i){const e=i.split("."),t=[];let s="";for(const n of e)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(t.push(s),s="");return t}(i);return t=>{for(const s of e){if(""===s)break;t=t&&t[s]}return t}}(e)))(i)}function fi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const re=i=>typeof i<"u",Dt=i=>"function"==typeof i,as=(i,e)=>{if(i.size!==e.size)return!1;for(const t of i)if(!e.has(t))return!1;return!0},V=Math.PI,W=2*V,Vo=W+V,Le=Number.POSITIVE_INFINITY,Wo=V/180,$=V/2,Et=V/4,rs=2*V/3,Ot=Math.log10,pt=Math.sign;function le(i,e,t){return Math.abs(i-e)<t}function ls(i){const e=Math.round(i);i=le(i,e,i/1e3)?e:i;const t=Math.pow(10,Math.floor(Ot(i))),s=i/t;return(s<=1?1:s<=2?2:s<=5?5:10)*t}function qt(i){return!function Ho(i){return"symbol"==typeof i||"object"==typeof i&&null!==i&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function cs(i,e,t){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][t],isNaN(o)||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}function dt(i){return i*(V/180)}function gi(i){return i*(180/V)}function hs(i){if(!j(i))return;let e=1,t=0;for(;Math.round(i*e)/e!==i;)e*=10,t++;return t}function ds(i,e){const t=e.x-i.x,s=e.y-i.y,n=Math.sqrt(t*t+s*s);let o=Math.atan2(s,t);return o<-.5*V&&(o+=W),{angle:o,distance:n}}function pi(i,e){return Math.sqrt(Math.pow(e.x-i.x,2)+Math.pow(e.y-i.y,2))}function $o(i,e){return(i-e+Vo)%W-V}function ot(i){return(i%W+W)%W}function ce(i,e,t,s){const n=ot(i),o=ot(e),a=ot(t),r=ot(o-n),l=ot(a-n),c=ot(n-o),h=ot(n-a);return n===o||n===a||s&&o===a||r>l&&c<h}function G(i,e,t){return Math.max(e,Math.min(t,i))}function xt(i,e,t,s=1e-6){return i>=Math.min(e,t)-s&&i<=Math.max(e,t)+s}function mi(i,e,t){t=t||(a=>i[a]<e);let o,s=i.length-1,n=0;for(;s-n>1;)o=n+s>>1,t(o)?n=o:s=o;return{lo:n,hi:s}}const yt=(i,e,t,s)=>mi(i,t,s?n=>{const o=i[n][e];return o<t||o===t&&i[n+1][e]===t}:n=>i[n][e]<t),Uo=(i,e,t)=>mi(i,t,s=>i[s][e]>=t),us=["push","pop","shift","splice","unshift"];function fs(i,e){const t=i._chartjs;if(!t)return;const s=t.listeners,n=s.indexOf(e);-1!==n&&s.splice(n,1),!(s.length>0)&&(us.forEach(o=>{delete i[o]}),delete i._chartjs)}function gs(i){const e=new Set(i);return e.size===i.length?i:Array.from(e)}const ps=typeof window>"u"?function(i){return i()}:window.requestAnimationFrame;function ms(i,e){let t=[],s=!1;return function(...n){t=n,s||(s=!0,ps.call(window,()=>{s=!1,i.apply(e,t)}))}}const bi=i=>"start"===i?"left":"end"===i?"right":"center",Z=(i,e,t)=>"start"===i?e:"end"===i?t:(e+t)/2;function bs(i,e,t){const s=e.length;let n=0,o=s;if(i._sorted){const{iScale:a,vScale:r,_parsed:l}=i,c=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,h=a.axis,{min:d,max:u,minDefined:f,maxDefined:g}=a.getUserBounds();if(f){if(n=Math.min(yt(l,h,d).lo,t?s:yt(e,h,a.getPixelForValue(d)).lo),c){const p=l.slice(0,n+1).reverse().findIndex(m=>!A(m[r.axis]));n-=Math.max(0,p)}n=G(n,0,s-1)}if(g){let p=Math.max(yt(l,a.axis,u,!0).hi+1,t?0:yt(e,h,a.getPixelForValue(u),!0).hi+1);if(c){const m=l.slice(p-1).findIndex(b=>!A(b[r.axis]));p+=Math.max(0,m)}o=G(p,n,s)-n}else o=s-n}return{start:n,count:o}}function _s(i){const{xScale:e,yScale:t,_scaleRanges:s}=i,n={xmin:e.min,xmax:e.max,ymin:t.min,ymax:t.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==t.min||s.ymax!==t.max;return Object.assign(s,n),o}const Re=i=>0===i||1===i,xs=(i,e,t)=>-Math.pow(2,10*(i-=1))*Math.sin((i-e)*W/t),ys=(i,e,t)=>Math.pow(2,-10*i)*Math.sin((i-e)*W/t)+1,he={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>1-Math.cos(i*$),easeOutSine:i=>Math.sin(i*$),easeInOutSine:i=>-.5*(Math.cos(V*i)-1),easeInExpo:i=>0===i?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>1===i?1:1-Math.pow(2,-10*i),easeInOutExpo:i=>Re(i)?i:i<.5?.5*Math.pow(2,10*(2*i-1)):.5*(2-Math.pow(2,-10*(2*i-1))),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>Re(i)?i:xs(i,.075,.3),easeOutElastic:i=>Re(i)?i:ys(i,.075,.3),easeInOutElastic:i=>Re(i)?i:i<.5?.5*xs(2*i,.1125,.45):.5+.5*ys(2*i-1,.1125,.45),easeInBack:i=>i*i*(2.70158*i-1.70158),easeOutBack:i=>(i-=1)*i*(2.70158*i********)+1,easeInOutBack(i){let e=1.70158;return(i/=.5)<1?i*i*((1+(e*=1.525))*i-e)*.5:.5*((i-=2)*i*((1+(e*=1.525))*i+e)+2)},easeInBounce:i=>1-he.easeOutBounce(1-i),easeOutBounce:i=>i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375,easeInOutBounce:i=>i<.5?.5*he.easeInBounce(2*i):.5*he.easeOutBounce(2*i-1)+.5};function _i(i){if(i&&"object"==typeof i){const e=i.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function vs(i){return _i(i)?i:new Kt(i)}function xi(i){return _i(i)?i:new Kt(i).saturate(.5).darken(.1).hexString()}const Zo=["x","y","borderWidth","radius","tension"],Qo=["color","borderColor","backgroundColor"],Ms=new Map;function de(i,e,t){return function ea(i,e){e=e||{};const t=i+JSON.stringify(e);let s=Ms.get(t);return s||(s=new Intl.NumberFormat(i,e),Ms.set(t,s)),s}(e,t).format(i)}const ks={values:i=>B(i)?i:""+i,numeric(i,e,t){if(0===i)return"0";const s=this.chart.options.locale;let n,o=i;if(t.length>1){const c=Math.max(Math.abs(t[0].value),Math.abs(t[t.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=function ia(i,e){let t=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(t)>=1&&i!==Math.floor(i)&&(t=i-Math.floor(i)),t}(i,t)}const a=Ot(Math.abs(o)),r=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:n,minimumFractionDigits:r,maximumFractionDigits:r};return Object.assign(l,this.options.ticks.format),de(i,s,l)},logarithmic(i,e,t){if(0===i)return"0";const s=t[e].significand||i/Math.pow(10,Math.floor(Ot(i)));return[1,2,3,5,10,15].includes(s)||e>.8*t.length?ks.numeric.call(this,i,e,t):""}};var Ee={formatters:ks};const Ft=Object.create(null),yi=Object.create(null);function ue(i,e){if(!e)return i;const t=e.split(".");for(let s=0,n=t.length;s<n;++s){const o=t[s];i=i[o]||(i[o]=Object.create(null))}return i}function vi(i,e,t){return"string"==typeof e?oe(ue(i,e),t):oe(ue(i,""),e)}class na{constructor(e,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>xi(n.backgroundColor),this.hoverBorderColor=(s,n)=>xi(n.borderColor),this.hoverColor=(s,n)=>xi(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(t)}set(e,t){return vi(this,e,t)}get(e){return ue(this,e)}describe(e,t){return vi(yi,e,t)}override(e,t){return vi(Ft,e,t)}route(e,t,s,n){const o=ue(this,e),a=ue(this,s),r="_"+t;Object.defineProperties(o,{[r]:{value:o[t],writable:!0},[t]:{enumerable:!0,get(){const l=this[r],c=a[n];return T(l)?Object.assign({},c,l):P(l,c)},set(l){this[r]=l}}})}apply(e){e.forEach(t=>t(this))}}var N=new na({_scriptable:i=>!i.startsWith("on"),_indexable:i=>"events"!==i,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function Jo(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>"onProgress"!==e&&"onComplete"!==e&&"fn"!==e}),i.set("animations",{colors:{type:"color",properties:Qo},numbers:{type:"number",properties:Zo}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>0|e}}}})},function ta(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function sa(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,t)=>t.lineWidth,tickColor:(e,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ee.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&"callback"!==e&&"parser"!==e,_indexable:e=>"borderDash"!==e&&"tickBorderDash"!==e&&"dash"!==e}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:e=>"backdropPadding"!==e&&"callback"!==e,_indexable:e=>"backdropPadding"!==e})}]);function Fe(i,e,t,s,n){let o=e[n];return o||(o=e[n]=i.measureText(n).width,t.push(n)),o>s&&(s=o),s}function aa(i,e,t,s){let n=(s=s||{}).data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(n=s.data={},o=s.garbageCollect=[],s.font=e),i.save(),i.font=e;let a=0;const r=t.length;let l,c,h,d,u;for(l=0;l<r;l++)if(d=t[l],null==d||B(d)){if(B(d))for(c=0,h=d.length;c<h;c++)u=d[c],null!=u&&!B(u)&&(a=Fe(i,n,o,a,u))}else a=Fe(i,n,o,a,d);i.restore();const f=o.length/2;if(f>t.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return a}function It(i,e,t){const s=i.currentDevicePixelRatio,n=0!==t?Math.max(t/2,.5):0;return Math.round((e-n)*s)/s+n}function Ss(i,e){!e&&!i||((e=e||i.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,i.width,i.height),e.restore())}function Mi(i,e,t,s){ws(i,e,t,s,null)}function ws(i,e,t,s,n){let o,a,r,l,c,h,d,u;const f=e.pointStyle,g=e.rotation,p=e.radius;let m=(g||0)*Wo;if(f&&"object"==typeof f&&(o=f.toString(),"[object HTMLImageElement]"===o||"[object HTMLCanvasElement]"===o))return i.save(),i.translate(t,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),void i.restore();if(!(isNaN(p)||p<=0)){switch(i.beginPath(),f){default:n?i.ellipse(t,s,n/2,p,0,0,W):i.arc(t,s,p,0,W),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(t+Math.sin(m)*h,s-Math.cos(m)*p),m+=rs,i.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*p),m+=rs,i.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=.516*p,l=p-c,a=Math.cos(m+Et)*l,d=Math.cos(m+Et)*(n?n/2-c:l),r=Math.sin(m+Et)*l,u=Math.sin(m+Et)*(n?n/2-c:l),i.arc(t-d,s-r,c,m-V,m-$),i.arc(t+u,s-a,c,m-$,m),i.arc(t+d,s+r,c,m,m+$),i.arc(t-u,s+a,c,m+$,m+V),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(t-h,s-l,2*h,2*l);break}m+=Et;case"rectRot":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(t-d,s-r),i.lineTo(t+u,s-a),i.lineTo(t+d,s+r),i.lineTo(t-u,s+a),i.closePath();break;case"crossRot":m+=Et;case"cross":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(t-d,s-r),i.lineTo(t+d,s+r),i.moveTo(t+u,s-a),i.lineTo(t-u,s+a);break;case"star":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(t-d,s-r),i.lineTo(t+d,s+r),i.moveTo(t+u,s-a),i.lineTo(t-u,s+a),m+=Et,d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(t-d,s-r),i.lineTo(t+d,s+r),i.moveTo(t+u,s-a),i.lineTo(t-u,s+a);break;case"line":a=n?n/2:Math.cos(m)*p,r=Math.sin(m)*p,i.moveTo(t-a,s-r),i.lineTo(t+a,s+r);break;case"dash":i.moveTo(t,s),i.lineTo(t+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath()}i.fill(),e.borderWidth>0&&i.stroke()}}function vt(i,e,t){return t=t||.5,!e||i&&i.x>e.left-t&&i.x<e.right+t&&i.y>e.top-t&&i.y<e.bottom+t}function Ie(i,e){i.save(),i.beginPath(),i.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),i.clip()}function ze(i){i.restore()}function ra(i,e,t,s,n){if(!e)return i.lineTo(t.x,t.y);if("middle"===n){const o=(e.x+t.x)/2;i.lineTo(o,e.y),i.lineTo(o,t.y)}else"after"===n!=!!s?i.lineTo(e.x,t.y):i.lineTo(t.x,e.y);i.lineTo(t.x,t.y)}function la(i,e,t,s){if(!e)return i.lineTo(t.x,t.y);i.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?t.cp2x:t.cp1x,s?t.cp2y:t.cp1y,t.x,t.y)}function ha(i,e,t,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),a=e-o.actualBoundingBoxLeft,r=e+o.actualBoundingBoxRight,c=t+o.actualBoundingBoxDescent,h=n.strikethrough?(t-o.actualBoundingBoxAscent+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(a,h),i.lineTo(r,h),i.stroke()}}function da(i,e){const t=i.fillStyle;i.fillStyle=e.color,i.fillRect(e.left,e.top,e.width,e.height),i.fillStyle=t}function zt(i,e,t,s,n,o={}){const a=B(e)?e:[e],r=o.strokeWidth>0&&""!==o.strokeColor;let l,c;for(i.save(),i.font=n.string,function ca(i,e){e.translation&&i.translate(e.translation[0],e.translation[1]),A(e.rotation)||i.rotate(e.rotation),e.color&&(i.fillStyle=e.color),e.textAlign&&(i.textAlign=e.textAlign),e.textBaseline&&(i.textBaseline=e.textBaseline)}(i,o),l=0;l<a.length;++l)c=a[l],o.backdrop&&da(i,o.backdrop),r&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),A(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,t,s,o.maxWidth)),i.fillText(c,t,s,o.maxWidth),ha(i,t,s,c,o),s+=Number(n.lineHeight);i.restore()}function fe(i,e){const{x:t,y:s,w:n,h:o,radius:a}=e;i.arc(t+a.topLeft,s+a.topLeft,a.topLeft,1.5*V,V,!0),i.lineTo(t,s+o-a.bottomLeft),i.arc(t+a.bottomLeft,s+o-a.bottomLeft,a.bottomLeft,V,$,!0),i.lineTo(t+n-a.bottomRight,s+o),i.arc(t+n-a.bottomRight,s+o-a.bottomRight,a.bottomRight,$,0,!0),i.lineTo(t+n,s+a.topRight),i.arc(t+n-a.topRight,s+a.topRight,a.topRight,0,-$,!0),i.lineTo(t+a.topLeft,s)}const ua=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,fa=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function ga(i,e){const t=(""+i).match(ua);if(!t||"normal"===t[1])return 1.2*e;switch(i=+t[2],t[3]){case"px":return i;case"%":i/=100}return e*i}const pa=i=>+i||0;function ki(i,e){const t={},s=T(e),n=s?Object.keys(e):e,o=T(i)?s?a=>P(i[a],i[e[a]]):a=>i[a]:()=>i;for(const a of n)t[a]=pa(o(a));return t}function Cs(i){return ki(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Bt(i){return ki(i,["topLeft","topRight","bottomLeft","bottomRight"])}function Q(i){const e=Cs(i);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function X(i,e){let t=P((i=i||{}).size,(e=e||N.font).size);"string"==typeof t&&(t=parseInt(t,10));let s=P(i.style,e.style);s&&!(""+s).match(fa)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:P(i.family,e.family),lineHeight:ga(P(i.lineHeight,e.lineHeight),t),size:t,style:s,weight:P(i.weight,e.weight),string:""};return n.string=function oa(i){return!i||A(i.size)||A(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}(n),n}function ge(i,e,t,s){let o,a,r,n=!0;for(o=0,a=i.length;o<a;++o)if(r=i[o],void 0!==r&&(void 0!==e&&"function"==typeof r&&(r=r(e),n=!1),void 0!==t&&B(r)&&(r=r[t%r.length],n=!1),void 0!==r))return s&&!n&&(s.cacheable=!1),r}function At(i,e){return Object.assign(Object.create(i),e)}function Si(i,e=[""],t,s,n=(()=>i[0])){const o=t||i;typeof s>"u"&&(s=Ts("_fallback",i));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:r=>Si([r,...i],e,o,s)};return new Proxy(a,{deleteProperty:(r,l)=>(delete r[l],delete r._keys,delete i[0][l],!0),get:(r,l)=>Ds(r,l,()=>function Sa(i,e,t,s){let n;for(const o of e)if(n=Ts(ba(o,i),t),typeof n<"u")return wi(i,n)?Ci(t,s,i,n):n}(l,e,i,r)),getOwnPropertyDescriptor:(r,l)=>Reflect.getOwnPropertyDescriptor(r._scopes[0],l),getPrototypeOf:()=>Reflect.getPrototypeOf(i[0]),has:(r,l)=>Ls(r).includes(l),ownKeys:r=>Ls(r),set(r,l,c){const h=r._storage||(r._storage=n());return r[l]=h[l]=c,delete r._keys,!0}})}function Zt(i,e,t,s){const n={_cacheable:!1,_proxy:i,_context:e,_subProxy:t,_stack:new Set,_descriptors:Ps(i,s),setContext:o=>Zt(i,o,t,s),override:o=>Zt(i.override(o),e,t,s)};return new Proxy(n,{deleteProperty:(o,a)=>(delete o[a],delete i[a],!0),get:(o,a,r)=>Ds(o,a,()=>function _a(i,e,t){const{_proxy:s,_context:n,_subProxy:o,_descriptors:a}=i;let r=s[e];return Dt(r)&&a.isScriptable(e)&&(r=function xa(i,e,t,s){const{_proxy:n,_context:o,_subProxy:a,_stack:r}=t;if(r.has(i))throw new Error("Recursion detected: "+Array.from(r).join("->")+"->"+i);r.add(i);let l=e(o,a||s);return r.delete(i),wi(i,l)&&(l=Ci(n._scopes,n,i,l)),l}(e,r,i,t)),B(r)&&r.length&&(r=function ya(i,e,t,s){const{_proxy:n,_context:o,_subProxy:a,_descriptors:r}=t;if(typeof o.index<"u"&&s(i))return e[o.index%e.length];if(T(e[0])){const l=e,c=n._scopes.filter(h=>h!==l);e=[];for(const h of l){const d=Ci(c,n,i,h);e.push(Zt(d,o,a&&a[i],r))}}return e}(e,r,i,a.isIndexable)),wi(e,r)&&(r=Zt(r,n,o&&o[e],a)),r}(o,a,r)),getOwnPropertyDescriptor:(o,a)=>o._descriptors.allKeys?Reflect.has(i,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,a),getPrototypeOf:()=>Reflect.getPrototypeOf(i),has:(o,a)=>Reflect.has(i,a),ownKeys:()=>Reflect.ownKeys(i),set:(o,a,r)=>(i[a]=r,delete o[a],!0)})}function Ps(i,e={scriptable:!0,indexable:!0}){const{_scriptable:t=e.scriptable,_indexable:s=e.indexable,_allKeys:n=e.allKeys}=i;return{allKeys:n,scriptable:t,indexable:s,isScriptable:Dt(t)?t:()=>t,isIndexable:Dt(s)?s:()=>s}}const ba=(i,e)=>i?i+fi(e):e,wi=(i,e)=>T(e)&&"adapters"!==i&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function Ds(i,e,t){if(Object.prototype.hasOwnProperty.call(i,e)||"constructor"===e)return i[e];const s=t();return i[e]=s,s}function Os(i,e,t){return Dt(i)?i(e,t):i}const va=(i,e)=>!0===i?e:"string"==typeof i?Pt(e,i):void 0;function Ma(i,e,t,s,n){for(const o of e){const a=va(t,o);if(a){i.add(a);const r=Os(a._fallback,t,n);if(typeof r<"u"&&r!==t&&r!==s)return r}else if(!1===a&&typeof s<"u"&&t!==s)return null}return!1}function Ci(i,e,t,s){const n=e._rootScopes,o=Os(e._fallback,t,s),a=[...i,...n],r=new Set;r.add(s);let l=As(r,a,t,o||t,s);return!(null===l||typeof o<"u"&&o!==t&&(l=As(r,a,o,l,s),null===l))&&Si(Array.from(r),[""],n,o,()=>function ka(i,e,t){const s=i._getTarget();e in s||(s[e]={});const n=s[e];return B(n)&&T(t)?t:n||{}}(e,t,s))}function As(i,e,t,s,n){for(;t;)t=Ma(i,e,t,s,n);return t}function Ts(i,e){for(const t of e){if(!t)continue;const s=t[i];if(typeof s<"u")return s}}function Ls(i){let e=i._keys;return e||(e=i._keys=function wa(i){const e=new Set;for(const t of i)for(const s of Object.keys(t).filter(n=>!n.startsWith("_")))e.add(s);return Array.from(e)}(i._scopes)),e}function Rs(i,e,t,s){const{iScale:n}=i,{key:o="r"}=this._parsing,a=new Array(s);let r,l,c,h;for(r=0,l=s;r<l;++r)c=r+t,h=e[c],a[r]={r:n.parse(Pt(h,o),c)};return a}const Ca=Number.EPSILON||1e-14,Qt=(i,e)=>e<i.length&&!i[e].skip&&i[e],Es=i=>"x"===i?"y":"x";function Pa(i,e,t,s){const n=i.skip?e:i,o=e,a=t.skip?e:t,r=pi(o,n),l=pi(a,o);let c=r/(r+l),h=l/(r+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,u=s*h;return{previous:{x:o.x-d*(a.x-n.x),y:o.y-d*(a.y-n.y)},next:{x:o.x+u*(a.x-n.x),y:o.y+u*(a.y-n.y)}}}function Be(i,e,t){return Math.max(Math.min(i,t),e)}function La(i,e,t,s,n){let o,a,r,l;if(e.spanGaps&&(i=i.filter(c=>!c.skip)),"monotone"===e.cubicInterpolationMode)!function Aa(i,e="x"){const t=Es(e),s=i.length,n=Array(s).fill(0),o=Array(s);let a,r,l,c=Qt(i,0);for(a=0;a<s;++a)if(r=l,l=c,c=Qt(i,a+1),l){if(c){const h=c[e]-l[e];n[a]=0!==h?(c[t]-l[t])/h:0}o[a]=r?c?pt(n[a-1])!==pt(n[a])?0:(n[a-1]+n[a])/2:n[a-1]:n[a]}(function Da(i,e,t){const s=i.length;let n,o,a,r,l,c=Qt(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Qt(i,h+1),l&&c){if(le(e[h],0,Ca)){t[h]=t[h+1]=0;continue}n=t[h]/e[h],o=t[h+1]/e[h],r=Math.pow(n,2)+Math.pow(o,2),!(r<=9)&&(a=3/Math.sqrt(r),t[h]=n*a*e[h],t[h+1]=o*a*e[h])}})(i,n,o),function Oa(i,e,t="x"){const s=Es(t),n=i.length;let o,a,r,l=Qt(i,0);for(let c=0;c<n;++c){if(a=r,r=l,l=Qt(i,c+1),!r)continue;const h=r[t],d=r[s];a&&(o=(h-a[t])/3,r[`cp1${t}`]=h-o,r[`cp1${s}`]=d-o*e[c]),l&&(o=(l[t]-h)/3,r[`cp2${t}`]=h+o,r[`cp2${s}`]=d+o*e[c])}}(i,o,e)}(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,a=i.length;o<a;++o)r=i[o],l=Pa(c,r,i[Math.min(o+1,a-(s?0:1))%a],e.tension),r.cp1x=l.previous.x,r.cp1y=l.previous.y,r.cp2x=l.next.x,r.cp2y=l.next.y,c=r}e.capBezierPoints&&function Ta(i,e){let t,s,n,o,a,r=vt(i[0],e);for(t=0,s=i.length;t<s;++t)a=o,o=r,r=t<s-1&&vt(i[t+1],e),o&&(n=i[t],a&&(n.cp1x=Be(n.cp1x,e.left,e.right),n.cp1y=Be(n.cp1y,e.top,e.bottom)),r&&(n.cp2x=Be(n.cp2x,e.left,e.right),n.cp2y=Be(n.cp2y,e.top,e.bottom)))}(i,t)}function Pi(){return typeof window<"u"&&typeof document<"u"}function Di(i){let e=i.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function Ve(i,e,t){let s;return"string"==typeof i?(s=parseInt(i,10),-1!==i.indexOf("%")&&(s=s/100*e.parentNode[t])):s=i,s}const We=i=>i.ownerDocument.defaultView.getComputedStyle(i,null),Ea=["top","right","bottom","left"];function Vt(i,e,t){const s={};t=t?"-"+t:"";for(let n=0;n<4;n++){const o=Ea[n];s[o]=parseFloat(i[e+"-"+o+t])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Fa=(i,e,t)=>(i>0||e>0)&&(!t||!t.shadowRoot);function Wt(i,e){if("native"in i)return i;const{canvas:t,currentDevicePixelRatio:s}=e,n=We(t),o="border-box"===n.boxSizing,a=Vt(n,"padding"),r=Vt(n,"border","width"),{x:l,y:c,box:h}=function Ia(i,e){const t=i.touches,s=t&&t.length?t[0]:i,{offsetX:n,offsetY:o}=s;let r,l,a=!1;if(Fa(n,o,i.target))r=n,l=o;else{const c=e.getBoundingClientRect();r=s.clientX-c.left,l=s.clientY-c.top,a=!0}return{x:r,y:l,box:a}}(i,t),d=a.left+(h&&r.left),u=a.top+(h&&r.top);let{width:f,height:g}=e;return o&&(f-=a.width+r.width,g-=a.height+r.height),{x:Math.round((l-d)/f*t.width/s),y:Math.round((c-u)/g*t.height/s)}}const Ne=i=>Math.round(10*i)/10;function Fs(i,e,t){const s=e||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const a=i.canvas;return a.style&&(t||!a.style.height&&!a.style.width)&&(a.style.height=`${i.height}px`,a.style.width=`${i.width}px`),(i.currentDevicePixelRatio!==s||a.height!==n||a.width!==o)&&(i.currentDevicePixelRatio=s,a.height=n,a.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0)}const Va=function(){let i=!1;try{const e={get passive(){return i=!0,!1}};Pi()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return i}();function Is(i,e){const t=function Ra(i,e){return We(i).getPropertyValue(e)}(i,e),s=t&&t.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Nt(i,e,t,s){return{x:i.x+t*(e.x-i.x),y:i.y+t*(e.y-i.y)}}function Wa(i,e,t,s){return{x:i.x+t*(e.x-i.x),y:"middle"===s?t<.5?i.y:e.y:"after"===s?t<1?i.y:e.y:t>0?e.y:i.y}}function Na(i,e,t,s){const n={x:i.cp2x,y:i.cp2y},o={x:e.cp1x,y:e.cp1y},a=Nt(i,n,t),r=Nt(n,o,t),l=Nt(o,e,t),c=Nt(a,r,t),h=Nt(r,l,t);return Nt(c,h,t)}function Jt(i,e,t){return i?function(i,e){return{x:t=>i+i+e-t,setWidth(t){e=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,s)=>t-s,leftForLtr:(t,s)=>t-s}}(e,t):{x:i=>i,setWidth(i){},textAlign:i=>i,xPlus:(i,e)=>i+e,leftForLtr:(i,e)=>i}}function zs(i,e){let t,s;("ltr"===e||"rtl"===e)&&(t=i.canvas.style,s=[t.getPropertyValue("direction"),t.getPropertyPriority("direction")],t.setProperty("direction",e,"important"),i.prevTextDirection=s)}function Bs(i,e){void 0!==e&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",e[0],e[1]))}function Vs(i){return"angle"===i?{between:ce,compare:$o,normalize:ot}:{between:xt,compare:(e,t)=>e-t,normalize:e=>e}}function Ws({start:i,end:e,count:t,loop:s,style:n}){return{start:i%t,end:e%t,loop:s&&(e-i+1)%t==0,style:n}}function Ns(i,e,t){if(!t)return[i];const{property:s,start:n,end:o}=t,a=e.length,{compare:r,between:l,normalize:c}=Vs(s),{start:h,end:d,loop:u,style:f}=function $a(i,e,t){const{property:s,start:n,end:o}=t,{between:a,normalize:r}=Vs(s),l=e.length;let u,f,{start:c,end:h,loop:d}=i;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&a(r(e[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}(i,e,t),g=[];let b,_,y,p=!1,m=null;for(let k=h,C=h;k<=d;++k)_=e[k%a],!_.skip&&(b=c(_[s]),b!==y&&(p=l(b,n,o),null===m&&(p||l(n,y,b)&&0!==r(n,y))&&(m=0===r(b,n)?k:C),null!==m&&(!p||0===r(o,b)||l(o,y,b))&&(g.push(Ws({start:m,end:k,loop:u,count:a,style:f})),m=null),C=k,y=b));return null!==m&&g.push(Ws({start:m,end:d,loop:u,count:a,style:f})),g}function Hs(i,e){const t=[],s=i.segments;for(let n=0;n<s.length;n++){const o=Ns(s[n],i.points,e);o.length&&t.push(...o)}return t}function $s(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function Ka(i,e){if(!e)return!1;const t=[],s=function(n,o){return _i(o)?(t.includes(o)||t.push(o),t.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(e,s)}function He(i,e,t){return i.options.clip?i[t]:e[t]}function Ys(i,e){const t=e._clip;if(t.disabled)return!1;const s=function qa(i,e){const{xScale:t,yScale:s}=i;return t&&s?{left:He(t,e,"left"),right:He(t,e,"right"),top:He(s,e,"top"),bottom:He(s,e,"bottom")}:e}(e,i.chartArea);return{left:!1===t.left?0:s.left-(!0===t.left?0:t.left),right:!1===t.right?i.width:s.right+(!0===t.right?0:t.right),top:!1===t.top?0:s.top-(!0===t.top?0:t.top),bottom:!1===t.bottom?i.height:s.bottom+(!0===t.bottom?0:t.bottom)}}class Za{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,t,s,n){const a=t.duration;t.listeners[n].forEach(r=>r({chart:e,initial:t.initial,numSteps:a,currentStep:Math.min(s-t.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=ps.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let t=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let l,a=o.length-1,r=!1;for(;a>=0;--a)l=o[a],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(e),r=!0):(o[a]=o[o.length-1],o.pop());r&&(n.draw(),this._notify(n,s,e,"progress")),o.length||(s.running=!1,this._notify(n,s,e,"complete"),s.initial=!1),t+=o.length}),this._lastDate=e,0===t&&(this._running=!1)}_getAnims(e){const t=this._charts;let s=t.get(e);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(e,s)),s}listen(e,t,s){this._getAnims(e).listeners[t].push(s)}add(e,t){!t||!t.length||this._getAnims(e).items.push(...t)}has(e){return this._getAnims(e).items.length>0}start(e){const t=this._charts.get(e);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(e){if(!this._running)return!1;const t=this._charts.get(e);return!(!t||!t.running||!t.items.length)}stop(e){const t=this._charts.get(e);if(!t||!t.items.length)return;const s=t.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();t.items=[],this._notify(e,t,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Mt=new Za;const Us="transparent",Qa={boolean:(i,e,t)=>t>.5?e:i,color(i,e,t){const s=vs(i||Us),n=s.valid&&vs(e||Us);return n&&n.valid?n.mix(s,t).hexString():e},number:(i,e,t)=>i+(e-i)*t};class Ja{constructor(e,t,s,n){const o=t[s];n=ge([e.to,n,o,e.from]);const a=ge([e.from,o,n]);this._active=!0,this._fn=e.fn||Qa[e.type||typeof a],this._easing=he[e.easing]||he.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=t,this._prop=s,this._from=a,this._to=n,this._promises=void 0}active(){return this._active}update(e,t,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,a=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(a,e.duration)),this._total+=o,this._loop=!!e.loop,this._to=ge([e.to,t,n,e.from]),this._from=ge([e.from,n,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const t=e-this._start,s=this._duration,n=this._prop,o=this._from,a=this._loop,r=this._to;let l;if(this._active=o!==r&&(a||t<s),!this._active)return this._target[n]=r,void this._notify(!0);t<0?this._target[n]=o:(l=t/s%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,r,l))}wait(){const e=this._promises||(this._promises=[]);return new Promise((t,s)=>{e.push({res:t,rej:s})})}_notify(e){const t=e?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][t]()}}class Xs{constructor(e,t){this._chart=e,this._properties=new Map,this.configure(t)}configure(e){if(!T(e))return;const t=Object.keys(N.animation),s=this._properties;Object.getOwnPropertyNames(e).forEach(n=>{const o=e[n];if(!T(o))return;const a={};for(const r of t)a[r]=o[r];(B(o.properties)&&o.properties||[n]).forEach(r=>{(r===n||!s.has(r))&&s.set(r,a)})})}_animateOptions(e,t){const s=t.options,n=function er(i,e){if(!e)return;let t=i.options;if(t)return t.$shared&&(i.options=t=Object.assign({},t,{$shared:!1,$animations:{}})),t;i.options=e}(e,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&function tr(i,e){const t=[],s=Object.keys(e);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&t.push(o.wait())}return Promise.all(t)}(e.options.$animations,s).then(()=>{e.options=s},()=>{}),o}_createAnimations(e,t){const s=this._properties,n=[],o=e.$animations||(e.$animations={}),a=Object.keys(t),r=Date.now();let l;for(l=a.length-1;l>=0;--l){const c=a[l];if("$"===c.charAt(0))continue;if("options"===c){n.push(...this._animateOptions(e,t));continue}const h=t[c];let d=o[c];const u=s.get(c);if(d){if(u&&d.active()){d.update(u,h,r);continue}d.cancel()}u&&u.duration?(o[c]=d=new Ja(u,e,c,h),n.push(d)):e[c]=h}return n}update(e,t){if(0===this._properties.size)return void Object.assign(e,t);const s=this._createAnimations(e,t);return s.length?(Mt.add(this._chart,s),!0):void 0}}function Gs(i,e){const t=i&&i.options||{},s=t.reverse,n=void 0===t.min?e:0,o=void 0===t.max?e:0;return{start:s?o:n,end:s?n:o}}function Ks(i,e){const t=[],s=i._getSortedDatasetMetas(e);let n,o;for(n=0,o=s.length;n<o;++n)t.push(s[n].index);return t}function qs(i,e,t,s={}){const n=i.keys,o="single"===s.mode;let a,r,l,c;if(null===e)return;let h=!1;for(a=0,r=n.length;a<r;++a){if(l=+n[a],l===t){if(h=!0,s.all)continue;break}c=i.values[l],j(c)&&(o||0===e||pt(e)===pt(c))&&(e+=c)}return h||s.all?e:0}function Oi(i,e){const t=i&&i.options.stacked;return t||void 0===t&&void 0!==e.stack}function rr(i,e,t){const s=i[e]||(i[e]={});return s[t]||(s[t]={})}function Zs(i,e,t,s){for(const n of e.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(t&&o>0||!t&&o<0)return n.index}return null}function Qs(i,e){const{chart:t,_cachedMeta:s}=i,n=t._stacks||(t._stacks={}),{iScale:o,vScale:a,index:r}=s,l=o.axis,c=a.axis,h=function or(i,e,t){return`${i.id}.${e.id}.${t.stack||t.type}`}(o,a,s),d=e.length;let u;for(let f=0;f<d;++f){const g=e[f],{[l]:p,[c]:m}=g;u=(g._stacks||(g._stacks={}))[c]=rr(n,h,p),u[r]=m,u._top=Zs(u,a,!0,s.type),u._bottom=Zs(u,a,!1,s.type),(u._visualValues||(u._visualValues={}))[r]=m}}function Ai(i,e){const t=i.scales;return Object.keys(t).filter(s=>t[s].axis===e).shift()}function pe(i,e){const t=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){e=e||i._parsed;for(const n of e){const o=n._stacks;if(!o||void 0===o[s]||void 0===o[s][t])return;delete o[s][t],void 0!==o[s]._visualValues&&void 0!==o[s]._visualValues[t]&&delete o[s]._visualValues[t]}}}const Ti=i=>"reset"===i||"none"===i,Js=(i,e)=>e?i:Object.assign({},i);let Tt=(()=>class i{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,s){this.chart=t,this._ctx=t.ctx,this.index=s,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Oi(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&pe(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,s=this._cachedMeta,n=this.getDataset(),o=(u,f,g,p)=>"x"===u?f:"r"===u?p:g,a=s.xAxisID=P(n.xAxisID,Ai(t,"x")),r=s.yAxisID=P(n.yAxisID,Ai(t,"y")),l=s.rAxisID=P(n.rAxisID,Ai(t,"r")),c=s.indexAxis,h=s.iAxisID=o(c,a,r,l),d=s.vAxisID=o(c,r,a,l);s.xScale=this.getScaleForId(a),s.yScale=this.getScaleForId(r),s.rScale=this.getScaleForId(l),s.iScale=this.getScaleForId(h),s.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const s=this._cachedMeta;return t===s.iScale?s.vScale:s.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&fs(this._data,this),t._stacked&&pe(t)}_dataCheck(){const t=this.getDataset(),s=t.data||(t.data=[]),n=this._data;if(T(s))this._data=function nr(i,e){const{iScale:t,vScale:s}=e,n="x"===t.axis?"x":"y",o="x"===s.axis?"x":"y",a=Object.keys(i),r=new Array(a.length);let l,c,h;for(l=0,c=a.length;l<c;++l)h=a[l],r[l]={[n]:h,[o]:i[h]};return r}(s,this._cachedMeta);else if(n!==s){if(n){fs(n,this);const o=this._cachedMeta;pe(o),o._parsed=[]}s&&Object.isExtensible(s)&&function Go(i,e){i._chartjs?i._chartjs.listeners.push(e):(Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),us.forEach(t=>{const s="_onData"+fi(t),n=i[t];Object.defineProperty(i,t,{configurable:!0,enumerable:!1,value(...o){const a=n.apply(this,o);return i._chartjs.listeners.forEach(r=>{"function"==typeof r[s]&&r[s](...o)}),a}})}))}(s,this),this._syncList=[],this._data=s}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const s=this._cachedMeta,n=this.getDataset();let o=!1;this._dataCheck();const a=s._stacked;s._stacked=Oi(s.vScale,s),s.stack!==n.stack&&(o=!0,pe(s),s.stack=n.stack),this._resyncElements(t),(o||a!==s._stacked)&&(Qs(this,s._parsed),s._stacked=Oi(s.vScale,s))}configure(){const t=this.chart.config,s=t.datasetScopeKeys(this._type),n=t.getOptionScopes(this.getDataset(),s,!0);this.options=t.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,s){const{_cachedMeta:n,_data:o}=this,{iScale:a,_stacked:r}=n,l=a.axis;let d,u,f,c=0===t&&s===o.length||n._sorted,h=t>0&&n._parsed[t-1];if(!1===this._parsing)n._parsed=o,n._sorted=!0,f=o;else{f=B(o[t])?this.parseArrayData(n,o,t,s):T(o[t])?this.parseObjectData(n,o,t,s):this.parsePrimitiveData(n,o,t,s);const g=()=>null===u[l]||h&&u[l]<h[l];for(d=0;d<s;++d)n._parsed[d+t]=u=f[d],c&&(g()&&(c=!1),h=u);n._sorted=c}r&&Qs(this,f)}parsePrimitiveData(t,s,n,o){const{iScale:a,vScale:r}=t,l=a.axis,c=r.axis,h=a.getLabels(),d=a===r,u=new Array(o);let f,g,p;for(f=0,g=o;f<g;++f)p=f+n,u[f]={[l]:d||a.parse(h[p],p),[c]:r.parse(s[p],p)};return u}parseArrayData(t,s,n,o){const{xScale:a,yScale:r}=t,l=new Array(o);let c,h,d,u;for(c=0,h=o;c<h;++c)d=c+n,u=s[d],l[c]={x:a.parse(u[0],d),y:r.parse(u[1],d)};return l}parseObjectData(t,s,n,o){const{xScale:a,yScale:r}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h=new Array(o);let d,u,f,g;for(d=0,u=o;d<u;++d)f=d+n,g=s[f],h[d]={x:a.parse(Pt(g,l),f),y:r.parse(Pt(g,c),f)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,s,n){const a=this._cachedMeta,r=s[t.axis];return qs({keys:Ks(this.chart,!0),values:s._stacks[t.axis]._visualValues},r,a.index,{mode:n})}updateRangeFromParsed(t,s,n,o){const a=n[s.axis];let r=null===a?NaN:a;const l=o&&n._stacks[s.axis];o&&l&&(o.values=l,r=qs(o,a,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,s){const n=this._cachedMeta,o=n._parsed,a=n._sorted&&t===n.iScale,r=o.length,l=this._getOtherScale(t),c=((i,e,t)=>i&&!e.hidden&&e._stacked&&{keys:Ks(this.chart,!0),values:null})(s,n),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:u}=function ar(i){const{min:e,max:t,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:n?t:Number.POSITIVE_INFINITY}}(l);let f,g;function p(){g=o[f];const m=g[l.axis];return!j(g[t.axis])||d>m||u<m}for(f=0;f<r&&(p()||(this.updateRangeFromParsed(h,t,g,c),!a));++f);if(a)for(f=r-1;f>=0;--f)if(!p()){this.updateRangeFromParsed(h,t,g,c);break}return h}getAllParsedValues(t){const s=this._cachedMeta._parsed,n=[];let o,a,r;for(o=0,a=s.length;o<a;++o)r=s[o][t.axis],j(r)&&n.push(r);return n}getMaxOverflow(){return!1}getLabelAndValue(t){const s=this._cachedMeta,n=s.iScale,o=s.vScale,a=this.getParsed(t);return{label:n?""+n.getLabelForValue(a[n.axis]):"",value:o?""+o.getLabelForValue(a[o.axis]):""}}_update(t){const s=this._cachedMeta;this.update(t||"default"),s._clip=function sr(i){let e,t,s,n;return T(i)?(e=i.top,t=i.right,s=i.bottom,n=i.left):e=t=s=n=i,{top:e,right:t,bottom:s,left:n,disabled:!1===i}}(P(this.options.clip,function ir(i,e,t){if(!1===t)return!1;const s=Gs(i,t),n=Gs(e,t);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}(s.xScale,s.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,n=this._cachedMeta,o=n.data||[],a=this.chart.chartArea,r=[],l=this._drawStart||0,c=this._drawCount||o.length-l,h=this.options.drawActiveElementsOnTop;let d;for(n.dataset&&n.dataset.draw(t,a,l,c),d=l;d<l+c;++d){const u=o[d];u.hidden||(u.active&&h?r.push(u):u.draw(t,a))}for(d=0;d<r.length;++d)r[d].draw(t,a)}getStyle(t,s){const n=s?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(t||0,n)}getContext(t,s,n){const o=this.getDataset();let a;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];a=r.$context||(r.$context=function cr(i,e,t){return At(i,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:t,index:e,mode:"default",type:"data"})}(this.getContext(),t,r)),a.parsed=this.getParsed(t),a.raw=o.data[t],a.index=a.dataIndex=t}else a=this.$context||(this.$context=function lr(i,e){return At(i,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),a.dataset=o,a.index=a.datasetIndex=this.index;return a.active=!!s,a.mode=n,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,s){return this._resolveElementOptions(this.dataElementType.id,s,t)}_resolveElementOptions(t,s="default",n){const o="active"===s,a=this._cachedDataOpts,r=t+"-"+s,l=a[r],c=this.enableOptionSharing&&re(n);if(l)return Js(l,c);const h=this.chart.config,d=h.datasetElementScopeKeys(this._type,t),u=o?[`${t}Hover`,"hover",t,""]:[t,""],f=h.getOptionScopes(this.getDataset(),d),g=Object.keys(N.elements[t]),m=h.resolveNamedOptions(f,g,()=>this.getContext(n,o,s),u);return m.$shared&&(m.$shared=c,a[r]=Object.freeze(Js(m,c))),m}_resolveAnimations(t,s,n){const o=this.chart,a=this._cachedDataOpts,r=`animation-${s}`,l=a[r];if(l)return l;let c;if(!1!==o.options.animation){const d=this.chart.config,u=d.datasetAnimationScopeKeys(this._type,s),f=d.getOptionScopes(this.getDataset(),u);c=d.createResolver(f,this.getContext(t,n,s))}const h=new Xs(o,c&&c.animations);return c&&c._cacheable&&(a[r]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,s){return!s||Ti(t)||this.chart._animationsDisabled}_getSharedOptions(t,s){const n=this.resolveDataElementOptions(t,s),o=this._sharedOptions,a=this.getSharedOptions(n),r=this.includeOptions(s,a)||a!==o;return this.updateSharedOptions(a,s,n),{sharedOptions:a,includeOptions:r}}updateElement(t,s,n,o){Ti(o)?Object.assign(t,n):this._resolveAnimations(s,o).update(t,n)}updateSharedOptions(t,s,n){t&&!Ti(s)&&this._resolveAnimations(void 0,s).update(t,n)}_setStyle(t,s,n,o){t.active=o;const a=this.getStyle(s,o);this._resolveAnimations(s,n,o).update(t,{options:!o&&this.getSharedOptions(a)||a})}removeHoverStyle(t,s,n){this._setStyle(t,n,"active",!1)}setHoverStyle(t,s,n){this._setStyle(t,n,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const s=this._data,n=this._cachedMeta.data;for(const[l,c,h]of this._syncList)this[l](c,h);this._syncList=[];const o=n.length,a=s.length,r=Math.min(a,o);r&&this.parse(0,r),a>o?this._insertElements(o,a-o,t):a<o&&this._removeElements(a,o-a)}_insertElements(t,s,n=!0){const o=this._cachedMeta,a=o.data,r=t+s;let l;const c=h=>{for(h.length+=s,l=h.length-1;l>=r;l--)h[l]=h[l-s]};for(c(a),l=t;l<r;++l)a[l]=new this.dataElementType;this._parsing&&c(o._parsed),this.parse(t,s),n&&this.updateElements(a,t,s,"reset")}updateElements(t,s,n,o){}_removeElements(t,s){const n=this._cachedMeta;if(this._parsing){const o=n._parsed.splice(t,s);n._stacked&&pe(n,o)}n.data.splice(t,s)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[s,n,o]=t;this[s](n,o)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,s){s&&this._sync(["_removeElements",t,s]);const n=arguments.length-2;n&&this._sync(["_insertElements",t,n])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}})();function ur(i){const e=i.iScale,t=function dr(i,e){if(!i._cache.$bar){const t=i.getMatchingVisibleMetas(e);let s=[];for(let n=0,o=t.length;n<o;n++)s=s.concat(t[n].controller.getAllParsedValues(i));i._cache.$bar=gs(s.sort((n,o)=>n-o))}return i._cache.$bar}(e,i.type);let n,o,a,r,s=e._length;const l=()=>{32767===a||-32768===a||(re(r)&&(s=Math.min(s,Math.abs(a-r)||s)),r=a)};for(n=0,o=t.length;n<o;++n)a=e.getPixelForValue(t[n]),l();for(r=void 0,n=0,o=e.ticks.length;n<o;++n)a=e.getPixelForTick(n),l();return s}function tn(i,e,t,s){return B(i)?function pr(i,e,t,s){const n=t.parse(i[0],s),o=t.parse(i[1],s),a=Math.min(n,o),r=Math.max(n,o);let l=a,c=r;Math.abs(a)>Math.abs(r)&&(l=r,c=a),e[t.axis]=c,e._custom={barStart:l,barEnd:c,start:n,end:o,min:a,max:r}}(i,e,t,s):e[t.axis]=t.parse(i,s),e}function en(i,e,t,s){const n=i.iScale,o=i.vScale,a=n.getLabels(),r=n===o,l=[];let c,h,d,u;for(c=t,h=t+s;c<h;++c)u=e[c],d={},d[n.axis]=r||n.parse(a[c],c),l.push(tn(u,d,o,c));return l}function Li(i){return i&&void 0!==i.barStart&&void 0!==i.barEnd}function _r(i,e,t,s){let n=e.borderSkipped;const o={};if(!n)return void(i.borderSkipped=o);if(!0===n)return void(i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0});const{start:a,end:r,reverse:l,top:c,bottom:h}=function br(i){let e,t,s,n,o;return i.horizontal?(e=i.base>i.x,t="left",s="right"):(e=i.base<i.y,t="bottom",s="top"),e?(n="end",o="start"):(n="start",o="end"),{start:t,end:s,reverse:e,top:n,bottom:o}}(i);"middle"===n&&t&&(i.enableBorderRadius=!0,(t._top||0)===s?n=c:(t._bottom||0)===s?n=h:(o[sn(h,a,r,l)]=!0,n=c)),o[sn(n,a,r,l)]=!0,i.borderSkipped=o}function sn(i,e,t,s){return s?(i=function xr(i,e,t){return i===e?t:i===t?e:i}(i,e,t),i=nn(i,t,e)):i=nn(i,e,t),i}function nn(i,e,t){return"start"===i?e:"end"===i?t:i}function yr(i,{inflateAmount:e},t){i.inflateAmount="auto"===e?1===t?.33:0:e}let vr=(()=>class i extends Tt{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,s,n,o){return en(t,s,n,o)}parseArrayData(t,s,n,o){return en(t,s,n,o)}parseObjectData(t,s,n,o){const{iScale:a,vScale:r}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h="x"===a.axis?l:c,d="x"===r.axis?l:c,u=[];let f,g,p,m;for(f=n,g=n+o;f<g;++f)m=s[f],p={},p[a.axis]=a.parse(Pt(m,h),f),u.push(tn(Pt(m,d),p,r,f));return u}updateRangeFromParsed(t,s,n,o){super.updateRangeFromParsed(t,s,n,o);const a=n._custom;a&&s===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const s=this._cachedMeta,{iScale:n,vScale:o}=s,a=this.getParsed(t),r=a._custom,l=Li(r)?"["+r.start+", "+r.end+"]":""+o.getLabelForValue(a[o.axis]);return{label:""+n.getLabelForValue(a[n.axis]),value:l}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){const s=this._cachedMeta;this.updateElements(s.data,0,s.data.length,t)}updateElements(t,s,n,o){const a="reset"===o,{index:r,_cachedMeta:{vScale:l}}=this,c=l.getBasePixel(),h=l.isHorizontal(),d=this._getRuler(),{sharedOptions:u,includeOptions:f}=this._getSharedOptions(s,o);for(let g=s;g<s+n;g++){const p=this.getParsed(g),m=a||A(p[l.axis])?{base:c,head:c}:this._calculateBarValuePixels(g),b=this._calculateBarIndexPixels(g,d),_=(p._stacks||{})[l.axis],y={horizontal:h,base:m.base,enableBorderRadius:!_||Li(p._custom)||r===_._top||r===_._bottom,x:h?m.head:b.center,y:h?b.center:m.head,height:h?b.size:Math.abs(m.size),width:h?Math.abs(m.size):b.size};f&&(y.options=u||this.resolveDataElementOptions(g,t[g].active?"active":o));const M=y.options||t[g].options;_r(y,M,_,r),yr(y,M,d.ratio),this.updateElement(t[g],g,y,o)}}_getStacks(t,s){const{iScale:n}=this._cachedMeta,o=n.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),a=n.options.stacked,r=[],l=this._cachedMeta.controller.getParsed(s),c=l&&l[n.axis],h=d=>{const u=d._parsed.find(g=>g[n.axis]===c),f=u&&u[d.vScale.axis];if(A(f)||isNaN(f))return!0};for(const d of o)if((void 0===s||!h(d))&&((!1===a||-1===r.indexOf(d.stack)||void 0===a&&void 0===d.stack)&&r.push(d.stack),d.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,s,n){const o=this._getStacks(t,n),a=void 0!==s?o.indexOf(s):-1;return-1===a?o.length-1:a}_getRuler(){const t=this.options,s=this._cachedMeta,n=s.iScale,o=[];let a,r;for(a=0,r=s.data.length;a<r;++a)o.push(n.getPixelForValue(this.getParsed(a)[n.axis],a));const l=t.barThickness;return{min:l||ur(s),pixels:o,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:t.grouped,ratio:l?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:s,_stacked:n,index:o},options:{base:a,minBarLength:r}}=this,l=a||0,c=this.getParsed(t),h=c._custom,d=Li(h);let p,m,u=c[s.axis],f=0,g=n?this.applyStack(s,c,n):u;g!==u&&(f=g-u,g=u),d&&(u=h.barStart,g=h.barEnd-h.barStart,0!==u&&pt(u)!==pt(h.barEnd)&&(f=0),f+=u);const b=A(a)||d?f:a;let _=s.getPixelForValue(b);if(p=this.chart.getDataVisibility(t)?s.getPixelForValue(f+g):_,m=p-_,Math.abs(m)<r){m=function mr(i,e,t){return 0!==i?pt(i):(e.isHorizontal()?1:-1)*(e.min>=t?1:-1)}(m,s,l)*r,u===l&&(_-=m/2);const y=s.getPixelForDecimal(0),M=s.getPixelForDecimal(1),x=Math.min(y,M),v=Math.max(y,M);_=Math.max(Math.min(_,v),x),p=_+m,n&&!d&&(c._stacks[s.axis]._visualValues[o]=s.getValueForPixel(p)-s.getValueForPixel(_))}if(_===s.getPixelForValue(l)){const y=pt(m)*s.getLineWidthForValue(l)/2;_+=y,m-=y}return{size:m,base:_,head:p,center:p+m/2}}_calculateBarIndexPixels(t,s){const n=s.scale,o=this.options,a=o.skipNull,r=P(o.maxBarThickness,1/0);let l,c;if(s.grouped){const h=a?this._getStackCount(t):s.stackCount,d="flex"===o.barThickness?function gr(i,e,t,s){const n=e.pixels,o=n[i];let a=i>0?n[i-1]:null,r=i<n.length-1?n[i+1]:null;const l=t.categoryPercentage;null===a&&(a=o-(null===r?e.end-e.start:r-o)),null===r&&(r=o+o-a);const c=o-(o-Math.min(a,r))/2*l;return{chunk:Math.abs(r-a)/2*l/s,ratio:t.barPercentage,start:c}}(t,s,o,h):function fr(i,e,t,s){const n=t.barThickness;let o,a;return A(n)?(o=e.min*t.categoryPercentage,a=t.barPercentage):(o=n*s,a=1),{chunk:o/s,ratio:a,start:e.pixels[i]-o/2}}(t,s,o,h),u=this._getStackIndex(this.index,this._cachedMeta.stack,a?t:void 0);l=d.start+d.chunk*u+d.chunk/2,c=Math.min(r,d.chunk*d.ratio)}else l=n.getPixelForValue(this.getParsed(t)[n.axis],t),c=Math.min(r,s.min*s.ratio);return{base:l-c/2,head:l+c/2,center:l,size:c}}draw(){const t=this._cachedMeta,s=t.vScale,n=t.data,o=n.length;let a=0;for(;a<o;++a)null!==this.getParsed(a)[s.axis]&&!n[a].hidden&&n[a].draw(this._ctx)}})(),Mr=(()=>class i extends Tt{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,s,n,o){const a=super.parsePrimitiveData(t,s,n,o);for(let r=0;r<a.length;r++)a[r]._custom=this.resolveDataElementOptions(r+n).radius;return a}parseArrayData(t,s,n,o){const a=super.parseArrayData(t,s,n,o);for(let r=0;r<a.length;r++)a[r]._custom=P(s[n+r][2],this.resolveDataElementOptions(r+n).radius);return a}parseObjectData(t,s,n,o){const a=super.parseObjectData(t,s,n,o);for(let r=0;r<a.length;r++){const l=s[n+r];a[r]._custom=P(l&&l.r&&+l.r,this.resolveDataElementOptions(r+n).radius)}return a}getMaxOverflow(){const t=this._cachedMeta.data;let s=0;for(let n=t.length-1;n>=0;--n)s=Math.max(s,t[n].size(this.resolveDataElementOptions(n))/2);return s>0&&s}getLabelAndValue(t){const s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:a}=s,r=this.getParsed(t),l=o.getLabelForValue(r.x),c=a.getLabelForValue(r.y),h=r._custom;return{label:n[t]||"",value:"("+l+", "+c+(h?", "+h:"")+")"}}update(t){const s=this._cachedMeta.data;this.updateElements(s,0,s.length,t)}updateElements(t,s,n,o){const a="reset"===o,{iScale:r,vScale:l}=this._cachedMeta,{sharedOptions:c,includeOptions:h}=this._getSharedOptions(s,o),d=r.axis,u=l.axis;for(let f=s;f<s+n;f++){const g=t[f],p=!a&&this.getParsed(f),m={},b=m[d]=a?r.getPixelForDecimal(.5):r.getPixelForValue(p[d]),_=m[u]=a?l.getBasePixel():l.getPixelForValue(p[u]);m.skip=isNaN(b)||isNaN(_),h&&(m.options=c||this.resolveDataElementOptions(f,g.active?"active":o),a&&(m.options.radius=0)),this.updateElement(g,f,m,o)}}resolveDataElementOptions(t,s){const n=this.getParsed(t);let o=super.resolveDataElementOptions(t,s);o.$shared&&(o=Object.assign({},o,{$shared:!1}));const a=o.radius;return"active"!==s&&(o.radius=0),o.radius+=P(n&&n._custom,a),o}})(),Ri=(()=>class i extends Tt{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const s=t.data;if(s.labels.length&&s.datasets.length){const{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((a,r)=>{const c=t.getDatasetMeta(0).controller.getStyle(r);return{text:a,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}}};constructor(t,s){super(t,s),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,s){const n=this.getDataset().data,o=this._cachedMeta;if(!1===this._parsing)o._parsed=n;else{let r,l,a=c=>+n[c];if(T(n[t])){const{key:c="value"}=this._parsing;a=h=>+Pt(n[h],c)}for(r=t,l=t+s;r<l;++r)o._parsed[r]=a(r)}}_getRotation(){return dt(this.options.rotation-90)}_getCircumference(){return dt(this.options.circumference)}_getRotationExtents(){let t=W,s=-W;for(let n=0;n<this.chart.data.datasets.length;++n)if(this.chart.isDatasetVisible(n)&&this.chart.getDatasetMeta(n).type===this._type){const o=this.chart.getDatasetMeta(n).controller,a=o._getRotation(),r=o._getCircumference();t=Math.min(t,a),s=Math.max(s,a+r)}return{rotation:t,circumference:s-t}}update(t){const s=this.chart,{chartArea:n}=s,o=this._cachedMeta,a=o.data,r=this.getMaxBorderWidth()+this.getMaxOffset(a)+this.options.spacing,l=Math.max((Math.min(n.width,n.height)-r)/2,0),c=Math.min(((i,e)=>"string"==typeof i&&i.endsWith("%")?parseFloat(i)/100:+i/e)(this.options.cutout,l),1),h=this._getRingWeight(this.index),{circumference:d,rotation:u}=this._getRotationExtents(),{ratioX:f,ratioY:g,offsetX:p,offsetY:m}=function kr(i,e,t){let s=1,n=1,o=0,a=0;if(e<W){const r=i,l=r+e,c=Math.cos(r),h=Math.sin(r),d=Math.cos(l),u=Math.sin(l),f=(y,M,x)=>ce(y,r,l,!0)?1:Math.max(M,M*t,x,x*t),g=(y,M,x)=>ce(y,r,l,!0)?-1:Math.min(M,M*t,x,x*t),p=f(0,c,d),m=f($,h,u),b=g(V,c,d),_=g(V+$,h,u);s=(p-b)/2,n=(m-_)/2,o=-(p+b)/2,a=-(m+_)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:a}}(u,d,c),y=Math.max(Math.min((n.width-r)/f,(n.height-r)/g)/2,0),M=ss(this.options.radius,y),v=(M-Math.max(M*c,0))/this._getVisibleDatasetWeightTotal();this.offsetX=p*M,this.offsetY=m*M,o.total=this.calculateTotal(),this.outerRadius=M-v*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-v*h,0),this.updateElements(a,0,a.length,t)}_circumference(t,s){const n=this.options,o=this._cachedMeta,a=this._getCircumference();return s&&n.animation.animateRotate||!this.chart.getDataVisibility(t)||null===o._parsed[t]||o.data[t].hidden?0:this.calculateCircumference(o._parsed[t]*a/W)}updateElements(t,s,n,o){const a="reset"===o,r=this.chart,l=r.chartArea,d=(l.left+l.right)/2,u=(l.top+l.bottom)/2,f=a&&r.options.animation.animateScale,g=f?0:this.innerRadius,p=f?0:this.outerRadius,{sharedOptions:m,includeOptions:b}=this._getSharedOptions(s,o);let y,_=this._getRotation();for(y=0;y<s;++y)_+=this._circumference(y,a);for(y=s;y<s+n;++y){const M=this._circumference(y,a),x=t[y],v={x:d+this.offsetX,y:u+this.offsetY,startAngle:_,endAngle:_+M,circumference:M,outerRadius:p,innerRadius:g};b&&(v.options=m||this.resolveDataElementOptions(y,x.active?"active":o)),_+=M,this.updateElement(x,y,v,o)}}calculateTotal(){const t=this._cachedMeta,s=t.data;let o,n=0;for(o=0;o<s.length;o++){const a=t._parsed[o];null!==a&&!isNaN(a)&&this.chart.getDataVisibility(o)&&!s[o].hidden&&(n+=Math.abs(a))}return n}calculateCircumference(t){const s=this._cachedMeta.total;return s>0&&!isNaN(t)?W*(Math.abs(t)/s):0}getLabelAndValue(t){const n=this.chart,o=n.data.labels||[],a=de(this._cachedMeta._parsed[t],n.options.locale);return{label:o[t]||"",value:a}}getMaxBorderWidth(t){let s=0;const n=this.chart;let o,a,r,l,c;if(!t)for(o=0,a=n.data.datasets.length;o<a;++o)if(n.isDatasetVisible(o)){r=n.getDatasetMeta(o),t=r.data,l=r.controller;break}if(!t)return 0;for(o=0,a=t.length;o<a;++o)c=l.resolveDataElementOptions(o),"inner"!==c.borderAlign&&(s=Math.max(s,c.borderWidth||0,c.hoverBorderWidth||0));return s}getMaxOffset(t){let s=0;for(let n=0,o=t.length;n<o;++n){const a=this.resolveDataElementOptions(n);s=Math.max(s,a.offset||0,a.hoverOffset||0)}return s}_getRingWeightOffset(t){let s=0;for(let n=0;n<t;++n)this.chart.isDatasetVisible(n)&&(s+=this._getRingWeight(n));return s}_getRingWeight(t){return Math.max(P(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}})(),Sr=(()=>class i extends Tt{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const s=this._cachedMeta,{dataset:n,data:o=[],_dataset:a}=s,r=this.chart._animationsDisabled;let{start:l,count:c}=bs(s,o,r);this._drawStart=l,this._drawCount=c,_s(s)&&(l=0,c=o.length),n._chart=this.chart,n._datasetIndex=this.index,n._decimated=!!a._decimated,n.points=o;const h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(n,void 0,{animated:!r,options:h},t),this.updateElements(o,l,c,t)}updateElements(t,s,n,o){const a="reset"===o,{iScale:r,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,{sharedOptions:d,includeOptions:u}=this._getSharedOptions(s,o),f=r.axis,g=l.axis,{spanGaps:p,segment:m}=this.options,b=qt(p)?p:Number.POSITIVE_INFINITY,_=this.chart._animationsDisabled||a||"none"===o,y=s+n,M=t.length;let x=s>0&&this.getParsed(s-1);for(let v=0;v<M;++v){const w=t[v],k=_?w:{};if(v<s||v>=y){k.skip=!0;continue}const C=this.getParsed(v),O=A(C[g]),D=k[f]=r.getPixelForValue(C[f],v),L=k[g]=a||O?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,C,c):C[g],v);k.skip=isNaN(D)||isNaN(L)||O,k.stop=v>0&&Math.abs(C[f]-x[f])>b,m&&(k.parsed=C,k.raw=h.data[v]),u&&(k.options=d||this.resolveDataElementOptions(v,w.active?"active":o)),_||this.updateElement(w,v,k,o),x=C}}getMaxOverflow(){const t=this._cachedMeta,s=t.dataset,n=s.options&&s.options.borderWidth||0,o=t.data||[];if(!o.length)return n;const a=o[0].size(this.resolveDataElementOptions(0)),r=o[o.length-1].size(this.resolveDataElementOptions(o.length-1));return Math.max(n,a,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}})(),on=(()=>class i extends Tt{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const s=t.data;if(s.labels.length&&s.datasets.length){const{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((a,r)=>{const c=t.getDatasetMeta(0).controller.getStyle(r);return{text:a,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,s){super(t,s),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const n=this.chart,o=n.data.labels||[],a=de(this._cachedMeta._parsed[t].r,n.options.locale);return{label:o[t]||"",value:a}}parseObjectData(t,s,n,o){return Rs.bind(this)(t,s,n,o)}update(t){const s=this._cachedMeta.data;this._updateRadius(),this.updateElements(s,0,s.length,t)}getMinMax(){const s={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return this._cachedMeta.data.forEach((n,o)=>{const a=this.getParsed(o).r;!isNaN(a)&&this.chart.getDataVisibility(o)&&(a<s.min&&(s.min=a),a>s.max&&(s.max=a))}),s}_updateRadius(){const t=this.chart,s=t.chartArea,n=t.options,o=Math.min(s.right-s.left,s.bottom-s.top),a=Math.max(o/2,0),l=(a-Math.max(n.cutoutPercentage?a/100*n.cutoutPercentage:1,0))/t.getVisibleDatasetCount();this.outerRadius=a-l*this.index,this.innerRadius=this.outerRadius-l}updateElements(t,s,n,o){const a="reset"===o,r=this.chart,c=r.options.animation,h=this._cachedMeta.rScale,d=h.xCenter,u=h.yCenter,f=h.getIndexAngle(0)-.5*V;let p,g=f;const m=360/this.countVisibleElements();for(p=0;p<s;++p)g+=this._computeAngle(p,o,m);for(p=s;p<s+n;p++){const b=t[p];let _=g,y=g+this._computeAngle(p,o,m),M=r.getDataVisibility(p)?h.getDistanceFromCenterForValue(this.getParsed(p).r):0;g=y,a&&(c.animateScale&&(M=0),c.animateRotate&&(_=y=f));const x={x:d,y:u,innerRadius:0,outerRadius:M,startAngle:_,endAngle:y,options:this.resolveDataElementOptions(p,b.active?"active":o)};this.updateElement(b,p,x,o)}}countVisibleElements(){let s=0;return this._cachedMeta.data.forEach((n,o)=>{!isNaN(this.getParsed(o).r)&&this.chart.getDataVisibility(o)&&s++}),s}_computeAngle(t,s,n){return this.chart.getDataVisibility(t)?dt(this.resolveDataElementOptions(t,s).angle||n):0}})();var wr=Object.freeze({__proto__:null,BarController:vr,BubbleController:Mr,DoughnutController:Ri,LineController:Sr,PieController:(()=>class i extends Ri{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}})(),PolarAreaController:on,RadarController:(()=>class i extends Tt{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const s=this._cachedMeta.vScale,n=this.getParsed(t);return{label:s.getLabels()[t],value:""+s.getLabelForValue(n[s.axis])}}parseObjectData(t,s,n,o){return Rs.bind(this)(t,s,n,o)}update(t){const s=this._cachedMeta,n=s.dataset,o=s.data||[],a=s.iScale.getLabels();if(n.points=o,"resize"!==t){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0),this.updateElement(n,void 0,{_loop:!0,_fullLoop:a.length===o.length,options:r},t)}this.updateElements(o,0,o.length,t)}updateElements(t,s,n,o){const a=this._cachedMeta.rScale,r="reset"===o;for(let l=s;l<s+n;l++){const c=t[l],h=this.resolveDataElementOptions(l,c.active?"active":o),d=a.getPointPositionForValue(l,this.getParsed(l).r),u=r?a.xCenter:d.x,f=r?a.yCenter:d.y,g={x:u,y:f,angle:d.angle,skip:isNaN(u)||isNaN(f),options:h};this.updateElement(c,l,g,o)}}})(),ScatterController:(()=>class i extends Tt{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:a}=s,r=this.getParsed(t),l=o.getLabelForValue(r.x),c=a.getLabelForValue(r.y);return{label:n[t]||"",value:"("+l+", "+c+")"}}update(t){const s=this._cachedMeta,{data:n=[]}=s,o=this.chart._animationsDisabled;let{start:a,count:r}=bs(s,n,o);if(this._drawStart=a,this._drawCount=r,_s(s)&&(a=0,r=n.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:l,_dataset:c}=s;l._chart=this.chart,l._datasetIndex=this.index,l._decimated=!!c._decimated,l.points=n;const h=this.resolveDatasetElementOptions(t);h.segment=this.options.segment,this.updateElement(l,void 0,{animated:!o,options:h},t)}else this.datasetElementType&&(delete s.dataset,this.datasetElementType=!1);this.updateElements(n,a,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,s,n,o){const a="reset"===o,{iScale:r,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,d=this.resolveDataElementOptions(s,o),u=this.getSharedOptions(d),f=this.includeOptions(o,u),g=r.axis,p=l.axis,{spanGaps:m,segment:b}=this.options,_=qt(m)?m:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||a||"none"===o;let M=s>0&&this.getParsed(s-1);for(let x=s;x<s+n;++x){const v=t[x],w=this.getParsed(x),k=y?v:{},C=A(w[p]),O=k[g]=r.getPixelForValue(w[g],x),D=k[p]=a||C?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,w,c):w[p],x);k.skip=isNaN(O)||isNaN(D)||C,k.stop=x>0&&Math.abs(w[g]-M[g])>_,b&&(k.parsed=w,k.raw=h.data[x]),f&&(k.options=u||this.resolveDataElementOptions(x,v.active?"active":o)),y||this.updateElement(v,x,k,o),M=w}this.updateSharedOptions(u,o,d)}getMaxOverflow(){const t=this._cachedMeta,s=t.data||[];if(!this.options.showLine){let l=0;for(let c=s.length-1;c>=0;--c)l=Math.max(l,s[c].size(this.resolveDataElementOptions(c))/2);return l>0&&l}const n=t.dataset,o=n.options&&n.options.borderWidth||0;if(!s.length)return o;const a=s[0].size(this.resolveDataElementOptions(0)),r=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(o,a,r)/2}})()});function Ht(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Ei{static override(e){Object.assign(Ei.prototype,e)}options;constructor(e){this.options=e||{}}init(){}formats(){return Ht()}parse(){return Ht()}format(){return Ht()}add(){return Ht()}diff(){return Ht()}startOf(){return Ht()}endOf(){return Ht()}}var Cr__date=Ei;function Pr(i,e,t,s){const{controller:n,data:o,_sorted:a}=i,r=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(r&&e===r.axis&&"r"!==e&&a&&o.length){const c=r._reversePixels?Uo:yt;if(!s){const h=c(o,e,t);if(l){const{vScale:d}=n._cachedMeta,{_parsed:u}=i,f=u.slice(0,h.lo+1).reverse().findIndex(p=>!A(p[d.axis]));h.lo-=Math.max(0,f);const g=u.slice(h.hi).findIndex(p=>!A(p[d.axis]));h.hi+=Math.max(0,g)}return h}if(n._sharedOptions){const h=o[0],d="function"==typeof h.getRange&&h.getRange(e);if(d){const u=c(o,e,t-d),f=c(o,e,t+d);return{lo:u.lo,hi:f.hi}}}}return{lo:0,hi:o.length-1}}function me(i,e,t,s,n){const o=i.getSortedVisibleDatasetMetas(),a=t[e];for(let r=0,l=o.length;r<l;++r){const{index:c,data:h}=o[r],{lo:d,hi:u}=Pr(o[r],e,a,n);for(let f=d;f<=u;++f){const g=h[f];g.skip||s(g,c,f)}}}function Fi(i,e,t,s,n){const o=[];return!n&&!i.isPointInArea(e)||me(i,t,e,function(r,l,c){!n&&!vt(r,i.chartArea,0)||r.inRange(e.x,e.y,s)&&o.push({element:r,datasetIndex:l,index:c})},!0),o}function Ii(i,e,t,s,n,o){return o||i.isPointInArea(e)?"r"!==t||s?function Ar(i,e,t,s,n,o){let a=[];const r=function Dr(i){const e=-1!==i.indexOf("x"),t=-1!==i.indexOf("y");return function(s,n){const o=e?Math.abs(s.x-n.x):0,a=t?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(a,2))}}(t);let l=Number.POSITIVE_INFINITY;return me(i,t,e,function c(h,d,u){const f=h.inRange(e.x,e.y,n);if(s&&!f)return;const g=h.getCenterPoint(n);if(!o&&!i.isPointInArea(g)&&!f)return;const m=r(e,g);m<l?(a=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&a.push({element:h,datasetIndex:d,index:u})}),a}(i,e,t,s,n,o):function Or(i,e,t,s){let n=[];return me(i,t,e,function o(a,r,l){const{startAngle:c,endAngle:h}=a.getProps(["startAngle","endAngle"],s),{angle:d}=ds(a,{x:e.x,y:e.y});ce(d,c,h)&&n.push({element:a,datasetIndex:r,index:l})}),n}(i,e,t,n):[]}function an(i,e,t,s,n){const o=[],a="x"===t?"inXRange":"inYRange";let r=!1;return me(i,t,e,(l,c,h)=>{l[a]&&l[a](e[t],n)&&(o.push({element:l,datasetIndex:c,index:h}),r=r||l.inRange(e.x,e.y,n))}),s&&!r?[]:o}var Tr={evaluateInteractionItems:me,modes:{index(i,e,t,s){const n=Wt(e,i),o=t.axis||"x",a=t.includeInvisible||!1,r=t.intersect?Fi(i,n,o,s,a):Ii(i,n,o,!1,s,a),l=[];return r.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=r[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,e,t,s){const n=Wt(e,i),o=t.axis||"xy",a=t.includeInvisible||!1;let r=t.intersect?Fi(i,n,o,s,a):Ii(i,n,o,!1,s,a);if(r.length>0){const l=r[0].datasetIndex,c=i.getDatasetMeta(l).data;r=[];for(let h=0;h<c.length;++h)r.push({element:c[h],datasetIndex:l,index:h})}return r},point:(i,e,t,s)=>Fi(i,Wt(e,i),t.axis||"xy",s,t.includeInvisible||!1),nearest:(i,e,t,s)=>Ii(i,Wt(e,i),t.axis||"xy",t.intersect,s,t.includeInvisible||!1),x:(i,e,t,s)=>an(i,Wt(e,i),"x",t.intersect,s),y:(i,e,t,s)=>an(i,Wt(e,i),"y",t.intersect,s)}};const rn=["left","top","right","bottom"];function be(i,e){return i.filter(t=>t.pos===e)}function ln(i,e){return i.filter(t=>-1===rn.indexOf(t.pos)&&t.box.axis===e)}function _e(i,e){return i.sort((t,s)=>{const n=e?s:t,o=e?t:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function cn(i,e,t,s){return Math.max(i[t],e[t])+Math.max(i[s],e[s])}function hn(i,e){i.top=Math.max(i.top,e.top),i.left=Math.max(i.left,e.left),i.bottom=Math.max(i.bottom,e.bottom),i.right=Math.max(i.right,e.right)}function Ir(i,e,t,s){const{pos:n,box:o}=t,a=i.maxPadding;if(!T(n)){t.size&&(i[n]-=t.size);const d=s[t.stack]||{size:0,count:1};d.size=Math.max(d.size,t.horizontal?o.height:o.width),t.size=d.size/d.count,i[n]+=t.size}o.getPadding&&hn(a,o.getPadding());const r=Math.max(0,e.outerWidth-cn(a,i,"left","right")),l=Math.max(0,e.outerHeight-cn(a,i,"top","bottom")),c=r!==i.w,h=l!==i.h;return i.w=r,i.h=l,t.horizontal?{same:c,other:h}:{same:h,other:c}}function Br(i,e){const t=e.maxPadding;return function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(a=>{o[a]=Math.max(e[a],t[a])}),o}(i?["left","right"]:["top","bottom"])}function xe(i,e,t,s){const n=[];let o,a,r,l,c,h;for(o=0,a=i.length,c=0;o<a;++o){r=i[o],l=r.box,l.update(r.width||e.w,r.height||e.h,Br(r.horizontal,e));const{same:d,other:u}=Ir(e,t,r,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(r)}return c&&xe(n,e,t,s)||h}function je(i,e,t,s,n){i.top=t,i.left=e,i.right=e+s,i.bottom=t+n,i.width=s,i.height=n}function dn(i,e,t,s){const n=t.padding;let{x:o,y:a}=e;for(const r of i){const l=r.box,c=s[r.stack]||{count:1,placed:0,weight:1},h=r.stackWeight/c.weight||1;if(r.horizontal){const d=e.w*h,u=c.size||l.height;re(c.start)&&(a=c.start),l.fullSize?je(l,n.left,a,t.outerWidth-n.right-n.left,u):je(l,e.left+c.placed,a,d,u),c.start=a,c.placed+=d,a=l.bottom}else{const d=e.h*h,u=c.size||l.width;re(c.start)&&(o=c.start),l.fullSize?je(l,o,n.top,u,t.outerHeight-n.bottom-n.top):je(l,o,e.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}e.x=o,e.y=a}var J={addBox(i,e){i.boxes||(i.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},i.boxes.push(e)},removeBox(i,e){const t=i.boxes?i.boxes.indexOf(e):-1;-1!==t&&i.boxes.splice(t,1)},configure(i,e,t){e.fullSize=t.fullSize,e.position=t.position,e.weight=t.weight},update(i,e,t,s){if(!i)return;const n=Q(i.options.layout.padding),o=Math.max(e-n.width,0),a=Math.max(t-n.height,0),r=function Fr(i){const e=function Lr(i){const e=[];let t,s,n,o,a,r;for(t=0,s=(i||[]).length;t<s;++t)n=i[t],({position:o,options:{stack:a,stackWeight:r=1}}=n),e.push({index:t,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r});return e}(i),t=_e(e.filter(c=>c.box.fullSize),!0),s=_e(be(e,"left"),!0),n=_e(be(e,"right")),o=_e(be(e,"top"),!0),a=_e(be(e,"bottom")),r=ln(e,"x"),l=ln(e,"y");return{fullSize:t,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(a).concat(r),chartArea:be(e,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(a).concat(r)}}(i.boxes),l=r.vertical,c=r.horizontal;E(i.boxes,p=>{"function"==typeof p.beforeLayout&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&!1===m.box.options.display?p:p+1,0)||1,d=Object.freeze({outerWidth:e,outerHeight:t,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2}),u=Object.assign({},n);hn(u,Q(s));const f=Object.assign({maxPadding:u,w:o,h:a,x:n.left,y:n.top},n),g=function Er(i,e){const t=function Rr(i){const e={};for(const t of i){const{stack:s,pos:n,stackWeight:o}=t;if(!s||!rn.includes(n))continue;const a=e[s]||(e[s]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=o}return e}(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=e;let o,a,r;for(o=0,a=i.length;o<a;++o){r=i[o];const{fullSize:l}=r.box,c=t[r.stack],h=c&&r.stackWeight/c.weight;r.horizontal?(r.width=h?h*s:l&&e.availableWidth,r.height=n):(r.width=s,r.height=h?h*n:l&&e.availableHeight)}return t}(l.concat(c),d);xe(r.fullSize,f,d,g),xe(l,f,d,g),xe(c,f,d,g)&&xe(l,f,d,g),function zr(i){const e=i.maxPadding;function t(s){const n=Math.max(e[s]-i[s],0);return i[s]+=n,n}i.y+=t("top"),i.x+=t("left"),t("right"),t("bottom")}(f),dn(r.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,dn(r.rightAndBottom,f,d,g),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},E(r.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class un{acquireContext(e,t){}releaseContext(e){return!1}addEventListener(e,t,s){}removeEventListener(e,t,s){}getDevicePixelRatio(){return 1}getMaximumSize(e,t,s,n){return t=Math.max(0,t||e.width),s=s||e.height,{width:t,height:Math.max(0,n?Math.floor(t/n):s)}}isAttached(e){return!0}updateConfig(e){}}class Vr extends un{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const $e="$chartjs",Wr={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},fn=i=>null===i||""===i,gn=!!Va&&{passive:!0};function jr(i,e,t){i&&i.canvas&&i.canvas.removeEventListener(e,t,gn)}function Ye(i,e){for(const t of i)if(t===e||t.contains(e))return!0}function Yr(i,e,t){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||Ye(r.addedNodes,s),a=a&&!Ye(r.removedNodes,s);a&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}function Ur(i,e,t){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||Ye(r.removedNodes,s),a=a&&!Ye(r.addedNodes,s);a&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}const ye=new Map;let pn=0;function mn(){const i=window.devicePixelRatio;i!==pn&&(pn=i,ye.forEach((e,t)=>{t.currentDevicePixelRatio!==i&&e()}))}function Kr(i,e,t){const s=i.canvas,n=s&&Di(s);if(!n)return;const o=ms((r,l)=>{const c=n.clientWidth;t(r,l),c<n.clientWidth&&t()},window),a=new ResizeObserver(r=>{const l=r[0],c=l.contentRect.width,h=l.contentRect.height;0===c&&0===h||o(c,h)});return a.observe(n),function Xr(i,e){ye.size||window.addEventListener("resize",mn),ye.set(i,e)}(i,o),a}function zi(i,e,t){t&&t.disconnect(),"resize"===e&&function Gr(i){ye.delete(i),ye.size||window.removeEventListener("resize",mn)}(i)}function qr(i,e,t){const s=i.canvas,n=ms(o=>{null!==i.ctx&&t(function $r(i,e){const t=Wr[i.type]||i.type,{x:s,y:n}=Wt(i,e);return{type:t,chart:e,native:i,x:void 0!==s?s:null,y:void 0!==n?n:null}}(o,i))},i);return function Hr(i,e,t){i&&i.addEventListener(e,t,gn)}(s,e,n),n}class Zr extends un{acquireContext(e,t){const s=e&&e.getContext&&e.getContext("2d");return s&&s.canvas===e?(function Nr(i,e){const t=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[$e]={initial:{height:s,width:n,style:{display:t.display,height:t.height,width:t.width}}},t.display=t.display||"block",t.boxSizing=t.boxSizing||"border-box",fn(n)){const o=Is(i,"width");void 0!==o&&(i.width=o)}if(fn(s))if(""===i.style.height)i.height=i.width/(e||2);else{const o=Is(i,"height");void 0!==o&&(i.height=o)}}(e,t),s):null}releaseContext(e){const t=e.canvas;if(!t[$e])return!1;const s=t[$e].initial;["height","width"].forEach(o=>{const a=s[o];A(a)?t.removeAttribute(o):t.setAttribute(o,a)});const n=s.style||{};return Object.keys(n).forEach(o=>{t.style[o]=n[o]}),t.width=t.width,delete t[$e],!0}addEventListener(e,t,s){this.removeEventListener(e,t),(e.$proxies||(e.$proxies={}))[t]=({attach:Yr,detach:Ur,resize:Kr}[t]||qr)(e,t,s)}removeEventListener(e,t){const s=e.$proxies||(e.$proxies={}),n=s[t];n&&(({attach:zi,detach:zi,resize:zi}[t]||jr)(e,t,n),s[t]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,t,s,n){return function Ba(i,e,t,s){const n=We(i),o=Vt(n,"margin"),a=Ve(n.maxWidth,i,"clientWidth")||Le,r=Ve(n.maxHeight,i,"clientHeight")||Le,l=function za(i,e,t){let s,n;if(void 0===e||void 0===t){const o=i&&Di(i);if(o){const a=o.getBoundingClientRect(),r=We(o),l=Vt(r,"border","width"),c=Vt(r,"padding");e=a.width-c.width-l.width,t=a.height-c.height-l.height,s=Ve(r.maxWidth,o,"clientWidth"),n=Ve(r.maxHeight,o,"clientHeight")}else e=i.clientWidth,t=i.clientHeight}return{width:e,height:t,maxWidth:s||Le,maxHeight:n||Le}}(i,e,t);let{width:c,height:h}=l;if("content-box"===n.boxSizing){const u=Vt(n,"border","width"),f=Vt(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=Ne(Math.min(c,a,l.maxWidth)),h=Ne(Math.min(h,r,l.maxHeight)),c&&!h&&(h=Ne(c/2)),(void 0!==e||void 0!==t)&&s&&l.height&&h>l.height&&(h=l.height,c=Ne(Math.floor(h*s))),{width:c,height:h}}(e,t,s,n)}isAttached(e){const t=e&&Di(e);return!(!t||!t.isConnected)}}class kt{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(e){const{x:t,y:s}=this.getProps(["x","y"],e);return{x:t,y:s}}hasValue(){return qt(this.x)&&qt(this.y)}getProps(e,t){const s=this.$animations;if(!t||!s)return this;const n={};return e.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}function Ue(i,e,t,s,n){const o=P(s,0),a=Math.min(P(n,i.length),i.length);let l,c,h,r=0;for(t=Math.ceil(t),n&&(l=n-s,t=l/Math.floor(l/t)),h=o;h<0;)r++,h=Math.round(o+r*t);for(c=Math.max(o,0);c<a;c++)c===h&&(e.push(i[c]),r++,h=Math.round(o+r*t))}const bn=(i,e,t)=>"top"===e||"left"===e?i[e]+t:i[e]-t,_n=(i,e)=>Math.min(e||i,i);function xn(i,e){const t=[],s=i.length/e,n=i.length;let o=0;for(;o<n;o+=s)t.push(i[Math.floor(o)]);return t}function al(i,e,t){const s=i.ticks.length,n=Math.min(e,s-1),o=i._startPixel,a=i._endPixel,r=1e-6;let c,l=i.getPixelForTick(n);if(!(t&&(c=1===s?Math.max(l-o,a-l):0===e?(i.getPixelForTick(1)-l)/2:(l-i.getPixelForTick(n-1))/2,l+=n<e?c:-c,l<o-r||l>a+r)))return l}function ve(i){return i.drawTicks?i.tickLength:0}function yn(i,e){if(!i.display)return 0;const t=X(i.font,e),s=Q(i.padding);return(B(i.text)?i.text.length:1)*t.lineHeight+s.height}function hl(i,e,t){let s=bi(i);return(t&&"right"!==e||!t&&"right"===e)&&(s=(i=>"left"===i?"right":"right"===i?"left":i)(s)),s}class jt extends kt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,t){return e}getUserBounds(){let{_userMin:e,_userMax:t,_suggestedMin:s,_suggestedMax:n}=this;return e=nt(e,Number.POSITIVE_INFINITY),t=nt(t,Number.NEGATIVE_INFINITY),s=nt(s,Number.POSITIVE_INFINITY),n=nt(n,Number.NEGATIVE_INFINITY),{min:nt(e,s),max:nt(t,n),minDefined:j(e),maxDefined:j(t)}}getMinMax(e){let a,{min:t,max:s,minDefined:n,maxDefined:o}=this.getUserBounds();if(n&&o)return{min:t,max:s};const r=this.getMatchingVisibleMetas();for(let l=0,c=r.length;l<c;++l)a=r[l].controller.getMinMax(this,e),n||(t=Math.min(t,a.min)),o||(s=Math.max(s,a.max));return t=o&&t>s?s:t,s=n&&t>s?t:s,{min:nt(t,nt(s,t)),max:nt(s,nt(t,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){I(this.options.beforeUpdate,[this])}update(e,t,s){const{beginAtZero:n,grace:o,ticks:a}=this.options,r=a.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=t,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function ma(i,e,t){const{min:s,max:n}=i,o=ss(e,(n-s)/2),a=(r,l)=>t&&0===r?0:r+l;return{min:a(s,-Math.abs(o)),max:a(n,o)}}(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=r<this.ticks.length;this._convertTicksToLabels(l?xn(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||"auto"===a.source)&&(this.ticks=function Jr(i,e){const t=i.options.ticks,s=function tl(i){const e=i.options.offset,t=i._tickSize();return Math.floor(Math.min(i._length/t+(e?0:1),i._maxLength/t))}(i),n=Math.min(t.maxTicksLimit||s,s),o=t.major.enabled?function il(i){const e=[];let t,s;for(t=0,s=i.length;t<s;t++)i[t].major&&e.push(t);return e}(e):[],a=o.length,r=o[0],l=o[a-1],c=[];if(a>n)return function sl(i,e,t,s){let a,n=0,o=t[0];for(s=Math.ceil(s),a=0;a<i.length;a++)a===o&&(e.push(i[a]),n++,o=t[n*s])}(e,c,o,a/n),c;const h=function el(i,e,t){const s=function nl(i){const e=i.length;let t,s;if(e<2)return!1;for(s=i[0],t=1;t<e;++t)if(i[t]-i[t-1]!==s)return!1;return s}(i),n=e.length/t;if(!s)return Math.max(n,1);const o=function No(i){const e=[],t=Math.sqrt(i);let s;for(s=1;s<t;s++)i%s==0&&(e.push(s),e.push(i/s));return t===(0|t)&&e.push(t),e.sort((n,o)=>n-o).pop(),e}(s);for(let a=0,r=o.length-1;a<r;a++){const l=o[a];if(l>n)return l}return Math.max(n,1)}(o,e,n);if(a>0){let d,u;const f=a>1?Math.round((l-r)/(a-1)):null;for(Ue(e,c,h,A(f)?0:r-f,r),d=0,u=a-1;d<u;d++)Ue(e,c,h,o[d],o[d+1]);return Ue(e,c,h,l,A(f)?e.length:l+f),c}return Ue(e,c,h),c}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,s,e=this.options.reverse;this.isHorizontal()?(t=this.left,s=this.right):(t=this.top,s=this.bottom,e=!e),this._startPixel=t,this._endPixel=s,this._reversePixels=e,this._length=s-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){I(this.options.afterUpdate,[this])}beforeSetDimensions(){I(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){I(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),I(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){I(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const t=this.options.ticks;let s,n,o;for(s=0,n=e.length;s<n;s++)o=e[s],o.label=I(t.callback,[o.value,s,e],this)}afterTickToLabelConversion(){I(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){I(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,t=e.ticks,s=_n(this.ticks.length,e.ticks.maxTicksLimit),n=t.minRotation||0,o=t.maxRotation;let r,l,c,a=n;if(!this._isVisible()||!t.display||n>=o||s<=1||!this.isHorizontal())return void(this.labelRotation=n);const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=G(this.chart.width-d,0,this.maxWidth);r=e.offset?this.maxWidth/s:f/(s-1),d+6>r&&(r=f/(s-(e.offset?.5:1)),l=this.maxHeight-ve(e.grid)-t.padding-yn(e.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),a=gi(Math.min(Math.asin(G((h.highest.height+6)/r,-1,1)),Math.asin(G(l/c,-1,1))-Math.asin(G(u/c,-1,1)))),a=Math.max(n,Math.min(o,a))),this.labelRotation=a}afterCalculateLabelRotation(){I(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){I(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:t,options:{ticks:s,title:n,grid:o}}=this,a=this._isVisible(),r=this.isHorizontal();if(a){const l=yn(n,t.options.font);if(r?(e.width=this.maxWidth,e.height=ve(o)+l):(e.height=this.maxHeight,e.width=ve(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=2*s.padding,g=dt(this.labelRotation),p=Math.cos(g),m=Math.sin(g);r?e.height=Math.min(this.maxHeight,e.height+(s.mirror?0:m*d.width+p*u.height)+f):e.width=Math.min(this.maxWidth,e.width+(s.mirror?0:p*d.width+m*u.height)+f),this._calculatePadding(c,h,m,p)}}this._handleMargins(),r?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,t,s,n){const{ticks:{align:o,padding:a},position:r}=this.options,l=0!==this.labelRotation,c="top"!==r&&"x"===this.axis;if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*e.width,f=s*t.height):(u=s*e.height,f=n*t.width):"start"===o?f=t.width:"end"===o?u=e.width:"inner"!==o&&(u=e.width/2,f=t.width/2),this.paddingLeft=Math.max((u-h+a)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+a)*this.width/(this.width-d),0)}else{let h=t.height/2,d=e.height/2;"start"===o?(h=0,d=e.height):"end"===o&&(h=t.height,d=0),this.paddingTop=h+a,this.paddingBottom=d+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){I(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:t}=this.options;return"top"===t||"bottom"===t||"x"===e}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){let t,s;for(this.beforeTickToLabelConversion(),this.generateTickLabels(e),t=0,s=e.length;t<s;t++)A(e[t].label)&&(e.splice(t,1),s--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const t=this.options.ticks.sampleSize;let s=this.ticks;t<s.length&&(s=xn(s,t)),this._labelSizes=e=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,t,s){const{ctx:n,_longestTextCache:o}=this,a=[],r=[],l=Math.floor(t/_n(t,s));let d,u,f,g,p,m,b,_,y,M,x,c=0,h=0;for(d=0;d<t;d+=l){if(g=e[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},_=p.lineHeight,y=M=0,A(g)||B(g)){if(B(g))for(u=0,f=g.length;u<f;++u)x=g[u],!A(x)&&!B(x)&&(y=Fe(n,b.data,b.gc,y,x),M+=_)}else y=Fe(n,b.data,b.gc,y,g),M=_;a.push(y),r.push(M),c=Math.max(y,c),h=Math.max(M,h)}!function rl(i,e){E(i,t=>{const s=t.gc,n=s.length/2;let o;if(n>e){for(o=0;o<n;++o)delete t.data[s[o]];s.splice(0,n)}})}(o,t);const v=a.indexOf(c),w=r.indexOf(h),k=C=>({width:a[C]||0,height:r[C]||0});return{first:k(0),last:k(t-1),widest:k(v),highest:k(w),widths:a,heights:r}}getLabelForValue(e){return e}getPixelForValue(e,t){return NaN}getValueForPixel(e){}getPixelForTick(e){const t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const t=this._startPixel+e*this._length;return function Yo(i){return G(i,-32768,32767)}(this._alignToPixels?It(this.chart,t,0):t)}getDecimalForPixel(e){const t=(e-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:t}=this;return e<0&&t<0?t:e>0&&t>0?e:0}getContext(e){const t=this.ticks||[];if(e>=0&&e<t.length){const s=t[e];return s.$context||(s.$context=function cl(i,e,t){return At(i,{tick:t,index:e,type:"tick"})}(this.getContext(),e,s))}return this.$context||(this.$context=function ll(i,e){return At(i,{scale:e,type:"scale"})}(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,t=dt(this.labelRotation),s=Math.abs(Math.cos(t)),n=Math.abs(Math.sin(t)),o=this._getLabelSizes(),a=e.autoSkipPadding||0,r=o?o.widest.width+a:0,l=o?o.highest.height+a:0;return this.isHorizontal()?l*s>r*n?r/s:l/n:l*n<r*s?l/s:r/n}_isVisible(){const e=this.options.display;return"auto"!==e?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const t=this.axis,s=this.chart,n=this.options,{grid:o,position:a,border:r}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=ve(o),f=[],g=r.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(H){return It(s,H,p)};let _,y,M,x,v,w,k,C,O,D,L,q;if("top"===a)_=b(this.bottom),w=this.bottom-u,C=_-m,D=b(e.top)+m,q=e.bottom;else if("bottom"===a)_=b(this.top),D=e.top,q=b(e.bottom)-m,w=_+m,C=this.top+u;else if("left"===a)_=b(this.right),v=this.right-u,k=_-m,O=b(e.left)+m,L=e.right;else if("right"===a)_=b(this.left),O=e.left,L=b(e.right)-m,v=_+m,k=this.left+u;else if("x"===t){if("center"===a)_=b((e.top+e.bottom)/2+.5);else if(T(a)){const H=Object.keys(a)[0];_=b(this.chart.scales[H].getPixelForValue(a[H]))}D=e.top,q=e.bottom,w=_+m,C=w+u}else if("y"===t){if("center"===a)_=b((e.left+e.right)/2);else if(T(a)){const H=Object.keys(a)[0];_=b(this.chart.scales[H].getPixelForValue(a[H]))}v=_-m,k=v-u,O=e.left,L=e.right}const at=P(n.ticks.maxTicksLimit,d),F=Math.max(1,Math.ceil(d/at));for(y=0;y<d;y+=F){const H=this.getContext(y),Y=o.setContext(H),ut=r.setContext(H),tt=Y.lineWidth,ee=Y.color,si=ut.dash||[],ie=ut.dashOffset,we=Y.tickWidth,Yt=Y.tickColor,Ce=Y.tickBorderDash||[],Ut=Y.tickBorderDashOffset;M=al(this,y,l),void 0!==M&&(x=It(s,M,tt),c?v=k=O=L=x:w=C=D=q=x,f.push({tx1:v,ty1:w,tx2:k,ty2:C,x1:O,y1:D,x2:L,y2:q,width:tt,color:ee,borderDash:si,borderDashOffset:ie,tickWidth:we,tickColor:Yt,tickBorderDash:Ce,tickBorderDashOffset:Ut}))}return this._ticksLength=d,this._borderValue=_,f}_computeLabelItems(e){const t=this.axis,s=this.options,{position:n,ticks:o}=s,a=this.isHorizontal(),r=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=ve(s.grid),f=u+h,g=d?-h:f,p=-dt(this.labelRotation),m=[];let b,_,y,M,x,v,w,k,C,O,D,L,q="middle";if("top"===n)v=this.bottom-g,w=this._getXAxisLabelAlignment();else if("bottom"===n)v=this.top+g,w=this._getXAxisLabelAlignment();else if("left"===n){const F=this._getYAxisLabelAlignment(u);w=F.textAlign,x=F.x}else if("right"===n){const F=this._getYAxisLabelAlignment(u);w=F.textAlign,x=F.x}else if("x"===t){if("center"===n)v=(e.top+e.bottom)/2+f;else if(T(n)){const F=Object.keys(n)[0];v=this.chart.scales[F].getPixelForValue(n[F])+f}w=this._getXAxisLabelAlignment()}else if("y"===t){if("center"===n)x=(e.left+e.right)/2-f;else if(T(n)){const F=Object.keys(n)[0];x=this.chart.scales[F].getPixelForValue(n[F])}w=this._getYAxisLabelAlignment(u).textAlign}"y"===t&&("start"===l?q="top":"end"===l&&(q="bottom"));const at=this._getLabelSizes();for(b=0,_=r.length;b<_;++b){y=r[b],M=y.label;const F=o.setContext(this.getContext(b));k=this.getPixelForTick(b)+o.labelOffset,C=this._resolveTickFontOptions(b),O=C.lineHeight,D=B(M)?M.length:1;const H=D/2,Y=F.color,ut=F.textStrokeColor,tt=F.textStrokeWidth;let si,ee=w;if(a?(x=k,"inner"===w&&(ee=b===_-1?this.options.reverse?"left":"right":0===b?this.options.reverse?"right":"left":"center"),L="top"===n?"near"===c||0!==p?-D*O+O/2:"center"===c?-at.highest.height/2-H*O+O:O/2-at.highest.height:"near"===c||0!==p?O/2:"center"===c?at.highest.height/2-H*O:at.highest.height-D*O,d&&(L*=-1),0!==p&&!F.showLabelBackdrop&&(x+=O/2*Math.sin(p))):(v=k,L=(1-D)*O/2),F.showLabelBackdrop){const ie=Q(F.backdropPadding),we=at.heights[b],Yt=at.widths[b];let Ce=L-ie.top,Ut=0-ie.left;switch(q){case"middle":Ce-=we/2;break;case"bottom":Ce-=we}switch(w){case"center":Ut-=Yt/2;break;case"right":Ut-=Yt;break;case"inner":b===_-1?Ut-=Yt:b>0&&(Ut-=Yt/2)}si={left:Ut,top:Ce,width:Yt+ie.width,height:we+ie.height,color:F.backdropColor}}m.push({label:M,font:C,textOffset:L,options:{rotation:p,color:Y,strokeColor:ut,strokeWidth:tt,textAlign:ee,textBaseline:q,translation:[x,v],backdrop:si}})}return m}_getXAxisLabelAlignment(){const{position:e,ticks:t}=this.options;if(-dt(this.labelRotation))return"top"===e?"left":"right";let n="center";return"start"===t.align?n="left":"end"===t.align?n="right":"inner"===t.align&&(n="inner"),n}_getYAxisLabelAlignment(e){const{position:t,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=e+o,l=this._getLabelSizes().widest.width;let c,h;return"left"===t?n?(h=this.right+o,"near"===s?c="left":"center"===s?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-r,"near"===s?c="right":"center"===s?(c="center",h-=l/2):(c="left",h=this.left)):"right"===t?n?(h=this.left+o,"near"===s?c="right":"center"===s?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+r,"near"===s?c="left":"center"===s?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,t=this.options.position;return"left"===t||"right"===t?{top:0,left:this.left,bottom:e.height,right:this.right}:"top"===t||"bottom"===t?{top:this.top,left:0,bottom:this.bottom,right:e.width}:void 0}drawBackground(){const{ctx:e,options:{backgroundColor:t},left:s,top:n,width:o,height:a}=this;t&&(e.save(),e.fillStyle=t,e.fillRect(s,n,o,a),e.restore())}getLineWidthForValue(e){const t=this.options.grid;if(!this._isVisible()||!t.display)return 0;const n=this.ticks.findIndex(o=>o.value===e);return n>=0?t.setContext(this.getContext(n)).lineWidth:0}drawGrid(e){const t=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let o,a;const r=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(t.display)for(o=0,a=n.length;o<a;++o){const l=n[o];t.drawOnChartArea&&r({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),t.drawTicks&&r({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:t,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),a=s.display?o.width:0;if(!a)return;const r=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=It(e,this.left,a)-a/2,h=It(e,this.right,r)+r/2,d=u=l):(d=It(e,this.top,a)-a/2,u=It(e,this.bottom,r)+r/2,c=h=l),t.save(),t.lineWidth=o.width,t.strokeStyle=o.color,t.beginPath(),t.moveTo(c,d),t.lineTo(h,u),t.stroke(),t.restore()}drawLabels(e){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Ie(s,n);const o=this.getLabelItems(e);for(const a of o)zt(s,a.label,0,a.textOffset,a.font,a.options);n&&ze(s)}drawTitle(){const{ctx:e,options:{position:t,title:s,reverse:n}}=this;if(!s.display)return;const o=X(s.font),a=Q(s.padding),r=s.align;let l=o.lineHeight/2;"bottom"===t||"center"===t||T(t)?(l+=a.bottom,B(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=a.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=function dl(i,e,t,s){const{top:n,left:o,bottom:a,right:r,chart:l}=i,{chartArea:c,scales:h}=l;let u,f,g,d=0;const p=a-n,m=r-o;if(i.isHorizontal()){if(f=Z(s,o,r),T(t)){const b=Object.keys(t)[0];g=h[b].getPixelForValue(t[b])+p-e}else g="center"===t?(c.bottom+c.top)/2+p-e:bn(i,t,e);u=r-o}else{if(T(t)){const b=Object.keys(t)[0];f=h[b].getPixelForValue(t[b])-m+e}else f="center"===t?(c.left+c.right)/2-m+e:bn(i,t,e);g=Z(s,a,n),d="left"===t?-$:$}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}(this,l,t,r);zt(e,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:hl(r,t,n),textBaseline:"middle",translation:[c,h]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,t=e.ticks&&e.ticks.z||0,s=P(e.grid&&e.grid.z,-1),n=P(e.border&&e.border.z,0);return this._isVisible()&&this.draw===jt.prototype.draw?[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:t,draw:o=>{this.drawLabels(o)}}]:[{z:t,draw:o=>{this.draw(o)}}]}getMatchingVisibleMetas(e){const t=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,a;for(o=0,a=t.length;o<a;++o){const r=t[o];r[s]===this.id&&(!e||r.type===e)&&n.push(r)}return n}_resolveTickFontOptions(e){return X(this.options.ticks.setContext(this.getContext(e)).font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Xe{constructor(e,t,s){this.type=e,this.scope=t,this.override=s,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const t=Object.getPrototypeOf(e);let s;(function gl(i){return"id"in i&&"defaults"in i})(t)&&(s=this.register(t));const n=this.items,o=e.id,a=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+e);return o in n||(n[o]=e,function ul(i,e,t){const s=oe(Object.create(null),[t?N.get(t):{},N.get(e),i.defaults]);N.set(e,s),i.defaultRoutes&&function fl(i,e){Object.keys(e).forEach(t=>{const s=t.split("."),n=s.pop(),o=[i].concat(s).join("."),a=e[t].split("."),r=a.pop(),l=a.join(".");N.route(o,n,l,r)})}(e,i.defaultRoutes),i.descriptors&&N.describe(e,i.descriptors)}(e,a,s),this.override&&N.override(e.id,e.overrides)),a}get(e){return this.items[e]}unregister(e){const t=this.items,s=e.id,n=this.scope;s in t&&delete t[s],n&&s in N[n]&&(delete N[n][s],this.override&&delete Ft[s])}}class pl{constructor(){this.controllers=new Xe(Tt,"datasets",!0),this.elements=new Xe(kt,"elements"),this.plugins=new Xe(Object,"plugins"),this.scales=new Xe(jt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,t,s){[...t].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(e,o,n):E(n,a=>{const r=s||this._getRegistryForType(a);this._exec(e,r,a)})})}_exec(e,t,s){const n=fi(e);I(s["before"+n],[],s),t[e](s),I(s["after"+n],[],s)}_getRegistryForType(e){for(let t=0;t<this._typedRegistries.length;t++){const s=this._typedRegistries[t];if(s.isForType(e))return s}return this.plugins}_get(e,t,s){const n=t.get(e);if(void 0===n)throw new Error('"'+e+'" is not a registered '+s+".");return n}}var mt=new pl;class ml{constructor(){this._init=[]}notify(e,t,s,n){"beforeInit"===t&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const o=n?this._descriptors(e).filter(n):this._descriptors(e),a=this._notify(o,e,t,s);return"afterDestroy"===t&&(this._notify(o,e,"stop"),this._notify(this._init,e,"uninstall")),a}_notify(e,t,s,n){n=n||{};for(const o of e){const a=o.plugin;if(!1===I(a[s],[t,n,o.options],a)&&n.cancelable)return!1}return!0}invalidate(){A(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const t=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),t}_createDescriptors(e,t){const s=e&&e.config,n=P(s.options&&s.options.plugins,{}),o=function bl(i){const e={},t=[],s=Object.keys(mt.plugins.items);for(let o=0;o<s.length;o++)t.push(mt.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const a=n[o];-1===t.indexOf(a)&&(t.push(a),e[a.id]=!0)}return{plugins:t,localIds:e}}(s);return!1!==n||t?function xl(i,{plugins:e,localIds:t},s,n){const o=[],a=i.getContext();for(const r of e){const l=r.id,c=_l(s[l],n);null!==c&&o.push({plugin:r,options:yl(i.config,{plugin:r,local:t[l]},c,a)})}return o}(e,o,n,t):[]}_notifyStateChanges(e){const t=this._oldCache||[],s=this._cache,n=(o,a)=>o.filter(r=>!a.some(l=>r.plugin.id===l.plugin.id));this._notify(n(t,s),e,"stop"),this._notify(n(s,t),e,"start")}}function _l(i,e){return e||!1!==i?!0===i?{}:i:null}function yl(i,{plugin:e,local:t},s,n){const o=i.pluginScopeKeys(e),a=i.getOptionScopes(s,o);return t&&e.defaults&&a.push(e.defaults),i.createResolver(a,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Bi(i,e){return((e.datasets||{})[i]||{}).indexAxis||e.indexAxis||(N.datasets[i]||{}).indexAxis||"x"}function vn(i){if("x"===i||"y"===i||"r"===i)return i}function kl(i){return"top"===i||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0}function Vi(i,...e){if(vn(i))return i;for(const t of e){const s=t.axis||kl(t.position)||i.length>1&&vn(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function Mn(i,e,t){if(t[e+"AxisID"]===i)return{axis:e}}function kn(i){const e=i.options||(i.options={});e.plugins=P(e.plugins,{}),e.scales=function wl(i,e){const t=Ft[i.type]||{scales:{}},s=e.scales||{},n=Bi(i.type,e),o=Object.create(null);return Object.keys(s).forEach(a=>{const r=s[a];if(!T(r))return console.error(`Invalid scale configuration for scale: ${a}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);const l=Vi(a,r,function Sl(i,e){if(e.data&&e.data.datasets){const t=e.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(t.length)return Mn(i,"x",t[0])||Mn(i,"y",t[0])}return{}}(a,i),N.scales[r.type]),c=function Ml(i,e){return i===e?"_index_":"_value_"}(l,n),h=t.scales||{};o[a]=ae(Object.create(null),[{axis:l},r,h[l],h[c]])}),i.data.datasets.forEach(a=>{const r=a.type||i.type,l=a.indexAxis||Bi(r,e),h=(Ft[r]||{}).scales||{};Object.keys(h).forEach(d=>{const u=function vl(i,e){let t=i;return"_index_"===i?t=e:"_value_"===i&&(t="x"===e?"y":"x"),t}(d,l),f=a[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),ae(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(a=>{const r=o[a];ae(r,[N.scales[r.type],N.scale])}),o}(i,e)}function Sn(i){return(i=i||{}).datasets=i.datasets||[],i.labels=i.labels||[],i}const wn=new Map,Cn=new Set;function Ge(i,e){let t=wn.get(i);return t||(t=e(),wn.set(i,t),Cn.add(t)),t}const Me=(i,e,t)=>{const s=Pt(e,t);void 0!==s&&i.add(s)};class Pl{constructor(e){this._config=function Cl(i){return(i=i||{}).data=Sn(i.data),kn(i),i}(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=Sn(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),kn(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Ge(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,t){return Ge(`${e}.transition.${t}`,()=>[[`datasets.${e}.transitions.${t}`,`transitions.${t}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,t){return Ge(`${e}-${t}`,()=>[[`datasets.${e}.elements.${t}`,`datasets.${e}`,`elements.${t}`,""]])}pluginScopeKeys(e){const t=e.id;return Ge(`${this.type}-plugin-${t}`,()=>[[`plugins.${t}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,t){const s=this._scopeCache;let n=s.get(e);return(!n||t)&&(n=new Map,s.set(e,n)),n}getOptionScopes(e,t,s){const{options:n,type:o}=this,a=this._cachedScopes(e,s),r=a.get(t);if(r)return r;const l=new Set;t.forEach(h=>{e&&(l.add(e),h.forEach(d=>Me(l,e,d))),h.forEach(d=>Me(l,n,d)),h.forEach(d=>Me(l,Ft[o]||{},d)),h.forEach(d=>Me(l,N,d)),h.forEach(d=>Me(l,yi,d))});const c=Array.from(l);return 0===c.length&&c.push(Object.create(null)),Cn.has(t)&&a.set(t,c),c}chartOptionScopes(){const{options:e,type:t}=this;return[e,Ft[t]||{},N.datasets[t]||{},{type:t},N,yi]}resolveNamedOptions(e,t,s,n=[""]){const o={$shared:!0},{resolver:a,subPrefixes:r}=Pn(this._resolverCache,e,n);let l=a;(function Ol(i,e){const{isScriptable:t,isIndexable:s}=Ps(i);for(const n of e){const o=t(n),a=s(n),r=(a||o)&&i[n];if(o&&(Dt(r)||Dl(r))||a&&B(r))return!0}return!1})(a,t)&&(o.$shared=!1,l=Zt(a,s=Dt(s)?s():s,this.createResolver(e,s,r)));for(const c of t)o[c]=l[c];return o}createResolver(e,t,s=[""],n){const{resolver:o}=Pn(this._resolverCache,e,s);return T(t)?Zt(o,t,void 0,n):o}}function Pn(i,e,t){let s=i.get(e);s||(s=new Map,i.set(e,s));const n=t.join();let o=s.get(n);return o||(o={resolver:Si(e,t),subPrefixes:t.filter(r=>!r.toLowerCase().includes("hover"))},s.set(n,o)),o}const Dl=i=>T(i)&&Object.getOwnPropertyNames(i).some(e=>Dt(i[e])),Tl=["top","bottom","left","right","chartArea"];function Dn(i,e){return"top"===i||"bottom"===i||-1===Tl.indexOf(i)&&"x"===e}function On(i,e){return function(t,s){return t[i]===s[i]?t[e]-s[e]:t[i]-s[i]}}function An(i){const e=i.chart,t=e.options.animation;e.notifyPlugins("afterRender"),I(t&&t.onComplete,[i],e)}function Ll(i){const e=i.chart,t=e.options.animation;I(t&&t.onProgress,[i],e)}function Tn(i){return Pi()&&"string"==typeof i?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const Ke={},Ln=i=>{const e=Tn(i);return Object.values(Ke).filter(t=>t.canvas===e).pop()};function Rl(i,e,t){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=e){const a=i[n];delete i[n],(t>0||o>e)&&(i[o+t]=a)}}}let Wi=(()=>class i{static defaults=N;static instances=Ke;static overrides=Ft;static registry=mt;static version="4.4.9";static getChart=Ln;static register(...t){mt.add(...t),Rn()}static unregister(...t){mt.remove(...t),Rn()}constructor(t,s){const n=this.config=new Pl(s),o=Tn(t),a=Ln(o);if(a)throw new Error("Canvas is already in use. Chart with ID '"+a.id+"' must be destroyed before the canvas with ID '"+a.canvas.id+"' can be reused.");const r=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||function Qr(i){return!Pi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Vr:Zr}(o)),this.platform.updateConfig(n);const l=this.platform.acquireContext(o,r.aspectRatio),c=l&&l.canvas,h=c&&c.height,d=c&&c.width;this.id=Lo(),this.ctx=l,this.canvas=c,this.width=d,this.height=h,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ml,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function Ko(i,e){let t;return function(...s){return e?(clearTimeout(t),t=setTimeout(i,e,s)):i.apply(this,s),e}}(u=>this.update(u),r.resizeDelay||0),this._dataChanges=[],Ke[this.id]=this,l&&c?(Mt.listen(this,"complete",An),Mt.listen(this,"progress",Ll),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:s},width:n,height:o,_aspectRatio:a}=this;return A(t)?s&&a?a:o?n/o:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return mt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Fs(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ss(this.canvas,this.ctx),this}stop(){return Mt.stop(this),this}resize(t,s){Mt.running(this)?this._resizeBeforeDraw={width:t,height:s}:this._resize(t,s)}_resize(t,s){const n=this.options,r=this.platform.getMaximumSize(this.canvas,t,s,n.maintainAspectRatio&&this.aspectRatio),l=n.devicePixelRatio||this.platform.getDevicePixelRatio(),c=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Fs(this,l,!0)&&(this.notifyPlugins("resize",{size:r}),I(n.onResize,[this,r],this),this.attached&&this._doResize(c)&&this.render())}ensureScalesHaveIDs(){E(this.options.scales||{},(n,o)=>{n.id=o})}buildOrUpdateScales(){const t=this.options,s=t.scales,n=this.scales,o=Object.keys(n).reduce((r,l)=>(r[l]=!1,r),{});let a=[];s&&(a=a.concat(Object.keys(s).map(r=>{const l=s[r],c=Vi(r,l),h="r"===c,d="x"===c;return{options:l,dposition:h?"chartArea":d?"bottom":"left",dtype:h?"radialLinear":d?"category":"linear"}}))),E(a,r=>{const l=r.options,c=l.id,h=Vi(c,l),d=P(l.type,r.dtype);(void 0===l.position||Dn(l.position,h)!==Dn(r.dposition))&&(l.position=r.dposition),o[c]=!0;let u=null;c in n&&n[c].type===d?u=n[c]:(u=new(mt.getScale(d))({id:c,type:d,ctx:this.ctx,chart:this}),n[u.id]=u),u.init(l,t)}),E(o,(r,l)=>{r||delete n[l]}),E(n,r=>{J.configure(this,r,r.options),J.addBox(this,r)})}_updateMetasets(){const t=this._metasets,s=this.data.datasets.length,n=t.length;if(t.sort((o,a)=>o.index-a.index),n>s){for(let o=s;o<n;++o)this._destroyDatasetMeta(o);t.splice(s,n-s)}this._sortedMetasets=t.slice(0).sort(On("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:s}}=this;t.length>s.length&&delete this._stacks,t.forEach((n,o)=>{0===s.filter(a=>a===n._dataset).length&&this._destroyDatasetMeta(o)})}buildOrUpdateControllers(){const t=[],s=this.data.datasets;let n,o;for(this._removeUnreferencedMetasets(),n=0,o=s.length;n<o;n++){const a=s[n];let r=this.getDatasetMeta(n);const l=a.type||this.config.type;if(r.type&&r.type!==l&&(this._destroyDatasetMeta(n),r=this.getDatasetMeta(n)),r.type=l,r.indexAxis=a.indexAxis||Bi(l,this.options),r.order=a.order||0,r.index=n,r.label=""+a.label,r.visible=this.isDatasetVisible(n),r.controller)r.controller.updateIndex(n),r.controller.linkScales();else{const c=mt.getController(l),{datasetElementType:h,dataElementType:d}=N.datasets[l];Object.assign(c,{dataElementType:mt.getElement(d),datasetElementType:h&&mt.getElement(h)}),r.controller=new c(this,n),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){E(this.data.datasets,(t,s)=>{this.getDatasetMeta(s).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const s=this.config;s.update();const n=this._options=s.createResolver(s.chartOptionScopes(),this.getContext()),o=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let h=0,d=this.data.datasets.length;h<d;h++){const{controller:u}=this.getDatasetMeta(h),f=!o&&-1===a.indexOf(u);u.buildOrUpdateElements(f),r=Math.max(+u.getMaxOverflow(),r)}r=this._minPadding=n.layout.autoPadding?r:0,this._updateLayout(r),o||E(a,h=>{h.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(On("z","_idx"));const{_active:l,_lastEvent:c}=this;c?this._eventHandler(c,!0):l.length&&this._updateHoverStyles(l,l,!0),this.render()}_updateScales(){E(this.scales,t=>{J.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,s=new Set(Object.keys(this._listeners)),n=new Set(t.events);(!as(s,n)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,s=this._getUniformDataChanges()||[];for(const{method:n,start:o,count:a}of s)Rl(t,o,"_removeElements"===n?-a:a)}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const s=this.data.datasets.length,n=a=>new Set(t.filter(r=>r[0]===a).map((r,l)=>l+","+r.splice(1).join(","))),o=n(0);for(let a=1;a<s;a++)if(!as(o,n(a)))return;return Array.from(o).map(a=>a.split(",")).map(a=>({method:a[1],start:+a[2],count:+a[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;J.update(this,this.width,this.height,t);const s=this.chartArea,n=s.width<=0||s.height<=0;this._layers=[],E(this.boxes,o=>{n&&"chartArea"===o.position||(o.configure&&o.configure(),this._layers.push(...o._layers()))},this),this._layers.forEach((o,a)=>{o._idx=a}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let s=0,n=this.data.datasets.length;s<n;++s)this.getDatasetMeta(s).controller.configure();for(let s=0,n=this.data.datasets.length;s<n;++s)this._updateDataset(s,Dt(t)?t({datasetIndex:s}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,s){const n=this.getDatasetMeta(t),o={meta:n,index:t,mode:s,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",o)&&(n.controller._update(s),o.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",o))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(Mt.has(this)?this.attached&&!Mt.running(this)&&Mt.start(this):(this.draw(),An({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:n,height:o}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(n,o)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const s=this._layers;for(t=0;t<s.length&&s[t].z<=0;++t)s[t].draw(this.chartArea);for(this._drawDatasets();t<s.length;++t)s[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const s=this._sortedMetasets,n=[];let o,a;for(o=0,a=s.length;o<a;++o){const r=s[o];(!t||r.visible)&&n.push(r)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let s=t.length-1;s>=0;--s)this._drawDataset(t[s]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const s=this.ctx,n={meta:t,index:t.index,cancelable:!0},o=Ys(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",n)&&(o&&Ie(s,o),t.controller.draw(),o&&ze(s),n.cancelable=!1,this.notifyPlugins("afterDatasetDraw",n))}isPointInArea(t){return vt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,s,n,o){const a=Tr.modes[s];return"function"==typeof a?a(this,t,n,o):[]}getDatasetMeta(t){const s=this.data.datasets[t],n=this._metasets;let o=n.filter(a=>a&&a._dataset===s).pop();return o||(o={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:s&&s.order||0,index:t,_dataset:s,_parsed:[],_sorted:!1},n.push(o)),o}getContext(){return this.$context||(this.$context=At(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const s=this.data.datasets[t];if(!s)return!1;const n=this.getDatasetMeta(t);return"boolean"==typeof n.hidden?!n.hidden:!s.hidden}setDatasetVisibility(t,s){this.getDatasetMeta(t).hidden=!s}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,s,n){const o=n?"show":"hide",a=this.getDatasetMeta(t),r=a.controller._resolveAnimations(void 0,o);re(s)?(a.data[s].hidden=!n,this.update()):(this.setDatasetVisibility(t,n),r.update(a,{visible:n}),this.update(l=>l.datasetIndex===t?o:void 0))}hide(t,s){this._updateVisibility(t,s,!1)}show(t,s){this._updateVisibility(t,s,!0)}_destroyDatasetMeta(t){const s=this._metasets[t];s&&s.controller&&s.controller._destroy(),delete this._metasets[t]}_stop(){let t,s;for(this.stop(),Mt.remove(this),t=0,s=this.data.datasets.length;t<s;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:s}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ss(t,s),this.platform.releaseContext(s),this.canvas=null,this.ctx=null),delete Ke[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,s=this.platform,n=(a,r)=>{s.addEventListener(this,a,r),t[a]=r},o=(a,r,l)=>{a.offsetX=r,a.offsetY=l,this._eventHandler(a)};E(this.options.events,a=>n(a,o))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,s=this.platform,n=(c,h)=>{s.addEventListener(this,c,h),t[c]=h},o=(c,h)=>{t[c]&&(s.removeEventListener(this,c,h),delete t[c])},a=(c,h)=>{this.canvas&&this.resize(c,h)};let r;const l=()=>{o("attach",l),this.attached=!0,this.resize(),n("resize",a),n("detach",r)};r=()=>{this.attached=!1,o("resize",a),this._stop(),this._resize(0,0),n("attach",l)},s.isAttached(this.canvas)?l():r()}unbindEvents(){E(this._listeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._listeners={},E(this._responsiveListeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,s,n){const o=n?"set":"remove";let a,r,l,c;for("dataset"===s&&(a=this.getDatasetMeta(t[0].datasetIndex),a.controller["_"+o+"DatasetHoverStyle"]()),l=0,c=t.length;l<c;++l){r=t[l];const h=r&&this.getDatasetMeta(r.datasetIndex).controller;h&&h[o+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const s=this._active||[],n=t.map(({datasetIndex:a,index:r})=>{const l=this.getDatasetMeta(a);if(!l)throw new Error("No dataset found at index "+a);return{datasetIndex:a,element:l.data[r],index:r}});!Ae(n,s)&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,s))}notifyPlugins(t,s,n){return this._plugins.notify(this,t,s,n)}isPluginEnabled(t){return 1===this._plugins._cache.filter(s=>s.plugin.id===t).length}_updateHoverStyles(t,s,n){const o=this.options.hover,a=(c,h)=>c.filter(d=>!h.some(u=>d.datasetIndex===u.datasetIndex&&d.index===u.index)),r=a(s,t),l=n?t:a(t,s);r.length&&this.updateHoverStyle(r,o.mode,!1),l.length&&o.mode&&this.updateHoverStyle(l,o.mode,!0)}_eventHandler(t,s){const n={event:t,replay:s,cancelable:!0,inChartArea:this.isPointInArea(t)},o=r=>(r.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",n,o))return;const a=this._handleEvent(t,s,n.inChartArea);return n.cancelable=!1,this.notifyPlugins("afterEvent",n,o),(a||n.changed)&&this.render(),this}_handleEvent(t,s,n){const{_active:o=[],options:a}=this,l=this._getActiveElements(t,o,n,s),c=function Bo(i){return"mouseup"===i.type||"click"===i.type||"contextmenu"===i.type}(t),h=function El(i,e,t,s){return t&&"mouseout"!==i.type?s?e:i:null}(t,this._lastEvent,n,c);n&&(this._lastEvent=null,I(a.onHover,[t,l,this],this),c&&I(a.onClick,[t,l,this],this));const d=!Ae(l,o);return(d||s)&&(this._active=l,this._updateHoverStyles(l,o,s)),this._lastEvent=h,d}_getActiveElements(t,s,n,o){if("mouseout"===t.type)return[];if(!n)return s;const a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,o)}})();function Rn(){return E(Wi.instances,i=>i._plugins.invalidate())}function te(i,e,t,s){return{x:t+i*Math.cos(e),y:s+i*Math.sin(e)}}function qe(i,e,t,s,n,o){const{x:a,y:r,startAngle:l,pixelMargin:c,innerRadius:h}=e,d=Math.max(e.outerRadius+s+t-c,0),u=h>0?h+s+t+c:0;let f=0;const g=n-l;if(s){const Y=((h>0?h-s:0)+(d>0?d-s:0))/2;f=(g-(0!==Y?g*Y/(Y+s):g))/2}const m=(g-Math.max(.001,g*d-t/V)/d)/2,b=l+m+f,_=n-m-f,{outerStart:y,outerEnd:M,innerStart:x,innerEnd:v}=function zl(i,e,t,s){const n=function Il(i){return ki(i,["outerStart","outerEnd","innerStart","innerEnd"])}(i.options.borderRadius),o=(t-e)/2,a=Math.min(o,s*e/2),r=l=>{const c=(t-Math.min(o,l))*s/2;return G(l,0,Math.min(o,c))};return{outerStart:r(n.outerStart),outerEnd:r(n.outerEnd),innerStart:G(n.innerStart,0,a),innerEnd:G(n.innerEnd,0,a)}}(e,u,d,_-b),w=d-y,k=d-M,C=b+y/w,O=_-M/k,D=u+x,L=u+v,q=b+x/D,at=_-v/L;if(i.beginPath(),o){const F=(C+O)/2;if(i.arc(a,r,d,C,F),i.arc(a,r,d,F,O),M>0){const tt=te(k,O,a,r);i.arc(tt.x,tt.y,M,O,_+$)}const H=te(L,_,a,r);if(i.lineTo(H.x,H.y),v>0){const tt=te(L,at,a,r);i.arc(tt.x,tt.y,v,_+$,at+Math.PI)}const Y=(_-v/u+(b+x/u))/2;if(i.arc(a,r,u,_-v/u,Y,!0),i.arc(a,r,u,Y,b+x/u,!0),x>0){const tt=te(D,q,a,r);i.arc(tt.x,tt.y,x,q+Math.PI,b-$)}const ut=te(w,b,a,r);if(i.lineTo(ut.x,ut.y),y>0){const tt=te(w,C,a,r);i.arc(tt.x,tt.y,y,b-$,C)}}else{i.moveTo(a,r);const F=Math.cos(C)*d+a,H=Math.sin(C)*d+r;i.lineTo(F,H);const Y=Math.cos(O)*d+a,ut=Math.sin(O)*d+r;i.lineTo(Y,ut)}i.closePath()}function En(i,e,t=e){i.lineCap=P(t.borderCapStyle,e.borderCapStyle),i.setLineDash(P(t.borderDash,e.borderDash)),i.lineDashOffset=P(t.borderDashOffset,e.borderDashOffset),i.lineJoin=P(t.borderJoinStyle,e.borderJoinStyle),i.lineWidth=P(t.borderWidth,e.borderWidth),i.strokeStyle=P(t.borderColor,e.borderColor)}function Nl(i,e,t){i.lineTo(t.x,t.y)}function Fn(i,e,t={}){const s=i.length,{start:n=0,end:o=s-1}=t,{start:a,end:r}=e,l=Math.max(n,a),c=Math.min(o,r);return{count:s,start:l,loop:e.loop,ilen:c<l&&!(n<a&&o<a||n>r&&o>r)?s+c-l:c-l}}function jl(i,e,t,s){const{points:n,options:o}=e,{count:a,start:r,loop:l,ilen:c}=Fn(n,t,s),h=function Hl(i){return i.stepped?ra:i.tension||"monotone"===i.cubicInterpolationMode?la:Nl}(o);let f,g,p,{move:d=!0,reverse:u}=s||{};for(f=0;f<=c;++f)g=n[(r+(u?c-f:f))%a],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,p,g,u,o.stepped),p=g);return l&&(g=n[(r+(u?c:0))%a],h(i,p,g,u,o.stepped)),!!l}function $l(i,e,t,s){const n=e.points,{count:o,start:a,ilen:r}=Fn(n,t,s),{move:l=!0,reverse:c}=s||{};let u,f,g,p,m,b,h=0,d=0;const _=M=>(a+(c?r-M:M))%o,y=()=>{p!==m&&(i.lineTo(h,m),i.lineTo(h,p),i.lineTo(h,b))};for(l&&(f=n[_(0)],i.moveTo(f.x,f.y)),u=0;u<=r;++u){if(f=n[_(u)],f.skip)continue;const M=f.x,x=f.y,v=0|M;v===g?(x<p?p=x:x>m&&(m=x),h=(d*h+M)/++d):(y(),i.lineTo(M,x),g=v,d=0,p=m=x),b=x}y()}function Ni(i){const e=i.options;return i._decimated||i._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||e.borderDash&&e.borderDash.length?jl:$l}const Gl="function"==typeof Path2D;let Ze=(()=>class i extends kt{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,s){const n=this.options;!n.tension&&"monotone"!==n.cubicInterpolationMode||n.stepped||this._pointsUpdated||(La(this._points,n,t,n.spanGaps?this._loop:this._fullLoop,s),this._pointsUpdated=!0)}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function Xa(i,e){const t=i.points,s=i.options.spanGaps,n=t.length;if(!n)return[];const o=!!i._loop,{start:a,end:r}=function Ya(i,e,t,s){let n=0,o=e-1;if(t&&!s)for(;n<e&&!i[n].skip;)n++;for(;n<e&&i[n].skip;)n++;for(n%=e,t&&(o+=n);o>n&&i[o%e].skip;)o--;return o%=e,{start:n,end:o}}(t,n,o,s);return function js(i,e,t,s){return s&&s.setContext&&t?function Ga(i,e,t,s){const n=i._chart.getContext(),o=$s(i.options),{_datasetIndex:a,options:{spanGaps:r}}=i,l=t.length,c=[];let h=o,d=e[0].start,u=d;function f(g,p,m,b){const _=r?-1:1;if(g!==p){for(g+=l;t[g%l].skip;)g-=_;for(;t[p%l].skip;)p+=_;g%l!=p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(const g of e){d=r?d:g.start;let m,p=t[d%l];for(u=d+1;u<=g.end;u++){const b=t[u%l];m=$s(s.setContext(At(n,{type:"segment",p0:p,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:a}))),Ka(m,h)&&f(d,u-1,g.loop,h),p=b,h=m}d<u-1&&f(d,u-1,g.loop,h)}return c}(i,e,t,s):e}(i,!0===s?[{start:a,end:r,loop:o}]:function Ua(i,e,t,s){const n=i.length,o=[];let l,a=e,r=i[e];for(l=e+1;l<=t;++l){const c=i[l%n];c.skip||c.stop?r.skip||(o.push({start:e%n,end:(l-1)%n,loop:s=!1}),e=a=c.stop?l:null):(a=l,r.skip&&(e=l)),r=c}return null!==a&&o.push({start:e%n,end:a%n,loop:s}),o}(t,a,r<a?r+n:r,!!i._fullLoop&&0===a&&r===n-1),t,e)}(this,this.options.segment))}first(){const t=this.segments;return t.length&&this.points[t[0].start]}last(){const t=this.segments,n=t.length;return n&&this.points[t[n-1].end]}interpolate(t,s){const n=this.options,o=t[s],a=this.points,r=Hs(this,{property:s,start:o,end:o});if(!r.length)return;const l=[],c=function Yl(i){return i.stepped?Wa:i.tension||"monotone"===i.cubicInterpolationMode?Na:Nt}(n);let h,d;for(h=0,d=r.length;h<d;++h){const{start:u,end:f}=r[h],g=a[u],p=a[f];if(g===p){l.push(g);continue}const b=c(g,p,Math.abs((o-g[s])/(p[s]-g[s])),n.stepped);b[s]=t[s],l.push(b)}return 1===l.length?l[0]:l}pathSegment(t,s,n){return Ni(this)(t,this,s,n)}path(t,s,n){const o=this.segments,a=Ni(this);let r=this._loop;s=s||0,n=n||this.points.length-s;for(const l of o)r&=a(t,this,l,{start:s,end:s+n-1});return!!r}draw(t,s,n,o){(this.points||[]).length&&(this.options||{}).borderWidth&&(t.save(),function Kl(i,e,t,s){Gl&&!e.options.segment?function Ul(i,e,t,s){let n=e._path;n||(n=e._path=new Path2D,e.path(n,t,s)&&n.closePath()),En(i,e.options),i.stroke(n)}(i,e,t,s):function Xl(i,e,t,s){const{segments:n,options:o}=e,a=Ni(e);for(const r of n)En(i,o,r.style),i.beginPath(),a(i,e,r,{start:t,end:t+s-1})&&i.closePath(),i.stroke()}(i,e,t,s)}(t,this,n,o),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}})();function In(i,e,t,s){const n=i.options,{[t]:o}=i.getProps([t],s);return Math.abs(e-o)<n.radius+n.hitRadius}let ql=(()=>class i extends kt{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,s,n){const o=this.options,{x:a,y:r}=this.getProps(["x","y"],n);return Math.pow(t-a,2)+Math.pow(s-r,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(t,s){return In(this,t,"x",s)}inYRange(t,s){return In(this,t,"y",s)}getCenterPoint(t){const{x:s,y:n}=this.getProps(["x","y"],t);return{x:s,y:n}}size(t){let s=(t=t||this.options||{}).radius||0;return s=Math.max(s,s&&t.hoverRadius||0),2*(s+(s&&t.borderWidth||0))}draw(t,s){const n=this.options;this.skip||n.radius<.1||!vt(this,s,this.size(n)/2)||(t.strokeStyle=n.borderColor,t.lineWidth=n.borderWidth,t.fillStyle=n.backgroundColor,Mi(t,n,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}})();function zn(i,e){const{x:t,y:s,base:n,width:o,height:a}=i.getProps(["x","y","base","width","height"],e);let r,l,c,h,d;return i.horizontal?(d=a/2,r=Math.min(t,n),l=Math.max(t,n),c=s-d,h=s+d):(d=o/2,r=t-d,l=t+d,c=Math.min(s,n),h=Math.max(s,n)),{left:r,top:c,right:l,bottom:h}}function Lt(i,e,t,s){return i?0:G(e,t,s)}function Hi(i,e,t,s){const n=null===e,o=null===t,r=i&&!(n&&o)&&zn(i,s);return r&&(n||xt(e,r.left,r.right))&&(o||xt(t,r.top,r.bottom))}function ec(i,e){i.rect(e.x,e.y,e.w,e.h)}function ji(i,e,t={}){const s=i.x!==t.x?-e:0,n=i.y!==t.y?-e:0;return{x:i.x+s,y:i.y+n,w:i.w+((i.x+i.w!==t.x+t.w?e:0)-s),h:i.h+((i.y+i.h!==t.y+t.h?e:0)-n),radius:i.radius}}var sc=Object.freeze({__proto__:null,ArcElement:class Wl extends kt{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:e=>"borderDash"!==e};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(e){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,t,s){const n=this.getProps(["x","y"],s),{angle:o,distance:a}=ds(n,{x:e,y:t}),{startAngle:r,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),u=(this.options.spacing+this.options.borderWidth)/2,f=P(d,l-r),g=ce(o,r,l)&&r!==l,p=f>=W||g,m=xt(a,c+u,h+u);return p&&m}getCenterPoint(e){const{x:t,y:s,startAngle:n,endAngle:o,innerRadius:a,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(a+r+c+l)/2;return{x:t+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:t,circumference:s}=this,n=(t.offset||0)/4,o=(t.spacing||0)/2,a=t.circular;if(this.pixelMargin="inner"===t.borderAlign?.33:0,this.fullCircles=s>W?Math.floor(s/W):0,0===s||this.innerRadius<0||this.outerRadius<0)return;e.save();const r=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(r)*n,Math.sin(r)*n);const c=n*(1-Math.sin(Math.min(V,s||0)));e.fillStyle=t.backgroundColor,e.strokeStyle=t.borderColor,function Bl(i,e,t,s,n){const{fullCircles:o,startAngle:a,circumference:r}=e;let l=e.endAngle;if(o){qe(i,e,t,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(r)||(l=a+(r%W||W))}qe(i,e,t,s,l,n),i.fill()}(e,this,c,o,a),function Vl(i,e,t,s,n){const{fullCircles:o,startAngle:a,circumference:r,options:l}=e,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l,f="inner"===l.borderAlign;if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=u,f?(i.lineWidth=2*c,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=e.endAngle;if(o){qe(i,e,t,s,g,n);for(let p=0;p<o;++p)i.stroke();isNaN(r)||(g=a+(r%W||W))}f&&function Fl(i,e,t){const{startAngle:s,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:l}=e;let c=n/r;i.beginPath(),i.arc(o,a,r,s-c,t+c),l>n?(c=n/l,i.arc(o,a,l,t+c,s-c,!0)):i.arc(o,a,n,t+$,s-$),i.closePath(),i.clip()}(i,e,g),o||(qe(i,e,t,s,g,n),i.stroke())}(e,this,c,o,a),e.restore()}},BarElement:class ic extends kt{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:t,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:a}=function Jl(i){const e=zn(i),t=e.right-e.left,s=e.bottom-e.top,n=function Zl(i,e,t){const n=i.borderSkipped,o=Cs(i.options.borderWidth);return{t:Lt(n.top,o.top,0,t),r:Lt(n.right,o.right,0,e),b:Lt(n.bottom,o.bottom,0,t),l:Lt(n.left,o.left,0,e)}}(i,t/2,s/2),o=function Ql(i,e,t){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Bt(n),a=Math.min(e,t),r=i.borderSkipped,l=s||T(n);return{topLeft:Lt(!l||r.top||r.left,o.topLeft,0,a),topRight:Lt(!l||r.top||r.right,o.topRight,0,a),bottomLeft:Lt(!l||r.bottom||r.left,o.bottomLeft,0,a),bottomRight:Lt(!l||r.bottom||r.right,o.bottomRight,0,a)}}(i,t/2,s/2);return{outer:{x:e.left,y:e.top,w:t,h:s,radius:o},inner:{x:e.left+n.l,y:e.top+n.t,w:t-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}(this),r=function tc(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}(a.radius)?fe:ec;e.save(),(a.w!==o.w||a.h!==o.h)&&(e.beginPath(),r(e,ji(a,t,o)),e.clip(),r(e,ji(o,-t,a)),e.fillStyle=s,e.fill("evenodd")),e.beginPath(),r(e,ji(o,t)),e.fillStyle=n,e.fill(),e.restore()}inRange(e,t,s){return Hi(this,e,t,s)}inXRange(e,t){return Hi(this,e,null,t)}inYRange(e,t){return Hi(this,null,e,t)}getCenterPoint(e){const{x:t,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],e);return{x:o?(t+n)/2:t,y:o?s:(s+n)/2}}getRange(e){return"x"===e?this.width/2:this.height/2}},LineElement:Ze,PointElement:ql});const $i=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Bn=$i.map(i=>i.replace("rgb(","rgba(").replace(")",", 0.5)"));function Vn(i){return $i[i%$i.length]}function Wn(i){return Bn[i%Bn.length]}function Nn(i){let e;for(e in i)if(i[e].borderColor||i[e].backgroundColor)return!0;return!1}var hc={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(i,e,t){if(!t.enabled)return;const{data:{datasets:s},options:n}=i.config,{elements:o}=n,a=Nn(s)||function lc(i){return i&&(i.borderColor||i.backgroundColor)}(n)||o&&Nn(o)||function cc(){return"rgba(0,0,0,0.1)"!==N.borderColor||"rgba(0,0,0,0.1)"!==N.backgroundColor}();if(!t.forceOverride&&a)return;const r=function rc(i){let e=0;return(t,s)=>{const n=i.getDatasetMeta(s).controller;n instanceof Ri?e=function oc(i,e){return i.backgroundColor=i.data.map(()=>Vn(e++)),e}(t,e):n instanceof on?e=function ac(i,e){return i.backgroundColor=i.data.map(()=>Wn(e++)),e}(t,e):n&&(e=function nc(i,e){return i.borderColor=Vn(e),i.backgroundColor=Wn(e),++e}(t,e))}}(i);s.forEach(r)}};function Hn(i){if(i._decimated){const e=i._data;delete i._decimated,delete i._data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,writable:!0,value:e})}}function jn(i){i.data.datasets.forEach(e=>{Hn(e)})}var gc={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(i,e,t)=>{if(!t.enabled)return void jn(i);const s=i.width;i.data.datasets.forEach((n,o)=>{const{_data:a,indexAxis:r}=n,l=i.getDatasetMeta(o),c=a||n.data;if("y"===ge([r,i.options.indexAxis])||!l.controller.supportsDecimation)return;const h=i.scales[l.xAxisID];if("linear"!==h.type&&"time"!==h.type||i.options.parsing)return;let g,{start:d,count:u}=function fc(i,e){const t=e.length;let n,s=0;const{iScale:o}=i,{min:a,max:r,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=G(yt(e,o.axis,a).lo,0,t-1)),n=c?G(yt(e,o.axis,r).hi+1,s,t)-s:t-s,{start:s,count:n}}(l,c);if(u<=(t.threshold||4*s))Hn(n);else{switch(A(a)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(p){this._data=p}})),t.algorithm){case"lttb":g=function dc(i,e,t,s,n){const o=n.samples||s;if(o>=t)return i.slice(e,e+t);const a=[],r=(t-2)/(o-2);let l=0;const c=e+t-1;let d,u,f,g,p,h=e;for(a[l++]=i[h],d=0;d<o-2;d++){let _,m=0,b=0;const y=Math.floor((d+1)*r)+1+e,M=Math.min(Math.floor((d+2)*r)+1,t)+e,x=M-y;for(_=y;_<M;_++)m+=i[_].x,b+=i[_].y;m/=x,b/=x;const v=Math.floor(d*r)+1+e,w=Math.min(Math.floor((d+1)*r)+1,t)+e,{x:k,y:C}=i[h];for(f=g=-1,_=v;_<w;_++)g=.5*Math.abs((k-m)*(i[_].y-C)-(k-i[_].x)*(b-C)),g>f&&(f=g,u=i[_],p=_);a[l++]=u,h=p}return a[l++]=i[c],a}(c,d,u,s,t);break;case"min-max":g=function uc(i,e,t,s){let a,r,l,c,h,d,u,f,g,p,n=0,o=0;const m=[],_=i[e].x,M=i[e+t-1].x-_;for(a=e;a<e+t;++a){r=i[a],l=(r.x-_)/M*s,c=r.y;const x=0|l;if(x===h)c<g?(g=c,d=a):c>p&&(p=c,u=a),n=(o*n+r.x)/++o;else{const v=a-1;if(!A(d)&&!A(u)){const w=Math.min(d,u),k=Math.max(d,u);w!==f&&w!==v&&m.push({...i[w],x:n}),k!==f&&k!==v&&m.push({...i[k],x:n})}a>0&&v!==f&&m.push(i[v]),m.push(r),h=x,o=0,g=p=c,d=u=f=a}}return m}(c,d,u,s);break;default:throw new Error(`Unsupported decimation algorithm '${t.algorithm}'`)}n._decimated=g}})},destroy(i){jn(i)}};function Yi(i,e,t,s){if(s)return;let n=e[i],o=t[i];return"angle"===i&&(n=ot(n),o=ot(o)),{property:i,start:n,end:o}}function Ui(i,e,t){for(;e>i;e--){const s=t[e];if(!isNaN(s.x)&&!isNaN(s.y))break}return e}function $n(i,e,t,s){return i&&e?s(i[t],e[t]):i?i[t]:e?e[t]:0}function Yn(i,e){let t=[],s=!1;return B(i)?(s=!0,t=i):t=function mc(i,e){const{x:t=null,y:s=null}=i||{},n=e.points,o=[];return e.segments.forEach(({start:a,end:r})=>{r=Ui(a,r,n);const l=n[a],c=n[r];null!==s?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):null!==t&&(o.push({x:t,y:l.y}),o.push({x:t,y:c.y}))}),o}(i,e),t.length?new Ze({points:t,options:{tension:0},_loop:s,_fullLoop:s}):null}function Un(i){return i&&!1!==i.fill}function bc(i,e,t){let n=i[e].fill;const o=[e];let a;if(!t)return n;for(;!1!==n&&-1===o.indexOf(n);){if(!j(n))return n;if(a=i[n],!a)return!1;if(a.visible)return n;o.push(n),n=a.fill}return!1}function _c(i,e,t){const s=function Mc(i){const e=i.options,t=e.fill;let s=P(t&&t.target,t);return void 0===s&&(s=!!e.backgroundColor),!1!==s&&null!==s&&(!0===s?"origin":s)}(i);if(T(s))return!isNaN(s.value)&&s;let n=parseFloat(s);return j(n)&&Math.floor(n)===n?function xc(i,e,t,s){return("-"===i||"+"===i)&&(t=e+t),!(t===e||t<0||t>=s)&&t}(s[0],e,n,t):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function wc(i,e,t){const s=[];for(let n=0;n<t.length;n++){const o=t[n],{first:a,last:r,point:l}=Cc(o,e,"x");if(!(!l||a&&r))if(a)s.unshift(l);else if(i.push(l),!r)break}i.push(...s)}function Cc(i,e,t){const s=i.interpolate(e,t);if(!s)return{};const n=s[t],o=i.segments,a=i.points;let r=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=a[h.start][t],u=a[h.end][t];if(xt(n,d,u)){r=n===d,l=n===u;break}}return{first:r,last:l,point:s}}class Xn{constructor(e){this.x=e.x,this.y=e.y,this.radius=e.radius}pathSegment(e,t,s){const{x:n,y:o,radius:a}=this;return e.arc(n,o,a,(t=t||{start:0,end:W}).end,t.start,!0),!s.bounds}interpolate(e){const{x:t,y:s,radius:n}=this,o=e.angle;return{x:t+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function Xi(i,e,t){const s=function Pc(i){const{chart:e,fill:t,line:s}=i;if(j(t))return function Dc(i,e){const t=i.getDatasetMeta(e);return t&&i.isDatasetVisible(e)?t.dataset:null}(e,t);if("stack"===t)return function kc(i){const{scale:e,index:t,line:s}=i,n=[],o=s.segments,a=s.points,r=function Sc(i,e){const t=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===e)break;o.hidden||t.unshift(o.dataset)}return t}(e,t);r.push(Yn({x:null,y:e.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)wc(n,a[h],r)}return new Ze({points:n,options:{}})}(i);if("shape"===t)return!0;const n=function Oc(i){return(i.scale||{}).getPointPositionForValue?function Tc(i){const{scale:e,fill:t}=i,s=e.options,n=e.getLabels().length,o=s.reverse?e.max:e.min,a=function vc(i,e,t){let s;return s="start"===i?t:"end"===i?e.options.reverse?e.min:e.max:T(i)?i.value:e.getBaseValue(),s}(t,e,o),r=[];if(s.grid.circular){const l=e.getPointPositionForValue(0,o);return new Xn({x:l.x,y:l.y,radius:e.getDistanceFromCenterForValue(a)})}for(let l=0;l<n;++l)r.push(e.getPointPositionForValue(l,a));return r}(i):function Ac(i){const{scale:e={},fill:t}=i,s=function yc(i,e){let t=null;return"start"===i?t=e.bottom:"end"===i?t=e.top:T(i)?t=e.getPixelForValue(i.value):e.getBasePixel&&(t=e.getBasePixel()),t}(t,e);if(j(s)){const n=e.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}(i)}(i);return n instanceof Xn?n:Yn(n,s)}(e),{chart:n,index:o,line:a,scale:r,axis:l}=e,c=a.options,h=c.fill,d=c.backgroundColor,{above:u=d,below:f=d}=h||{},g=n.getDatasetMeta(o),p=Ys(n,g);s&&a.points.length&&(Ie(i,t),function Lc(i,e){const{line:t,target:s,above:n,below:o,area:a,scale:r,clip:l}=e,c=t._loop?"angle":e.axis;i.save(),"x"===c&&o!==n&&(Gn(i,s,a.top),Kn(i,{line:t,target:s,color:n,scale:r,property:c,clip:l}),i.restore(),i.save(),Gn(i,s,a.bottom)),Kn(i,{line:t,target:s,color:o,scale:r,property:c,clip:l}),i.restore()}(i,{line:a,target:s,above:u,below:f,area:t,scale:r,axis:l,clip:p}),ze(i))}function Gn(i,e,t){const{segments:s,points:n}=e;let o=!0,a=!1;i.beginPath();for(const r of s){const{start:l,end:c}=r,h=n[l],d=n[Ui(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,t),i.lineTo(h.x,h.y)),a=!!e.pathSegment(i,r,{move:a}),a?i.closePath():i.lineTo(d.x,t)}i.lineTo(e.first().x,t),i.closePath(),i.clip()}function Kn(i,e){const{line:t,target:s,property:n,color:o,scale:a,clip:r}=e,l=function pc(i,e,t){const s=i.segments,n=i.points,o=e.points,a=[];for(const r of s){let{start:l,end:c}=r;c=Ui(l,c,n);const h=Yi(t,n[l],n[c],r.loop);if(!e.segments){a.push({source:r,target:h,start:n[l],end:n[c]});continue}const d=Hs(e,h);for(const u of d){const f=Yi(t,o[u.start],o[u.end],u.loop),g=Ns(r,n,f);for(const p of g)a.push({source:p,target:u,start:{[t]:$n(h,f,"start",Math.max)},end:{[t]:$n(h,f,"end",Math.min)}})}}return a}(t,s,n);for(const{source:c,target:h,start:d,end:u}of l){const{style:{backgroundColor:f=o}={}}=c,g=!0!==s;i.save(),i.fillStyle=f,Rc(i,a,r,g&&Yi(n,d,u)),i.beginPath();const p=!!t.pathSegment(i,c);let m;if(g){p?i.closePath():qn(i,s,u,n);const b=!!s.pathSegment(i,h,{move:p,reverse:!0});m=p&&b,m||qn(i,s,d,n)}i.closePath(),i.fill(m?"evenodd":"nonzero"),i.restore()}}function Rc(i,e,t,s){const n=e.chart.chartArea,{property:o,start:a,end:r}=s||{};if("x"===o||"y"===o){let l,c,h,d;"x"===o?(l=a,c=n.top,h=r,d=n.bottom):(l=n.left,c=a,h=n.right,d=r),i.beginPath(),t&&(l=Math.max(l,t.left),h=Math.min(h,t.right),c=Math.max(c,t.top),d=Math.min(d,t.bottom)),i.rect(l,c,h-l,d-c),i.clip()}}function qn(i,e,t,s){const n=e.interpolate(t,s);n&&i.lineTo(n.x,n.y)}var Ec={id:"filler",afterDatasetsUpdate(i,e,t){const s=(i.data.datasets||[]).length,n=[];let o,a,r,l;for(a=0;a<s;++a)o=i.getDatasetMeta(a),r=o.dataset,l=null,r&&r.options&&r instanceof Ze&&(l={visible:i.isDatasetVisible(a),index:a,fill:_c(r,a,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:r}),o.$filler=l,n.push(l);for(a=0;a<s;++a)l=n[a],l&&!1!==l.fill&&(l.fill=bc(n,a,t.propagate))},beforeDraw(i,e,t){const s="beforeDraw"===t.drawTime,n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let a=n.length-1;a>=0;--a){const r=n[a].$filler;r&&(r.line.updateControlPoints(o,r.axis),s&&r.fill&&Xi(i.ctx,r,o))}},beforeDatasetsDraw(i,e,t){if("beforeDatasetsDraw"!==t.drawTime)return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;Un(o)&&Xi(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,e,t){const s=e.meta.$filler;!Un(s)||"beforeDatasetDraw"!==t.drawTime||Xi(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const Zn=(i,e)=>{let{boxHeight:t=e,boxWidth:s=e}=i;return i.usePointStyle&&(t=Math.min(t,e),s=i.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:t,itemHeight:Math.max(e,t)}};class Qn extends kt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t,s){this.maxWidth=e,this.maxHeight=t,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let t=I(e.generateLabels,[this.chart],this)||[];e.filter&&(t=t.filter(s=>e.filter(s,this.chart.data))),e.sort&&(t=t.sort((s,n)=>e.sort(s,n,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){const{options:e,ctx:t}=this;if(!e.display)return void(this.width=this.height=0);const s=e.labels,n=X(s.font),o=n.size,a=this._computeTitleHeight(),{boxWidth:r,itemHeight:l}=Zn(s,o);let c,h;t.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(a,o,r,l)+10):(h=this.maxHeight,c=this._fitCols(a,n,r,l)+10),this.width=Math.min(c,e.maxWidth||this.maxWidth),this.height=Math.min(h,e.maxHeight||this.maxHeight)}_fitRows(e,t,s,n){const{ctx:o,maxWidth:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+r;let d=e;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{const m=s+t/2+o.measureText(g.text).width;(0===p||c[c.length-1]+m+2*r>a)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+r}),d}_fitCols(e,t,s,n){const{ctx:o,maxHeight:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=a-e;let d=r,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:_,itemHeight:y}=function Ic(i,e,t,s,n){const o=function zc(i,e,t,s){let n=i.text;return n&&"string"!=typeof n&&(n=n.reduce((o,a)=>o.length>a.length?o:a)),e+t.size/2+s.measureText(n).width}(s,i,e,t),a=function Bc(i,e,t){let s=i;return"string"!=typeof e.text&&(s=Jn(e,t)),s}(n,s,e.lineHeight);return{itemWidth:o,itemHeight:a}}(s,t,o,m,n);b>0&&f+y+2*r>h&&(d+=u+r,c.push({width:u,height:f}),g+=u+r,p++,u=f=0),l[b]={left:g,top:f,col:p,width:_,height:y},u=Math.max(u,_),f+=y+r}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:s,labels:{padding:n},rtl:o}}=this,a=Jt(o,this.left,this.width);if(this.isHorizontal()){let r=0,l=Z(s,this.left+n,this.right-this.lineWidths[r]);for(const c of t)r!==c.row&&(r=c.row,l=Z(s,this.left+n,this.right-this.lineWidths[r])),c.top+=this.top+e+n,c.left=a.leftForLtr(a.x(l),c.width),l+=c.width+n}else{let r=0,l=Z(s,this.top+e+n,this.bottom-this.columnSizes[r].height);for(const c of t)c.col!==r&&(r=c.col,l=Z(s,this.top+e+n,this.bottom-this.columnSizes[r].height)),c.top=l,c.left+=this.left+n,c.left=a.leftForLtr(a.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const e=this.ctx;Ie(e,this),this._draw(),ze(e)}}_draw(){const{options:e,columnSizes:t,lineWidths:s,ctx:n}=this,{align:o,labels:a}=e,r=N.color,l=Jt(e.rtl,this.left,this.width),c=X(a.font),{padding:h}=a,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=Zn(a,d),y=this.isHorizontal(),M=this._computeTitleHeight();f=y?{x:Z(o,this.left+h,this.right-s[0]),y:this.top+h+M,line:0}:{x:this.left+h,y:Z(o,this.top+M+h,this.bottom-t[0].height),line:0},zs(this.ctx,e.textDirection);const x=m+h;this.legendItems.forEach((v,w)=>{n.strokeStyle=v.fontColor,n.fillStyle=v.fontColor;const k=n.measureText(v.text).width,C=l.textAlign(v.textAlign||(v.textAlign=a.textAlign)),O=g+u+k;let D=f.x,L=f.y;l.setWidth(this.width),y?w>0&&D+O+h>this.right&&(L=f.y+=x,f.line++,D=f.x=Z(o,this.left+h,this.right-s[f.line])):w>0&&L+x>this.bottom&&(D=f.x=D+t[f.line].width+h,f.line++,L=f.y=Z(o,this.top+M+h,this.bottom-t[f.line].height)),function(v,w,k){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const C=P(k.lineWidth,1);if(n.fillStyle=P(k.fillStyle,r),n.lineCap=P(k.lineCap,"butt"),n.lineDashOffset=P(k.lineDashOffset,0),n.lineJoin=P(k.lineJoin,"miter"),n.lineWidth=C,n.strokeStyle=P(k.strokeStyle,r),n.setLineDash(P(k.lineDash,[])),a.usePointStyle){const O={radius:p*Math.SQRT2/2,pointStyle:k.pointStyle,rotation:k.rotation,borderWidth:C},D=l.xPlus(v,g/2);ws(n,O,D,w+u,a.pointStyleWidth&&g)}else{const O=w+Math.max((d-p)/2,0),D=l.leftForLtr(v,g),L=Bt(k.borderRadius);n.beginPath(),Object.values(L).some(q=>0!==q)?fe(n,{x:D,y:O,w:g,h:p,radius:L}):n.rect(D,O,g,p),n.fill(),0!==C&&n.stroke()}n.restore()}(l.x(D),L,v),D=((i,e,t,s)=>i===(s?"left":"right")?t:"center"===i?(e+t)/2:e)(C,D+g+u,y?D+O:this.right,e.rtl),function(v,w,k){zt(n,k.text,v,w+m/2,c,{strikethrough:k.hidden,textAlign:l.textAlign(k.textAlign)})}(l.x(D),L,v),y?f.x+=O+h:f.y+="string"!=typeof v.text?Jn(v,c.lineHeight)+h:x}),Bs(this.ctx,e.textDirection)}drawTitle(){const e=this.options,t=e.title,s=X(t.font),n=Q(t.padding);if(!t.display)return;const o=Jt(e.rtl,this.left,this.width),a=this.ctx,r=t.position,c=n.top+s.size/2;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=Z(e.align,d,this.right-u);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+Z(e.align,this.top,this.bottom-g-e.labels.padding-this._computeTitleHeight())}const f=Z(r,d,d+u);a.textAlign=o.textAlign(bi(r)),a.textBaseline="middle",a.strokeStyle=t.color,a.fillStyle=t.color,a.font=s.string,zt(a,t.text,f,h,s)}_computeTitleHeight(){const e=this.options.title,t=X(e.font),s=Q(e.padding);return e.display?t.lineHeight+s.height:0}_getLegendItemAt(e,t){let s,n,o;if(xt(e,this.left,this.right)&&xt(t,this.top,this.bottom))for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],xt(e,n.left,n.left+n.width)&&xt(t,n.top,n.top+n.height))return this.legendItems[s];return null}handleEvent(e){const t=this.options;if(!function Vc(i,e){return!(("mousemove"!==i&&"mouseout"!==i||!e.onHover&&!e.onLeave)&&(!e.onClick||"click"!==i&&"mouseup"!==i))}(e.type,t))return;const s=this._getLegendItemAt(e.x,e.y);if("mousemove"===e.type||"mouseout"===e.type){const n=this._hoveredItem,o=((i,e)=>null!==i&&null!==e&&i.datasetIndex===e.datasetIndex&&i.index===e.index)(n,s);n&&!o&&I(t.onLeave,[e,n,this],this),this._hoveredItem=s,s&&!o&&I(t.onHover,[e,s,this],this)}else s&&I(t.onClick,[e,s,this],this)}}function Jn(i,e){return e*(i.text?i.text.length:0)}var Wc={id:"legend",_element:Qn,start(i,e,t){const s=i.legend=new Qn({ctx:i.ctx,options:t,chart:i});J.configure(i,s,t),J.addBox(i,s)},stop(i){J.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,e,t){const s=i.legend;J.configure(i,s,t),s.options=t},afterUpdate(i){const e=i.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(i,e){e.replay||i.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,e,t){const s=e.datasetIndex,n=t.chart;n.isDatasetVisible(s)?(n.hide(s),e.hidden=!0):(n.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const e=i.data.datasets,{labels:{usePointStyle:t,pointStyle:s,textAlign:n,color:o,useBorderRadius:a,borderRadius:r}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(t?0:void 0),h=Q(c.borderWidth);return{text:e[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:a&&(r||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class Gi extends kt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t){const s=this.options;if(this.left=0,this.top=0,!s.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=e,this.height=this.bottom=t;const n=B(s.text)?s.text.length:1;this._padding=Q(s.padding);const o=n*X(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const e=this.options.position;return"top"===e||"bottom"===e}_drawArgs(e){const{top:t,left:s,bottom:n,right:o,options:a}=this,r=a.align;let c,h,d,l=0;return this.isHorizontal()?(h=Z(r,s,o),d=t+e,c=o-s):("left"===a.position?(h=s+e,d=Z(r,n,t),l=-.5*V):(h=o-e,d=Z(r,t,n),l=.5*V),c=n-t),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const e=this.ctx,t=this.options;if(!t.display)return;const s=X(t.font),o=s.lineHeight/2+this._padding.top,{titleX:a,titleY:r,maxWidth:l,rotation:c}=this._drawArgs(o);zt(e,t.text,0,0,s,{color:t.color,maxWidth:l,rotation:c,textAlign:bi(t.align),textBaseline:"middle",translation:[a,r]})}}var Hc={id:"title",_element:Gi,start(i,e,t){!function Nc(i,e){const t=new Gi({ctx:i.ctx,options:e,chart:i});J.configure(i,t,e),J.addBox(i,t),i.titleBlock=t}(i,t)},stop(i){J.removeBox(i,i.titleBlock),delete i.titleBlock},beforeUpdate(i,e,t){const s=i.titleBlock;J.configure(i,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Qe=new WeakMap;var jc={id:"subtitle",start(i,e,t){const s=new Gi({ctx:i.ctx,options:t,chart:i});J.configure(i,s,t),J.addBox(i,s),Qe.set(i,s)},stop(i){J.removeBox(i,Qe.get(i)),Qe.delete(i)},beforeUpdate(i,e,t){const s=Qe.get(i);J.configure(i,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ke={average(i){if(!i.length)return!1;let e,t,s=new Set,n=0,o=0;for(e=0,t=i.length;e<t;++e){const r=i[e].element;if(r&&r.hasValue()){const l=r.tooltipPosition();s.add(l.x),n+=l.y,++o}}return 0!==o&&0!==s.size&&{x:[...s].reduce((r,l)=>r+l)/s.size,y:n/o}},nearest(i,e){if(!i.length)return!1;let o,a,r,t=e.x,s=e.y,n=Number.POSITIVE_INFINITY;for(o=0,a=i.length;o<a;++o){const l=i[o].element;if(l&&l.hasValue()){const h=pi(e,l.getCenterPoint());h<n&&(n=h,r=l)}}if(r){const l=r.tooltipPosition();t=l.x,s=l.y}return{x:t,y:s}}};function bt(i,e){return e&&(B(e)?Array.prototype.push.apply(i,e):i.push(e)),i}function St(i){return("string"==typeof i||i instanceof String)&&i.indexOf("\n")>-1?i.split("\n"):i}function $c(i,e){const{element:t,datasetIndex:s,index:n}=e,o=i.getDatasetMeta(s).controller,{label:a,value:r}=o.getLabelAndValue(n);return{chart:i,label:a,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:r,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:t}}function to(i,e){const t=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:a,boxHeight:r}=e,l=X(e.bodyFont),c=X(e.titleFont),h=X(e.footerFont),d=o.length,u=n.length,f=s.length,g=Q(e.padding);let p=g.height,m=0,b=s.reduce((M,x)=>M+x.before.length+x.lines.length+x.after.length,0);b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),b&&(p+=f*(e.displayColors?Math.max(r,l.lineHeight):l.lineHeight)+(b-f)*l.lineHeight+(b-1)*e.bodySpacing),u&&(p+=e.footerMarginTop+u*h.lineHeight+(u-1)*e.footerSpacing);let _=0;const y=function(M){m=Math.max(m,t.measureText(M).width+_)};return t.save(),t.font=c.string,E(i.title,y),t.font=l.string,E(i.beforeBody.concat(i.afterBody),y),_=e.displayColors?a+2+e.boxPadding:0,E(s,M=>{E(M.before,y),E(M.lines,y),E(M.after,y)}),_=0,t.font=h.string,E(i.footer,y),t.restore(),m+=g.width,{width:m,height:p}}function Xc(i,e,t,s){const{x:n,width:o}=t,{width:a,chartArea:{left:r,right:l}}=i;let c="center";return"center"===s?c=n<=(r+l)/2?"left":"right":n<=o/2?c="left":n>=a-o/2&&(c="right"),function Uc(i,e,t,s){const{x:n,width:o}=s,a=t.caretSize+t.caretPadding;if("left"===i&&n+o+a>e.width||"right"===i&&n-o-a<0)return!0}(c,i,e,t)&&(c="center"),c}function eo(i,e,t){const s=t.yAlign||e.yAlign||function Yc(i,e){const{y:t,height:s}=e;return t<s/2?"top":t>i.height-s/2?"bottom":"center"}(i,t);return{xAlign:t.xAlign||e.xAlign||Xc(i,e,t,s),yAlign:s}}function io(i,e,t,s){const{caretSize:n,caretPadding:o,cornerRadius:a}=i,{xAlign:r,yAlign:l}=t,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Bt(a);let g=function Gc(i,e){let{x:t,width:s}=i;return"right"===e?t-=s:"center"===e&&(t-=s/2),t}(e,r);const p=function Kc(i,e,t){let{y:s,height:n}=i;return"top"===e?s+=t:s-="bottom"===e?n+t:n/2,s}(e,l,c);return"center"===l?"left"===r?g+=c:"right"===r&&(g-=c):"left"===r?g-=Math.max(h,u)+n:"right"===r&&(g+=Math.max(d,f)+n),{x:G(g,0,s.width-e.width),y:G(p,0,s.height-e.height)}}function Je(i,e,t){const s=Q(t.padding);return"center"===e?i.x+i.width/2:"right"===e?i.x+i.width-s.right:i.x+s.left}function so(i){return bt([],St(i))}function no(i,e){const t=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return t?i.override(t):i}const oo={beforeTitle:_t,title(i){if(i.length>0){const e=i[0],t=e.chart.data.labels,s=t?t.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return t[e.dataIndex]}return""},afterTitle:_t,beforeBody:_t,beforeLabel:_t,label(i){if(this&&this.options&&"dataset"===this.options.mode)return i.label+": "+i.formattedValue||i.formattedValue;let e=i.dataset.label||"";e&&(e+=": ");const t=i.formattedValue;return A(t)||(e+=t),e},labelColor(i){const t=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const t=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:_t,afterBody:_t,beforeFooter:_t,footer:_t,afterFooter:_t};function it(i,e,t,s){const n=i[e].call(t,s);return typeof n>"u"?oo[e].call(t,s):n}let ao=(()=>class i extends kt{static positioners=ke;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const s=this.chart,n=this.options.setContext(this.getContext()),o=n.enabled&&s.options.animation&&n.animations,a=new Xs(this.chart,o);return o._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){return this.$context||(this.$context=function qc(i,e,t){return At(i,{tooltip:e,tooltipItems:t,type:"tooltip"})}(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,s){const{callbacks:n}=s,o=it(n,"beforeTitle",this,t),a=it(n,"title",this,t),r=it(n,"afterTitle",this,t);let l=[];return l=bt(l,St(o)),l=bt(l,St(a)),l=bt(l,St(r)),l}getBeforeBody(t,s){return so(it(s.callbacks,"beforeBody",this,t))}getBody(t,s){const{callbacks:n}=s,o=[];return E(t,a=>{const r={before:[],lines:[],after:[]},l=no(n,a);bt(r.before,St(it(l,"beforeLabel",this,a))),bt(r.lines,it(l,"label",this,a)),bt(r.after,St(it(l,"afterLabel",this,a))),o.push(r)}),o}getAfterBody(t,s){return so(it(s.callbacks,"afterBody",this,t))}getFooter(t,s){const{callbacks:n}=s,o=it(n,"beforeFooter",this,t),a=it(n,"footer",this,t),r=it(n,"afterFooter",this,t);let l=[];return l=bt(l,St(o)),l=bt(l,St(a)),l=bt(l,St(r)),l}_createItems(t){const s=this._active,n=this.chart.data,o=[],a=[],r=[];let c,h,l=[];for(c=0,h=s.length;c<h;++c)l.push($c(this.chart,s[c]));return t.filter&&(l=l.filter((d,u,f)=>t.filter(d,u,f,n))),t.itemSort&&(l=l.sort((d,u)=>t.itemSort(d,u,n))),E(l,d=>{const u=no(t.callbacks,d);o.push(it(u,"labelColor",this,d)),a.push(it(u,"labelPointStyle",this,d)),r.push(it(u,"labelTextColor",this,d))}),this.labelColors=o,this.labelPointStyles=a,this.labelTextColors=r,this.dataPoints=l,l}update(t,s){const n=this.options.setContext(this.getContext()),o=this._active;let a,r=[];if(o.length){const l=ke[n.position].call(this,o,this._eventPosition);r=this._createItems(n),this.title=this.getTitle(r,n),this.beforeBody=this.getBeforeBody(r,n),this.body=this.getBody(r,n),this.afterBody=this.getAfterBody(r,n),this.footer=this.getFooter(r,n);const c=this._size=to(this,n),h=Object.assign({},l,c),d=eo(this.chart,n,h),u=io(n,h,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,a={opacity:1,x:u.x,y:u.y,width:c.width,height:c.height,caretX:l.x,caretY:l.y}}else 0!==this.opacity&&(a={opacity:0});this._tooltipItems=r,this.$context=void 0,a&&this._resolveAnimations().update(this,a),t&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:s})}drawCaret(t,s,n,o){const a=this.getCaretPosition(t,n,o);s.lineTo(a.x1,a.y1),s.lineTo(a.x2,a.y2),s.lineTo(a.x3,a.y3)}getCaretPosition(t,s,n){const{xAlign:o,yAlign:a}=this,{caretSize:r,cornerRadius:l}=n,{topLeft:c,topRight:h,bottomLeft:d,bottomRight:u}=Bt(l),{x:f,y:g}=t,{width:p,height:m}=s;let b,_,y,M,x,v;return"center"===a?(x=g+m/2,"left"===o?(b=f,_=b-r,M=x+r,v=x-r):(b=f+p,_=b+r,M=x-r,v=x+r),y=b):(_="left"===o?f+Math.max(c,d)+r:"right"===o?f+p-Math.max(h,u)-r:this.caretX,"top"===a?(M=g,x=M-r,b=_-r,y=_+r):(M=g+m,x=M+r,b=_+r,y=_-r),v=M),{x1:b,x2:_,x3:y,y1:M,y2:x,y3:v}}drawTitle(t,s,n){const o=this.title,a=o.length;let r,l,c;if(a){const h=Jt(n.rtl,this.x,this.width);for(t.x=Je(this,n.titleAlign,n),s.textAlign=h.textAlign(n.titleAlign),s.textBaseline="middle",r=X(n.titleFont),l=n.titleSpacing,s.fillStyle=n.titleColor,s.font=r.string,c=0;c<a;++c)s.fillText(o[c],h.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+l,c+1===a&&(t.y+=n.titleMarginBottom-l)}}_drawColorBox(t,s,n,o,a){const r=this.labelColors[n],l=this.labelPointStyles[n],{boxHeight:c,boxWidth:h}=a,d=X(a.bodyFont),u=Je(this,"left",a),f=o.x(u),p=s.y+(c<d.lineHeight?(d.lineHeight-c)/2:0);if(a.usePointStyle){const m={radius:Math.min(h,c)/2,pointStyle:l.pointStyle,rotation:l.rotation,borderWidth:1},b=o.leftForLtr(f,h)+h/2,_=p+c/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,Mi(t,m,b,_),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,Mi(t,m,b,_)}else{t.lineWidth=T(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const m=o.leftForLtr(f,h),b=o.leftForLtr(o.xPlus(f,1),h-2),_=Bt(r.borderRadius);Object.values(_).some(y=>0!==y)?(t.beginPath(),t.fillStyle=a.multiKeyBackground,fe(t,{x:m,y:p,w:h,h:c,radius:_}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),fe(t,{x:b,y:p+1,w:h-2,h:c-2,radius:_}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(m,p,h,c),t.strokeRect(m,p,h,c),t.fillStyle=r.backgroundColor,t.fillRect(b,p+1,h-2,c-2))}t.fillStyle=this.labelTextColors[n]}drawBody(t,s,n){const{body:o}=this,{bodySpacing:a,bodyAlign:r,displayColors:l,boxHeight:c,boxWidth:h,boxPadding:d}=n,u=X(n.bodyFont);let f=u.lineHeight,g=0;const p=Jt(n.rtl,this.x,this.width),m=function(C){s.fillText(C,p.x(t.x+g),t.y+f/2),t.y+=f+a},b=p.textAlign(r);let _,y,M,x,v,w,k;for(s.textAlign=r,s.textBaseline="middle",s.font=u.string,t.x=Je(this,b,n),s.fillStyle=n.bodyColor,E(this.beforeBody,m),g=l&&"right"!==b?"center"===r?h/2+d:h+2+d:0,x=0,w=o.length;x<w;++x){for(_=o[x],y=this.labelTextColors[x],s.fillStyle=y,E(_.before,m),M=_.lines,l&&M.length&&(this._drawColorBox(s,t,x,p,n),f=Math.max(u.lineHeight,c)),v=0,k=M.length;v<k;++v)m(M[v]),f=u.lineHeight;E(_.after,m)}g=0,f=u.lineHeight,E(this.afterBody,m),t.y-=a}drawFooter(t,s,n){const o=this.footer,a=o.length;let r,l;if(a){const c=Jt(n.rtl,this.x,this.width);for(t.x=Je(this,n.footerAlign,n),t.y+=n.footerMarginTop,s.textAlign=c.textAlign(n.footerAlign),s.textBaseline="middle",r=X(n.footerFont),s.fillStyle=n.footerColor,s.font=r.string,l=0;l<a;++l)s.fillText(o[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+n.footerSpacing}}drawBackground(t,s,n,o){const{xAlign:a,yAlign:r}=this,{x:l,y:c}=t,{width:h,height:d}=n,{topLeft:u,topRight:f,bottomLeft:g,bottomRight:p}=Bt(o.cornerRadius);s.fillStyle=o.backgroundColor,s.strokeStyle=o.borderColor,s.lineWidth=o.borderWidth,s.beginPath(),s.moveTo(l+u,c),"top"===r&&this.drawCaret(t,s,n,o),s.lineTo(l+h-f,c),s.quadraticCurveTo(l+h,c,l+h,c+f),"center"===r&&"right"===a&&this.drawCaret(t,s,n,o),s.lineTo(l+h,c+d-p),s.quadraticCurveTo(l+h,c+d,l+h-p,c+d),"bottom"===r&&this.drawCaret(t,s,n,o),s.lineTo(l+g,c+d),s.quadraticCurveTo(l,c+d,l,c+d-g),"center"===r&&"left"===a&&this.drawCaret(t,s,n,o),s.lineTo(l,c+u),s.quadraticCurveTo(l,c,l+u,c),s.closePath(),s.fill(),o.borderWidth>0&&s.stroke()}_updateAnimationTarget(t){const s=this.chart,n=this.$animations,o=n&&n.x,a=n&&n.y;if(o||a){const r=ke[t.position].call(this,this._active,this._eventPosition);if(!r)return;const l=this._size=to(this,t),c=Object.assign({},r,this._size),h=eo(s,t,c),d=io(t,c,h,s);(o._to!==d.x||a._to!==d.y)&&(this.xAlign=h.xAlign,this.yAlign=h.yAlign,this.width=l.width,this.height=l.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(t){const s=this.options.setContext(this.getContext());let n=this.opacity;if(!n)return;this._updateAnimationTarget(s);const o={width:this.width,height:this.height},a={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;const r=Q(s.padding);s.enabled&&(this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length)&&(t.save(),t.globalAlpha=n,this.drawBackground(a,t,o,s),zs(t,s.textDirection),a.y+=r.top,this.drawTitle(a,t,s),this.drawBody(a,t,s),this.drawFooter(a,t,s),Bs(t,s.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,s){const n=this._active,o=t.map(({datasetIndex:l,index:c})=>{const h=this.chart.getDatasetMeta(l);if(!h)throw new Error("Cannot find a dataset at index "+l);return{datasetIndex:l,element:h.data[c],index:c}}),a=!Ae(n,o),r=this._positionChanged(o,s);(a||r)&&(this._active=o,this._eventPosition=s,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,s,n=!0){if(s&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const o=this.options,a=this._active||[],r=this._getActiveElements(t,a,s,n),l=this._positionChanged(r,t),c=s||!Ae(r,a)||l;return c&&(this._active=r,(o.enabled||o.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,s))),c}_getActiveElements(t,s,n,o){const a=this.options;if("mouseout"===t.type)return[];if(!o)return s.filter(l=>this.chart.data.datasets[l.datasetIndex]&&void 0!==this.chart.getDatasetMeta(l.datasetIndex).controller.getParsed(l.index));const r=this.chart.getElementsAtEventForMode(t,a.mode,a,n);return a.reverse&&r.reverse(),r}_positionChanged(t,s){const{caretX:n,caretY:o,options:a}=this,r=ke[a.position].call(this,t,s);return!1!==r&&(n!==r.x||o!==r.y)}})();var Qc=Object.freeze({__proto__:null,Colors:hc,Decimation:gc,Filler:Ec,Legend:Wc,SubTitle:jc,Title:Hc,Tooltip:{id:"tooltip",_element:ao,positioners:ke,afterInit(i,e,t){t&&(i.tooltip=new ao({chart:i,options:t}))},beforeUpdate(i,e,t){i.tooltip&&i.tooltip.initialize(t)},reset(i,e,t){i.tooltip&&i.tooltip.initialize(t)},afterDraw(i){const e=i.tooltip;if(e&&e._willRender()){const t={tooltip:e};if(!1===i.notifyPlugins("beforeTooltipDraw",{...t,cancelable:!0}))return;e.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",t)}},afterEvent(i,e){i.tooltip&&i.tooltip.handleEvent(e.event,e.replay,e.inChartArea)&&(e.changed=!0)},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,e)=>e.bodyFont.size,boxWidth:(i,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:oo},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>"filter"!==i&&"itemSort"!==i&&"external"!==i,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]}});function ro(i){const e=this.getLabels();return i>=0&&i<e.length?e[i]:i}let ih=(()=>class i extends jt{static id="category";static defaults={ticks:{callback:ro}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const s=this._addedLabels;if(s.length){const n=this.getLabels();for(const{index:o,label:a}of s)n[o]===a&&n.splice(o,1);this._addedLabels=[]}super.init(t)}parse(t,s){if(A(t))return null;const n=this.getLabels();return((i,e)=>null===i?null:G(Math.round(i),0,e))(s=isFinite(s)&&n[s]===t?s:function th(i,e,t,s){const n=i.indexOf(e);return-1===n?((i,e,t,s)=>("string"==typeof e?(t=i.push(e)-1,s.unshift({index:t,label:e})):isNaN(e)&&(t=null),t))(i,e,t,s):n!==i.lastIndexOf(e)?t:n}(n,t,P(s,t),this._addedLabels),n.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(n=0),s||(o=this.getLabels().length-1)),this.min=n,this.max=o}buildTicks(){const t=this.min,s=this.max,n=this.options.offset,o=[];let a=this.getLabels();a=0===t&&s===a.length-1?a:a.slice(t,s+1),this._valueRange=Math.max(a.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let r=t;r<=s;r++)o.push({value:r});return o}getLabelForValue(t){return ro.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const s=this.ticks;return t<0||t>s.length-1?null:this.getPixelForValue(s[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}})();function lo(i,e,{horizontal:t,minRotation:s}){const n=dt(s),o=(t?Math.sin(n):Math.cos(n))||.001;return Math.min(e/o,.75*e*(""+i).length)}class ti extends jt{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,t){return A(e)||("number"==typeof e||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:t,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const a=l=>n=t?n:l,r=l=>o=s?o:l;if(e){const l=pt(n),c=pt(o);l<0&&c<0?r(0):l>0&&c>0&&a(0)}if(n===o){let l=0===o?1:Math.abs(.05*o);r(o+l),e||a(n-l)}this.min=n,this.max=o}getTickLimit(){const e=this.options.ticks;let n,{maxTicksLimit:t,stepSize:s}=e;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),t=t||11),t&&(n=Math.min(t,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,t=e.ticks;let s=this.getTickLimit();s=Math.max(2,s);const a=function sh(i,e){const t=[],{bounds:n,step:o,min:a,max:r,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,g=h-1,{min:p,max:m}=e,b=!A(a),_=!A(r),y=!A(c),M=(m-p)/(d+1);let v,w,k,C,x=ls((m-p)/g/f)*f;if(x<1e-14&&!b&&!_)return[{value:p},{value:m}];C=Math.ceil(m/x)-Math.floor(p/x),C>g&&(x=ls(C*x/g/f)*f),A(l)||(v=Math.pow(10,l),x=Math.ceil(x*v)/v),"ticks"===n?(w=Math.floor(p/x)*x,k=Math.ceil(m/x)*x):(w=p,k=m),b&&_&&o&&function jo(i,e){const t=Math.round(i);return t-e<=i&&t+e>=i}((r-a)/o,x/1e3)?(C=Math.round(Math.min((r-a)/x,h)),x=(r-a)/C,w=a,k=r):y?(w=b?a:w,k=_?r:k,C=c-1,x=(k-w)/C):(C=(k-w)/x,C=le(C,Math.round(C),x/1e3)?Math.round(C):Math.ceil(C));const O=Math.max(hs(x),hs(w));v=Math.pow(10,A(l)?O:l),w=Math.round(w*v)/v,k=Math.round(k*v)/v;let D=0;for(b&&(u&&w!==a?(t.push({value:a}),w<a&&D++,le(Math.round((w+D*x)*v)/v,a,lo(a,M,i))&&D++):w<a&&D++);D<C;++D){const L=Math.round((w+D*x)*v)/v;if(_&&L>r)break;t.push({value:L})}return _&&u&&k!==r?t.length&&le(t[t.length-1].value,r,lo(r,M,i))?t[t.length-1].value=r:t.push({value:r}):(!_||k===r)&&t.push({value:k}),t}({maxTicks:s,bounds:e.bounds,min:e.min,max:e.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:!1!==t.includeBounds},this._range||this);return"ticks"===e.bounds&&cs(a,this,"value"),e.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){const e=this.ticks;let t=this.min,s=this.max;if(super.configure(),this.options.offset&&e.length){const n=(s-t)/Math.max(e.length-1,1)/2;t-=n,s+=n}this._startValue=t,this._endValue=s,this._valueRange=s-t}getLabelForValue(e){return de(e,this.chart.options.locale,this.options.ticks.format)}}const Se=i=>Math.floor(Ot(i)),$t=(i,e)=>Math.pow(10,Se(i)+e);function co(i){return i/Math.pow(10,Se(i))==1}function ho(i,e,t){const s=Math.pow(10,t),n=Math.floor(i/s);return Math.ceil(e/s)-n}function Ki(i){const e=i.ticks;if(e.display&&i.display){const t=Q(e.backdropPadding);return P(e.font&&e.font.size,N.font.size)+t.height}return 0}function lh(i,e,t){return t=B(t)?t:[t],{w:aa(i,e.string,t),h:t.length*e.lineHeight}}function uo(i,e,t,s,n){return i===s||i===n?{start:e-t/2,end:e+t/2}:i<s||i>n?{start:e-t,end:e}:{start:e,end:e+t}}function hh(i,e,t,s,n){const o=Math.abs(Math.sin(t)),a=Math.abs(Math.cos(t));let r=0,l=0;s.start<e.l?(r=(e.l-s.start)/o,i.l=Math.min(i.l,e.l-r)):s.end>e.r&&(r=(s.end-e.r)/o,i.r=Math.max(i.r,e.r+r)),n.start<e.t?(l=(e.t-n.start)/a,i.t=Math.min(i.t,e.t-l)):n.end>e.b&&(l=(n.end-e.b)/a,i.b=Math.max(i.b,e.b+l))}function dh(i,e,t){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:a,size:r}=t,l=i.getPointPosition(e,s+n+a,o),c=Math.round(gi(ot(l.angle+$))),h=function mh(i,e,t){return 90===t||270===t?i-=e/2:(t>270||t<90)&&(i-=e),i}(l.y,r.h,c),d=function gh(i){return 0===i||180===i?"center":i<180?"left":"right"}(c),u=function ph(i,e,t){return"right"===t?i-=e:"center"===t&&(i-=e/2),i}(l.x,r.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+r.w,bottom:h+r.h}}function uh(i,e){if(!e)return!0;const{left:t,top:s,right:n,bottom:o}=i;return!(vt({x:t,y:s},e)||vt({x:t,y:o},e)||vt({x:n,y:s},e)||vt({x:n,y:o},e))}function bh(i,e,t){const{left:s,top:n,right:o,bottom:a}=t,{backdropColor:r}=e;if(!A(r)){const l=Bt(e.borderRadius),c=Q(e.backdropPadding);i.fillStyle=r;const h=s-c.left,d=n-c.top,u=o-s+c.width,f=a-n+c.height;Object.values(l).some(g=>0!==g)?(i.beginPath(),fe(i,{x:h,y:d,w:u,h:f,radius:l}),i.fill()):i.fillRect(h,d,u,f)}}function fo(i,e,t,s){const{ctx:n}=i;if(t)n.arc(i.xCenter,i.yCenter,e,0,W);else{let o=i.getPointPosition(0,e);n.moveTo(o.x,o.y);for(let a=1;a<s;a++)o=i.getPointPosition(a,e),n.lineTo(o.x,o.y)}}const ei={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},st=Object.keys(ei);function go(i,e){return i-e}function po(i,e){if(A(e))return null;const t=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let a=e;return"function"==typeof s&&(a=s(a)),j(a)||(a="string"==typeof s?t.parse(a,s):t.parse(a)),null===a?null:(n&&(a="week"!==n||!qt(o)&&!0!==o?t.startOf(a,n):t.startOf(a,"isoWeek",o)),+a)}function mo(i,e,t,s){const n=st.length;for(let o=st.indexOf(i);o<n-1;++o){const a=ei[st[o]];if(a.common&&Math.ceil((t-e)/((a.steps?a.steps:Number.MAX_SAFE_INTEGER)*a.size))<=s)return st[o]}return st[n-1]}function bo(i,e,t){if(t){if(t.length){const{lo:s,hi:n}=mi(t,e);i[t[s]>=e?t[s]:t[n]]=!0}}else i[e]=!0}function _o(i,e,t){const s=[],n={},o=e.length;let a,r;for(a=0;a<o;++a)r=e[a],n[r]=a,s.push({value:r,major:!1});return 0!==o&&t?function Sh(i,e,t,s){const n=i._adapter,o=+n.startOf(e[0].value,s),a=e[e.length-1].value;let r,l;for(r=o;r<=a;r=+n.add(r,1,s))l=t[r],l>=0&&(e[l].major=!0);return e}(i,s,n,t):s}let qi=(()=>class i extends jt{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,s={}){const n=t.time||(t.time={}),o=this._adapter=new Cr__date(t.adapters.date);o.init(s),ae(n.displayFormats,o.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(t),this._normalized=s.normalized}parse(t,s){return void 0===t?null:po(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,s=this._adapter,n=t.time.unit||"day";let{min:o,max:a,minDefined:r,maxDefined:l}=this.getUserBounds();function c(h){!r&&!isNaN(h.min)&&(o=Math.min(o,h.min)),!l&&!isNaN(h.max)&&(a=Math.max(a,h.max))}(!r||!l)&&(c(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&c(this.getMinMax(!1))),o=j(o)&&!isNaN(o)?o:+s.startOf(Date.now(),n),a=j(a)&&!isNaN(a)?a:+s.endOf(Date.now(),n)+1,this.min=Math.min(o,a-1),this.max=Math.max(o+1,a)}_getLabelBounds(){const t=this.getLabelTimestamps();let s=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return t.length&&(s=t[0],n=t[t.length-1]),{min:s,max:n}}buildTicks(){const t=this.options,s=t.time,n=t.ticks,o="labels"===n.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&o.length&&(this.min=this._userMin||o[0],this.max=this._userMax||o[o.length-1]);const a=this.min,l=function Xo(i,e,t){let s=0,n=i.length;for(;s<n&&i[s]<e;)s++;for(;n>s&&i[n-1]>t;)n--;return s>0||n<i.length?i.slice(s,n):i}(o,a,this.max);return this._unit=s.unit||(n.autoSkip?mo(s.minUnit,this.min,this.max,this._getLabelCapacity(a)):function Mh(i,e,t,s,n){for(let o=st.length-1;o>=st.indexOf(t);o--){const a=st[o];if(ei[a].common&&i._adapter.diff(n,s,a)>=e-1)return a}return st[t?st.indexOf(t):0]}(this,l.length,s.minUnit,this.min,this.max)),this._majorUnit=n.major.enabled&&"year"!==this._unit?function kh(i){for(let e=st.indexOf(i)+1,t=st.length;e<t;++e)if(ei[st[e]].common)return st[e]}(this._unit):void 0,this.initOffsets(o),t.reverse&&l.reverse(),_o(this,l,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let o,a,s=0,n=0;this.options.offset&&t.length&&(o=this.getDecimalForValue(t[0]),s=1===t.length?1-o:(this.getDecimalForValue(t[1])-o)/2,a=this.getDecimalForValue(t[t.length-1]),n=1===t.length?a:(a-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;s=G(s,0,r),n=G(n,0,r),this._offsets={start:s,end:n,factor:1/(s+1+n)}}_generate(){const t=this._adapter,s=this.min,n=this.max,o=this.options,a=o.time,r=a.unit||mo(a.minUnit,s,n,this._getLabelCapacity(s)),l=P(o.ticks.stepSize,1),c="week"===r&&a.isoWeekday,h=qt(c)||!0===c,d={};let f,g,u=s;if(h&&(u=+t.startOf(u,"isoWeek",c)),u=+t.startOf(u,h?"day":r),t.diff(n,s,r)>1e5*l)throw new Error(s+" and "+n+" are too far apart with stepSize of "+l+" "+r);const p="data"===o.ticks.source&&this.getDataTimestamps();for(f=u,g=0;f<n;f=+t.add(f,l,r),g++)bo(d,f,p);return(f===n||"ticks"===o.bounds||1===g)&&bo(d,f,p),Object.keys(d).sort(go).map(m=>+m)}getLabelForValue(t){const n=this.options.time;return this._adapter.format(t,n.tooltipFormat?n.tooltipFormat:n.displayFormats.datetime)}format(t,s){return this._adapter.format(t,s||this.options.time.displayFormats[this._unit])}_tickFormatFunction(t,s,n,o){const a=this.options,r=a.ticks.callback;if(r)return I(r,[t,s,n],this);const l=a.time.displayFormats,c=this._unit,h=this._majorUnit,u=h&&l[h],f=n[s];return this._adapter.format(t,o||(h&&u&&f&&f.major?u:c&&l[c]))}generateTickLabels(t){let s,n,o;for(s=0,n=t.length;s<n;++s)o=t[s],o.label=this._tickFormatFunction(o.value,s,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const s=this._offsets,n=this.getDecimalForValue(t);return this.getPixelForDecimal((s.start+n)*s.factor)}getValueForPixel(t){const s=this._offsets,n=this.getDecimalForPixel(t)/s.factor-s.end;return this.min+n*(this.max-this.min)}_getLabelSize(t){const s=this.options.ticks,n=this.ctx.measureText(t).width,o=dt(this.isHorizontal()?s.maxRotation:s.minRotation),a=Math.cos(o),r=Math.sin(o),l=this._resolveTickFontOptions(0).size;return{w:n*a+l*r,h:n*r+l*a}}_getLabelCapacity(t){const s=this.options.time,n=s.displayFormats,o=n[s.unit]||n.millisecond,a=this._tickFormatFunction(t,0,_o(this,[t],this._majorUnit),o),r=this._getLabelSize(a),l=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return l>0?l:1}getDataTimestamps(){let s,n,t=this._cache.data||[];if(t.length)return t;const o=this.getMatchingVisibleMetas();if(this._normalized&&o.length)return this._cache.data=o[0].controller.getAllParsedValues(this);for(s=0,n=o.length;s<n;++s)t=t.concat(o[s].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let s,n;if(t.length)return t;const o=this.getLabels();for(s=0,n=o.length;s<n;++s)t.push(po(this,o[s]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return gs(t.sort(go))}})();function ii(i,e,t){let o,a,r,l,s=0,n=i.length-1;t?(e>=i[s].pos&&e<=i[n].pos&&({lo:s,hi:n}=yt(i,"pos",e)),({pos:o,time:r}=i[s]),({pos:a,time:l}=i[n])):(e>=i[s].time&&e<=i[n].time&&({lo:s,hi:n}=yt(i,"time",e)),({time:o,pos:r}=i[s]),({time:a,pos:l}=i[n]));const c=a-o;return c?r+(l-r)*(e-o)/c:r}Wi.register(wr,sc,Qc,Object.freeze({__proto__:null,CategoryScale:ih,LinearScale:class nh extends ti{static id="linear";static defaults={ticks:{callback:Ee.formatters.numeric}};determineDataLimits(){const{min:e,max:t}=this.getMinMax(!0);this.min=j(e)?e:0,this.max=j(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),t=e?this.width:this.height,s=dt(this.options.ticks.minRotation),n=(e?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,o.lineHeight/n))}getPixelForValue(e){return null===e?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}},LogarithmicScale:class rh extends jt{static id="logarithmic";static defaults={ticks:{callback:Ee.formatters.logarithmic,major:{enabled:!0}}};constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(e,t){const s=ti.prototype.parse.apply(this,[e,t]);if(0!==s)return j(s)&&s>0?s:null;this._zero=!0}determineDataLimits(){const{min:e,max:t}=this.getMinMax(!0);this.min=j(e)?Math.max(0,e):null,this.max=j(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!j(this._userMin)&&(this.min=e===$t(this.min,0)?$t(this.min,-1):$t(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:e,maxDefined:t}=this.getUserBounds();let s=this.min,n=this.max;const o=r=>s=e?s:r,a=r=>n=t?n:r;s===n&&(s<=0?(o(1),a(10)):(o($t(s,-1)),a($t(n,1)))),s<=0&&o($t(n,-1)),n<=0&&a($t(s,1)),this.min=s,this.max=n}buildTicks(){const e=this.options,s=function ah(i,{min:e,max:t}){e=nt(i.min,e);const s=[],n=Se(e);let o=function oh(i,e){let s=Se(e-i);for(;ho(i,e,s)>10;)s++;for(;ho(i,e,s)<10;)s--;return Math.min(s,Se(i))}(e,t),a=o<0?Math.pow(10,Math.abs(o)):1;const r=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((e-l)*a)/a,h=Math.floor((e-l)/r/10)*r*10;let d=Math.floor((c-h)/Math.pow(10,o)),u=nt(i.min,Math.round((l+h+d*Math.pow(10,o))*a)/a);for(;u<t;)s.push({value:u,major:co(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,a=o>=0?1:a),u=Math.round((l+h+d*Math.pow(10,o))*a)/a;const f=nt(i.max,u);return s.push({value:f,major:co(f),significand:d}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===e.bounds&&cs(s,this,"value"),e.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(e){return void 0===e?"0":de(e,this.chart.options.locale,this.options.ticks.format)}configure(){const e=this.min;super.configure(),this._startValue=Ot(e),this._valueRange=Ot(this.max)-Ot(e)}getPixelForValue(e){return(void 0===e||0===e)&&(e=this.min),null===e||isNaN(e)?NaN:this.getPixelForDecimal(e===this.min?0:(Ot(e)-this._startValue)/this._valueRange)}getValueForPixel(e){const t=this.getDecimalForPixel(e);return Math.pow(10,this._startValue+t*this._valueRange)}},RadialLinearScale:class vh extends ti{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ee.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:e=>e,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(e){super(e),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const e=this._padding=Q(Ki(this.options)/2),t=this.width=this.maxWidth-e.width,s=this.height=this.maxHeight-e.height;this.xCenter=Math.floor(this.left+t/2+e.left),this.yCenter=Math.floor(this.top+s/2+e.top),this.drawingArea=Math.floor(Math.min(t,s)/2)}determineDataLimits(){const{min:e,max:t}=this.getMinMax(!1);this.min=j(e)&&!isNaN(e)?e:0,this.max=j(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Ki(this.options))}generateTickLabels(e){ti.prototype.generateTickLabels.call(this,e),this._pointLabels=this.getLabels().map((t,s)=>{const n=I(this.options.pointLabels.callback,[t,s],this);return n||0===n?n:""}).filter((t,s)=>this.chart.getDataVisibility(s))}fit(){const e=this.options;e.display&&e.pointLabels.display?function ch(i){const e={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},t=Object.assign({},e),s=[],n=[],o=i._pointLabels.length,a=i.options.pointLabels,r=a.centerPointLabels?V/o:0;for(let l=0;l<o;l++){const c=a.setContext(i.getPointLabelContext(l));n[l]=c.padding;const h=i.getPointPosition(l,i.drawingArea+n[l],r),d=X(c.font),u=lh(i.ctx,d,i._pointLabels[l]);s[l]=u;const f=ot(i.getIndexAngle(l)+r),g=Math.round(gi(f));hh(t,e,f,uo(g,h.x,u.w,0,180),uo(g,h.y,u.h,90,270))}i.setCenterPoint(e.l-t.l,t.r-e.r,e.t-t.t,t.b-e.b),i._pointLabelItems=function fh(i,e,t){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:a,display:r}=o.pointLabels,l={extra:Ki(o)/2,additionalAngle:a?V/n:0};let c;for(let h=0;h<n;h++){l.padding=t[h],l.size=e[h];const d=dh(i,h,l);s.push(d),"auto"===r&&(d.visible=uh(d,c),d.visible&&(c=d))}return s}(i,s,n)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(e,t,s,n){this.xCenter+=Math.floor((e-t)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(e,t,s,n))}getIndexAngle(e){return ot(e*(W/(this._pointLabels.length||1))+dt(this.options.startAngle||0))}getDistanceFromCenterForValue(e){if(A(e))return NaN;const t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-e)*t:(e-this.min)*t}getValueForDistanceFromCenter(e){if(A(e))return NaN;const t=e/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(e){const t=this._pointLabels||[];if(e>=0&&e<t.length){const s=t[e];return function yh(i,e,t){return At(i,{label:t,index:e,type:"pointLabel"})}(this.getContext(),e,s)}}getPointPosition(e,t,s=0){const n=this.getIndexAngle(e)-$+s;return{x:Math.cos(n)*t+this.xCenter,y:Math.sin(n)*t+this.yCenter,angle:n}}getPointPositionForValue(e,t){return this.getPointPosition(e,this.getDistanceFromCenterForValue(t))}getBasePosition(e){return this.getPointPositionForValue(e||0,this.getBaseValue())}getPointLabelPosition(e){const{left:t,top:s,right:n,bottom:o}=this._pointLabelItems[e];return{left:t,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:e,grid:{circular:t}}=this.options;if(e){const s=this.ctx;s.save(),s.beginPath(),fo(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),s.closePath(),s.fillStyle=e,s.fill(),s.restore()}}drawGrid(){const e=this.ctx,t=this.options,{angleLines:s,grid:n,border:o}=t,a=this._pointLabels.length;let r,l,c;if(t.pointLabels.display&&function _h(i,e){const{ctx:t,options:{pointLabels:s}}=i;for(let n=e-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const a=s.setContext(i.getPointLabelContext(n));bh(t,a,o);const r=X(a.font),{x:l,y:c,textAlign:h}=o;zt(t,i._pointLabels[n],l,c+r.lineHeight/2,r,{color:a.color,textAlign:h,textBaseline:"middle"})}}(this,a),n.display&&this.ticks.forEach((h,d)=>{if(0!==d||0===d&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=n.setContext(u),g=o.setContext(u);!function xh(i,e,t,s,n){const o=i.ctx,a=e.circular,{color:r,lineWidth:l}=e;!a&&!s||!r||!l||t<0||(o.save(),o.strokeStyle=r,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),fo(i,t,a,s),o.closePath(),o.stroke(),o.restore())}(this,f,l,a,g)}}),s.display){for(e.save(),r=a-1;r>=0;r--){const h=s.setContext(this.getPointLabelContext(r)),{color:d,lineWidth:u}=h;!u||!d||(e.lineWidth=u,e.strokeStyle=d,e.setLineDash(h.borderDash),e.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),c=this.getPointPosition(r,l),e.beginPath(),e.moveTo(this.xCenter,this.yCenter),e.lineTo(c.x,c.y),e.stroke())}e.restore()}}drawBorder(){}drawLabels(){const e=this.ctx,t=this.options,s=t.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,a;e.save(),e.translate(this.xCenter,this.yCenter),e.rotate(n),e.textAlign="center",e.textBaseline="middle",this.ticks.forEach((r,l)=>{if(0===l&&this.min>=0&&!t.reverse)return;const c=s.setContext(this.getContext(l)),h=X(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){e.font=h.string,a=e.measureText(r.label).width,e.fillStyle=c.backdropColor;const d=Q(c.backdropPadding);e.fillRect(-a/2-d.left,-o-h.size/2-d.top,a+d.width,h.size+d.height)}zt(e,r.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),e.restore()}drawTitle(){}},TimeScale:qi,TimeSeriesScale:class wh extends qi{static id="timeseries";static defaults=qi.defaults;constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(e);this._minPos=ii(t,this.min),this._tableRange=ii(t,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:t,max:s}=this,n=[],o=[];let a,r,l,c,h;for(a=0,r=e.length;a<r;++a)c=e[a],c>=t&&c<=s&&n.push(c);if(n.length<2)return[{time:t,pos:0},{time:s,pos:1}];for(a=0,r=n.length;a<r;++a)h=n[a+1],l=n[a-1],c=n[a],Math.round((h+l)/2)!==c&&o.push({time:c,pos:a/(r-1)});return o}_generate(){const e=this.min,t=this.max;let s=super.getDataTimestamps();return(!s.includes(e)||!s.length)&&s.splice(0,0,e),(!s.includes(t)||1===s.length)&&s.push(t),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const t=this.getDataTimestamps(),s=this.getLabelTimestamps();return e=t.length&&s.length?this.normalize(t.concat(s)):t.length?t:s,e=this._cache.all=e,e}getDecimalForValue(e){return(ii(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const t=this._offsets,s=this.getDecimalForPixel(e)/t.factor-t.end;return ii(this._table,s*this._tableRange+this._minPos,!0)}}}));const Dh=Wi;let Oh=(()=>{class i{platformId;el;type;plugins=[];width;height;responsive=!0;ariaLabel;ariaLabelledBy;get data(){return this._data}set data(t){this._data=t,this.reinit()}get options(){return this._options}set options(t){this._options=t,this.reinit()}onDataSelect=new U.bkB;isBrowser=!1;initialized;_data;_options={};chart;constructor(t,s){this.platformId=t,this.el=s}ngAfterViewInit(){this.initChart(),this.initialized=!0}onCanvasClick(t){if(this.chart){const s=this.chart.getElementsAtEventForMode(t,"nearest",{intersect:!0},!1),n=this.chart.getElementsAtEventForMode(t,"dataset",{intersect:!0},!1);s&&s[0]&&n&&this.onDataSelect.emit({originalEvent:t,element:s[0],dataset:n})}}initChart(){if((0,S.UE)(this.platformId)){let t=this.options||{};t.responsive=this.responsive,t.responsive&&(this.height||this.width)&&(t.maintainAspectRatio=!1),this.chart=new Dh(this.el.nativeElement.children[0].children[0],{type:this.type,data:this.data,options:this.options,plugins:this.plugins})}}getCanvas(){return this.el.nativeElement.children[0].children[0]}getBase64Image(){return this.chart.toBase64Image()}generateLegend(){if(this.chart)return this.chart.generateLegend()}refresh(){this.chart&&this.chart.update()}reinit(){this.chart&&(this.chart.destroy(),this.initChart())}ngOnDestroy(){this.chart&&(this.chart.destroy(),this.initialized=!1,this.chart=null)}static \u0275fac=function(s){return new(s||i)(U.rXU(U.Agw),U.rXU(U.aKT))};static \u0275cmp=U.VBU({type:i,selectors:[["p-chart"]],hostAttrs:[1,"p-element"],inputs:{type:"type",plugins:"plugins",width:"width",height:"height",responsive:"responsive",ariaLabel:"ariaLabel",ariaLabelledBy:"ariaLabelledBy",data:"data",options:"options"},outputs:{onDataSelect:"onDataSelect"},decls:2,vars:8,consts:[[2,"position","relative"],["role","img",3,"click"]],template:function(s,n){1&s&&(U.j41(0,"div",0)(1,"canvas",1),U.bIt("click",function(a){return n.onCanvasClick(a)}),U.k0s()()),2&s&&(U.xc7("width",n.responsive&&!n.width?null:n.width)("height",n.responsive&&!n.height?null:n.height),U.R7$(1),U.BMQ("aria-label",n.ariaLabel)("aria-labelledby",n.ariaLabelledBy)("width",n.responsive&&!n.width?null:n.width)("height",n.responsive&&!n.height?null:n.height))},encapsulation:2,changeDetection:0})}return i})(),Ah=(()=>{class i{static \u0275fac=function(s){return new(s||i)};static \u0275mod=U.$C({type:i});static \u0275inj=U.G2t({imports:[S.MD]})}return i})()}}]);