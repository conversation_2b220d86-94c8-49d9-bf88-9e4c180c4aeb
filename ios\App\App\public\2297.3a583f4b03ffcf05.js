"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2297],{72297:(S,u,r)=>{r.r(u),r.d(u,{HomePageModule:()=>gn});var d=r(56610),P=r(37222),a=r(77897),m=r(77575),g=r(73308),p=r(58133),j=r(35025),f=r.n(j),F=r(32928),B=r(26843),z=r(82115),R=r(76060),I=r(39963),T=r(31909),U=r(80153),n=r(2978),A=r(23985),$=r(68896),E=r(39316),_=r(82571),v=r(97130),O=r(14599),G=r(96514),N=r(13217),h=r(94440);let D=(()=>{class i{constructor(){this.modalIsOpen=new n.bkB,this.skeletons=[1,2,3,4,5,6]}ngOnInit(){}static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-product-home-card"]],inputs:{item:"item"},outputs:{modalIsOpen:"modalIsOpen"},decls:14,vars:7,consts:[[1,"product-card-container"],[1,"card-max-container"],[1,"containerize"],[1,"product-container"],[1,"image-container"],[1,"prod-image"],[3,"src"],[1,"label-container"],[1,"packaging","item-label"],[1,"item-label"]],template:function(e,o){1&e&&(n.j41(0,"div",0)(1,"ion-card",1)(2,"div",1)(3,"div",2)(4,"div",3)(5,"div",4)(6,"div",5),n.nrm(7,"ion-img",6),n.k0s()()(),n.j41(8,"div",7)(9,"div",8)(10,"ion-label",9),n.EFF(11),n.nI1(12,"truncateString"),n.nI1(13,"titlecase"),n.k0s()()()()()()()),2&e&&(n.R7$(7),n.FS9("src",null==o.item?null:o.item.image),n.R7$(4),n.SpI(" ",n.i5U(12,2,n.bMT(13,5,o.item.label),14)," "))},dependencies:[a.b_,a.KW,a.he,d.PV,h.c],styles:['@charset "UTF-8";.product-card-container[_ngcontent-%COMP%]{width:125px;overflow-y:hidden;scrollbar-width:none;-webkit-scrollbar-width:none}.product-card-container[_ngcontent-%COMP%]   .head-card-price[_ngcontent-%COMP%]{width:100%}.product-card-container[_ngcontent-%COMP%]   .head-card-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{width:35%;height:20px;color:#fff;border-radius:0 0 10px;background-color:#419cfb;display:flex;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   .head-card-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:11px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{overflow-y:hidden;scrollbar-width:none;-webkit-scrollbar-width:none;height:100%;border-radius:10px;box-shadow:none}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]{height:140px;overflow:hidden;border-radius:10px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]{height:100%;background-color:#fff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]{height:80%;display:flex;justify-content:center;align-items:center;flex-direction:column;background:#EBF5FF}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;width:100%;padding-top:-1em}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .prod-image[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .prod-image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:90px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{height:15px;width:25%;display:flex;justify-content:center;align-self:flex-start}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]{display:flex;width:100%;color:#0b305c;padding:0 15px;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-weight:700;font-size:10px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .label-container[_ngcontent-%COMP%]{display:flex;height:20%;width:100%;justify-content:center;align-items:center;background:var(--clr-secondary-400);padding:5px 0}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .label-container[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]{width:80%;display:flex;justify-content:flex-start;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .card-max-container[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .label-container[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:var(--clr-white);text-transform:capitalize;font-family:var(--mont-bold);font-size:.65625rem;font-style:normal;letter-spacing:.03rem;font-weight:700}']})}}return i})();var C=r(74657);function X(i,l){if(1&i&&(n.j41(0,"div",30),n.EFF(1),n.k0s()),2&i){const t=n.XpG();n.R7$(1),n.JRh(t.unreadNotifications)}}const Y=function(i){return{active:i}};function V(i,l){if(1&i){const t=n.RV6();n.j41(0,"div",31)(1,"ion-chip",32),n.bIt("click",function(){const c=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.handleClick(c))}),n.j41(2,"ion-label",33),n.EFF(3),n.k0s()()()}if(2&i){const t=l.$implicit,e=n.XpG();n.R7$(1),n.Y8G("ngClass",n.eq3(3,Y,(null==t?null:t.title)===e.selectedItem))("outline",!0),n.R7$(2),n.SpI(" ",null==t?null:t.title," ")}}const x=function(i){return{display:i}};function H(i,l){if(1&i&&(n.j41(0,"ion-thumbnail",34),n.nrm(1,"ion-skeleton-text",35),n.k0s()),2&i){const t=n.XpG();n.Y8G("ngStyle",n.eq3(2,x,null!=t.listCard&&t.listCard.length?"none":"block")),n.R7$(1),n.Y8G("animated",!0)}}function W(i,l){if(1&i){const t=n.RV6();n.j41(0,"app-product-home-card",36),n.bIt("click",function(){const c=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.showDetail(c._id))}),n.k0s()}2&i&&n.Y8G("item",l.$implicit)}const y=function(i){return{"background-image":i}};function L(i,l){if(1&i){const t=n.RV6();n.j41(0,"ion-slide",37),n.bIt("click",function(){const c=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.handleBannerClick(c))}),n.j41(1,"div",38),n.bIt("click",function(o){n.eBV(t),n.XpG();const c=n.sdS(37);return o.stopPropagation(),n.Njj(c.slidePrev())}),n.k0s(),n.j41(2,"div",39),n.bIt("click",function(o){n.eBV(t),n.XpG();const c=n.sdS(37);return o.stopPropagation(),n.Njj(c.slideNext())}),n.k0s()()}2&i&&n.Y8G("ngStyle",n.eq3(1,y,"url("+l.$implicit.img+")"))}function J(i,l){if(1&i&&(n.j41(0,"ion-thumbnail",34),n.nrm(1,"ion-skeleton-text",35),n.k0s()),2&i){const t=n.XpG();n.Y8G("ngStyle",n.eq3(2,x,null!=t.listCard&&t.listCard.length?"none":"block")),n.R7$(1),n.Y8G("animated",!0)}}function Q(i,l){if(1&i){const t=n.RV6();n.j41(0,"app-product-home-card",36),n.bIt("click",function(){const c=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.showDetail(c._id))}),n.k0s()}2&i&&n.Y8G("item",l.$implicit)}function K(i,l){if(1&i){const t=n.RV6();n.j41(0,"ion-slide",37),n.bIt("click",function(){const c=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.handleBannerClick(c))}),n.k0s()}2&i&&n.Y8G("ngStyle",n.eq3(1,y,"url("+l.$implicit.img+")"))}const w=function(i){return{opacity:i}};let Z=(()=>{class i{constructor(t,e,o,c,s,M,b,ln,dn,pn,mn,un,_n){var Cn=this;this.router=t,this.platform=e,this.userSrv=o,this.scannerSrv=c,this.modalCtrl=s,this.productSrv=M,this.popOver=b,this.commonService=ln,this.versionService=dn,this.storageService=pn,this.notificationService=mn,this.modalController=un,this.fidelityProgramService=_n,this.skeletons=[1,2,3,4],this.slideOpts={initialSlide:0,speed:400,spaceBetween:16,autoplay:!0,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},pagination:{el:".swiper-pagination",clickable:!0}},this.slideOfferOpts={initialSlide:0,speed:2e3,spaceBetween:10,slidesPerView:2,autoplay:!0,grabCursor:!0,cubeEffect:{shadow:!0,slideShadows:!0,shadowOffset:20,shadowScale:.94}},this.attachment={file:"",name:"",contentType:""},this.slides=[],this.listTab=[{id:0,title:"Suggestions"}],this.offers=[{label:"",img:"/assets/images/Rectangle441.png",color:"primary"},{label:"",img:"/assets/images/pasta-prod.png",color:"danger"},{label:"",img:"/assets/images/Rectangle441.png",color:"primary"},{label:"",img:"/assets/images/pasta-prod.png",color:"danger"},{label:"",img:"/assets/images/Rectangle442.png",color:"primary"},{label:"",img:"/assets/images/Rectangle441.png",color:"danger"}],this.tendancySlides=[],this.products=[],this.userCategory=p.s,this.filterForm={level:"",enable:!0,created_at:"",date:{start:f()().startOf("year"),end:f()().endOf("year")}},this.offset=0,this.limit=50,this.total=0,this.hasMultipleAccounts=!1,this.verifyAppUpToDate().then(Mn=>{if(!Mn){const Pn=(0,F.Y)(5e4);this.subscription=Pn.subscribe((0,g.A)(function*(){yield Cn.verifyAppUpToDate()}))}})}ngOnInit(){var t=this;return(0,g.A)(function*(){t.user=t.storageService.getUserConnected(),t.commonService.tab="navigation/home",t.commonService.showNav=!0,yield t.getFirstProduct(),yield t.verifyAppUpToDate(),t.selectedItem=t.listTab[0].title,t.listCard=t.products,t.hasMultipleAccounts=t.user?.associatedCompanies&&t.user.associatedCompanies.length>1})()}ionViewWillEnter(){var t=this;return(0,g.A)(function*(){t.commonService.tab="navigation/home",t.commonService.showNav=!0,yield t.verifyAppUpToDate(),yield t.getNotificationsMessages(),yield t.getImage();const e=yield t.fidelityProgramService.getPendingInvitationForUser(+t.user?.tel);e&&e?.status===U.s.PENDING&&(yield t.showModalReferral(e))})()}showModalReferral(t){var e=this;return(0,g.A)(function*(){yield(yield e.modalCtrl.create({component:T.B,cssClass:"modalClass",componentProps:{invitation:t}})).present()})()}handleBannerClick(t){t.redirectUrl&&(t.redirectUrl.startsWith("http")?window.open(t.redirectUrl,"_blank"):this.router.navigate([t.redirectUrl]))}getImage(){var t=this;return(0,g.A)(function*(){t.isLoading=!0;const e={offset:t.offset,limit:t.limit,commercialRegion:t.commonService?.user?.address?.commercialRegion,...t.filterForm};try{const o=yield t.commonService.getAllImageBanner(e);t.images=o.data,t.total=o.count,t.slides=t.images.filter(c=>"home1"===c.level).map(c=>({img:c.image,redirectUrl:c.redirectUrl})),t.slides.length||(t.slides=[{img:"assets/images/banner-1.png"},{img:"assets/images/banner-2.png"}]),t.tendancySlides=t.images.filter(c=>"home2"===c.level).map(c=>({img:c.image,redirectUrl:c.redirectUrl})),t.tendancySlides.length||(t.tendancySlides=[{img:"/assets/images/tendancy-slide1.png"},{img:"/assets/images/tendancy-slide2.png"},{img:"/assets/images/tendancy-slide3.png"}])}catch(o){console.error("Error fetching banners:",o)}finally{t.isLoading=!1}})()}ionViewDidEnter(){return(0,g.A)(function*(){})()}goToScan(){var t=this;return(0,g.A)(function*(){t.router.navigate(["/order/product-scan"])})()}getFirstProduct(){var t=this;return(0,g.A)(function*(){return t.products=(yield t.productSrv.getProducts({limit:20}))?.data})()}ionViewDidLeave(){var t=this;return(0,g.A)(function*(){t.platform.backButton.subscribeWithPriority(1e4,()=>t.onBack())})()}onBack(){this.exitAppOnBackButtonPress()}exitAppOnBackButtonPress(){var t=this;this.platform.backButton.subscribe((0,g.A)(function*(){(t.router.isActive("/home",!0)||"navigation/home"===t.router.url)&&t.showBottomSheet()}))}setBackgroundColor(t){const e=document.querySelector(`#${t.title}`);document.querySelectorAll(".buttons-tabset ion-button").map(c=>(c.style.color="#143c5d",c.style.background="#E7EAEF",c.style.borderRadius="50px",c)),e.style.color="#fff",e.style.background="#143c5d",e.style.borderRadius="50px"}handleClick(t){this.selectedItem=t?.title}verifyAppUpToDate(){var t=this;return(0,g.A)(function*(){if(t.isAppMaintained)return;const e=yield t.versionService.isUpToDate();if(!e){t.isAppMaintained=!0;const o=yield t.popOver.create({component:B.v,cssClass:"updateModal"});o.present(),yield o.onWillDismiss(),t.isAppMaintained=!1}return e})()}showBottomSheet(){var t=this;return(0,g.A)(function*(){const e=yield t.modalCtrl.create({component:I.t,initialBreakpoint:.5,breakpoints:[0,.5],cssClass:["modal","bottom-sheet-container"],mode:"ios",componentProps:{label:"Quitter l'application ?",description:"Vous \xeates sur le point de quitter l'application"}});e.present(),yield e.onWillDismiss()})()}redirectToSabitou(){var t=this;return(0,g.A)(function*(){(yield t.modalCtrl.create({component:z.o,mode:"ios"})).present()})()}getNotificationsMessages(){var t=this;return(0,g.A)(function*(){t.user=t.storageService.getUserConnected();const e=yield t.userSrv?.find(t.user?._id),o={email:t.user?.email,userId:e?._id};o.notifications=JSON.stringify({$in:e?.notifications?.map(s=>s?.id)}),t.dataNotification=(yield t.notificationService.getMessages(o))?.data,t.dataNotification?.forEach(s=>{const M=e?.notifications?.find(b=>b?.id===s?._id?.toString());M&&(s.status=M?.status)});const c=t.dataNotification?.filter(s=>s?.status===R.I.CREATE)?.length||0;t.unreadNotifications=c>9?"+9":`${c}`})()}showDetailNotification(){var t=this;return(0,g.A)(function*(){t.router.navigate(["/navigation/notifications"])})()}trackByFn(t,e){return t}showDetail(t){this.router.navigate(["/order/product-detail/"+t])}showUserAccounts(){}static{this.\u0275fac=function(e){return new(e||i)(n.rXU(m.Ix),n.rXU(a.OD),n.rXU(A.D),n.rXU($.I),n.rXU(a.W3),n.rXU(E.b),n.rXU(a.IE),n.rXU(_.h),n.rXU(v.I),n.rXU(O.n),n.rXU(G.I),n.rXU(a.W3),n.rXU(N._))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-home"]],decls:49,vars:42,consts:[[3,"translucent","ngStyle"],[1,"header"],[1,"header_nav"],[1,"header_nav--profil"],[1,"img_profil"],["alt","profil image",1,"header-img",3,"src"],[1,"profil_info"],[1,"profil_info_bigText"],[1,"header_nav--icon"],[1,"icons_profil"],[1,"notification"],["src","assets/icons/bell.svg","alt","","routerLink","/navigation/notifications",1,"header-img-end"],["class","badge-alert",4,"ngIf"],["src","assets/icons/Message.svg","alt","","routerLink","/navigation/feedback",1,"header-img-end"],[1,"home-container",3,"fullscreen"],["id","container",3,"ngStyle"],[1,"part-search-chip"],["class","space-h-v",4,"ngFor","ngForOf","ngForTrackBy"],[1,"news"],[1,"slide-product-container"],[1,"home-slider-product"],["class","skeleton-cart ",3,"ngStyle",4,"ngFor","ngForOf"],[3,"item","click",4,"ngFor","ngForOf"],[1,"card","slide-container"],[1,"card-content"],[1,"slide-content",3,"pager","options"],["mySlider",""],[3,"ngStyle","click",4,"ngFor","ngForOf"],[1,"card","slide-container","tendency"],[1,"slide-content",3,"options"],[1,"badge-alert"],[1,"space-h-v"],[3,"ngClass","outline","click"],[1,"sub-title"],[1,"skeleton-cart",3,"ngStyle"],[3,"animated"],[3,"item","click"],[3,"ngStyle","click"],[1,"swiper-button-prev",3,"click"],[1,"swiper-button-next",3,"click"]],template:function(e,o){1&e&&(n.j41(0,"ion-header",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),n.nrm(5,"img",5),n.k0s(),n.j41(6,"div",6)(7,"div",7),n.EFF(8),n.nI1(9,"translate"),n.k0s(),n.j41(10,"div"),n.EFF(11),n.nI1(12,"truncateString"),n.nI1(13,"titlecase"),n.nI1(14,"truncateString"),n.nI1(15,"titlecase"),n.nI1(16,"truncateString"),n.nI1(17,"titlecase"),n.k0s()()(),n.j41(18,"div",8)(19,"div",9)(20,"div",10),n.nrm(21,"img",11),n.DNE(22,X,2,1,"div",12),n.k0s(),n.j41(23,"div"),n.nrm(24,"img",13),n.k0s()()()()()(),n.j41(25,"ion-content",14)(26,"div",15)(27,"div",16),n.DNE(28,V,4,5,"div",17),n.k0s(),n.j41(29,"section",18)(30,"div",19)(31,"div",20),n.DNE(32,H,2,4,"ion-thumbnail",21),n.DNE(33,W,1,1,"app-product-home-card",22),n.k0s()()(),n.j41(34,"ion-card",23)(35,"ion-card-content",24)(36,"ion-slides",25,26),n.DNE(38,L,3,3,"ion-slide",27),n.k0s()()(),n.j41(39,"section",18)(40,"div",19)(41,"div",20),n.DNE(42,J,2,4,"ion-thumbnail",21),n.DNE(43,Q,1,1,"app-product-home-card",22),n.k0s()()(),n.j41(44,"ion-card",28)(45,"ion-card-content",24)(46,"ion-slides",29,26),n.DNE(48,K,1,3,"ion-slide",27),n.k0s()()()()()),2&e&&(n.Y8G("translucent",!0)("ngStyle",n.eq3(38,w,null!=o.scannerSrv&&o.scannerSrv.currDisplay?0:1)),n.R7$(5),n.Y8G("src",(null==o.attachment?null:o.attachment.file)||(null==o.user?null:o.user.profilePicture)||"assets/icons/Profil2.png",n.B4B),n.R7$(3),n.JRh(n.bMT(9,21,"home-page.title")),n.R7$(3),n.E5c(" ",o.user.company?n.i5U(12,23,n.bMT(13,26,o.user.company.name),10)+" -":""," ",n.i5U(14,28,n.bMT(15,31,null==o.user?null:o.user.firstName),10)," ",n.i5U(16,33,n.bMT(17,36,null==o.user?null:o.user.lastName),10)," "),n.R7$(11),n.Y8G("ngIf",o.unreadNotifications),n.R7$(3),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngStyle",n.eq3(40,w,null!=o.scannerSrv&&o.scannerSrv.currDisplay?0:1)),n.R7$(2),n.Y8G("ngForOf",o.listTab)("ngForTrackBy",o.trackByFn),n.R7$(4),n.Y8G("ngForOf",o.skeletons),n.R7$(1),n.Y8G("ngForOf",o.listCard),n.R7$(3),n.Y8G("pager",!0)("options",o.slideOpts),n.R7$(2),n.Y8G("ngForOf",o.slides),n.R7$(4),n.Y8G("ngForOf",o.skeletons),n.R7$(1),n.Y8G("ngForOf",o.listCard),n.R7$(3),n.Y8G("options",o.slideOpts),n.R7$(2),n.Y8G("ngForOf",o.tendancySlides))},dependencies:[d.YU,d.Sq,d.bT,d.B3,a.b_,a.I9,a.ZB,a.W9,a.eU,a.he,a.ds,a.q3,a.tR,a.Zx,a.N7,m.Wk,D,d.PV,C.D9,h.c],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{--background: var(--clr-primary-0);width:calc(16 * var(--resW));height:calc(16 * var(--resW))}ion-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{rotate:90deg}ion-content[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]{margin-bottom:calc(20 * var(--resW));gap:.75rem}ion-content[_ngcontent-%COMP%]   .LiveChatBtn[_ngcontent-%COMP%]{--animate-duration: .3s;--background: #ffffff}ion-content[_ngcontent-%COMP%]   .LiveChatBtn[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%}ion-content[_ngcontent-%COMP%]   .cimencamBtn[_ngcontent-%COMP%]{background:var(--clr-secondary-100);border-radius:50%}ion-content[_ngcontent-%COMP%]   .cimencamBtn[_ngcontent-%COMP%]   .floatingBloc[_ngcontent-%COMP%]{height:55%}ion-content[_ngcontent-%COMP%]   .cimencamBtn[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:80%}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]{bottom:2%;right:5%}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   .img-coupons[_ngcontent-%COMP%]{width:5em;height:2em}ion-content[_ngcontent-%COMP%]   .ion-fab[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#0B305C!important}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .ion-fab[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#0B305C}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;font-size:var(--main-title);text-align:start}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:100}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .notifications[_ngcontent-%COMP%]{position:fixed;text-align:right;bottom:9%;right:4%;z-index:1}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .notifications[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{position:absolute;right:0;top:0;width:10px;height:10px;border-radius:50%;background:rgb(173,5,5)}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .notifications[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-top:3px;font-size:23px;color:#737397}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding-top:10px;background-color:#fff}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:100;z-index:1}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .background-opacity[_ngcontent-%COMP%]{position:absolute;width:100%;height:100%;top:0;z-index:1;background:linear-gradient(180deg,rgba(0,0,0,0) 0%,rgba(10,105,43,.54) 95.5%)}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]{width:100%;border-radius:9px;margin-top:.838rem;height:150px;margin-bottom:1.188rem;margin:2rem 0}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]{border-radius:10px;height:100%;--bullet-background: white;--bullet-background-active: red}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%]{width:19px;height:19px;background-size:19px 19px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%]{background-image:url(arrow-back-white.e97e691d01c56343.svg)}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%]{background-image:url(arrow-forward-white.a589d59d565b95fe.svg)}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{background-repeat:no-repeat;background-size:cover;background-position:center}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .row-product[_ngcontent-%COMP%]{display:grid;gap:.3em;grid-template-columns:auto auto auto}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .row-product[_ngcontent-%COMP%]   .card-product[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;padding:calc(30 * var(--res)) 0}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .row-product[_ngcontent-%COMP%]   .card-product[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;height:100px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .row-product[_ngcontent-%COMP%]   .card-product[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-top:calc(30 * var(--res));font-family:Mont SemiBold;text-align:center}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-offer[_ngcontent-%COMP%]{height:calc(48 * var(--resH))}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-offer[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{padding-bottom:calc(25 * var(--res));background-repeat:no-repeat;background-size:cover}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-offer[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .background-opacity[_ngcontent-%COMP%]{border-radius:calc(20 * var(--res))}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:auto}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .special-offer[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .special-offer[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:800;font-size:1rem}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .special-offer[_ngcontent-%COMP%]   .showAll[_ngcontent-%COMP%]{text-transform:none;background:none;color:#0b305c;font-weight:"Mont Bold";font-size:12px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .truck-background[_ngcontent-%COMP%]{background:url(truck.755c4a952b9c0526.svg);background-repeat:no-repeat;background-size:cover}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]{background-color:#6d839d}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .binastore-user[_ngcontent-%COMP%]{overflow:hidden;z-index:3;width:35%;display:flex;margin-right:5px;margin-left:5px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .binastore-user[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{margin-bottom:-1.9em}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .blue-card-part[_ngcontent-%COMP%]{position:absolute;clip-path:polygon(0 0,100% 0%,57% 100%,0% 100%);height:100%;width:35%;background-color:#016199}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .pub-content[_ngcontent-%COMP%]{display:flex;width:60%}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .pub-content[_ngcontent-%COMP%]   .information[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-around;padding-right:5px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .pub-content[_ngcontent-%COMP%]   .information[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: #014d81;margin-top:10px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .pub-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{display:flex;padding-bottom:calc(25 * var(--res));width:80%}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .binastore-pub[_ngcontent-%COMP%]   .pub-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{margin-top:auto;width:100%}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]{margin:16px 0;height:calc(32 * var(--resH));border-radius:6px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{height:100%;display:flex}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .background-opacity[_ngcontent-%COMP%]{background:linear-gradient(270deg,#0a692b 32.13%,rgba(255,255,255,0) 111.71%)}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:600;font-size:calc(70 * var(--res));line-height:111.5%;color:#fff}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{height:100%}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{padding-right:calc(25 * var(--res));padding-left:calc(25 * var(--res));z-index:2}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%], .home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:10px}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .version-display[_ngcontent-%COMP%]{width:100%}.home-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .pub-container[_ngcontent-%COMP%]   .version-display[_ngcontent-%COMP%]   .current-version[_ngcontent-%COMP%]{margin-top:1em;font-size:12px;text-align:end}.home-container[_ngcontent-%COMP%]   .sabitouBtn[_ngcontent-%COMP%]{--background: url(Mask_Group.7b6a64f997fccfaa.png) center no-repeat;margin-bottom:calc(40 * var(--res));--animate-duration: .5s}.home-container[_ngcontent-%COMP%]   .LiveChatBtn[_ngcontent-%COMP%]{--animate-duration: .3s;--background: #ffffff}.home-container[_ngcontent-%COMP%]   .LiveChatBtn[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%}.home-container[_ngcontent-%COMP%]   .cimencamBtn[_ngcontent-%COMP%]{background:#143c5d;border-radius:50%}.home-container[_ngcontent-%COMP%]   .cimencamBtn[_ngcontent-%COMP%]   .floatingBloc[_ngcontent-%COMP%]{height:70%}.home-container[_ngcontent-%COMP%]   .cimencamBtn[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:80%}.home-container[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]{bottom:2%;right:5%}ion-header[_ngcontent-%COMP%]{border-radius:0 0 15px 15px!important}.suggestion[_ngcontent-%COMP%], .tendance[_ngcontent-%COMP%]{margin-top:10px}.header[_ngcontent-%COMP%]{position:relative;height:110px;width:100%;border-radius:0 0 15px 15px;background:url(bg.084da384672e9d5b.png) center no-repeat;background-position:center;background-repeat:no-repeat;background-size:cover;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;gap:20px}.header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){position:absolute;top:30px;height:60px;width:100%}.header_nav[_ngcontent-%COMP%]{padding:1rem;display:flex;align-items:center;justify-content:space-between}.img_profil[_ngcontent-%COMP%]{height:43px;width:43px;border-radius:50%;overflow:hidden}.img_profil[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{object-fit:cover;width:100%}.header_nav--profil[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.home-tabset[_ngcontent-%COMP%]{width:105px}.slide-product-container[_ngcontent-%COMP%]{height:auto;overflow-x:scroll;scrollbar-width:none;-webkit-scrollbar-width:none}.icons_profil[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:20px;width:100px}.icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]{position:relative}.icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   .badge-alert[_ngcontent-%COMP%]{position:absolute;top:-10px;right:-10px;background:red;color:var(--clr-white);border-radius:50%;font-size:12px;padding:10px;width:1em;height:1em;display:flex;align-items:center;justify-content:center;font-family:var(--mont-bold)}@keyframes _ngcontent-%COMP%_translateHor{0%{transform:translateY(-100%)}to{transform:translateY(0)}}.home-tab-button[_ngcontent-%COMP%]{--border-radius: 50px;color:#143c5d;background:#E7EAEF;border:#419CFB;font-size:15px!important;text-transform:capitalize;height:35px;width:125px!important}.container-buttons-tabset[_ngcontent-%COMP%]{width:100%;overflow-x:scroll}.buttons-tabset[_ngcontent-%COMP%]{height:70px;width:450px;display:flex!important;align-items:center;gap:8px;padding-bottom:10px}.home-slider-product[_ngcontent-%COMP%]{display:flex;transform:translate(-18px);align-items:center;justify-content:flex-start;gap:10px;min-width:100%;height:auto}.slider_auto[_ngcontent-%COMP%]{margin-top:8px;height:150px;width:100%;border:1px solid black}.profil_info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;color:#fff}.profil_info[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(1){width:120px;height:29px;font-weight:800;font-size:1.419rem;line-height:1.813;white-space:nowrap}.profil_info[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){align-self:flex-start;white-space:nowrap;min-width:71px;width:auto;height:17px;font-weight:600;font-size:.813rem;line-height:1.038}.header_input[_ngcontent-%COMP%]{display:flex;width:100%;margin:0 2rem;align-items:center;gap:3px}.header_input[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){border:1px solid black;height:90%!important}.section_title[_ngcontent-%COMP%]{height:55px;padding-left:26px;width:100%;display:flex;align-items:center;justify-content:flex-start;gap:9px}.home-slider-product[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start;padding-left:20px;gap:11px;min-width:150%}.home-slider-product[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{border:1px solid #E7EAEF;width:auto;height:143px;border-radius:7.73px;background-color:#e7eaef}@media screen and (max-height: 560px){.header[_ngcontent-%COMP%]{padding:15px 0;height:80px}.header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){position:absolute;top:15px}.header_nav[_ngcontent-%COMP%]{padding:0 1.8rem}}.part-search-chip[_ngcontent-%COMP%]{display:flex;white-space:nowrap;overflow-x:scroll;scrollbar-width:none;-webkit-scrollbar-width:none}.part-search-chip[_ngcontent-%COMP%]   .space-all[_ngcontent-%COMP%]{padding:0 0 0 calc(41 * var(--res))}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{padding-top:5px;margin-bottom:20px}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{margin-right:8px;border-radius:20px;padding:0 12px;--background: #F0F0F0;--color: var(--ion-color-primary);border-color:#d8eafe}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]:active{--background: $color-primary;--color: $color-nine}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{font-size:11px!important}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#fff!important;background:var(--ion-color-primary)!important}@media screen and (min-width: 760px){.header[_ngcontent-%COMP%]{height:120px;padding:2rem 4rem;width:100%;display:flex;align-items:center;justify-content:center}.header[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:nth-child(1){position:absolute;top:50px;height:40px;width:100%}ion-button.home-tab-button[_ngcontent-%COMP%]{font-size:13px!important;text-transform:capitalize;width:auto!important}.container-buttons-tabset[_ngcontent-%COMP%]{width:100%;overflow-x:none;height:50px;margin-top:20px}.buttons-tabset[_ngcontent-%COMP%]{height:0}}.header-img[_ngcontent-%COMP%]{height:100%}.header-img-end[_ngcontent-%COMP%]{color:#fff;height:1em}.skeleton-cart[_ngcontent-%COMP%]{width:100%;height:100%}.skeleton-cart[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:130px;height:135px;background-color:#ebf5ff}.profil_info_bigText[_ngcontent-%COMP%]{font-weight:"Mont SemiBold"}app-qr-code-scanner[_ngcontent-%COMP%]{position:fixed;height:100%;width:100%;z-index:136}']})}}return i})();var k=r(99987),q=r(62049);const nn=["mainSlides"];function tn(i,l){if(1&i&&(n.j41(0,"ion-slide")(1,"div",13)(2,"div",14),n.nrm(3,"img",15),n.k0s(),n.j41(4,"h3",16),n.EFF(5),n.nrm(6,"br"),n.j41(7,"span"),n.EFF(8),n.k0s()()()()),2&i){const t=l.$implicit;n.R7$(3),n.Y8G("src",t.imgSrc,n.B4B),n.R7$(2),n.SpI("",t.title," "),n.R7$(3),n.JRh(t.description)}}const on=function(i){return{selected:i}};function cn(i,l){if(1&i){const t=n.RV6();n.j41(0,"ion-card",16),n.bIt("click",function(){const c=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.selectAccount(c))}),n.j41(1,"ion-card-header")(2,"ion-avatar",17),n.nrm(3,"img",18),n.k0s()(),n.j41(4,"ion-card-content")(5,"h3"),n.EFF(6),n.nI1(7,"truncateString"),n.k0s(),n.j41(8,"p"),n.EFF(9),n.nI1(10,"translate"),n.k0s()()()}if(2&i){const t=l.$implicit,e=n.XpG();n.Y8G("ngClass",n.eq3(9,on,(null==t||null==t._id?null:t._id.toString())===(null==e.selectedAccount||null==e.selectedAccount._id?null:e.selectedAccount._id.toString()))),n.R7$(3),n.Y8G("src",(null==e.user?null:e.user.profilePicture)||"assets/icons/Profil2.png",n.B4B),n.R7$(3),n.JRh(n.i5U(7,4,null==t?null:t.name,24)),n.R7$(3),n.JRh(n.bMT(10,7,"account-management.manager"))}}const rn=[{path:"anboarding",component:(()=>{class i{constructor(t,e,o,c){this.route=t,this.commSrv=e,this.translateService=o,this.storageSrv=c,this.commonService=(0,n.WQX)(_.h),this.getSlides=[{imgSrc:"assets/images/1.gif",title:"Achat de Marchandise",description:"D\xe9sormais, achetez vos marchandises en quelques clics."},{imgSrc:"assets/images/2.gif",title:"Gestion des comptes",description:"Ayez un \u0153il attentif sur votre compte. Consultez vos diff\xe9rents soldes, vos commandes ouvertes et votre capacit\xe9 d\u2019achat."},{imgSrc:"assets/images/3.gif",title:"Boutique",description:"D\xe9couvrez une large gamme de produits et services pour votre activit\xe9 au quotidien."},{imgSrc:"assets/images/4.gif",title:"Assistance et support",description:"Nous restons \xe0 votre \xe9coute 24H/24, 7J/7."}]}ngOnInit(){this.commonService.showNav=!1,this.confirmation=this.translateService.currentLang===k.T.French?"Passer":"Skip",this.confirmation1=this.translateService.currentLang===k.T.French?"Suivant":"Next",this.user=this.storageSrv.getUserConnected()}skipBtn(){this.route.navigate(this.user?.associatedCompanies&&this.user?.associatedCompanies?.length>0&&this.user?.category===p.s.CompanyUser?["navigation/home/<USER>"]:["/navigation/home"])}goBack(){var t=this;return(0,g.A)(function*(){(yield t.slides.getActiveIndex())===t.getSlides.length-1?(t.user?.associatedCompanies&&t.user?.associatedCompanies?.length>0&&(t.route.navigate(["navigation/home/<USER>"]),t.commSrv.anboardingView=!0),t.user?.category===p.s.Particular?(t.route.navigate(["navigation/home-alt"]),t.commSrv.anboardingView=!0):(t.route.navigate(["/navigation/home"]),t.commSrv.anboardingView=!0)):t.slides.slideNext()})()}static{this.\u0275fac=function(e){return new(e||i)(n.rXU(m.Ix),n.rXU(_.h),n.rXU(q.E),n.rXU(O.n))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-anboarding"]],viewQuery:function(e,o){if(1&e&&n.GBs(nn,7),2&e){let c;n.mGM(c=n.lsd())&&(o.slides=c.first)}},decls:17,vars:3,consts:[[1,"header"],[1,"icon-logo"],["src","assets/icons/logoAn.svg","alt","logo"],[1,"onboard-main"],[1,"onboard-top"],["pager","true"],["mainSlides",""],[4,"ngFor","ngForOf"],[1,"anboard-bot"],[1,"skip"],[3,"click"],["for","",1,"next",3,"click"],["src","assets/icons/s.svg","alt","logo",3,"click"],[1,"onb-cart"],[1,"onb-img"],["alt","",3,"src"],[1,"text-4"]],template:function(e,o){1&e&&(n.j41(0,"div",0)(1,"div",1),n.nrm(2,"img",2),n.k0s()(),n.j41(3,"ion-content")(4,"div",3)(5,"div",4)(6,"ion-slides",5,6),n.DNE(8,tn,9,3,"ion-slide",7),n.k0s()(),n.j41(9,"div",8)(10,"div",9)(11,"label",10),n.bIt("click",function(){return o.skipBtn()}),n.EFF(12),n.k0s()(),n.j41(13,"div",1)(14,"label",11),n.bIt("click",function(){return o.goBack()}),n.EFF(15),n.k0s(),n.j41(16,"img",12),n.bIt("click",function(){return o.goBack()}),n.k0s()()()()()),2&e&&(n.R7$(8),n.Y8G("ngForOf",o.getSlides),n.R7$(4),n.SpI(" ",o.confirmation,""),n.R7$(3),n.SpI(" ",o.confirmation1,""))},dependencies:[d.Sq,a.W9,a.q3,a.tR],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.header[_ngcontent-%COMP%]{background:#fff;padding:var(--container-padding)}.header[_ngcontent-%COMP%]   .icon-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:1.5em}.onboard-main[_ngcontent-%COMP%]{padding:var(--container-padding);height:100%}.onboard-main[_ngcontent-%COMP%]   .onboard-top[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:space-between;flex-direction:column}.onboard-main[_ngcontent-%COMP%]   .onboard-top[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]{padding-bottom:27%;flex:1}.onboard-main[_ngcontent-%COMP%]   .onboard-top[_ngcontent-%COMP%]   .onb-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:175px;height:175px;opacity:0px;object-fit:cover}.onboard-main[_ngcontent-%COMP%]   .onboard-top[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;width:800;font-family:Mont Bold;color:#143c5d;margin-top:23%}.onboard-main[_ngcontent-%COMP%]   .onboard-top[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#0b305c;text-align:center;font-size:12px!important;font-family:Mont Regular;padding-top:9px;display:block;position:relative;width:15rem}.onboard-main[_ngcontent-%COMP%]   .anboard-bot[_ngcontent-%COMP%]{display:flex;z-index:100;flex-direction:row;position:relative;top:-3rem;justify-content:space-between}.onboard-main[_ngcontent-%COMP%]   .anboard-bot[_ngcontent-%COMP%]   .skip[_ngcontent-%COMP%]{padding-top:.3rem;height:16%}.onboard-main[_ngcontent-%COMP%]   .anboard-bot[_ngcontent-%COMP%]   .skip[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:13px;font-family:Mont Regular}.onboard-main[_ngcontent-%COMP%]   .anboard-bot[_ngcontent-%COMP%]   .icon-logo[_ngcontent-%COMP%]{height:16%}.onboard-main[_ngcontent-%COMP%]   .anboard-bot[_ngcontent-%COMP%]   .icon-logo[_ngcontent-%COMP%]   .next[_ngcontent-%COMP%]{margin:.3rem;font-size:13px;margin-bottom:0!important}.onboard-main[_ngcontent-%COMP%]   .anboard-bot[_ngcontent-%COMP%]   .icon-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:28px;position:relative;top:8px}"]})}}return i})()},{path:"multiple-account",component:(()=>{class i{constructor(t,e,o,c){this.route=t,this.storageSrv=e,this.commonService=o,this.translateService=c,this.accountSelected=!1,this.userCompanies=[]}ngOnInit(){var t=this;return(0,g.A)(function*(){t.user=t.storageSrv.getUserConnected(),t.user?.category===p.s.CompanyUser&&(t.userCompanies=t.getUniqueCompanies(t.user.associatedCompanies??[]),t.selectedAccount=t.user.company,t.accountSelected=!0)})()}selectAccount(t){this.selectedAccount=t,this.accountSelected=!0}continue(){if(this.selectedAccount)try{this.user.company=this.selectedAccount,this.storageSrv.store("USER_INFO",JSON.stringify(this.user)),this.route.navigate(["/navigation/home"]).then(()=>{this.commonService.showToast({message:"fr"===this.translateService.currentLang?" Bienvenue !.":"Welcome !.",color:"success"})})}catch(t){console.error("Error during navigation:",t),this.commonService.showToast({message:"fr"===this.translateService.currentLang?"Une erreur est survenue lors de la connexion.":"An error occurred during login.",color:"danger"})}else this.commonService.showToast({message:"fr"===this.translateService.currentLang?"Veuillez s\xe9lectionner un compte avant de continuer.":"Please select an account before proceeding.",color:"warning"})}getUniqueCompanies(t){if(!t)return[];const e=new Set,o=[];return t.forEach(c=>{const s=c?._id?.toString();s&&!e.has(s)&&(e.add(s),o.push(c))}),o}static{this.\u0275fac=function(e){return new(e||i)(n.rXU(m.Ix),n.rXU(O.n),n.rXU(_.h),n.rXU(C.c$))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-multiple-account"]],decls:22,vars:10,consts:[[1,"header"],[1,"icon-logo"],["src","/assets/icons/logoAn.svg","alt","logo"],[1,"ion-padding"],[1,"auth-container"],[1,"auth-avatar"],["src","/assets/icons/Sticker.svg"],[1,"title-text"],[1,"title-content"],[1,"auth-title"],[1,"auth-description"],[1,"account-list-container","two-item"],[1,"account-list"],["class","account-card",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"btn-validate"],["expand","block","color","secondary",1,"continue-btn",3,"click"],[1,"account-card",3,"ngClass","click"],[1,"account-avatar"],["alt","profil image",1,"header-img",3,"src"]],template:function(e,o){1&e&&(n.j41(0,"div",0)(1,"div",1),n.nrm(2,"img",2),n.k0s()(),n.j41(3,"ion-content",3)(4,"div",4)(5,"ion-avatar",5),n.nrm(6,"ion-img",6),n.k0s(),n.j41(7,"div",7)(8,"div",8)(9,"h2",9),n.EFF(10),n.nI1(11,"translate"),n.k0s(),n.j41(12,"p",10),n.EFF(13),n.nI1(14,"translate"),n.k0s()()(),n.j41(15,"div",11)(16,"div",12),n.DNE(17,cn,11,11,"ion-card",13),n.k0s()(),n.j41(18,"div",14)(19,"ion-button",15),n.bIt("click",function(){return o.continue()}),n.EFF(20),n.nI1(21,"translate"),n.k0s()()()()),2&e&&(n.R7$(10),n.JRh(n.bMT(11,4,"account-management.title-account")),n.R7$(3),n.JRh(n.bMT(14,6,"account-management.text-account")),n.R7$(4),n.Y8G("ngForOf",o.userCompanies),n.R7$(3),n.SpI(" ",n.bMT(21,8,"account-management.btn")," "))},dependencies:[d.YU,d.Sq,a.mC,a.Jm,a.b_,a.I9,a.ME,a.W9,a.KW,C.D9,h.c],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.header[_ngcontent-%COMP%]{background:#fff;padding:1rem 1rem 0}.header[_ngcontent-%COMP%]   .icon-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:31.97px;height:31.97px;position:absolute;z-index:2}.auth-container[_ngcontent-%COMP%]{height:100%!important;position:relative;text-align:center;padding:0 12px 16px}.logo[_ngcontent-%COMP%]{height:40px;margin:auto}.auth-avatar[_ngcontent-%COMP%]{margin:0 auto 5px;width:122px;height:122px}.title-text[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;width:100%;padding:0 15px;margin:1rem 0}.title-content[_ngcontent-%COMP%]{text-align:center;max-width:600px;width:100%}.auth-title[_ngcontent-%COMP%]{font-family:var(--mont-bold);font-size:16px;color:#143c5d;margin-bottom:.5rem;line-height:1.2}.auth-description[_ngcontent-%COMP%]{color:#546e8d;font-size:13px;margin-top:.3em;font-family:var(--mont-regular);line-height:1.4}.account-list-container[_ngcontent-%COMP%]{width:100%;padding:0 12px;margin:20px auto;display:flex;justify-content:center}.account-list[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,145px);gap:18px;justify-content:center;max-width:800px;width:100%}.account-card[_ngcontent-%COMP%]{width:153px;height:148px;padding:10px;display:flex;gap:1em;flex-direction:column;justify-content:center;text-align:center;border-radius:17px;color:#143c5d;font-family:Mont SemiBold;box-shadow:0 2px 5px #0000001a;--background: var(--ion-color-light)}.account-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.625rem;color:#546e8d}.selected[_ngcontent-%COMP%]{color:var(--ion-color-tertiary-contrast);background-color:#143c5d}.selected[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-tertiary-contrast)}.account-avatar[_ngcontent-%COMP%]{width:48px;height:48px;margin:auto}.continue-btn[_ngcontent-%COMP%]{margin-top:20px}.btn-validate[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:50%;transform:translate(-50%);width:100%;border-radius:8px}.btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{font-family:var(--mont-semibold)}.btn-validate[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}@media (min-width: 700px){.account-list[_ngcontent-%COMP%]{justify-content:center;gap:25px}}@media (max-width: 320px){.account-list-container[_ngcontent-%COMP%]{padding:0 8px}.account-list[_ngcontent-%COMP%]{gap:8px}}@media (max-width: 390px){.account-list-container[_ngcontent-%COMP%]{padding:0 12px;margin:20px auto;display:flex;justify-content:center}.account-list-container.two-item[_ngcontent-%COMP%]   .account-list[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,135px);justify-content:center}.account-list[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,135px);gap:12px;justify-content:center;width:100%;max-width:800px}.account-card[_ngcontent-%COMP%]{width:140px}}@media (max-width: 390px) and (max-width: 370px) and (min-width: 321px){.account-list-container[_ngcontent-%COMP%]{padding:0 8px}.account-list-container.two-item[_ngcontent-%COMP%]   .account-list[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,116px);justify-content:center;gap:17px}.account-card[_ngcontent-%COMP%]{width:125px}}@media (max-width: 390px) and (max-width: 320px){.account-list-container[_ngcontent-%COMP%]{padding:0 8px}.account-list-container.two-item[_ngcontent-%COMP%]   .account-list[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);justify-content:center}.auth-title[_ngcontent-%COMP%]{font-size:14px}.auth-description[_ngcontent-%COMP%]{font-size:12px}}"]})}}return i})()},{path:"",component:Z}];let an=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[m.iI.forChild(rn),m.iI]})}}return i})();var sn=r(93887);let gn=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({providers:[v.I],imports:[d.MD,P.YN,a.bv,an,C.h,sn.G]})}}return i})()},94440:(S,u,r)=>{r.d(u,{c:()=>P});var d=r(2978);let P=(()=>{class a{transform(g,...p){return g?g.length>p[0]?`${g.substring(0,p[0]-3)}...`:g:""}static{this.\u0275fac=function(p){return new(p||a)}}static{this.\u0275pipe=d.EJ8({name:"truncateString",type:a,pure:!0})}}return a})()}}]);