"use strict";(self.webpack<PERSON>hunkapp=self.webpackChunkapp||[]).push([[8047],{92391:(v,u,o)=>{o.d(u,{Nn:()=>r,gH:()=>d});var d=function(n){return n.CalendarEvent="CALENDAR_EVENT",n.ContactInfo="CONTACT_INFO",n.DriversLicense="DRIVERS_LICENSE",n.Email="EMAIL",n.Geo="GEO",n.Isbn="ISBN",n.Phone="PHONE",n.Product="PRODUCT",n.Sms="SMS",n.Text="TEXT",n.Url="URL",n.Wifi="WIFI",n.Unknown="UNKNOWN",n}(d||{}),r=function(n){return n.Front="FRONT",n.Back="BACK",n}(r||{})},94761:(v,u,o)=>{o.d(u,{vi:()=>t});var e=o(22126);o(92391);const t=(0,e.F3)("BarcodeScanner",{web:()=>o.e(5499).then(o.bind(o,85499)).then(r=>new r.BarcodeScannerWeb)})},32928:(v,u,o)=>{o.d(u,{Y:()=>t});var e=o(44912),d=o(53229);function t(r=0,p=e.E){return r<0&&(r=0),(0,d.O)(r,r,p)}},82115:(v,u,o)=>{o.d(u,{o:()=>p});var e=o(2978),d=o(77575),t=o(77897),r=o(74657);let p=(()=>{class M{constructor(P,m,n){this.router=P,this.platform=m,this.modalCtrl=n}ngOnInit(){this.sabitouLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=lnd.cimencam.sabitou_construction&hl=en&gl=US":"https://apps.apple.com/us/app/sabitou-construction/id1453574772"}onNoClick(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(m){return new(m||M)(e.rXU(d.Ix),e.rXU(t.OD),e.rXU(t.W3))}}static{this.\u0275cmp=e.VBU({type:M,selectors:[["app-redirect-to-sabitou"]],decls:17,vars:10,consts:[[1,"mat-dialog"],["src","/assets/images/sabitou.png",1,"img-container"],[1,"text-form"],["src","/assets/icons/institutions-logos-cimencam-binastore.svg",1,"lafrageimg"],[1,"mat-dialogButton"],[1,"button","back",3,"click"],[1,"button","download"],[3,"href"]],template:function(m,n){1&m&&(e.j41(0,"div",0)(1,"div"),e.nrm(2,"ion-img",1),e.j41(3,"ion-title"),e.EFF(4,"Sabitou Construction"),e.k0s(),e.j41(5,"ion-label",2),e.EFF(6),e.nI1(7,"translate"),e.k0s(),e.nrm(8,"ion-img",3),e.k0s(),e.j41(9,"div",4)(10,"div",5),e.bIt("click",function(){return n.onNoClick()}),e.EFF(11),e.nI1(12,"translate"),e.k0s(),e.j41(13,"div",6)(14,"a",7),e.EFF(15),e.nI1(16,"translate"),e.k0s()()()()),2&m&&(e.R7$(6),e.SpI(" ",e.bMT(7,4,"redirect-sabitou-modal.text")," "),e.R7$(5),e.SpI(" ",e.bMT(12,6,"button.cancel"),""),e.R7$(3),e.Y8G("href",n.sabitouLink,e.B4B),e.R7$(1),e.JRh(e.bMT(16,8,"button.download")))},dependencies:[t.KW,t.he,t.BC,r.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.mat-dialog[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}.mat-dialog[_ngcontent-%COMP%]   .img-container[_ngcontent-%COMP%]{width:100%}.mat-dialog[_ngcontent-%COMP%]   .mat-dialogButton[_ngcontent-%COMP%]{font-family:Mont Regular;display:flex;justify-content:space-around}.mat-dialog[_ngcontent-%COMP%]   .text-form[_ngcontent-%COMP%]{text-align:center;letter-spacing:0,5px;font-family:Mont Regular;margin-bottom:16px}.mat-dialog[_ngcontent-%COMP%]   .lafrageimg[_ngcontent-%COMP%]{margin-bottom:20px;width:69%;margin-left:14%}.mat-dialog[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%]{font-size:14px;padding:12px 19px;font-family:Mont Bold;border-radius:4px}.mat-dialog[_ngcontent-%COMP%]   .back[_ngcontent-%COMP%]{background-color:#143c5d;color:#fff;border:#a3a6a8 1px solid}.mat-dialog[_ngcontent-%COMP%]   .download[_ngcontent-%COMP%]{background-color:#ffc500}.mat-dialog[_ngcontent-%COMP%]   .download[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:#fff}"]})}}return M})()},26843:(v,u,o)=>{o.d(u,{v:()=>P});var e=o(73308),d=o(35025),t=o.n(d),r=o(2978),p=o(77897),M=o(14599),C=o(74657);let P=(()=>{class m{constructor(g,l,a){this.platform=g,this.storageSrv=l,this.popOver=a,this.isContentShown=!1}ngOnInit(){this.storeLink=this.platform.is("android")?"https://play.google.com/store/apps/details?id=com.clickcadyst.mobile":"https://apps.apple.com/us/app/clic cadyst/id1467838902"}cancel(){var g=this;return(0,e.A)(function*(){yield g.storageSrv.store("lastDeniedUpdateApp",t()().toDate().getTime()),g.popOver.dismiss()})()}static{this.\u0275fac=function(l){return new(l||m)(r.rXU(p.OD),r.rXU(M.n),r.rXU(p.IE))}}static{this.\u0275cmp=r.VBU({type:m,selectors:[["app-update-app-modal"]],decls:18,vars:10,consts:[[1,"dialog-content"],[1,"phone-logo"],[1,"phone"],[1,"text"],[1,"mcm-btn-navigate-container"],[1,"mc-btn","full-width"],[1,"store-link",3,"href"],[1,"mcm-btn-navigate-container-close"],["color","light",1,"mc-btn","full-width","white-update",3,"click"],[1,"store-link"]],template:function(l,a){1&l&&(r.j41(0,"div",0)(1,"div",1),r.nrm(2,"div",2),r.k0s(),r.j41(3,"h1"),r.EFF(4,"CLICK CADYST"),r.k0s(),r.j41(5,"p",3),r.EFF(6),r.nI1(7,"translate"),r.k0s(),r.j41(8,"div",4)(9,"ion-button",5)(10,"a",6),r.EFF(11),r.nI1(12,"translate"),r.k0s()()(),r.j41(13,"div",7)(14,"ion-button",8),r.bIt("click",function(){return a.cancel()}),r.j41(15,"a",9),r.EFF(16),r.nI1(17,"translate"),r.k0s()()()()),2&l&&(r.R7$(6),r.SpI(" ",r.bMT(7,4,"home-page.update-text")," "),r.R7$(4),r.Y8G("href",a.storeLink,r.B4B),r.R7$(1),r.SpI(" ",r.bMT(12,6,"home-page.uptodate"),""),r.R7$(5),r.SpI(" ",r.bMT(17,8,"home-page.update-cancel")," "))},dependencies:[p.Jm,C.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.dialog-content[_ngcontent-%COMP%]{padding:16px}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]{height:200px;width:100%;height:22vh;display:flex;align-items:center;justify-content:center}.dialog-content[_ngcontent-%COMP%]   .phone-logo[_ngcontent-%COMP%]   .phone[_ngcontent-%COMP%]{height:100%;width:100%;background-image:url(updatepasta-icon.52e001d874e40d5e.png);background-repeat:no-repeat;background-position:center;background-size:contain}.dialog-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:Mont Regular;font-size:.7em;text-align:center;margin:0}.dialog-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:10px 0 25px;font-family:Mont Bold;font-size:12px;text-align:center}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:25px 0 0}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%;margin-top:4em}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:#fff;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]{bottom:16px;position:unset;width:100%;margin:10px 0 0;color:#ffffffbd}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .mc-btn[_ngcontent-%COMP%]{width:100%}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;text-decoration:none;color:--ion-color-primary;width:100%;height:100%;text-transform:uppercase;font-family:Mont Regular}.dialog-content[_ngcontent-%COMP%]   .mcm-btn-navigate-container-close[_ngcontent-%COMP%]   .store-link.cancel[_ngcontent-%COMP%]{color:#143c5d}"]})}}return m})()},39316:(v,u,o)=>{o.d(u,{b:()=>m});var e=o(73308),d=o(26409),t=o(94934),r=o(45312),p=o(2978),M=o(82571),C=o(33607),P=o(77897);let m=(()=>{class n{constructor(l,a,i,c){this.http=l,this.commonSrv=a,this.baseUrlService=i,this.toastController=c,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+r.c.basePath+"products"}getProducts(l){var a=this;return(0,e.A)(function*(){try{let i=new d.Nl;return l?.limit&&(i=i.append("limit",l?.limit)),yield(0,t.s)(a.http.get(a.url,{params:i}))}catch(i){const s={message:a.commonSrv.getError("",i).message,color:"danger"};return yield a.commonSrv.showToast(s),i}})()}getProduct(l){var a=this;return(0,e.A)(function*(){try{return yield(0,t.s)(a.http.get(`${a.url}/${l}`))}catch(i){const s={message:a.commonSrv.getError("",i).message,color:"danger"};return yield a.commonSrv.showToast(s),i}})()}static{this.\u0275fac=function(a){return new(a||n)(p.KVO(d.Qq),p.KVO(M.h),p.KVO(C.K),p.KVO(P.K_))}}static{this.\u0275prov=p.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})()},31909:(v,u,o)=>{o.d(u,{B:()=>n});var e=o(73308),d=o(99987),t=o(2978),r=o(77897),p=o(13217),M=o(74657),C=o(82571),P=o(56610);function m(g,l){if(1&g&&(t.j41(0,"span"),t.EFF(1),t.k0s()),2&g){const a=t.XpG();t.R7$(1),t.SpI(" : ",a.sponsorName,"")}}let n=(()=>{class g{constructor(a,i,c,s){this.modalController=a,this.fidelityProgramService=i,this.translateService=c,this.commonSrv=s}ngOnInit(){}closeModal(a){this.modalController.dismiss(a)}acceptSponsor(){var a=this;return(0,e.A)(function*(){(yield a.fidelityProgramService.acceptInvitation(a.invitation._id,a.commonSrv?.user?._id).catch(c=>(console.error("Error accepting sponsor:",c),a.commonSrv.showToast({message:a.translateService.currentLang===d.T.French?"Une erreur est survenue.":"An error occurred.",color:"danger"}),null)))&&(a.commonSrv.showToast({message:a.translateService.currentLang===d.T.French?"Parrainage accept\xe9 avec succ\xe8s!":"Sponsorship successfully accepted!",color:"success"}),a.closeModal({accepted:!0}))})()}static{this.\u0275fac=function(i){return new(i||g)(t.rXU(r.W3),t.rXU(p._),t.rXU(M.c$),t.rXU(C.h))}}static{this.\u0275cmp=t.VBU({type:g,selectors:[["app-modal"]],inputs:{invitation:"invitation",sponsorName:"sponsorName"},decls:13,vars:10,consts:[[1,"sponsorship-container"],[1,"icon-container"],["src","/assets/images/referral.png","alt","Sponsor"],[1,"sponsorship-title"],[1,"sponsorship-text"],[4,"ngIf"],["expand","block",1,"accept-button",3,"click"]],template:function(i,c){1&i&&(t.j41(0,"div",0)(1,"div",1),t.nrm(2,"img",2),t.k0s(),t.j41(3,"h2",3),t.EFF(4),t.nI1(5,"translate"),t.k0s(),t.j41(6,"p",4),t.EFF(7),t.nI1(8,"translate"),t.DNE(9,m,2,1,"span",5),t.k0s(),t.j41(10,"ion-button",6),t.bIt("click",function(){return c.acceptSponsor()}),t.EFF(11),t.nI1(12,"translate"),t.k0s()()),2&i&&(t.R7$(4),t.JRh(t.bMT(5,4,"fidelity-page.sponsor-title")),t.R7$(3),t.SpI("",t.bMT(8,6,"fidelity-page.sponsor-desc")," "),t.R7$(2),t.Y8G("ngIf",c.sponsorName),t.R7$(2),t.SpI(" ",t.bMT(12,8,"button.accept")," "))},dependencies:[r.Jm,P.bT,M.D9],styles:['@charset "UTF-8";.sponsorship-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem;text-align:center;height:100%;background-color:#fff;border-radius:10px;box-shadow:0 2px 10px #0000001a}.icon-container[_ngcontent-%COMP%]{width:80px;height:80px;background-color:#e6f0ff;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:1rem}.sponsorship-icon[_ngcontent-%COMP%]{font-size:40px;color:var(--clr-secondary-400)}.sponsorship-title[_ngcontent-%COMP%]{color:#333;font-size:24px;margin:0;margin-bottom:.5rem}.sponsorship-text[_ngcontent-%COMP%]{color:#666;font-size:16px;margin:0;margin-bottom:2rem}.sponsorship-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.accept-button[_ngcontent-%COMP%]{--background: var(--clr-secondary-400);--border-radius: 8px;--padding-top: 1rem;--padding-bottom: 1rem;--padding-start: 2rem;--padding-end: 2rem;font-weight:500;text-transform:uppercase;width:100%;max-width:300px}ion-content[_ngcontent-%COMP%]{--background: transparent}@media (max-width: 320px){.sponsorship-container[_ngcontent-%COMP%]{padding:1rem}.icon-container[_ngcontent-%COMP%]{width:60px;height:60px}.sponsorship-icon[_ngcontent-%COMP%]{font-size:30px}.sponsorship-title[_ngcontent-%COMP%]{font-size:20px}.sponsorship-text[_ngcontent-%COMP%]{font-size:14px}}']})}}return g})()},80153:(v,u,o)=>{o.d(u,{s:()=>d});var d=function(t){return t[t.PENDING=200]="PENDING",t[t.VALIDATED=300]="VALIDATED",t[t.EXPIRED=400]="EXPIRED",t}(d||{})},76060:(v,u,o)=>{o.d(u,{I:()=>e,a:()=>d});var e=function(t){return t[t.CREATE=100]="CREATE",t[t.READ=200]="READ",t[t.DELETE=300]="DELETE",t}(e||{}),d=function(t){return t[t.FEEDBACK=100]="FEEDBACK",t[t.ORDER=200]="ORDER",t[t.GENERAL=300]="GENERAL",t}(d||{})},13217:(v,u,o)=>{o.d(u,{_:()=>l});var e=o(73308),d=o(26409),t=o(45312),r=o(94934),p=o(79801),M=o(80153),C=o(99987),P=o(2978),m=o(33607),n=o(82571),g=o(62049);let l=(()=>{class a{constructor(c,s,_,O){this.baseUrl=c,this.http=s,this.commonSrv=_,this.translateSrv=O,this.ratioOfPointsByVolume=.2,this.base_url=`${this.baseUrl.getOrigin()}${t.c.basePath}`}calculateTotalPointsOrder(c,s){return c.reduce((O,h)=>O+h?.quantity*h.packaging?.unit?.value,0)*(s?.points?.status||p.Th.AMIGO)*this.ratioOfPointsByVolume}getPoints(c){var s=this;return(0,e.A)(function*(){let _=new d.Nl;const{companyId:O}=c;return O&&(_=_.append("companyId",O)),yield(0,r.s)(s.http.get(`${s.base_url}loyalty-program/points`,{params:_}))})()}getBenefitRewards(c){var s=this;return(0,e.A)(function*(){let _=new d.Nl;return c?.monthly&&(_=_.append("monthly",JSON.stringify(c.monthly))),c?.statusValue&&(_=_.append("statusValue",JSON.stringify(c?.statusValue))),c?.annual&&(_=_.append("annual",JSON.stringify(c.annual))),c?.punctual&&(_=_.append("punctual",JSON.stringify(c.punctual))),yield(0,r.s)(s.http.get(`${s.base_url}advantages`,{params:_}))})()}acceptInvitation(c,s){var _=this;return(0,e.A)(function*(){return yield(0,r.s)(_.http.patch(`${_.base_url}invitations/${c}/accept`,{referredUserId:s}))})()}getInvitations(c){var s=this;return(0,e.A)(function*(){let _=new d.Nl;const{limit:O,status:h,prospectTel:E}=c;return O&&(_=_.append("limit",O)),E&&(_=_.append("prospectTel",E)),h&&(_=_.append("status",h)),yield(0,r.s)(s.http.get(`${s.base_url}invitations`,{params:_}))})()}sendReferralInvitation(c,s){var _=this;return(0,e.A)(function*(){try{const O={referrerId:c,prospectTel:s};return yield(0,r.s)(_.http.post(`${_.base_url}invitations/${c}/invite-referral`,O))}catch(O){const h=_.translateSrv.currentLang===C.T.French?"Impossible d'envoyer l'invitation de parrainage":"Failed to send referral invitation";return yield _.commonSrv.showToast({message:O?.error?.message??_.commonSrv.getError(h,O).message,color:"danger"}),O}})()}getPendingInvitationForUser(c){var s=this;return(0,e.A)(function*(){let _=new d.Nl;return _=_.set("prospectTel",+c),_=_.append("status",M.s.PENDING),yield(0,r.s)(s.http.get(`${s.base_url}invitations/pending-invitation`,{params:_}))})()}getDisplayedPoints(c,s){return"pending"===s?c?.unValidated||0:c?.totalPoints||0}getDisplayedPointsLabel(c){return"pending"===c?"fidelity-page.waiting":"fidelity-page.total-points"}static{this.\u0275fac=function(s){return new(s||a)(P.KVO(m.K),P.KVO(d.Qq),P.KVO(n.h),P.KVO(g.E))}}static{this.\u0275prov=P.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}}return a})()},96514:(v,u,o)=>{o.d(u,{I:()=>m});var e=o(73308),d=o(26409),t=o(94934),r=o(35025),p=o.n(r),M=o(45312),C=o(2978),P=o(33607);let m=(()=>{class n{constructor(l,a){this.http=l,this.baseUrlService=a,this.url=this.baseUrlService.getOrigin()+M.c.basePath}getMessages(l){var a=this;return(0,e.A)(function*(){try{let i=new d.Nl;const{email:c,date:s,notifications:_,userId:O,enable:h=!0}=l;return s?.startDate&&s?.endDate&&(i=i.append("startDate",p()(s?.startDate).format("YYYY-MM-DD")),i=i.append("endDate",p()(s?.endDate).format("YYYY-MM-DD"))),c&&(i=i.append("feedback.user.email",c)),O&&(i=i.append("userId",O)),_&&(i=i.append("_id",_)),i=i.append("enable",h),yield(0,t.s)(a.http.get(`${a.url}notifications`,{params:i}))}catch(i){return i}})()}makeRead(l){var a=this;return(0,e.A)(function*(){try{return yield(0,t.s)(a.http.patch(`${a.url}notifications/${l}`,{}))}catch(i){return i}})()}deleteNotifications(l){var a=this;return(0,e.A)(function*(){return(0,t.s)(a.http.patch(`${a.url}notifications/${l[0]}/delete`,{ids:l}))})()}static{this.\u0275fac=function(a){return new(a||n)(C.KVO(d.Qq),C.KVO(P.K))}}static{this.\u0275prov=C.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})()},68896:(v,u,o)=>{o.d(u,{I:()=>g});var e=o(73308),d=o(2978),t=o(94761),r=o(82571),p=o(45312),M=o(26409),C=o(33607),P=o(14599),m=o(94934),n=o(56610);let g=(()=>{class l{constructor(){this.commonSrv=(0,d.WQX)(r.h),this.http=(0,d.WQX)(M.Qq),this.baseUrl=(0,d.WQX)(C.K),this.storageSrv=(0,d.WQX)(P.n),this.base_url=`${this.baseUrl.getOrigin()}${p.c.basePath}`}validateScanData(i){var c=this;return(0,e.A)(function*(){try{return yield(0,m.s)(c.http.post(`${c.base_url}scanner-data`,i))}catch(s){const O={message:c.commonSrv.getError("",s).message,color:"danger"};return yield c.commonSrv.showToast(O),s}})()}checkPermission(){return(0,e.A)(function*(){try{const{camera:i}=yield t.vi.requestPermissions();return"granted"===i}catch(i){return console.log(i),!1}})()}stopScan(){var i=this;return(0,e.A)(function*(){i.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(c=>{c.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,e.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var i=this;return(0,e.A)(function*(){try{if(!(yield i.checkPermission()))return void i.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"});yield i.prepareScanner();const{barcodes:s}=yield t.vi.scan();if(i.restoreUI(),s&&s.length>0)return s[0].displayValue;i.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(c){console.error("Erreur lors du scan",c),i.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{i.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(i){var c=this;return(0,e.A)(function*(){let s=new M.Nl;const{status:_=300,offset:O,limit:h,enable:E=!0,associatedCommercialId:b,startDate:D,endDate:A,customerName:I}=i;void 0!==O&&(s=s.append("offset",O)),h&&(s=s.append("limit",h)),_&&(s=s.append("status",_)),b&&(s=s.append("user.associatedCommercial._id",b)),s=s.append("enable",E),D&&A&&(s=s.append("startDate",new n.vh("fr").transform(D,"YYYY-MM-dd")),s=s.append("endDate",new n.vh("fr").transform(A,"YYYY-MM-dd"))),I&&(s=s.append("user.firstName",I));try{return yield(0,m.s)(c.http.get(`${c.base_url}scanner-data/volume-order-by-particular-client`,{params:s}))}catch(f){const U={message:c.commonSrv.getError("",f).message,color:"danger"};return yield c.commonSrv.showToast(U),f}})()}static{this.\u0275fac=function(c){return new(c||l)}}static{this.\u0275prov=d.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})()},97130:(v,u,o)=>{o.d(u,{I:()=>P});var e=o(73308),d=o(45312),t=o(2978),r=o(77897),p=o(49957),M=o(82571),C=o(14599);let P=(()=>{class m{constructor(g,l,a,i){this.platform=g,this.appVersion=l,this.commonService=a,this.storageService=i}isUpToDate(){var g=this;return(0,e.A)(function*(){let l;try{l=yield g.getAppVersion()}catch(a){"cordova_not_available"===a&&(l=g.platform.is("android")?d.c?.appVersionAndroid:d.c?.appVersionIos)}finally{return g.isLatestVersion(l)}})()}isLatestVersion(g){var l=this;return(0,e.A)(function*(){let a;console.log("appVersion:",g);try{a=yield l.getMinimalVersion()}catch{a=l.platform.is("android")?d.c?.appVersionAndroid:d.c?.appVersionIos}finally{return console.log(a,g),l.isNewerVersion(a,g)}})()}getMinimalVersion(){var g=this;return(0,e.A)(function*(){return g.currentPlatform=g.platform.is("android")?"android":"ios",yield g.commonService.getMinimalAppVersion(g.currentPlatform)})()}getAppVersion(){var g=this;return(0,e.A)(function*(){return yield g.appVersion.getVersionNumber()})()}isNewerVersion(g,l){const a=g?.split("."),i=l?.split("."),c=Math?.max(a?.length,i?.length);for(let s=0;s<c;s++){let _=parseInt(a[s]||"0",10),O=parseInt(i[s]||"0",10);if(O>_)return!0;if(O<_)return!1}return!0}static{this.\u0275fac=function(l){return new(l||m)(t.KVO(r.OD),t.KVO(p.U),t.KVO(M.h),t.KVO(C.n))}}static{this.\u0275prov=t.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()}}]);