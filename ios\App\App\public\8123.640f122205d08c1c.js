"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8123],{98123:(G,C,p)=>{p.r(C),p.d(C,{ion_route:()=>O,ion_route_redirect:()=>N,ion_router:()=>ot,ion_router_link:()=>H});var f=p(73308),d=p(29814),y=p(46184),L=p(42673),E=p(35367);const O=class{constructor(t){(0,d.r)(this,t),this.ionRouteDataChanged=(0,d.e)(this,"ionRouteDataChanged",7),this.url=""}onUpdate(t){this.ionRouteDataChanged.emit(t)}onComponentProps(t,e){if(t===e)return;const n=t?Object.keys(t):[],r=e?Object.keys(e):[];if(n.length===r.length){for(const o of n)if(t[o]!==e[o])return void this.onUpdate(t)}else this.onUpdate(t)}connectedCallback(){this.ionRouteDataChanged.emit()}static get watchers(){return{url:["onUpdate"],component:["onUpdate"],componentProps:["onComponentProps"]}}},N=class{constructor(t){(0,d.r)(this,t),this.ionRouteRedirectChanged=(0,d.e)(this,"ionRouteRedirectChanged",7)}propDidChange(){this.ionRouteRedirectChanged.emit()}connectedCallback(){this.ionRouteRedirectChanged.emit()}static get watchers(){return{from:["propDidChange"],to:["propDidChange"]}}},c="root",h="forward",_=t=>"/"+t.filter(n=>n.length>0).join("/"),g=t=>{let n,e=[""];if(null!=t){const r=t.indexOf("?");r>-1&&(n=t.substring(r+1),t=t.substring(0,r)),e=t.split("/").map(o=>o.trim()).filter(o=>o.length>0),0===e.length&&(e=[""])}return{segments:e,queryString:n}},j=function(){var t=(0,f.A)(function*(e,n,r,o,s=!1,i){try{const a=A(e);if(o>=n.length||!a)return s;yield new Promise(v=>(0,y.c)(a,v));const l=n[o],u=yield a.setRouteId(l.id,l.params,r,i);return u.changed&&(r=c,s=!0),s=yield j(u.element,n,r,o+1,s,i),u.markVisible&&(yield u.markVisible()),s}catch(a){return console.error(a),!1}});return function(n,r,o,s){return t.apply(this,arguments)}}(),Q=function(){var t=(0,f.A)(function*(e){const n=[];let r,o=e;for(;r=A(o);){const s=yield r.getRouteId();if(!s)break;o=s.element,s.element=void 0,n.push(s)}return{ids:n,outlet:r}});return function(n){return t.apply(this,arguments)}}(),I=":not([no-router]) ion-nav, :not([no-router]) ion-tabs, :not([no-router]) ion-router-outlet",A=t=>{if(t)return t.matches(I)?t:t.querySelector(I)??void 0},M=(t,e)=>e.find(n=>((t,e)=>{const{from:n,to:r}=e;if(void 0===r||n.length>t.length)return!1;for(let o=0;o<n.length;o++){const s=n[o];if("*"===s)return!0;if(s!==t[o])return!1}return n.length===t.length})(t,n)),Z=(t,e)=>{const n=Math.min(t.length,e.length);let r=0;for(let o=0;o<n;o++){const s=t[o],i=e[o];if(s.id.toLowerCase()!==i.id)break;if(s.params){const a=Object.keys(s.params);if(a.length===i.segments.length){const l=a.map(u=>`:${u}`);for(let u=0;u<l.length&&l[u].toLowerCase()===i.segments[u];u++)r++}}r++}return r},V=(t,e)=>{const n=new nt(t);let o,r=!1;for(let i=0;i<e.length;i++){const a=e[i].segments;if(""===a[0])r=!0;else{for(const l of a){const u=n.next();if(":"===l[0]){if(""===u)return null;o=o||[],(o[i]||(o[i]={}))[l.slice(1)]=u}else if(u!==l)return null}r=!1}}return r&&r!==(""===n.next())?null:o?e.map((i,a)=>({id:i.id,segments:i.segments,params:W(i.params,o[a]),beforeEnter:i.beforeEnter,beforeLeave:i.beforeLeave})):e},W=(t,e)=>t||e?Object.assign(Object.assign({},t),e):void 0,T=(t,e)=>{let n=null,r=0;for(const o of e){const s=V(t,o);if(null!==s){const i=et(s);i>r&&(r=i,n=s)}}return n},et=t=>{let e=1,n=1;for(const r of t)for(const o of r.segments)":"===o[0]?e+=Math.pow(1,n):""!==o&&(e+=Math.pow(2,n)),n++;return e};class nt{constructor(e){this.segments=e.slice()}next(){return this.segments.length>0?this.segments.shift():""}}const w=(t,e)=>e in t?t[e]:t.hasAttribute(e)?t.getAttribute(e):null,k=t=>Array.from(t.children).filter(e=>"ION-ROUTE-REDIRECT"===e.tagName).map(e=>{const n=w(e,"to");return{from:g(w(e,"from")).segments,to:null==n?void 0:g(n)}}),P=t=>rt($(t)),$=t=>Array.from(t.children).filter(e=>"ION-ROUTE"===e.tagName&&e.component).map(e=>{const n=w(e,"component");return{segments:g(w(e,"url")).segments,id:n.toLowerCase(),params:e.componentProps,beforeLeave:e.beforeLeave,beforeEnter:e.beforeEnter,children:$(e)}}),rt=t=>{const e=[];for(const n of t)B([],e,n);return e},B=(t,e,n)=>{if(t=[...t,{id:n.id,segments:n.segments,params:n.params,beforeLeave:n.beforeLeave,beforeEnter:n.beforeEnter}],0!==n.children.length)for(const r of n.children)B(t,e,r);else e.push(t)},ot=class{constructor(t){(0,d.r)(this,t),this.ionRouteWillChange=(0,d.e)(this,"ionRouteWillChange",7),this.ionRouteDidChange=(0,d.e)(this,"ionRouteDidChange",7),this.previousPath=null,this.busy=!1,this.state=0,this.lastState=0,this.root="/",this.useHash=!0}componentWillLoad(){var t=this;return(0,f.A)(function*(){yield A(document.body)?Promise.resolve():new Promise(t=>{window.addEventListener("ionNavWillLoad",()=>t(),{once:!0})});const e=yield t.runGuards(t.getSegments());if(!0!==e){if("object"==typeof e){const{redirect:n}=e,r=g(n);t.setSegments(r.segments,c,r.queryString),yield t.writeNavStateRoot(r.segments,c)}}else yield t.onRoutesChanged()})()}componentDidLoad(){window.addEventListener("ionRouteRedirectChanged",(0,y.q)(this.onRedirectChanged.bind(this),10)),window.addEventListener("ionRouteDataChanged",(0,y.q)(this.onRoutesChanged.bind(this),100))}onPopState(){var t=this;return(0,f.A)(function*(){const e=t.historyDirection();let n=t.getSegments();const r=yield t.runGuards(n);if(!0!==r){if("object"!=typeof r)return!1;n=g(r.redirect).segments}return t.writeNavStateRoot(n,e)})()}onBackButton(t){t.detail.register(0,e=>{this.back(),e()})}canTransition(){var t=this;return(0,f.A)(function*(){const e=yield t.runGuards();return!0===e||"object"==typeof e&&e.redirect})()}push(t){var e=this;return(0,f.A)(function*(n,r="forward",o){var s;if(n.startsWith(".")){const l=null!==(s=e.previousPath)&&void 0!==s?s:"/",u=new URL(n,`https://host/${l}`);n=u.pathname+u.search}let i=g(n);const a=yield e.runGuards(i.segments);if(!0!==a){if("object"!=typeof a)return!1;i=g(a.redirect)}return e.setSegments(i.segments,r,i.queryString),e.writeNavStateRoot(i.segments,r,o)}).apply(this,arguments)}back(){return window.history.back(),Promise.resolve(this.waitPromise)}printDebug(){var t=this;return(0,f.A)(function*(){(t=>{console.group(`[ion-core] ROUTES[${t.length}]`);for(const e of t){const n=[];e.forEach(o=>n.push(...o.segments));const r=e.map(o=>o.id);console.debug(`%c ${_(n)}`,"font-weight: bold; padding-left: 20px","=>\t",`(${r.join(", ")})`)}console.groupEnd()})(P(t.el)),(t=>{console.group(`[ion-core] REDIRECTS[${t.length}]`);for(const e of t)e.to&&console.debug("FROM: ",`$c ${_(e.from)}`,"font-weight: bold"," TO: ",`$c ${_(e.to.segments)}`,"font-weight: bold");console.groupEnd()})(k(t.el))})()}navChanged(t){var e=this;return(0,f.A)(function*(){if(e.busy)return console.warn("[ion-router] router is busy, navChanged was cancelled"),!1;const{ids:n,outlet:r}=yield Q(window.document.body),s=((t,e)=>{let n=null,r=0;for(const o of e){const s=Z(t,o);s>r&&(n=o,r=s)}return n?n.map((o,s)=>{var i;return{id:o.id,segments:o.segments,params:W(o.params,null===(i=t[s])||void 0===i?void 0:i.params)}}):null})(n,P(e.el));if(!s)return console.warn("[ion-router] no matching URL for ",n.map(a=>a.id)),!1;const i=(t=>{const e=[];for(const n of t)for(const r of n.segments)if(":"===r[0]){const o=n.params&&n.params[r.slice(1)];if(!o)return null;e.push(o)}else""!==r&&e.push(r);return e})(s);return i?(e.setSegments(i,t),yield e.safeWriteNavState(r,s,c,i,null,n.length),!0):(console.warn("[ion-router] router could not match path because some required param is missing"),!1)})()}onRedirectChanged(){const t=this.getSegments();t&&M(t,k(this.el))&&this.writeNavStateRoot(t,c)}onRoutesChanged(){return this.writeNavStateRoot(this.getSegments(),c)}historyDirection(){var t;const e=window;null===e.history.state&&(this.state++,e.history.replaceState(this.state,e.document.title,null===(t=e.document.location)||void 0===t?void 0:t.href));const n=e.history.state,r=this.lastState;return this.lastState=n,n>r||n>=r&&r>0?h:n<r?"back":c}writeNavStateRoot(t,e,n){var r=this;return(0,f.A)(function*(){if(!t)return console.error("[ion-router] URL is not part of the routing set"),!1;const o=k(r.el),s=M(t,o);let i=null;if(s){const{segments:u,queryString:v}=s.to;r.setSegments(u,e,v),i=s.from,t=u}const a=P(r.el),l=T(t,a);return l?r.safeWriteNavState(document.body,l,e,t,i,0,n):(console.error("[ion-router] the path does not match any route"),!1)})()}safeWriteNavState(t,e,n,r,o){var s=this;return(0,f.A)(function*(i,a,l,u,v,x=0,U){const R=yield s.lock();let D=!1;try{D=yield s.writeNavState(i,a,l,u,v,x,U)}catch(it){console.error(it)}return R(),D}).apply(this,arguments)}lock(){var t=this;return(0,f.A)(function*(){const e=t.waitPromise;let n;return t.waitPromise=new Promise(r=>n=r),void 0!==e&&(yield e),n})()}runGuards(){var t=this;return(0,f.A)(function*(e=t.getSegments(),n){if(void 0===n&&(n=g(t.previousPath).segments),!e||!n)return!0;const r=P(t.el),o=T(n,r),s=o&&o[o.length-1].beforeLeave,i=!s||(yield s());if(!1===i||"object"==typeof i)return i;const a=T(e,r),l=a&&a[a.length-1].beforeEnter;return!l||l()}).apply(this,arguments)}writeNavState(t,e,n,r,o){var s=this;return(0,f.A)(function*(i,a,l,u,v,x=0,U){if(s.busy)return console.warn("[ion-router] router is busy, transition was cancelled"),!1;s.busy=!0;const R=s.routeChangeEvent(u,v);R&&s.ionRouteWillChange.emit(R);const D=yield j(i,a,l,x,!1,U);return s.busy=!1,R&&s.ionRouteDidChange.emit(R),D}).apply(this,arguments)}setSegments(t,e,n){this.state++,((t,e,n,r,o,s,i)=>{const a=((t,e,n)=>{let r=_(t);return e&&(r="#"+r),void 0!==n&&(r+="?"+n),r})([...g(e).segments,...r],n,i);o===h?t.pushState(s,"",a):t.replaceState(s,"",a)})(window.history,this.root,this.useHash,t,e,this.state,n)}getSegments(){return((t,e,n)=>{const r=g(this.root).segments,o=n?t.hash.slice(1):t.pathname;return((t,e)=>{if(t.length>e.length)return null;if(t.length<=1&&""===t[0])return e;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return null;return e.length===t.length?[""]:e.slice(t.length)})(r,g(o).segments)})(window.location,0,this.useHash)}routeChangeEvent(t,e){const n=this.previousPath,r=_(t);return this.previousPath=r,r===n?null:{from:n,redirectedFrom:e?_(e):null,to:r}}get el(){return(0,d.i)(this)}},H=class{constructor(t){(0,d.r)(this,t),this.routerDirection="forward",this.onClick=e=>{(0,E.o)(this.href,e,this.routerDirection,this.routerAnimation)}}render(){const t=(0,L.b)(this),e={href:this.href,rel:this.rel,target:this.target};return(0,d.h)(d.H,{onClick:this.onClick,class:(0,E.c)(this.color,{[t]:!0,"ion-activatable":!0})},(0,d.h)("a",Object.assign({},e),(0,d.h)("slot",null)))}};H.style=":host{--background:transparent;--color:var(--ion-color-primary, #3880ff);background:var(--background);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}a{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit}"},35367:(G,C,p)=>{p.d(C,{c:()=>y,g:()=>E,h:()=>d,o:()=>N});var f=p(73308);const d=(c,h)=>null!==h.closest(c),y=(c,h)=>"string"==typeof c&&c.length>0?Object.assign({"ion-color":!0,[`ion-color-${c}`]:!0},h):h,E=c=>{const h={};return(c=>void 0!==c?(Array.isArray(c)?c:c.split(" ")).filter(m=>null!=m).map(m=>m.trim()).filter(m=>""!==m):[])(c).forEach(m=>h[m]=!0),h},O=/^[a-z][a-z0-9+\-.]*:/,N=function(){var c=(0,f.A)(function*(h,m,_,S){if(null!=h&&"#"!==h[0]&&!O.test(h)){const b=document.querySelector("ion-router");if(b)return m?.preventDefault(),b.push(h,_,S)}return!1});return function(m,_,S,b){return c.apply(this,arguments)}}()}}]);