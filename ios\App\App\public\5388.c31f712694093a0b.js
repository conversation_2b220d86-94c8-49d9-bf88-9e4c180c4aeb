"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5388],{95388:(w,C,a)=>{a.r(C),a.d(C,{FeedbackPageModule:()=>x});var s=a(56610),c=a(77897),p=a(37222),g=a(77575),d=a(73308),M=a(4238),P=a(58133),n=a(2978),u=a(28639),f=a(82571),h=a(14599),O=a(32205),_=a(74657);function v(e,m){1&e&&n.nrm(0,"ion-progress-bar",17)}function k(e,m){if(1&e){const t=n.RV6();n.j41(0,"ion-card",18),n.bIt("click",function(){const r=n.eBV(t).$implicit,l=n.XpG();return n.Njj(l.showFeedbackDetail(r))}),n.j41(1,"ion-card-content")(2,"div",19)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-label"),n.EFF(7),n.nI1(8,"translate"),n.k0s(),n.j41(9,"ion-label"),n.EFF(10),n.nI1(11,"date"),n.k0s(),n.j41(12,"ion-label"),n.EFF(13),n.nI1(14,"translate"),n.j41(15,"strong",20),n.nI1(16,"claimStatusColor"),n.EFF(17),n.nI1(18,"claimStatus"),n.k0s()()(),n.j41(19,"div",21),n.nrm(20,"ion-icon",22),n.k0s()()()}if(2&e){const t=m.$implicit;n.R7$(4),n.Lme("",n.bMT(5,8,"claims-page.reference")," ",(null==t?null:t.ref)||"N/A",""),n.R7$(3),n.Lme("",n.bMT(8,10,"claims-page.category")," ",(null==t||null==t.category?null:t.category.label)||"N/A",""),n.R7$(3),n.SpI("Date ",n.i5U(11,12,null==t?null:t.created_at,"dd/MM/YYYY \xe0 HH:mm"),""),n.R7$(3),n.SpI("",n.bMT(14,15,"claims-page.status")," "),n.R7$(2),n.Y8G("ngClass",n.bMT(16,17,null==t?null:t.status)),n.R7$(2),n.JRh(n.bMT(18,19,null==t?null:t.status))}}function y(e,m){1&e&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",23),n.nrm(2,"ion-skeleton-text",24),n.k0s()()),2&e&&(n.R7$(2),n.Y8G("animated",!0))}function F(e,m){1&e&&(n.j41(0,"div",25),n.nrm(1,"ion-img",26),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&e&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"claims-page.empty-claim")," "))}const b=function(e){return{active:e}},R=[{path:"",component:(()=>{class e{showFilter(){throw new Error("Method not implemented.")}constructor(t,i,o,r,l){this.location=t,this.claimFormService=i,this.commonService=o,this.storageService=r,this.router=l,this.isLoading=!1,this.tabOption=M.n.CREATED,this.claimStatus=M.n,this.claims=[],this.skeletons=[1,2,3,4,5,6],this.offset=0,this.limit=20}doRefresh(t){var i=this;return(0,d.A)(function*(){i.filterData=null,i.offset=0,i.claims=[],yield i.getClaimsByUser(),t.target.complete()})()}ionViewWillEnter(){var t=this;return(0,d.A)(function*(){t.isLoading=!0,t.storageService.getUserConnected(),t.claims=[],yield t.getClaimsByUser()})()}ionViewDidEnter(){this.isLoading=!1}getFlowClaims(t){var i=this;return(0,d.A)(function*(){i.offset=i.offset+i.limit+1,yield i.getClaimsByUser(),t.target.complete()})()}getClaimsByUser(){var t=this;return(0,d.A)(function*(){t.skeletons=[1,2,3,4,5,6],t.isLoading=!0;const i={status:t.tabOption,limit:t.limit,offset:t.offset,userId:t.commonService.user.category!==P.s.Commercial?t.commonService.user?._id:null,companyId:t.commonService?.user?.category===P.s?.CompanyUser&&t.commonService?.user?.associatedCompanies?.length>0?t.commonService.user?.company?._id:null,...t.filterData},o=(yield t.claimFormService.getAllClaims(i)).data;t.claims=t.claims.concat(o),t.isLoading=!1,t.skeletons=[]})()}showFeedbackDetail(t){this.claimFormService.Feedbackdetails=t,this.router.navigate(["/navigation/feedback-detail"])}back(){this.location.back()}static{this.\u0275fac=function(i){return new(i||e)(n.rXU(s.aZ),n.rXU(u.a),n.rXU(f.h),n.rXU(h.n),n.rXU(g.Ix))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-feedback"]],decls:33,vars:29,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click"],[1,"claim-list"],["class","claim",3,"click",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[3,"ionInfinite"],["class","empty-list",4,"ngIf"],[1,"btn-validate"],["routerLink","/navigation/claim-form","color","primary","expand","block",1,"btn--meduim","btn--upper"],["type","indeterminate"],[1,"claim",3,"click"],[1,"detail"],[3,"ngClass"],[1,"icon"],["src","/assets/icons/arrow-forward-green.svg"],[1,"skeleton"],[3,"animated"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(i,o){1&i&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return o.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content",3),n.DNE(7,v,1,0,"ion-progress-bar",4),n.j41(8,"div",5)(9,"ion-refresher",6),n.bIt("ionRefresh",function(l){return o.doRefresh(l)}),n.nrm(10,"ion-refresher-content",7),n.nI1(11,"translate"),n.nI1(12,"translate"),n.k0s(),n.j41(13,"ion-tab-bar",8)(14,"ion-tab-button",9),n.bIt("click",function(){return o.tabOption=o.claimStatus.CREATED,o.claims=[],o.getClaimsByUser()}),n.j41(15,"ion-title"),n.EFF(16),n.nI1(17,"translate"),n.k0s()(),n.j41(18,"ion-tab-button",9),n.bIt("click",function(){return o.tabOption=o.claimStatus.TREAT,o.claims=[],o.getClaimsByUser()}),n.j41(19,"ion-title"),n.EFF(20),n.nI1(21,"translate"),n.k0s()()(),n.j41(22,"div",10),n.DNE(23,k,21,21,"ion-card",11),n.DNE(24,y,3,1,"ion-cart",12),n.j41(25,"ion-infinite-scroll",13),n.bIt("ionInfinite",function(l){return o.getFlowClaims(l)}),n.nrm(26,"ion-infinite-scroll-content"),n.k0s()(),n.DNE(27,F,5,3,"div",14),n.k0s()(),n.j41(28,"div",15)(29,"ion-button",16)(30,"ion-label"),n.EFF(31),n.nI1(32,"translate"),n.k0s()()()),2&i&&(n.R7$(4),n.JRh(n.bMT(5,13,"claims-page.title")),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",o.isLoading),n.R7$(3),n.FS9("pullingText",n.bMT(11,15,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(12,17,"refresher.refreshing"),"..."),n.R7$(4),n.Y8G("ngClass",n.eq3(25,b,o.tabOption===o.claimStatus.CREATED)),n.R7$(2),n.JRh(n.bMT(17,19,"claim-page.tabs.in-progres")),n.R7$(2),n.Y8G("ngClass",n.eq3(27,b,o.tabOption===o.claimStatus.TREAT)),n.R7$(2),n.JRh(n.bMT(21,21,"claim-page.tabs.treat")),n.R7$(3),n.Y8G("ngForOf",o.claims),n.R7$(1),n.Y8G("ngForOf",o.skeletons),n.R7$(3),n.Y8G("ngIf",(null==o.claims?null:o.claims.length)<=0&&!o.isLoading),n.R7$(4),n.SpI(" ",n.bMT(32,23,"claims-page.add-claim")," "))},dependencies:[s.YU,s.Sq,s.bT,c.Jm,c.b_,c.I9,c.W9,c.eU,c.iq,c.KW,c.Ax,c.Hp,c.he,c.FH,c.To,c.Ki,c.ds,c.Jq,c.qW,c.Zx,c.BC,c.ai,c.N7,g.Wk,s.vh,O.t,O.w,_.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background:#cfcfcf;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));padding-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{margin:auto;min-height:40px;gap:1em;margin-bottom:calc(50 * var(--res));border:none}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-tab-button[_ngcontent-%COMP%]{min-width:-moz-fit-content;min-width:fit-content}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:calc(45 * var(--res));color:#6d839d;font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#6d839d;border-bottom:3px solid #419CFB}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#0b305c}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]{border-bottom:1px solid #8597AD;box-shadow:none;border-radius:unset;margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont SemiBold;color:#0b305c;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-primary-400[_ngcontent-%COMP%]{color:#0d7d3d!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-success-200[_ngcontent-%COMP%]{background-color:#b8ddb6!important;padding:3px 6px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-info-500[_ngcontent-%COMP%]{color:#0af!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-100[_ngcontent-%COMP%]{background-color:#cef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-default-400[_ngcontent-%COMP%]{color:#fff!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-500[_ngcontent-%COMP%]{background-color:#0af!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-tertiary-200[_ngcontent-%COMP%]{background-color:#f0efef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]   .claim[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:20vh;display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-infinite-scroll-content[_ngcontent-%COMP%]{min-height:15px}.btn-validate[_ngcontent-%COMP%]{margin:calc(41 * var(--res))}"]})}}return e})()}];let I=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[g.iI.forChild(R),g.iI]})}}return e})();var T=a(93887);let x=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[s.MD,p.YN,c.bv,I,T.G,_.h]})}}return e})()}}]);