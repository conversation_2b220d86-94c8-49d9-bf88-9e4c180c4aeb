"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3868],{14561:(G,j,u)=>{u.d(j,{a:()=>M,b:()=>d,p:()=>A});const A=(T,...h)=>console.warn(`[Ionic Warning]: ${T}`,...h),d=(T,...h)=>console.error(`[Ionic Error]: ${T}`,...h),M=(T,...h)=>console.error(`<${T.tagName.toLowerCase()}> must be used inside ${h.join(" or ")}.`)},63868:(G,j,u)=>{u.r(j),u.d(j,{ion_popover:()=>ee});var A=u(73308),d=u(29814),M=u(42673),T=u(9626),h=u(46184),F=u(14561),C=u(97255),m=u(35367),g=u(89345),p=u(28775);u(94706);const N=(o,e,t)=>{const r=e.getBoundingClientRect(),n=r.height;let i=r.width;return"cover"===o&&t&&(i=t.getBoundingClientRect().width),{contentWidth:i,contentHeight:n}},ne=(o,e,t)=>{let r=[];switch(e){case"hover":let n;r=[{eventName:"mouseenter",callback:(i=(0,A.A)(function*(s){s.stopPropagation(),n&&clearTimeout(n),n=setTimeout(()=>{(0,h.r)(()=>{t.presentFromTrigger(s),n=void 0})},100)}),function(a){return i.apply(this,arguments)})},{eventName:"mouseleave",callback:i=>{n&&clearTimeout(n);const s=i.relatedTarget;s&&s.closest("ion-popover")!==t&&t.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:i=>i.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:i=>t.presentFromTrigger(i,!0)}];break;case"context-menu":r=[{eventName:"contextmenu",callback:i=>{i.preventDefault(),t.presentFromTrigger(i)}},{eventName:"click",callback:i=>i.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:i=>t.presentFromTrigger(i,!0)}];break;default:r=[{eventName:"click",callback:i=>t.presentFromTrigger(i)},{eventName:"ionPopoverActivateTrigger",callback:i=>t.presentFromTrigger(i,!0)}]}var i;return r.forEach(({eventName:n,callback:i})=>o.addEventListener(n,i)),o.setAttribute("data-ion-popover-trigger","true"),()=>{r.forEach(({eventName:n,callback:i})=>o.removeEventListener(n,i)),o.removeAttribute("data-ion-popover-trigger")}},H=(o,e)=>e&&"ION-ITEM"===e.tagName?o.findIndex(t=>t===e):-1,U=o=>{const t=(0,h.g)(o).querySelector("button");t&&(0,h.r)(()=>t.focus())},ce=o=>{const e=function(){var t=(0,A.A)(function*(r){var n;const i=document.activeElement;let s=[];const a=null===(n=r.target)||void 0===n?void 0:n.tagName;if("ION-POPOVER"===a||"ION-ITEM"===a){try{s=Array.from(o.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(r.key){case"ArrowLeft":(yield o.getParentPopover())&&o.dismiss(void 0,void 0,!1);break;case"ArrowDown":r.preventDefault();const l=((o,e)=>o[H(o,e)+1])(s,i);void 0!==l&&U(l);break;case"ArrowUp":r.preventDefault();const y=((o,e)=>o[H(o,e)-1])(s,i);void 0!==y&&U(y);break;case"Home":r.preventDefault();const v=s[0];void 0!==v&&U(v);break;case"End":r.preventDefault();const b=s[s.length-1];void 0!==b&&U(b);break;case"ArrowRight":case" ":case"Enter":if(i&&(o=>o.hasAttribute("data-ion-popover-trigger"))(i)){const x=new CustomEvent("ionPopoverActivateTrigger");i.dispatchEvent(x)}}}});return function(n){return t.apply(this,arguments)}}();return o.addEventListener("keydown",e),()=>o.removeEventListener("keydown",e)},J=(o,e,t,r,n,i,s,a,c,l,y)=>{var v;let b={top:0,left:0,width:0,height:0};if("event"===i){if(!y)return c;b={top:y.clientY,left:y.clientX,width:1,height:1}}else{const f=y,S=l||(null===(v=f?.detail)||void 0===v?void 0:v.ionShadowTarget)||f?.target;if(!S)return c;const D=S.getBoundingClientRect();b={top:D.top,left:D.left,width:D.width,height:D.height}}const x=de(s,b,e,t,r,n,o),P=fe(a,s,b,e,t),_=x.top+P.top,E=x.left+P.left,{arrowTop:w,arrowLeft:I}=le(s,r,n,_,E,e,t,o),{originX:k,originY:O}=pe(s,a,o);return{top:_,left:E,referenceCoordinates:b,arrowTop:w,arrowLeft:I,originX:k,originY:O}},pe=(o,e,t)=>{switch(o){case"top":return{originX:Q(e),originY:"bottom"};case"bottom":return{originX:Q(e),originY:"top"};case"left":return{originX:"right",originY:X(e)};case"right":return{originX:"left",originY:X(e)};case"start":return{originX:t?"left":"right",originY:X(e)};case"end":return{originX:t?"right":"left",originY:X(e)}}},Q=o=>{switch(o){case"start":return"left";case"center":return"center";case"end":return"right"}},X=o=>{switch(o){case"start":return"top";case"center":return"center";case"end":return"bottom"}},le=(o,e,t,r,n,i,s,a)=>{const c={arrowTop:r+s/2-e/2,arrowLeft:n+i-e/2},l={arrowTop:r+s/2-e/2,arrowLeft:n-1.5*e};switch(o){case"top":return{arrowTop:r+s,arrowLeft:n+i/2-e/2};case"bottom":return{arrowTop:r-t,arrowLeft:n+i/2-e/2};case"left":return c;case"right":return l;case"start":return a?l:c;case"end":return a?c:l;default:return{arrowTop:0,arrowLeft:0}}},de=(o,e,t,r,n,i,s)=>{const a={top:e.top,left:e.left-t-n},c={top:e.top,left:e.left+e.width+n};switch(o){case"top":return{top:e.top-r-i,left:e.left};case"right":return c;case"bottom":return{top:e.top+e.height+i,left:e.left};case"left":return a;case"start":return s?c:a;case"end":return s?a:c}},fe=(o,e,t,r,n)=>{switch(o){case"center":return ue(e,t,r,n);case"end":return he(e,t,r,n);default:return{top:0,left:0}}},he=(o,e,t,r)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(r-e.height),left:0};default:return{top:0,left:-(t-e.width)}}},ue=(o,e,t,r)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(r/2-e.height/2),left:0};default:return{top:0,left:-(t/2-e.width/2)}}},Z=(o,e,t,r,n,i,s,a,c,l,y,v,b=0,x=0,P=0)=>{let _=b;const E=x;let k,w=t,I=e,O=l,L=y,f=!1,S=!1;const D=v?v.top+v.height:i/2-a/2,$=v?v.height:0;let R=!1;return w<r+c?(w=r,f=!0,O="left"):s+r+w+c>n&&(S=!0,w=n-s-r,O="right"),D+$+a>i&&("top"===o||"bottom"===o)&&(D-a>0?(I=Math.max(12,D-a-$-(P-1)),_=I+a,L="bottom",R=!0):k=r),{top:I,left:w,bottom:k,originX:O,originY:L,checkSafeAreaLeft:f,checkSafeAreaRight:S,arrowTop:_,arrowLeft:E,addPopoverBottomClass:R}},ge=(o,e)=>{var t;const{event:r,size:n,trigger:i,reference:s,side:a,align:c}=e,l=o.ownerDocument,y="rtl"===l.dir,v=l.defaultView.innerWidth,b=l.defaultView.innerHeight,x=(0,h.g)(o),P=x.querySelector(".popover-content"),_=x.querySelector(".popover-arrow"),E=i||(null===(t=r?.detail)||void 0===t?void 0:t.ionShadowTarget)||r?.target,{contentWidth:w,contentHeight:I}=N(n,P,E),{arrowWidth:k,arrowHeight:O}=(o=>{if(!o)return{arrowWidth:0,arrowHeight:0};const{width:e,height:t}=o.getBoundingClientRect();return{arrowWidth:e,arrowHeight:t}})(_),f=J(y,w,I,k,O,s,a,c,{top:b/2-I/2,left:v/2-w/2,originX:y?"right":"left",originY:"top"},i,r),S="cover"===n?0:5,D="cover"===n?0:25,{originX:$,originY:R,top:Y,left:W,bottom:K,checkSafeAreaLeft:V,checkSafeAreaRight:Ae,arrowTop:Te,arrowLeft:Ee,addPopoverBottomClass:Ie}=Z(a,f.top,f.left,S,v,b,w,I,D,f.originX,f.originY,f.referenceCoordinates,f.arrowTop,f.arrowLeft,O),Oe=(0,p.c)(),te=(0,p.c)(),oe=(0,p.c)();return te.addElement(x.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),oe.addElement(x.querySelector(".popover-wrapper")).fromTo("opacity",.01,1),Oe.easing("ease").duration(100).beforeAddWrite(()=>{"cover"===n&&o.style.setProperty("--width",`${w}px`),Ie&&o.classList.add("popover-bottom"),void 0!==K&&P.style.setProperty("bottom",`${K}px`);let B=`${W}px`;V&&(B=`${W}px + var(--ion-safe-area-left, 0)`),Ae&&(B=`${W}px - var(--ion-safe-area-right, 0)`),P.style.setProperty("top",`calc(${Y}px + var(--offset-y, 0))`),P.style.setProperty("left",`calc(${B} + var(--offset-x, 0))`),P.style.setProperty("transform-origin",`${R} ${$}`),null!==_&&(((o,e=!1,t,r)=>!(!t&&!r||"top"!==o&&"bottom"!==o&&e))(a,f.top!==Y||f.left!==W,r,i)?(_.style.setProperty("top",`calc(${Te}px + var(--offset-y, 0))`),_.style.setProperty("left",`calc(${Ee}px + var(--offset-x, 0))`)):_.style.setProperty("display","none"))}).addAnimation([te,oe])},be=o=>{const e=(0,h.g)(o),t=e.querySelector(".popover-content"),r=e.querySelector(".popover-arrow"),n=(0,p.c)(),i=(0,p.c)(),s=(0,p.c)();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),n.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin"),r&&(r.style.removeProperty("top"),r.style.removeProperty("left"),r.style.removeProperty("display"))}).duration(300).addAnimation([i,s])},we=(o,e)=>{var t;const{event:r,size:n,trigger:i,reference:s,side:a,align:c}=e,l=o.ownerDocument,y="rtl"===l.dir,v=l.defaultView.innerWidth,b=l.defaultView.innerHeight,x=(0,h.g)(o),P=x.querySelector(".popover-content"),_=i||(null===(t=r?.detail)||void 0===t?void 0:t.ionShadowTarget)||r?.target,{contentWidth:E,contentHeight:w}=N(n,P,_),k=J(y,E,w,0,0,s,a,c,{top:b/2-w/2,left:v/2-E/2,originX:y?"right":"left",originY:"top"},i,r),O="cover"===n?0:12,{originX:L,originY:f,top:S,left:D,bottom:$}=Z(a,k.top,k.left,O,v,b,E,w,0,k.originX,k.originY,k.referenceCoordinates),R=(0,p.c)(),Y=(0,p.c)(),W=(0,p.c)(),K=(0,p.c)(),V=(0,p.c)();return Y.addElement(x.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),W.addElement(x.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),K.addElement(P).beforeStyles({top:`calc(${S}px + var(--offset-y, 0px))`,left:`calc(${D}px + var(--offset-x, 0px))`,"transform-origin":`${f} ${L}`}).beforeAddWrite(()=>{void 0!==$&&P.style.setProperty("bottom",`${$}px`)}).fromTo("transform","scale(0.8)","scale(1)"),V.addElement(x.querySelector(".popover-viewport")).fromTo("opacity",.01,1),R.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{"cover"===n&&o.style.setProperty("--width",`${E}px`),"bottom"===f&&o.classList.add("popover-bottom")}).addAnimation([Y,W,K,V])},ye=o=>{const e=(0,h.g)(o),t=e.querySelector(".popover-content"),r=(0,p.c)(),n=(0,p.c)(),i=(0,p.c)();return n.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),i.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),r.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin")}).duration(150).addAnimation([n,i])},ee=class{constructor(o){(0,d.r)(this,o),this.didPresent=(0,d.e)(this,"ionPopoverDidPresent",7),this.willPresent=(0,d.e)(this,"ionPopoverWillPresent",7),this.willDismiss=(0,d.e)(this,"ionPopoverWillDismiss",7),this.didDismiss=(0,d.e)(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=(0,d.e)(this,"didPresent",7),this.willPresentShorthand=(0,d.e)(this,"willPresent",7),this.willDismissShorthand=(0,d.e)(this,"willDismiss",7),this.didDismissShorthand=(0,d.e)(this,"didDismiss",7),this.ionMount=(0,d.e)(this,"ionMount",7),this.parentPopover=null,this.popoverIndex=De++,this.coreDelegate=(0,T.C)(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,C.B)},this.onLifecycle=e=>{const t=this.usersElement,r=ke[e.type];if(t&&r){const n=new CustomEvent(r,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(n)}},this.configureTriggerInteraction=()=>{const{trigger:e,triggerAction:t,el:r,destroyTriggerInteraction:n}=this;if(n&&n(),void 0===e)return;const i=this.triggerEl=void 0!==e?document.getElementById(e):null;i?this.destroyTriggerInteraction=ne(i,t,r):(0,F.p)(`A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el)},this.configureKeyboardInteraction=()=>{const{destroyKeyboardInteraction:e,el:t}=this;e&&e(),this.destroyKeyboardInteraction=ce(t)},this.configureDismissInteraction=()=>{const{destroyDismissInteraction:e,parentPopover:t,triggerAction:r,triggerEl:n,el:i}=this;!t||!n||(e&&e(),this.destroyDismissInteraction=((o,e,t,r)=>{let n=[];const s=(0,h.g)(r).querySelector(".popover-content");return n="hover"===e?[{eventName:"mouseenter",callback:a=>{document.elementFromPoint(a.clientX,a.clientY)!==o&&t.dismiss(void 0,void 0,!1)}}]:[{eventName:"click",callback:a=>{a.target.closest("[data-ion-popover-trigger]")!==o?t.dismiss(void 0,void 0,!1):a.stopPropagation()}}],n.forEach(({eventName:a,callback:c})=>s.addEventListener(a,c)),()=>{n.forEach(({eventName:a,callback:c})=>s.removeEventListener(a,c))}})(n,r,i,t))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(o,e){!0===o&&!1===e?this.present():!1===o&&!0===e&&this.dismiss()}connectedCallback(){const{configureTriggerInteraction:o,el:e}=this;(0,C.e)(e),o()}disconnectedCallback(){const{destroyTriggerInteraction:o}=this;o&&o()}componentWillLoad(){this.popoverId=this.el.hasAttribute("id")?this.el.getAttribute("id"):`ion-popover-${this.popoverIndex}`,this.parentPopover=this.el.closest(`ion-popover:not(#${this.popoverId})`),void 0===this.alignment&&(this.alignment="ios"===(0,M.b)(this)?"center":"start")}componentDidLoad(){const{parentPopover:o,isOpen:e}=this;!0===e&&(0,h.r)(()=>this.present()),o&&(0,h.a)(o,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)})}presentFromTrigger(o){var e=this;return(0,A.A)(function*(t,r=!1){e.focusDescendantOnPresent=r,yield e.present(t),e.focusDescendantOnPresent=!1}).apply(this,arguments)}getDelegate(o=!1){if(this.workingDelegate&&!o)return{delegate:this.workingDelegate,inline:this.inline};const t=this.inline=null!==this.el.parentNode&&!this.hasController;return{inline:t,delegate:this.workingDelegate=t?this.delegate||this.coreDelegate:this.delegate}}present(o){var e=this;return(0,A.A)(function*(){if(e.presented)return;void 0!==e.currentTransition&&(yield e.currentTransition);const{inline:t,delegate:r}=e.getDelegate(!0);return e.usersElement=yield(0,T.a)(r,e.el,e.component,["popover-viewport"],e.componentProps,t),yield(0,g.e)(e.usersElement),e.keyboardEvents||e.configureKeyboardInteraction(),e.configureDismissInteraction(),e.ionMount.emit(),new Promise(n=>{(0,h.r)(()=>{(0,h.r)((0,A.A)(function*(){e.currentTransition=(0,C.d)(e,"popoverEnter",ge,we,{event:o||e.event,size:e.size,trigger:e.triggerEl,reference:e.reference,side:e.side,align:e.alignment}),yield e.currentTransition,e.currentTransition=void 0,e.focusDescendantOnPresent&&(0,C.j)(e.el,e.el),n()}))})})})()}dismiss(o,e){var t=this;return(0,A.A)(function*(r,n,i=!0){void 0!==t.currentTransition&&(yield t.currentTransition);const{destroyKeyboardInteraction:s,destroyDismissInteraction:a}=t;i&&t.parentPopover&&t.parentPopover.dismiss(r,n,i),t.currentTransition=(0,C.f)(t,r,n,"popoverLeave",be,ye,t.event);const c=yield t.currentTransition;if(c){s&&(s(),t.destroyKeyboardInteraction=void 0),a&&(a(),t.destroyDismissInteraction=void 0);const{delegate:l}=t.getDelegate();yield(0,T.d)(l,t.usersElement)}return t.currentTransition=void 0,c}).apply(this,arguments)}getParentPopover(){var o=this;return(0,A.A)(function*(){return o.parentPopover})()}onDidDismiss(){return(0,C.g)(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return(0,C.g)(this.el,"ionPopoverWillDismiss")}render(){const o=(0,M.b)(this),{onLifecycle:e,popoverId:t,parentPopover:r,dismissOnSelect:n,side:i,arrow:s,htmlAttributes:a}=this,c=(0,M.a)("desktop"),l=s&&!r;return(0,d.h)(d.H,Object.assign({"aria-modal":"true","no-router":!0,tabindex:"-1"},a,{style:{zIndex:`${2e4+this.overlayIndex}`},id:t,class:Object.assign(Object.assign({},(0,m.g)(this.cssClass)),{[o]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":c,[`popover-side-${i}`]:!0,"popover-nested":!!r}),onIonPopoverDidPresent:e,onIonPopoverWillPresent:e,onIonPopoverWillDismiss:e,onIonPopoverDidDismiss:e,onIonBackdropTap:this.onBackdropTap}),!r&&(0,d.h)("ion-backdrop",{tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),(0,d.h)("div",{class:"popover-wrapper ion-overlay-wrapper",onClick:n?()=>this.dismiss():void 0},l&&(0,d.h)("div",{class:"popover-arrow",part:"arrow"}),(0,d.h)("div",{class:"popover-content",part:"content"},(0,d.h)("slot",null))))}get el(){return(0,d.i)(this)}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}},ke={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};let De=0;ee.style={ios:':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{opacity:0;z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}.popover-viewport{--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;overflow:hidden}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, #e6e6e6)}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden}.popover-arrow::after{left:3px;top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}[dir=rtl] .popover-arrow::after,:host-context([dir=rtl]) .popover-arrow::after{left:unset;right:unset;right:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',md:":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{opacity:0;z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}.popover-viewport{--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;overflow:hidden}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}[dir=rtl] .popover-content,:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}"}},35367:(G,j,u)=>{u.d(j,{c:()=>M,g:()=>h,h:()=>d,o:()=>C});var A=u(73308);const d=(m,g)=>null!==g.closest(m),M=(m,g)=>"string"==typeof m&&m.length>0?Object.assign({"ion-color":!0,[`ion-color-${m}`]:!0},g):g,h=m=>{const g={};return(m=>void 0!==m?(Array.isArray(m)?m:m.split(" ")).filter(p=>null!=p).map(p=>p.trim()).filter(p=>""!==p):[])(m).forEach(p=>g[p]=!0),g},F=/^[a-z][a-z0-9+\-.]*:/,C=function(){var m=(0,A.A)(function*(g,p,q,z){if(null!=g&&"#"!==g[0]&&!F.test(g)){const N=document.querySelector("ion-router");if(N)return p?.preventDefault(),N.push(g,q,z)}return!1});return function(p,q,z,N){return m.apply(this,arguments)}}()}}]);