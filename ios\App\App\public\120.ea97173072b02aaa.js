"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[120],{20120:(u,g,i)=>{i.d(g,{$:()=>m});var s=i(73308),e=i(37222),_=i(99987),n=i(2978),d=i(82571),t=i(77897),M=i(62049),C=i(56610),O=i(74657);function P(a,p){if(1&a&&(n.j41(0,"ion-select-option",16),n.EFF(1),n.k0s()),2&a){const o=p.$implicit;n.Y8G("value",o),n.R7$(1),n.SpI(" ",o," ")}}let m=(()=>{class a{constructor(o,r,c){this.commonSrv=o,this.modalCtrl=r,this.translateService=c,this.filterForm=new e.gE({startDate:new e.MJ(""),endDate:new e.MJ(""),product:new e.MJ("")})}ngOnInit(){this.filterForm.reset(),this.filterForm.patchValue({startDate:this.filterData?.startDate,endDate:this.filterData?.endDate,customerReference:this.filterData?.customerReference,product:this.filterData?.product}),this.filterForm?.updateValueAndValidity()}resetFilter(){this.filterForm=new e.gE({startDate:new e.MJ(new Date((new Date).getFullYear(),0,1)),endDate:new e.MJ(new Date)}),this.modalCtrl.dismiss({...this.filterForm.value})}closeModal(){var o=this;return(0,s.A)(function*(){const r=o.filterForm.value;if(r.startDate>r.endDate)return yield o.commonSrv.showToast({message:o.translateService.currentLang===_.T.French?"Veuillez renseigner une date de d\xe9but inf\xe9rieure \xe0 celle de la date de fin":"Please enter a start date less than the end date",color:"warning"});o.modalCtrl.dismiss({...o.filterForm.value})})()}static{this.\u0275fac=function(r){return new(r||a)(n.rXU(d.h),n.rXU(t.W3),n.rXU(M.E))}}static{this.\u0275cmp=n.VBU({type:a,selectors:[["app-filter-stats"]],inputs:{filterData:"filterData",products:"products",isShow:"isShow"},decls:28,vars:18,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end"],["src","assets/icons/close.svg",3,"click"],["id","content",3,"formGroup"],[1,"form-group","padding-horizontal"],[1,"mbottom"],[1,"filter-item"],["position","floating",1,"title"],["interface","action-sheet","formControlName","product"],[3,"value",4,"ngFor","ngForOf"],[1,"add-qty-btn","mbottom","padding-horizontal",3,"click"],["color","primary",1,"btn","add-line",3,"disabled","readonly"],["name","search-sharp"],["color","medium",1,"btn","add-line",3,"disabled","readonly"],["name","refresh-outline"],[3,"value"]],template:function(r,c){1&r&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2)(4,"ion-img",3),n.bIt("click",function(){return c.closeModal()}),n.k0s()(),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"form",4)(10,"div",5)(11,"div",6)(12,"ion-item",7)(13,"ion-label",8),n.EFF(14),n.nI1(15,"translate"),n.k0s(),n.j41(16,"ion-select",9),n.DNE(17,P,2,2,"ion-select-option",10),n.k0s()()()(),n.j41(18,"div",11),n.bIt("click",function(){return c.closeModal()}),n.j41(19,"ion-button",12),n.nrm(20,"ion-icon",13),n.EFF(21),n.nI1(22,"translate"),n.k0s()(),n.j41(23,"div",11),n.bIt("click",function(){return c.resetFilter()}),n.j41(24,"ion-button",14),n.nrm(25,"ion-icon",15),n.EFF(26),n.nI1(27,"translate"),n.k0s()()()()()),2&r&&(n.R7$(6),n.JRh(n.bMT(7,10,"history-page.title-filter")),n.R7$(3),n.Y8G("formGroup",c.filterForm),n.R7$(5),n.SpI(" ",n.bMT(15,12,"reporting.products")," "),n.R7$(3),n.Y8G("ngForOf",c.products),n.R7$(2),n.Y8G("disabled",c.filterForm.invalid)("readonly",c.filterForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(22,14,"history-page.btn-filter")," "),n.R7$(3),n.Y8G("disabled",c.filterForm.invalid)("readonly",c.filterForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(27,16,"history-page.btn-reset")," "))},dependencies:[e.qT,e.BC,e.cb,C.Sq,t.Jm,t.W9,t.eU,t.iq,t.KW,t.uz,t.he,t.Nm,t.Ip,t.Zx,t.ai,t.Je,e.j4,e.JD,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;color:#000;display:flex;flex-direction:column;height:50%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{flex:1}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;--padding-start: 0;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: var(--space-4) !important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .unit[_ngcontent-%COMP%]{margin-right:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .tonne[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{margin-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .full-input[_ngcontent-%COMP%]{margin:.5em 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .btn-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-schedule[_ngcontent-%COMP%]{margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{height:3em}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res));width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]   .ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#1e1e1e!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fMedium[_ngcontent-%COMP%]{font-family:Mont Light}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{padding:calc(25 * var(--res)) 0 calc(75 * var(--res)) 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(30 * var(--res));line-height:initial}ion-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));width:100%}"]})}}return a})()}}]);