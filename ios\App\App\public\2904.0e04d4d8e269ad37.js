"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2904],{32904:(j,D,l)=>{l.r(D),l.d(D,{ValidateOrderPageModule:()=>x});var _=l(56610),g=l(37222),a=l(77897),P=l(77575),m=l(73308),R=l(99987),n=l(2978),I=l(82571),T=l(62049),y=l(74657);function A(i,F){if(1&i){const e=n.RV6();n.j41(0,"ion-datetime",21,22),n.bIt("ionChange",function(){n.eBV(e);const o=n.sdS(1);return n.Njj(o.confirm(!0))}),n.k0s()}}function s(i,F){if(1&i){const e=n.RV6();n.j41(0,"ion-datetime",23,22),n.bIt("ionChange",function(){n.eBV(e);const o=n.sdS(1);return n.Njj(o.confirm(!0))}),n.k0s()}}let c=(()=>{class i{constructor(e,r,o){this.commonSrv=e,this.modalCtrl=r,this.translateService=o,this.filterForm=new g.gE({startDate:new g.MJ(""),endDate:new g.MJ(""),customerReference:new g.MJ("")})}ngOnInit(){this.filterForm.reset(),this.filterForm.patchValue({startDate:this.filterData?.startDate,endDate:this.filterData?.endDate,customerReference:this.filterData?.customerReference}),this.filterForm?.updateValueAndValidity()}resetFilter(){this.modalCtrl.dismiss({})}closeModal(){var e=this;return(0,m.A)(function*(){const r=e.filterForm.value;if(r.startDate>r.endDate)return yield e.commonSrv.showToast({message:e.translateService.currentLang===R.T.French?"Veuillez renseigner une date de d\xe9but inf\xe9rieure \xe0 celle de la date de fin":"Please enter a start date less than the end date",color:"warning"});e.modalCtrl.dismiss({...e.filterForm.value})})()}static{this.\u0275fac=function(r){return new(r||i)(n.rXU(I.h),n.rXU(a.W3),n.rXU(T.E))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-filter-modal"]],inputs:{filterData:"filterData"},decls:48,vars:31,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end"],["src","assets/icons/close.svg",3,"click"],["id","content",3,"formGroup"],[1,"form-group","padding-horizontal"],[1,"title"],[1,"mbottom"],[1,"date-time"],["slot","start","src","assets/icons/calendar.svg",1,"ion-text-center"],["id","date","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","date","size","cover","side","top","alignment","center"],["id","enddate","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","enddate","size","cover","side","top","alignment","center"],["position","floating",1,"title"],["formControlName","customerReference","clearInput",""],[1,"add-qty-btn","mbottom","padding-horizontal",3,"click"],["color","primary",1,"btn","add-line",3,"disabled","readonly"],["name","search-sharp"],["color","medium",1,"btn","add-line",3,"disabled","readonly"],["name","refresh-outline"],["formControlName","startDate","presentation","date","locale","fr-FR",3,"ionChange"],["popoverDatetime",""],["formControlName","endDate","presentation","date","locale","fr-FR",3,"ionChange"]],template:function(r,o){1&r&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2)(4,"ion-img",3),n.bIt("click",function(){return o.closeModal()}),n.k0s()(),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"form",4)(10,"div",5)(11,"ion-label",6),n.EFF(12),n.nI1(13,"translate"),n.k0s(),n.j41(14,"div",7)(15,"ion-item",8),n.nrm(16,"ion-icon",9)(17,"ion-input",10),n.nI1(18,"date"),n.j41(19,"ion-popover",11),n.DNE(20,A,2,0,"ng-template"),n.k0s()()()(),n.j41(21,"div",5)(22,"ion-label",6),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"div",7)(26,"ion-item",8),n.nrm(27,"ion-icon",9)(28,"ion-input",12),n.nI1(29,"date"),n.j41(30,"ion-popover",13),n.DNE(31,s,2,0,"ng-template"),n.k0s()()()(),n.j41(32,"div",5)(33,"ion-item",7)(34,"ion-label",14),n.EFF(35),n.nI1(36,"translate"),n.k0s(),n.nrm(37,"ion-input",15),n.k0s()(),n.j41(38,"div",16),n.bIt("click",function(){return o.closeModal()}),n.j41(39,"ion-button",17),n.nrm(40,"ion-icon",18),n.EFF(41),n.nI1(42,"translate"),n.k0s()(),n.j41(43,"div",16),n.bIt("click",function(){return o.resetFilter()}),n.j41(44,"ion-button",19),n.nrm(45,"ion-icon",20),n.EFF(46),n.nI1(47,"translate"),n.k0s()()()()()),2&r&&(n.R7$(6),n.JRh(n.bMT(7,13,"history-page.title-filter")),n.R7$(3),n.Y8G("formGroup",o.filterForm),n.R7$(3),n.SpI("",n.bMT(13,15,"history-page.startDate")," "),n.R7$(5),n.FS9("value",n.i5U(18,17,o.filterForm.get("startDate").value,"dd/MM/yyyy")),n.R7$(6),n.SpI("",n.bMT(24,20,"history-page.endDate")," "),n.R7$(5),n.FS9("value",n.i5U(29,22,o.filterForm.get("endDate").value,"dd/MM/yyyy")),n.R7$(7),n.JRh(n.bMT(36,25,"history-page.ref")),n.R7$(4),n.Y8G("disabled",o.filterForm.invalid)("readonly",o.filterForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(42,27,"history-page.btn-filter")," "),n.R7$(3),n.Y8G("disabled",o.filterForm.invalid)("readonly",o.filterForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(47,29,"history-page.btn-reset")," "))},dependencies:[g.qT,g.BC,g.cb,a.Jm,a.W9,a.A9,a.eU,a.iq,a.KW,a.$w,a.uz,a.he,a.Zx,a.ai,a.CF,a.Je,a.Gw,g.j4,g.JD,_.vh,y.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;color:#000;display:flex;flex-direction:column;height:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;--padding-start: 0;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: var(--space-4) !important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .unit[_ngcontent-%COMP%]{margin-right:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .tonne[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{margin-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .full-input[_ngcontent-%COMP%]{margin:.5em 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .btn-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-schedule[_ngcontent-%COMP%]{margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res));width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]   .ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#1e1e1e!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fMedium[_ngcontent-%COMP%]{font-family:Mont Light}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{padding:calc(25 * var(--res)) 0 calc(75 * var(--res)) 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(30 * var(--res));line-height:initial}ion-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));width:100%}"]})}}return i})();var t=l(88233),d=l(79801),M=l(57870),u=l(14599),O=l(2611);let C=(()=>{class i{transform(e,r){return(e||[]).reduce((p,b)=>p+b?.quantity*b.packaging?.unit?.value,0)*(r?.points?.status||d.Th.AMIGO)}static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275pipe=n.EJ8({name:"getTotalPoints",type:i,pure:!0})}}return i})();function f(i,F){1&i&&n.nrm(0,"ion-progress-bar",18)}function h(i,F){if(1&i){const e=n.RV6();n.j41(0,"ion-card",19),n.bIt("click",function(){const p=n.eBV(e).$implicit,b=n.XpG();return n.Njj(b.orderRetailSrv.orderRetail=p)}),n.j41(1,"ion-card-content")(2,"div",20)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.j41(6,"strong"),n.EFF(7),n.k0s()(),n.j41(8,"ion-label"),n.EFF(9),n.nI1(10,"translate"),n.j41(11,"strong"),n.EFF(12),n.k0s()(),n.j41(13,"ion-label"),n.EFF(14),n.nI1(15,"translate"),n.j41(16,"strong",21),n.nI1(17,"colorStatusOrder"),n.EFF(18),n.nI1(19,"statusOrderRetail"),n.k0s()(),n.j41(20,"ion-label"),n.EFF(21,"Date: "),n.j41(22,"strong"),n.EFF(23),n.nI1(24,"date"),n.k0s()(),n.j41(25,"ion-label"),n.EFF(26),n.nI1(27,"translate"),n.j41(28,"strong"),n.EFF(29),n.nI1(30,"getTotalPoints"),n.k0s()()(),n.j41(31,"div",22),n.nrm(32,"ion-icon",23),n.k0s()()()}if(2&i){const e=F.$implicit,r=n.XpG();n.Mz_("routerLink","/order/order-reseller/order-detail/",null==e?null:e._id,""),n.R7$(4),n.SpI("",n.bMT(5,11,"history-page.reference"),": "),n.R7$(3),n.JRh(null==e?null:e.appReference),n.R7$(2),n.SpI("",n.bMT(10,13,"reseller-new-page.history-page.amount"),": "),n.R7$(3),n.SpI("",r.calculateTotalAmount(null==e||null==e.cart?null:e.cart.items)," XAF"),n.R7$(2),n.SpI("",n.bMT(15,15,"removals.removal-status"),": "),n.R7$(2),n.Y8G("ngClass",n.bMT(17,17,null==e?null:e.status)),n.R7$(2),n.JRh(n.bMT(19,19,null==e?null:e.status)),n.R7$(5),n.JRh(n.i5U(24,21,null==e?null:e.created_at,"dd/MM/YYYY \xe0 HH:mm")),n.R7$(3),n.SpI("",n.bMT(27,24,"reseller-new-page.history-page.points"),": "),n.R7$(3),n.JRh(n.i5U(30,26,null==e||null==e.cart?null:e.cart.items,null==e?null:e.user)||"N/A")}}function v(i,F){1&i&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",24),n.nrm(2,"ion-skeleton-text",25),n.k0s()()),2&i&&(n.R7$(2),n.Y8G("animated",!0))}function k(i,F){1&i&&(n.j41(0,"div",26),n.nrm(1,"ion-img",27),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&i&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"reseller-new-page.history-page.empty-order")," "))}const w=function(i){return{active:i}},Y=[{path:"",component:(()=>{class i{constructor(e,r,o,p,b,V){this.modalCtrl=e,this.orderRetailSrv=r,this.storageService=o,this.router=p,this.location=b,this.commonSrv=V,this.isLoading=!1,this.orders=[],this.skeletons=[1,2,3,4,5,6],this.limit=20,this.offset=0,this.tabOption=t.Dp.CREATED,this.orderStatus=t.Dp}ngOnInit(){}showFilter(){var e=this;return(0,m.A)(function*(){const r=yield e.modalCtrl.create({component:c,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios",componentProps:{filterData:e.filterData}});r.present(),e.filterData=(yield r.onWillDismiss()).data,e.filterData&&(yield e.getAllOrder())})()}doRefresh(e){var r=this;return(0,m.A)(function*(){r.filterData=null,r.orders=[],yield r.getAllOrder(),e.target.complete()})()}ionViewWillEnter(){var e=this;return(0,m.A)(function*(){e.isLoading=!0,e.storageService.getUserConnected(),e.orders=[],yield e.getAllOrder(),e.user=e.storageService.getUserConnected()})()}ionViewDidEnter(){var e=this;return(0,m.A)(function*(){e.isLoading=!1})()}getFlowOrder(e){var r=this;return(0,m.A)(function*(){r.offset=r.offset+r.limit,yield r.getAllOrder(),e.target.complete()})()}getAllOrder(){var e=this;return(0,m.A)(function*(){e.skeletons=[1,2,3,4,5,6],e.isLoading=!0;const r={status:e.tabOption,limit:e.limit,offset:e.offset,userId:e.commonSrv?.user?._id,...e.filterData},o=(yield e.orderRetailSrv.getAllOrderRetaillForCommercial(r)).data;e.orders=e.orders.concat(o),e.isLoading=!1,e.skeletons=[]})()}calculateTotalAmount(e){let r=0;return(e||[]).forEach(o=>{r+=o.unitPrice*o.quantity}),r}calculateTotalPointsOrder(e,r){return e.reduce((p,b)=>p+b?.quantity*b.packaging?.unit?.value,0)*(r?.points?.status||d.Th.AMIGO)}back(){this.location.back()}sumQdty(e){return e?.map(o=>o.quantity)?.reduce((o,p)=>o+p,0)}static{this.\u0275fac=function(r){return new(r||i)(n.rXU(a.W3),n.rXU(M.l),n.rXU(u.n),n.rXU(P.Ix),n.rXU(_.aZ),n.rXU(I.h))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-validate-order"]],decls:38,vars:36,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/funnel-outline.svg",3,"click"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click"],[1,"order-list"],["class","order",3,"routerLink","click",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[3,"ionInfinite"],["class","empty-list",4,"ngIf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"routerLink"],["type","indeterminate"],[1,"order",3,"routerLink","click"],[1,"detail"],["color","primary",3,"ngClass"],[1,"icon"],["src","/assets/icons/arrow-forward-green.svg"],[1,"skeleton"],[3,"animated"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(r,o){1&r&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return o.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.j41(6,"ion-img",3),n.bIt("click",function(){return o.showFilter()}),n.k0s()()(),n.j41(7,"ion-content",4),n.DNE(8,f,1,0,"ion-progress-bar",5),n.j41(9,"div",6)(10,"ion-refresher",7),n.bIt("ionRefresh",function(b){return o.doRefresh(b)}),n.nrm(11,"ion-refresher-content",8),n.nI1(12,"translate"),n.nI1(13,"translate"),n.k0s(),n.j41(14,"ion-tab-bar",9)(15,"ion-tab-button",10),n.bIt("click",function(){return o.tabOption=o.orderStatus.CREATED,o.orders=[],o.getAllOrder()}),n.j41(16,"ion-title"),n.EFF(17),n.nI1(18,"translate"),n.k0s()(),n.j41(19,"ion-tab-button",10),n.bIt("click",function(){return o.tabOption=o.orderStatus.VALIDATED,o.orders=[],o.getAllOrder()}),n.j41(20,"ion-title"),n.EFF(21),n.nI1(22,"translate"),n.k0s()(),n.j41(23,"ion-tab-button",10),n.bIt("click",function(){return o.tabOption=o.orderStatus.REJECTED,o.orders=[],o.getAllOrder()}),n.j41(24,"ion-title"),n.EFF(25),n.nI1(26,"translate"),n.k0s()()(),n.j41(27,"div",11),n.DNE(28,h,33,29,"ion-card",12),n.DNE(29,v,3,1,"ion-cart",13),n.j41(30,"ion-infinite-scroll",14),n.bIt("ionInfinite",function(b){return o.getFlowOrder(b)}),n.nrm(31,"ion-infinite-scroll-content"),n.k0s()(),n.DNE(32,k,5,3,"div",15),n.k0s()(),n.j41(33,"div",16)(34,"ion-button",17)(35,"ion-label"),n.EFF(36),n.nI1(37,"translate"),n.k0s()()()),2&r&&(n.R7$(4),n.JRh(n.bMT(5,16,"reseller-new-page.history-page.title")),n.R7$(3),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",o.isLoading),n.R7$(3),n.FS9("pullingText",n.bMT(12,18,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(13,20,"refresher.refreshing"),"..."),n.R7$(4),n.Y8G("ngClass",n.eq3(30,w,o.tabOption===o.orderStatus.CREATED)),n.R7$(2),n.SpI("",n.bMT(18,22,"reseller-new-page.history-page.tabs.in-progres")," "),n.R7$(2),n.Y8G("ngClass",n.eq3(32,w,o.tabOption===o.orderStatus.VALIDATED)),n.R7$(2),n.SpI("",n.bMT(22,24,"reseller-new-page.history-page.tabs.validate")," "),n.R7$(2),n.Y8G("ngClass",n.eq3(34,w,o.tabOption===o.orderStatus.REJECTED)),n.R7$(2),n.SpI("",n.bMT(26,26,"reseller-new-page.history-page.tabs.rejected")," "),n.R7$(3),n.Y8G("ngForOf",o.orders),n.R7$(1),n.Y8G("ngForOf",o.skeletons),n.R7$(3),n.Y8G("ngIf",(null==o.orders?null:o.orders.length)<=0&&!o.isLoading),n.R7$(2),n.Y8G("routerLink","navigation/home"),n.R7$(2),n.SpI(" ",n.bMT(37,28,"order-new-page.last-step.back-button-label")," "))},dependencies:[_.YU,_.Sq,_.bT,a.Jm,a.b_,a.I9,a.W9,a.eU,a.iq,a.KW,a.Ax,a.Hp,a.he,a.FH,a.To,a.Ki,a.ds,a.Jq,a.qW,a.Zx,a.BC,a.ai,a.N7,P.Wk,_.vh,O.qZ,O.Uu,C,y.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background:#cfcfcf;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));padding-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{margin:auto;min-height:35px;gap:1em;margin-bottom:calc(50 * var(--res));border:none}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-tab-button[_ngcontent-%COMP%]{min-width:-moz-fit-content;min-width:fit-content}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:calc(41 * var(--res));color:#6d839d;font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#6d839d;border-bottom:3px solid #419CFB}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#0b305c}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]{border-bottom:1px solid #8597AD;box-shadow:none;border-radius:unset;margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont SemiBold;color:#0b305c;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-primary-400[_ngcontent-%COMP%]{color:#0d7d3d!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-success-200[_ngcontent-%COMP%]{background-color:#b8ddb6!important;padding:3px 6px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-info-500[_ngcontent-%COMP%]{color:#0af!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-100[_ngcontent-%COMP%]{background-color:#cef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .clr-default-400[_ngcontent-%COMP%]{color:#fff!important}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-info-500[_ngcontent-%COMP%]{background-color:#0af!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .bg-tertiary-200[_ngcontent-%COMP%]{background-color:#f0efef!important;padding:2px;border-radius:4px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:20vh;display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-infinite-scroll-content[_ngcontent-%COMP%]{min-height:15px}.btn-validate[_ngcontent-%COMP%]{margin:calc(41 * var(--res))}"]})}}return i})()}];let $=(()=>{class i{static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[P.iI.forChild(Y),P.iI]})}}return i})();var S=l(93887);let x=(()=>{class i{static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[_.MD,g.YN,a.bv,S.G,$,y.h,g.X1]})}}return i})()},57870:(j,D,l)=>{l.d(D,{l:()=>T});var _=l(73308),g=l(26409),a=l(45312),P=l(56610),m=l(94934),R=l(2978),n=l(82571),I=l(33607);let T=(()=>{class y{constructor(s,c,t){this.http=s,this.commonSrv=c,this.baseUrlService=t,this.url=this.baseUrlService.getOrigin()+a.c.basePath}getOrdersRetaill(s){var c=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{num:d,status:M,offset:u,limit:O,startDate:C,endDate:f,customerReference:h,particularId:v}=s;return C&&f&&(t=t.append("startDate",new P.vh("fr").transform(C,"YYYY-MM-dd")),t=t.append("endDate",new P.vh("fr").transform(f,"YYYY-MM-dd"))),h&&(t=t.append("customerReference",h)),void 0!==u&&(t=t.append("offset",u)),O&&(t=t.append("limit",O)),M&&(t=t.append("status",M)),d&&(t=t.append("appReference",d)),v&&(t=t.append("particular",v)),yield(0,m.s)(c.http.get(`${c.url}order-supplier/particulars`,{params:t}))}catch(t){return t}})()}getAllOrderRetaillForCommercial(s){var c=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{num:d,status:M,offset:u,limit:O,startDate:C,endDate:f,customerReference:h,region:v,userId:k}=s;return C&&f&&(t=t.append("startDate",new P.vh("fr").transform(C,"YYYY-MM-dd")),t=t.append("endDate",new P.vh("fr").transform(f,"YYYY-MM-dd"))),h&&(t=t.append("customerReference",h)),void 0!==u&&(t=t.append("offset",u)),O&&(t=t.append("limit",O)),M&&(t=t.append("status",M)),d&&(t=t.append("appReference",d)),v&&(t=t.append("distributors.address.region",v)),k&&(t=t.append("supplier.associatedCommercial._id",k)),yield(0,m.s)(c.http.get(`${c.url}order-supplier`,{params:t}))}catch(t){return t}})()}getOrderRetaillParticular(s){var c=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{status:d,offset:M,limit:u,startDate:O,endDate:C,particularId:f}=s;return O&&C&&(t=t.append("startDate",new P.vh("fr").transform(O,"YYYY-MM-dd")).append("endDate",new P.vh("fr").transform(C,"YYYY-MM-dd"))),void 0!==M&&(t=t.append("offset",M)),u&&(t=t.append("limit",u)),d&&(t=t.append("status",d)),f&&(t=t.append("particularId",f)),yield(0,m.s)(c.http.get(`${c.url}order-supplier/particulars`,{params:t}))}catch(t){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",t),t}})()}getScannerOrderParticular(s){var c=this;return(0,_.A)(function*(){try{let t=new g.Nl;const{status:d,offset:M,limit:u,startDate:O,endDate:C,particularId:f}=s;return O&&C&&(t=t.append("startDate",new P.vh("fr").transform(O,"YYYY-MM-dd")).append("endDate",new P.vh("fr").transform(C,"YYYY-MM-dd"))),void 0!==M&&(t=t.append("offset",M)),u&&(t=t.append("limit",u)),d&&(t=t.append("status",d)),yield(0,m.s)(c.http.get(`${c.url}scanner-data/particular/${f}`,{params:t}))}catch(t){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",t),t}})()}validateRetailOrder(s){var c=this;return(0,_.A)(function*(){try{return yield(0,m.s)(c.http.patch(`${c.url}order-supplier/${s._id}`,{orders:s},{headers:{Authorization:`Bearer ${c.commonSrv?.user?.accessToken}`}}))}catch(t){return c.commonSrv.getError(t?.message,t)}})()}rejectRetailOrder(s,c){var t=this;return(0,_.A)(function*(){try{return yield(0,m.s)(t.http.patch(`${t.url}order-supplier/${s._id}/rejectOder`,{rejectMessage:c,user:t.commonSrv.user},{headers:{Authorization:`Bearer ${t.commonSrv?.user?.accessToken}`}}))}catch(d){return t.commonSrv.getError(d?.message,d)}})()}imageOrderValidated(s){var c=this;return(0,_.A)(function*(){try{return yield(0,m.s)(c.http.post(`${c.url}images`,s))}catch(t){return t}})()}findOrderRetail(s){var c=this;return(0,_.A)(function*(){try{return yield(0,m.s)(c.http.get(c.url+"scanner-data/"+s))}catch{return null}})()}getImageRetail(s){var c=this;return(0,_.A)(function*(){let t=new g.Nl;const{appRef:d}=s;return d&&(t=t.append("appRef",d)),yield(0,m.s)(c.http.get(`${c.url}images`,{params:t}))})()}getVolumeOrderByParticularClient(s){var c=this;return(0,_.A)(function*(){let t=new g.Nl;const{status:d=300,offset:M,limit:u,enable:O=!0,associatedCommercialId:C,startDate:f,endDate:h,customerName:v}=s;void 0!==M&&(t=t.append("offset",M)),u&&(t=t.append("limit",u)),d&&(t=t.append("status",d)),C&&(t=t.append("user.associatedCommercial._id",C)),t=t.append("enable",O),f&&h&&(t=t.append("startDate",new P.vh("fr").transform(f,"YYYY-MM-dd")),t=t.append("endDate",new P.vh("fr").transform(h,"YYYY-MM-dd"))),v&&(t=t.append("user.firstName",v));try{return yield(0,m.s)(c.http.get(`${c.url}scanner-data/volume-order-by-particular-client`,{params:t}))}catch(k){const E={message:c.commonSrv.getError("",k).message,color:"danger"};return yield c.commonSrv.showToast(E),k}})()}static{this.\u0275fac=function(c){return new(c||y)(R.KVO(g.Qq),R.KVO(n.h),R.KVO(I.K))}}static{this.\u0275prov=R.jDH({token:y,factory:y.\u0275fac,providedIn:"root"})}}return y})()}}]);