"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4608],{24608:(Js,Ne,le)=>{le.d(Ne,{vj:()=>Qs});var be=le(2978),He=le(56610);function Ee(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function re(e={},s={}){Object.keys(s).forEach(t=>{typeof e[t]>"u"?e[t]=s[t]:Ee(s[t])&&Ee(e[t])&&Object.keys(s[t]).length>0&&re(e[t],s[t])})}const Se={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function W(){const e=typeof document<"u"?document:{};return re(e,Se),e}const Be={document:Se,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>typeof setTimeout>"u"?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function G(){const e=typeof window<"u"?window:{};return re(e,Be),e}class j extends Array{constructor(s){"number"==typeof s?super(s):(super(...s||[]),function Ge(e){const s=e.__proto__;Object.defineProperty(e,"__proto__",{get:()=>s,set(t){s.__proto__=t}})}(this))}}function Z(e=[]){const s=[];return e.forEach(t=>{Array.isArray(t)?s.push(...Z(t)):s.push(t)}),s}function ye(e,s){return Array.prototype.filter.call(e,s)}function A(e,s){const t=G(),a=W();let n=[];if(!s&&e instanceof j)return e;if(!e)return new j(n);if("string"==typeof e){const i=e.trim();if(i.indexOf("<")>=0&&i.indexOf(">")>=0){let c="div";0===i.indexOf("<li")&&(c="ul"),0===i.indexOf("<tr")&&(c="tbody"),(0===i.indexOf("<td")||0===i.indexOf("<th"))&&(c="tr"),0===i.indexOf("<tbody")&&(c="table"),0===i.indexOf("<option")&&(c="select");const f=a.createElement(c);f.innerHTML=i;for(let l=0;l<f.childNodes.length;l+=1)n.push(f.childNodes[l])}else n=function Fe(e,s){if("string"!=typeof e)return[e];const t=[],a=s.querySelectorAll(e);for(let n=0;n<a.length;n+=1)t.push(a[n]);return t}(e.trim(),s||a)}else if(e.nodeType||e===t||e===a)n.push(e);else if(Array.isArray(e)){if(e instanceof j)return e;n=e}return new j(function Ve(e){const s=[];for(let t=0;t<e.length;t+=1)-1===s.indexOf(e[t])&&s.push(e[t]);return s}(n))}A.fn=j.prototype;const Mt="resize scroll".split(" ");function B(e){return function s(...t){if(typeof t[0]>"u"){for(let a=0;a<this.length;a+=1)Mt.indexOf(e)<0&&(e in this[a]?this[a][e]():A(this[a]).trigger(e));return this}return this.on(e,...t)}}B("click"),B("blur"),B("focus"),B("focusin"),B("focusout"),B("keyup"),B("keydown"),B("keypress"),B("submit"),B("change"),B("mousedown"),B("mousemove"),B("mouseup"),B("mouseenter"),B("mouseleave"),B("mouseout"),B("mouseover"),B("touchstart"),B("touchend"),B("touchmove"),B("resize"),B("scroll");const xe={addClass:function Xe(...e){const s=Z(e.map(t=>t.split(" ")));return this.forEach(t=>{t.classList.add(...s)}),this},removeClass:function Ye(...e){const s=Z(e.map(t=>t.split(" ")));return this.forEach(t=>{t.classList.remove(...s)}),this},hasClass:function qe(...e){const s=Z(e.map(t=>t.split(" ")));return ye(this,t=>s.filter(a=>t.classList.contains(a)).length>0).length>0},toggleClass:function je(...e){const s=Z(e.map(t=>t.split(" ")));this.forEach(t=>{s.forEach(a=>{t.classList.toggle(a)})})},attr:function Ue(e,s){if(1===arguments.length&&"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(let t=0;t<this.length;t+=1)if(2===arguments.length)this[t].setAttribute(e,s);else for(const a in e)this[t][a]=e[a],this[t].setAttribute(a,e[a]);return this},removeAttr:function Ze(e){for(let s=0;s<this.length;s+=1)this[s].removeAttribute(e);return this},transform:function Ke(e){for(let s=0;s<this.length;s+=1)this[s].style.transform=e;return this},transition:function Qe(e){for(let s=0;s<this.length;s+=1)this[s].style.transitionDuration="string"!=typeof e?`${e}ms`:e;return this},on:function Je(...e){let[s,t,a,n]=e;function i(d){const o=d.target;if(!o)return;const r=d.target.dom7EventData||[];if(r.indexOf(d)<0&&r.unshift(d),A(o).is(t))a.apply(o,r);else{const u=A(o).parents();for(let m=0;m<u.length;m+=1)A(u[m]).is(t)&&a.apply(u[m],r)}}function c(d){const o=d&&d.target&&d.target.dom7EventData||[];o.indexOf(d)<0&&o.unshift(d),a.apply(this,o)}"function"==typeof e[1]&&([s,a,n]=e,t=void 0),n||(n=!1);const f=s.split(" ");let l;for(let d=0;d<this.length;d+=1){const o=this[d];if(t)for(l=0;l<f.length;l+=1){const r=f[l];o.dom7LiveListeners||(o.dom7LiveListeners={}),o.dom7LiveListeners[r]||(o.dom7LiveListeners[r]=[]),o.dom7LiveListeners[r].push({listener:a,proxyListener:i}),o.addEventListener(r,i,n)}else for(l=0;l<f.length;l+=1){const r=f[l];o.dom7Listeners||(o.dom7Listeners={}),o.dom7Listeners[r]||(o.dom7Listeners[r]=[]),o.dom7Listeners[r].push({listener:a,proxyListener:c}),o.addEventListener(r,c,n)}}return this},off:function et(...e){let[s,t,a,n]=e;"function"==typeof e[1]&&([s,a,n]=e,t=void 0),n||(n=!1);const i=s.split(" ");for(let c=0;c<i.length;c+=1){const f=i[c];for(let l=0;l<this.length;l+=1){const d=this[l];let o;if(!t&&d.dom7Listeners?o=d.dom7Listeners[f]:t&&d.dom7LiveListeners&&(o=d.dom7LiveListeners[f]),o&&o.length)for(let r=o.length-1;r>=0;r-=1){const u=o[r];a&&u.listener===a||a&&u.listener&&u.listener.dom7proxy&&u.listener.dom7proxy===a?(d.removeEventListener(f,u.proxyListener,n),o.splice(r,1)):a||(d.removeEventListener(f,u.proxyListener,n),o.splice(r,1))}}}return this},trigger:function tt(...e){const s=G(),t=e[0].split(" "),a=e[1];for(let n=0;n<t.length;n+=1){const i=t[n];for(let c=0;c<this.length;c+=1){const f=this[c];if(s.CustomEvent){const l=new s.CustomEvent(i,{detail:a,bubbles:!0,cancelable:!0});f.dom7EventData=e.filter((d,o)=>o>0),f.dispatchEvent(l),f.dom7EventData=[],delete f.dom7EventData}}}return this},transitionEnd:function st(e){const s=this;return e&&s.on("transitionend",function t(a){a.target===this&&(e.call(this,a),s.off("transitionend",t))}),this},outerWidth:function at(e){if(this.length>0){if(e){const s=this.styles();return this[0].offsetWidth+parseFloat(s.getPropertyValue("margin-right"))+parseFloat(s.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function nt(e){if(this.length>0){if(e){const s=this.styles();return this[0].offsetHeight+parseFloat(s.getPropertyValue("margin-top"))+parseFloat(s.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function lt(){const e=G();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function it(){if(this.length>0){const e=G(),s=W(),t=this[0],a=t.getBoundingClientRect(),n=s.body;return{top:a.top+(t===e?e.scrollY:t.scrollTop)-(t.clientTop||n.clientTop||0),left:a.left+(t===e?e.scrollX:t.scrollLeft)-(t.clientLeft||n.clientLeft||0)}}return null},css:function rt(e,s){const t=G();let a;if(1===arguments.length){if("string"!=typeof e){for(a=0;a<this.length;a+=1)for(const n in e)this[a].style[n]=e[n];return this}if(this[0])return t.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"==typeof e){for(a=0;a<this.length;a+=1)this[a].style[e]=s;return this}return this},each:function ot(e){return e?(this.forEach((s,t)=>{e.apply(s,[s,t])}),this):this},html:function ct(e){if(typeof e>"u")return this[0]?this[0].innerHTML:null;for(let s=0;s<this.length;s+=1)this[s].innerHTML=e;return this},text:function ft(e){if(typeof e>"u")return this[0]?this[0].textContent.trim():null;for(let s=0;s<this.length;s+=1)this[s].textContent=e;return this},is:function ut(e){const s=G(),t=W(),a=this[0];let n,i;if(!a||typeof e>"u")return!1;if("string"==typeof e){if(a.matches)return a.matches(e);if(a.webkitMatchesSelector)return a.webkitMatchesSelector(e);if(a.msMatchesSelector)return a.msMatchesSelector(e);for(n=A(e),i=0;i<n.length;i+=1)if(n[i]===a)return!0;return!1}if(e===t)return a===t;if(e===s)return a===s;if(e.nodeType||e instanceof j){for(n=e.nodeType?[e]:e,i=0;i<n.length;i+=1)if(n[i]===a)return!0;return!1}return!1},index:function pt(){let s,e=this[0];if(e){for(s=0;null!==(e=e.previousSibling);)1===e.nodeType&&(s+=1);return s}},eq:function ht(e){if(typeof e>"u")return this;const s=this.length;if(e>s-1)return A([]);if(e<0){const t=s+e;return A(t<0?[]:[this[t]])}return A([this[e]])},append:function mt(...e){let s;const t=W();for(let a=0;a<e.length;a+=1){s=e[a];for(let n=0;n<this.length;n+=1)if("string"==typeof s){const i=t.createElement("div");for(i.innerHTML=s;i.firstChild;)this[n].appendChild(i.firstChild)}else if(s instanceof j)for(let i=0;i<s.length;i+=1)this[n].appendChild(s[i]);else this[n].appendChild(s)}return this},prepend:function gt(e){const s=W();let t,a;for(t=0;t<this.length;t+=1)if("string"==typeof e){const n=s.createElement("div");for(n.innerHTML=e,a=n.childNodes.length-1;a>=0;a-=1)this[t].insertBefore(n.childNodes[a],this[t].childNodes[0])}else if(e instanceof j)for(a=0;a<e.length;a+=1)this[t].insertBefore(e[a],this[t].childNodes[0]);else this[t].insertBefore(e,this[t].childNodes[0]);return this},next:function vt(e){return this.length>0?e?this[0].nextElementSibling&&A(this[0].nextElementSibling).is(e)?A([this[0].nextElementSibling]):A([]):A(this[0].nextElementSibling?[this[0].nextElementSibling]:[]):A([])},nextAll:function bt(e){const s=[];let t=this[0];if(!t)return A([]);for(;t.nextElementSibling;){const a=t.nextElementSibling;e?A(a).is(e)&&s.push(a):s.push(a),t=a}return A(s)},prev:function Et(e){if(this.length>0){const s=this[0];return e?s.previousElementSibling&&A(s.previousElementSibling).is(e)?A([s.previousElementSibling]):A([]):A(s.previousElementSibling?[s.previousElementSibling]:[])}return A([])},prevAll:function St(e){const s=[];let t=this[0];if(!t)return A([]);for(;t.previousElementSibling;){const a=t.previousElementSibling;e?A(a).is(e)&&s.push(a):s.push(a),t=a}return A(s)},parent:function yt(e){const s=[];for(let t=0;t<this.length;t+=1)null!==this[t].parentNode&&(e?A(this[t].parentNode).is(e)&&s.push(this[t].parentNode):s.push(this[t].parentNode));return A(s)},parents:function xt(e){const s=[];for(let t=0;t<this.length;t+=1){let a=this[t].parentNode;for(;a;)e?A(a).is(e)&&s.push(a):s.push(a),a=a.parentNode}return A(s)},closest:function Ct(e){let s=this;return typeof e>"u"?A([]):(s.is(e)||(s=s.parents(e).eq(0)),s)},find:function Tt(e){const s=[];for(let t=0;t<this.length;t+=1){const a=this[t].querySelectorAll(e);for(let n=0;n<a.length;n+=1)s.push(a[n])}return A(s)},children:function _t(e){const s=[];for(let t=0;t<this.length;t+=1){const a=this[t].children;for(let n=0;n<a.length;n+=1)(!e||A(a[n]).is(e))&&s.push(a[n])}return A(s)},filter:function dt(e){return A(ye(this,e))},remove:function $t(){for(let e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}};Object.keys(xe).forEach(e=>{Object.defineProperty(A.fn,e,{value:xe[e],writable:!0})});const H=A;function oe(e,s=0){return setTimeout(e,s)}function K(){return Date.now()}function J(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function zt(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function F(...e){const s=Object(e[0]),t=["__proto__","constructor","prototype"];for(let a=1;a<e.length;a+=1){const n=e[a];if(null!=n&&!zt(n)){const i=Object.keys(Object(n)).filter(c=>t.indexOf(c)<0);for(let c=0,f=i.length;c<f;c+=1){const l=i[c],d=Object.getOwnPropertyDescriptor(n,l);void 0!==d&&d.enumerable&&(J(s[l])&&J(n[l])?n[l].__swiper__?s[l]=n[l]:F(s[l],n[l]):!J(s[l])&&J(n[l])?(s[l]={},n[l].__swiper__?s[l]=n[l]:F(s[l],n[l])):s[l]=n[l])}}}return s}function ee(e,s,t){e.style.setProperty(s,t)}function Ce({swiper:e,targetPosition:s,side:t}){const a=G(),n=-e.translate;let c,i=null;const f=e.params.speed;e.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(e.cssModeFrameID);const l=s>n?"next":"prev",d=(r,u)=>"next"===l&&r>=u||"prev"===l&&r<=u,o=()=>{c=(new Date).getTime(),null===i&&(i=c);const r=Math.max(Math.min((c-i)/f,1),0),u=.5-Math.cos(r*Math.PI)/2;let m=n+u*(s-n);if(d(m,s)&&(m=s),e.wrapperEl.scrollTo({[t]:m}),d(m,s))return e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[t]:m})}),void a.cancelAnimationFrame(e.cssModeFrameID);e.cssModeFrameID=a.requestAnimationFrame(o)};o()}let de,ce,fe;function Te(){return de||(de=function wt(){const e=G(),s=W();return{smoothScroll:s.documentElement&&"scrollBehavior"in s.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&s instanceof e.DocumentTouch),passiveListener:function(){let a=!1;try{const n=Object.defineProperty({},"passive",{get(){a=!0}});e.addEventListener("testPassiveListener",null,n)}catch{}return a}(),gestures:"ongesturestart"in e}}()),de}function _e({swiper:e,runCallbacks:s,direction:t,step:a}){const{activeIndex:n,previousIndex:i}=e;let c=t;if(c||(c=n>i?"next":n<i?"prev":"reset"),e.emit(`transition${a}`),s&&n!==i){if("reset"===c)return void e.emit(`slideResetTransition${a}`);e.emit(`slideChangeTransition${a}`),e.emit("next"===c?`slideNextTransition${a}`:`slidePrevTransition${a}`)}}function xs(e){const s=this,t=W(),a=G(),n=s.touchEventsData,{params:i,touches:c,enabled:f}=s;if(!f||s.animating&&i.preventInteractionOnTransition)return;!s.animating&&i.cssMode&&i.loop&&s.loopFix();let l=e;l.originalEvent&&(l=l.originalEvent);let d=H(l.target);if("wrapper"===i.touchEventsTarget&&!d.closest(s.wrapperEl).length||(n.isTouchEvent="touchstart"===l.type,!n.isTouchEvent&&"which"in l&&3===l.which)||!n.isTouchEvent&&"button"in l&&l.button>0||n.isTouched&&n.isMoved)return;const o=!!i.noSwipingClass&&""!==i.noSwipingClass,r=e.composedPath?e.composedPath():e.path;o&&l.target&&l.target.shadowRoot&&r&&(d=H(r[0]));const u=i.noSwipingSelector?i.noSwipingSelector:`.${i.noSwipingClass}`;if(i.noSwiping&&(l.target&&l.target.shadowRoot?function ys(e,s=this){return function t(a){if(!a||a===W()||a===G())return null;a.assignedSlot&&(a=a.assignedSlot);const n=a.closest(e);return n||a.getRootNode?n||t(a.getRootNode().host):null}(s)}(u,d[0]):d.closest(u)[0]))return void(s.allowClick=!0);if(i.swipeHandler&&!d.closest(i.swipeHandler)[0])return;c.currentX="touchstart"===l.type?l.targetTouches[0].pageX:l.pageX,c.currentY="touchstart"===l.type?l.targetTouches[0].pageY:l.pageY;const g=c.currentX,p=c.currentY,h=i.edgeSwipeDetection||i.iOSEdgeSwipeDetection,b=i.edgeSwipeThreshold||i.iOSEdgeSwipeThreshold;if(h&&(g<=b||g>=a.innerWidth-b)){if("prevent"!==h)return;e.preventDefault()}if(Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),c.startX=g,c.startY=p,n.touchStartTime=K(),s.allowClick=!0,s.updateSize(),s.swipeDirection=void 0,i.threshold>0&&(n.allowThresholdMove=!1),"touchstart"!==l.type){let v=!0;d.is(n.focusableElements)&&(v=!1,"SELECT"===d[0].nodeName&&(n.isTouched=!1)),t.activeElement&&H(t.activeElement).is(n.focusableElements)&&t.activeElement!==d[0]&&t.activeElement.blur(),(i.touchStartForcePreventDefault||v&&s.allowTouchMove&&i.touchStartPreventDefault)&&!d[0].isContentEditable&&l.preventDefault()}s.params.freeMode&&s.params.freeMode.enabled&&s.freeMode&&s.animating&&!i.cssMode&&s.freeMode.onTouchStart(),s.emit("touchStart",l)}function Cs(e){const s=W(),t=this,a=t.touchEventsData,{params:n,touches:i,rtlTranslate:c,enabled:f}=t;if(!f)return;let l=e;if(l.originalEvent&&(l=l.originalEvent),!a.isTouched)return void(a.startMoving&&a.isScrolling&&t.emit("touchMoveOpposite",l));if(a.isTouchEvent&&"touchmove"!==l.type)return;const d="touchmove"===l.type&&l.targetTouches&&(l.targetTouches[0]||l.changedTouches[0]),o="touchmove"===l.type?d.pageX:l.pageX,r="touchmove"===l.type?d.pageY:l.pageY;if(l.preventedByNestedSwiper)return i.startX=o,void(i.startY=r);if(!t.allowTouchMove)return H(l.target).is(a.focusableElements)||(t.allowClick=!1),void(a.isTouched&&(Object.assign(i,{startX:o,startY:r,currentX:o,currentY:r}),a.touchStartTime=K()));if(a.isTouchEvent&&n.touchReleaseOnEdges&&!n.loop)if(t.isVertical()){if(r<i.startY&&t.translate<=t.maxTranslate()||r>i.startY&&t.translate>=t.minTranslate())return a.isTouched=!1,void(a.isMoved=!1)}else if(o<i.startX&&t.translate<=t.maxTranslate()||o>i.startX&&t.translate>=t.minTranslate())return;if(a.isTouchEvent&&s.activeElement&&l.target===s.activeElement&&H(l.target).is(a.focusableElements))return a.isMoved=!0,void(t.allowClick=!1);if(a.allowTouchCallbacks&&t.emit("touchMove",l),l.targetTouches&&l.targetTouches.length>1)return;i.currentX=o,i.currentY=r;const u=i.currentX-i.startX,m=i.currentY-i.startY;if(t.params.threshold&&Math.sqrt(u**2+m**2)<t.params.threshold)return;if(typeof a.isScrolling>"u"){let b;t.isHorizontal()&&i.currentY===i.startY||t.isVertical()&&i.currentX===i.startX?a.isScrolling=!1:u*u+m*m>=25&&(b=180*Math.atan2(Math.abs(m),Math.abs(u))/Math.PI,a.isScrolling=t.isHorizontal()?b>n.touchAngle:90-b>n.touchAngle)}if(a.isScrolling&&t.emit("touchMoveOpposite",l),typeof a.startMoving>"u"&&(i.currentX!==i.startX||i.currentY!==i.startY)&&(a.startMoving=!0),a.isScrolling)return void(a.isTouched=!1);if(!a.startMoving)return;t.allowClick=!1,!n.cssMode&&l.cancelable&&l.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&l.stopPropagation(),a.isMoved||(n.loop&&!n.cssMode&&t.loopFix(),a.startTranslate=t.getTranslate(),t.setTransition(0),t.animating&&t.$wrapperEl.trigger("webkitTransitionEnd transitionend"),a.allowMomentumBounce=!1,n.grabCursor&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",l)),t.emit("sliderMove",l),a.isMoved=!0;let g=t.isHorizontal()?u:m;i.diff=g,g*=n.touchRatio,c&&(g=-g),t.swipeDirection=g>0?"prev":"next",a.currentTranslate=g+a.startTranslate;let p=!0,h=n.resistanceRatio;if(n.touchReleaseOnEdges&&(h=0),g>0&&a.currentTranslate>t.minTranslate()?(p=!1,n.resistance&&(a.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+a.startTranslate+g)**h)):g<0&&a.currentTranslate<t.maxTranslate()&&(p=!1,n.resistance&&(a.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-a.startTranslate-g)**h)),p&&(l.preventedByNestedSwiper=!0),!t.allowSlideNext&&"next"===t.swipeDirection&&a.currentTranslate<a.startTranslate&&(a.currentTranslate=a.startTranslate),!t.allowSlidePrev&&"prev"===t.swipeDirection&&a.currentTranslate>a.startTranslate&&(a.currentTranslate=a.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(a.currentTranslate=a.startTranslate),n.threshold>0){if(!(Math.abs(g)>n.threshold||a.allowThresholdMove))return void(a.currentTranslate=a.startTranslate);if(!a.allowThresholdMove)return a.allowThresholdMove=!0,i.startX=i.currentX,i.startY=i.currentY,a.currentTranslate=a.startTranslate,void(i.diff=t.isHorizontal()?i.currentX-i.startX:i.currentY-i.startY)}!n.followFinger||n.cssMode||((n.freeMode&&n.freeMode.enabled&&t.freeMode||n.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),t.params.freeMode&&n.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(a.currentTranslate),t.setTranslate(a.currentTranslate))}function Ts(e){const s=this,t=s.touchEventsData,{params:a,touches:n,rtlTranslate:i,slidesGrid:c,enabled:f}=s;if(!f)return;let l=e;if(l.originalEvent&&(l=l.originalEvent),t.allowTouchCallbacks&&s.emit("touchEnd",l),t.allowTouchCallbacks=!1,!t.isTouched)return t.isMoved&&a.grabCursor&&s.setGrabCursor(!1),t.isMoved=!1,void(t.startMoving=!1);a.grabCursor&&t.isMoved&&t.isTouched&&(!0===s.allowSlideNext||!0===s.allowSlidePrev)&&s.setGrabCursor(!1);const d=K(),o=d-t.touchStartTime;if(s.allowClick){const v=l.path||l.composedPath&&l.composedPath();s.updateClickedSlide(v&&v[0]||l.target),s.emit("tap click",l),o<300&&d-t.lastClickTime<300&&s.emit("doubleTap doubleClick",l)}if(t.lastClickTime=K(),oe(()=>{s.destroyed||(s.allowClick=!0)}),!t.isTouched||!t.isMoved||!s.swipeDirection||0===n.diff||t.currentTranslate===t.startTranslate)return t.isTouched=!1,t.isMoved=!1,void(t.startMoving=!1);let r;if(t.isTouched=!1,t.isMoved=!1,t.startMoving=!1,r=a.followFinger?i?s.translate:-s.translate:-t.currentTranslate,a.cssMode)return;if(s.params.freeMode&&a.freeMode.enabled)return void s.freeMode.onTouchEnd({currentPos:r});let u=0,m=s.slidesSizesGrid[0];for(let v=0;v<c.length;v+=v<a.slidesPerGroupSkip?1:a.slidesPerGroup){const E=v<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;typeof c[v+E]<"u"?r>=c[v]&&r<c[v+E]&&(u=v,m=c[v+E]-c[v]):r>=c[v]&&(u=v,m=c[c.length-1]-c[c.length-2])}let g=null,p=null;a.rewind&&(s.isBeginning?p=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1:s.isEnd&&(g=0));const h=(r-c[u])/m,b=u<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(o>a.longSwipesMs){if(!a.longSwipes)return void s.slideTo(s.activeIndex);"next"===s.swipeDirection&&s.slideTo(h>=a.longSwipesRatio?a.rewind&&s.isEnd?g:u+b:u),"prev"===s.swipeDirection&&(h>1-a.longSwipesRatio?s.slideTo(u+b):null!==p&&h<0&&Math.abs(h)>a.longSwipesRatio?s.slideTo(p):s.slideTo(u))}else{if(!a.shortSwipes)return void s.slideTo(s.activeIndex);!s.navigation||l.target!==s.navigation.nextEl&&l.target!==s.navigation.prevEl?("next"===s.swipeDirection&&s.slideTo(null!==g?g:u+b),"prev"===s.swipeDirection&&s.slideTo(null!==p?p:u)):s.slideTo(l.target===s.navigation.nextEl?u+b:u)}}function $e(){const e=this,{params:s,el:t}=e;if(t&&0===t.offsetWidth)return;s.breakpoints&&e.setBreakpoint();const{allowSlideNext:a,allowSlidePrev:n,snapGrid:i}=e;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),e.slideTo(("auto"===s.slidesPerView||s.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides?e.slides.length-1:e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=n,e.allowSlideNext=a,e.params.watchOverflow&&i!==e.snapGrid&&e.checkOverflow()}function _s(e){const s=this;s.enabled&&(s.allowClick||(s.params.preventClicks&&e.preventDefault(),s.params.preventClicksPropagation&&s.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function $s(){const e=this,{wrapperEl:s,rtlTranslate:t,enabled:a}=e;if(!a)return;let n;e.previousTranslate=e.translate,e.translate=e.isHorizontal()?-s.scrollLeft:-s.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const i=e.maxTranslate()-e.minTranslate();n=0===i?0:(e.translate-e.minTranslate())/i,n!==e.progress&&e.updateProgress(t?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}let Me=!1;function Ms(){}const Pe=(e,s)=>{const t=W(),{params:a,touchEvents:n,el:i,wrapperEl:c,device:f,support:l}=e,d=!!a.nested,o="on"===s?"addEventListener":"removeEventListener",r=s;if(l.touch){const u=!("touchstart"!==n.start||!l.passiveListener||!a.passiveListeners)&&{passive:!0,capture:!1};i[o](n.start,e.onTouchStart,u),i[o](n.move,e.onTouchMove,l.passiveListener?{passive:!1,capture:d}:d),i[o](n.end,e.onTouchEnd,u),n.cancel&&i[o](n.cancel,e.onTouchEnd,u)}else i[o](n.start,e.onTouchStart,!1),t[o](n.move,e.onTouchMove,d),t[o](n.end,e.onTouchEnd,!1);(a.preventClicks||a.preventClicksPropagation)&&i[o]("click",e.onClick,!0),a.cssMode&&c[o]("scroll",e.onScroll),e[r](a.updateOnWindowResize?f.ios||f.android?"resize orientationchange observerUpdate":"resize observerUpdate":"observerUpdate",$e,!0)},Ie=(e,s)=>e.grid&&s.grid&&s.grid.rows>1,ke={init:!0,direction:"horizontal",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopedSlidesLimit:!0,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};function Ws(e,s){return function(a={}){const n=Object.keys(a)[0],i=a[n];"object"==typeof i&&null!==i?(["navigation","pagination","scrollbar"].indexOf(n)>=0&&!0===e[n]&&(e[n]={auto:!0}),n in e&&"enabled"in i?(!0===e[n]&&(e[n]={enabled:!0}),"object"==typeof e[n]&&!("enabled"in e[n])&&(e[n].enabled=!0),e[n]||(e[n]={enabled:!1}),F(s,a)):F(s,a)):F(s,a)}}const ue={eventsEmitter:{on(e,s,t){const a=this;if(!a.eventsListeners||a.destroyed||"function"!=typeof s)return a;const n=t?"unshift":"push";return e.split(" ").forEach(i=>{a.eventsListeners[i]||(a.eventsListeners[i]=[]),a.eventsListeners[i][n](s)}),a},once(e,s,t){const a=this;if(!a.eventsListeners||a.destroyed||"function"!=typeof s)return a;function n(...i){a.off(e,n),n.__emitterProxy&&delete n.__emitterProxy,s.apply(a,i)}return n.__emitterProxy=s,a.on(e,n,t)},onAny(e,s){const t=this;if(!t.eventsListeners||t.destroyed||"function"!=typeof e)return t;const a=s?"unshift":"push";return t.eventsAnyListeners.indexOf(e)<0&&t.eventsAnyListeners[a](e),t},offAny(e){const s=this;if(!s.eventsListeners||s.destroyed||!s.eventsAnyListeners)return s;const t=s.eventsAnyListeners.indexOf(e);return t>=0&&s.eventsAnyListeners.splice(t,1),s},off(e,s){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||e.split(" ").forEach(a=>{typeof s>"u"?t.eventsListeners[a]=[]:t.eventsListeners[a]&&t.eventsListeners[a].forEach((n,i)=>{(n===s||n.__emitterProxy&&n.__emitterProxy===s)&&t.eventsListeners[a].splice(i,1)})}),t},emit(...e){const s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;let t,a,n;return"string"==typeof e[0]||Array.isArray(e[0])?(t=e[0],a=e.slice(1,e.length),n=s):(t=e[0].events,a=e[0].data,n=e[0].context||s),a.unshift(n),(Array.isArray(t)?t:t.split(" ")).forEach(c=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(f=>{f.apply(n,[c,...a])}),s.eventsListeners&&s.eventsListeners[c]&&s.eventsListeners[c].forEach(f=>{f.apply(n,a)})}),s}},update:{updateSize:function Bt(){const e=this;let s,t;const a=e.$el;s=typeof e.params.width<"u"&&null!==e.params.width?e.params.width:a[0].clientWidth,t=typeof e.params.height<"u"&&null!==e.params.height?e.params.height:a[0].clientHeight,!(0===s&&e.isHorizontal()||0===t&&e.isVertical())&&(s=s-parseInt(a.css("padding-left")||0,10)-parseInt(a.css("padding-right")||0,10),t=t-parseInt(a.css("padding-top")||0,10)-parseInt(a.css("padding-bottom")||0,10),Number.isNaN(s)&&(s=0),Number.isNaN(t)&&(t=0),Object.assign(e,{width:s,height:t,size:e.isHorizontal()?s:t}))},updateSlides:function Gt(){const e=this;function s(I){return e.isHorizontal()?I:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[I]}function t(I,y){return parseFloat(I.getPropertyValue(s(y))||0)}const a=e.params,{$wrapperEl:n,size:i,rtlTranslate:c,wrongRTL:f}=e,l=e.virtual&&a.virtual.enabled,d=l?e.virtual.slides.length:e.slides.length,o=n.children(`.${e.params.slideClass}`),r=l?e.virtual.slides.length:o.length;let u=[];const m=[],g=[];let p=a.slidesOffsetBefore;"function"==typeof p&&(p=a.slidesOffsetBefore.call(e));let h=a.slidesOffsetAfter;"function"==typeof h&&(h=a.slidesOffsetAfter.call(e));const b=e.snapGrid.length,v=e.slidesGrid.length;let E=a.spaceBetween,M=-p,T=0,P=0;if(typeof i>"u")return;"string"==typeof E&&E.indexOf("%")>=0&&(E=parseFloat(E.replace("%",""))/100*i),e.virtualSize=-E,o.css(c?{marginLeft:"",marginBottom:"",marginTop:""}:{marginRight:"",marginBottom:"",marginTop:""}),a.centeredSlides&&a.cssMode&&(ee(e.wrapperEl,"--swiper-centered-offset-before",""),ee(e.wrapperEl,"--swiper-centered-offset-after",""));const _=a.grid&&a.grid.rows>1&&e.grid;let k;_&&e.grid.initSlides(r);const O="auto"===a.slidesPerView&&a.breakpoints&&Object.keys(a.breakpoints).filter(I=>typeof a.breakpoints[I].slidesPerView<"u").length>0;for(let I=0;I<r;I+=1){k=0;const y=o.eq(I);if(_&&e.grid.updateSlide(I,y,r,s),"none"!==y.css("display")){if("auto"===a.slidesPerView){O&&(o[I].style[s("width")]="");const S=getComputedStyle(y[0]),C=y[0].style.transform,D=y[0].style.webkitTransform;if(C&&(y[0].style.transform="none"),D&&(y[0].style.webkitTransform="none"),a.roundLengths)k=e.isHorizontal()?y.outerWidth(!0):y.outerHeight(!0);else{const L=t(S,"width"),x=t(S,"padding-left"),w=t(S,"padding-right"),z=t(S,"margin-left"),R=t(S,"margin-right"),N=S.getPropertyValue("box-sizing");if(N&&"border-box"===N)k=L+z+R;else{const{clientWidth:V,offsetWidth:q}=y[0];k=L+x+w+z+R+(q-V)}}C&&(y[0].style.transform=C),D&&(y[0].style.webkitTransform=D),a.roundLengths&&(k=Math.floor(k))}else k=(i-(a.slidesPerView-1)*E)/a.slidesPerView,a.roundLengths&&(k=Math.floor(k)),o[I]&&(o[I].style[s("width")]=`${k}px`);o[I]&&(o[I].swiperSlideSize=k),g.push(k),a.centeredSlides?(M=M+k/2+T/2+E,0===T&&0!==I&&(M=M-i/2-E),0===I&&(M=M-i/2-E),Math.abs(M)<.001&&(M=0),a.roundLengths&&(M=Math.floor(M)),P%a.slidesPerGroup==0&&u.push(M),m.push(M)):(a.roundLengths&&(M=Math.floor(M)),(P-Math.min(e.params.slidesPerGroupSkip,P))%e.params.slidesPerGroup==0&&u.push(M),m.push(M),M=M+k+E),e.virtualSize+=k+E,T=k,P+=1}}if(e.virtualSize=Math.max(e.virtualSize,i)+h,c&&f&&("slide"===a.effect||"coverflow"===a.effect)&&n.css({width:`${e.virtualSize+a.spaceBetween}px`}),a.setWrapperSize&&n.css({[s("width")]:`${e.virtualSize+a.spaceBetween}px`}),_&&e.grid.updateWrapperSize(k,u,s),!a.centeredSlides){const I=[];for(let y=0;y<u.length;y+=1){let S=u[y];a.roundLengths&&(S=Math.floor(S)),u[y]<=e.virtualSize-i&&I.push(S)}u=I,Math.floor(e.virtualSize-i)-Math.floor(u[u.length-1])>1&&u.push(e.virtualSize-i)}if(0===u.length&&(u=[0]),0!==a.spaceBetween){const I=e.isHorizontal()&&c?"marginLeft":s("marginRight");o.filter((y,S)=>!a.cssMode||S!==o.length-1).css({[I]:`${E}px`})}if(a.centeredSlides&&a.centeredSlidesBounds){let I=0;g.forEach(S=>{I+=S+(a.spaceBetween?a.spaceBetween:0)}),I-=a.spaceBetween;const y=I-i;u=u.map(S=>S<0?-p:S>y?y+h:S)}if(a.centerInsufficientSlides){let I=0;if(g.forEach(y=>{I+=y+(a.spaceBetween?a.spaceBetween:0)}),I-=a.spaceBetween,I<i){const y=(i-I)/2;u.forEach((S,C)=>{u[C]=S-y}),m.forEach((S,C)=>{m[C]=S+y})}}if(Object.assign(e,{slides:o,snapGrid:u,slidesGrid:m,slidesSizesGrid:g}),a.centeredSlides&&a.cssMode&&!a.centeredSlidesBounds){ee(e.wrapperEl,"--swiper-centered-offset-before",-u[0]+"px"),ee(e.wrapperEl,"--swiper-centered-offset-after",e.size/2-g[g.length-1]/2+"px");const I=-e.snapGrid[0],y=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(S=>S+I),e.slidesGrid=e.slidesGrid.map(S=>S+y)}if(r!==d&&e.emit("slidesLengthChange"),u.length!==b&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),m.length!==v&&e.emit("slidesGridLengthChange"),a.watchSlidesProgress&&e.updateSlidesOffset(),!(l||a.cssMode||"slide"!==a.effect&&"fade"!==a.effect)){const I=`${a.containerModifierClass}backface-hidden`,y=e.$el.hasClass(I);r<=a.maxBackfaceHiddenSlides?y||e.$el.addClass(I):y&&e.$el.removeClass(I)}},updateAutoHeight:function Vt(e){const s=this,t=[],a=s.virtual&&s.params.virtual.enabled;let i,n=0;"number"==typeof e?s.setTransition(e):!0===e&&s.setTransition(s.params.speed);const c=f=>a?s.slides.filter(l=>parseInt(l.getAttribute("data-swiper-slide-index"),10)===f)[0]:s.slides.eq(f)[0];if("auto"!==s.params.slidesPerView&&s.params.slidesPerView>1)if(s.params.centeredSlides)(s.visibleSlides||H([])).each(f=>{t.push(f)});else for(i=0;i<Math.ceil(s.params.slidesPerView);i+=1){const f=s.activeIndex+i;if(f>s.slides.length&&!a)break;t.push(c(f))}else t.push(c(s.activeIndex));for(i=0;i<t.length;i+=1)if(typeof t[i]<"u"){const f=t[i].offsetHeight;n=f>n?f:n}(n||0===n)&&s.$wrapperEl.css("height",`${n}px`)},updateSlidesOffset:function Wt(){const e=this,s=e.slides;for(let t=0;t<s.length;t+=1)s[t].swiperSlideOffset=e.isHorizontal()?s[t].offsetLeft:s[t].offsetTop},updateSlidesProgress:function Ft(e=this&&this.translate||0){const s=this,t=s.params,{slides:a,rtlTranslate:n,snapGrid:i}=s;if(0===a.length)return;typeof a[0].swiperSlideOffset>"u"&&s.updateSlidesOffset();let c=-e;n&&(c=e),a.removeClass(t.slideVisibleClass),s.visibleSlidesIndexes=[],s.visibleSlides=[];for(let f=0;f<a.length;f+=1){const l=a[f];let d=l.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(d-=a[0].swiperSlideOffset);const o=(c+(t.centeredSlides?s.minTranslate():0)-d)/(l.swiperSlideSize+t.spaceBetween),r=(c-i[0]+(t.centeredSlides?s.minTranslate():0)-d)/(l.swiperSlideSize+t.spaceBetween),u=-(c-d),m=u+s.slidesSizesGrid[f];(u>=0&&u<s.size-1||m>1&&m<=s.size||u<=0&&m>=s.size)&&(s.visibleSlides.push(l),s.visibleSlidesIndexes.push(f),a.eq(f).addClass(t.slideVisibleClass)),l.progress=n?-o:o,l.originalProgress=n?-r:r}s.visibleSlides=H(s.visibleSlides)},updateProgress:function Xt(e){const s=this;typeof e>"u"&&(e=s&&s.translate&&s.translate*(s.rtlTranslate?-1:1)||0);const t=s.params,a=s.maxTranslate()-s.minTranslate();let{progress:n,isBeginning:i,isEnd:c}=s;const f=i,l=c;0===a?(n=0,i=!0,c=!0):(n=(e-s.minTranslate())/a,i=n<=0,c=n>=1),Object.assign(s,{progress:n,isBeginning:i,isEnd:c}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&s.updateSlidesProgress(e),i&&!f&&s.emit("reachBeginning toEdge"),c&&!l&&s.emit("reachEnd toEdge"),(f&&!i||l&&!c)&&s.emit("fromEdge"),s.emit("progress",n)},updateSlidesClasses:function Yt(){const e=this,{slides:s,params:t,$wrapperEl:a,activeIndex:n,realIndex:i}=e,c=e.virtual&&t.virtual.enabled;let f;s.removeClass(`${t.slideActiveClass} ${t.slideNextClass} ${t.slidePrevClass} ${t.slideDuplicateActiveClass} ${t.slideDuplicateNextClass} ${t.slideDuplicatePrevClass}`),f=c?e.$wrapperEl.find(`.${t.slideClass}[data-swiper-slide-index="${n}"]`):s.eq(n),f.addClass(t.slideActiveClass),t.loop&&(f.hasClass(t.slideDuplicateClass)?a.children(`.${t.slideClass}:not(.${t.slideDuplicateClass})[data-swiper-slide-index="${i}"]`).addClass(t.slideDuplicateActiveClass):a.children(`.${t.slideClass}.${t.slideDuplicateClass}[data-swiper-slide-index="${i}"]`).addClass(t.slideDuplicateActiveClass));let l=f.nextAll(`.${t.slideClass}`).eq(0).addClass(t.slideNextClass);t.loop&&0===l.length&&(l=s.eq(0),l.addClass(t.slideNextClass));let d=f.prevAll(`.${t.slideClass}`).eq(0).addClass(t.slidePrevClass);t.loop&&0===d.length&&(d=s.eq(-1),d.addClass(t.slidePrevClass)),t.loop&&(l.hasClass(t.slideDuplicateClass)?a.children(`.${t.slideClass}:not(.${t.slideDuplicateClass})[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`).addClass(t.slideDuplicateNextClass):a.children(`.${t.slideClass}.${t.slideDuplicateClass}[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`).addClass(t.slideDuplicateNextClass),d.hasClass(t.slideDuplicateClass)?a.children(`.${t.slideClass}:not(.${t.slideDuplicateClass})[data-swiper-slide-index="${d.attr("data-swiper-slide-index")}"]`).addClass(t.slideDuplicatePrevClass):a.children(`.${t.slideClass}.${t.slideDuplicateClass}[data-swiper-slide-index="${d.attr("data-swiper-slide-index")}"]`).addClass(t.slideDuplicatePrevClass)),e.emitSlidesClasses()},updateActiveIndex:function jt(e){const s=this,t=s.rtlTranslate?s.translate:-s.translate,{slidesGrid:a,snapGrid:n,params:i,activeIndex:c,realIndex:f,snapIndex:l}=s;let o,d=e;if(typeof d>"u"){for(let u=0;u<a.length;u+=1)typeof a[u+1]<"u"?t>=a[u]&&t<a[u+1]-(a[u+1]-a[u])/2?d=u:t>=a[u]&&t<a[u+1]&&(d=u+1):t>=a[u]&&(d=u);i.normalizeSlideIndex&&(d<0||typeof d>"u")&&(d=0)}if(n.indexOf(t)>=0)o=n.indexOf(t);else{const u=Math.min(i.slidesPerGroupSkip,d);o=u+Math.floor((d-u)/i.slidesPerGroup)}if(o>=n.length&&(o=n.length-1),d===c)return void(o!==l&&(s.snapIndex=o,s.emit("snapIndexChange")));const r=parseInt(s.slides.eq(d).attr("data-swiper-slide-index")||d,10);Object.assign(s,{snapIndex:o,realIndex:r,previousIndex:c,activeIndex:d}),s.emit("activeIndexChange"),s.emit("snapIndexChange"),f!==r&&s.emit("realIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&s.emit("slideChange")},updateClickedSlide:function qt(e){const s=this,t=s.params,a=H(e).closest(`.${t.slideClass}`)[0];let i,n=!1;if(a)for(let c=0;c<s.slides.length;c+=1)if(s.slides[c]===a){n=!0,i=c;break}if(!a||!n)return s.clickedSlide=void 0,void(s.clickedIndex=void 0);s.clickedSlide=a,s.clickedIndex=s.virtual&&s.params.virtual.enabled?parseInt(H(a).attr("data-swiper-slide-index"),10):i,t.slideToClickedSlide&&void 0!==s.clickedIndex&&s.clickedIndex!==s.activeIndex&&s.slideToClickedSlide()}},translate:{getTranslate:function Zt(e=(this.isHorizontal()?"x":"y")){const{params:t,rtlTranslate:a,translate:n,$wrapperEl:i}=this;if(t.virtualTranslate)return a?-n:n;if(t.cssMode)return n;let c=function kt(e,s="x"){const t=G();let a,n,i;const c=function It(e){const s=G();let t;return s.getComputedStyle&&(t=s.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return t.WebKitCSSMatrix?(n=c.transform||c.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(f=>f.replace(",",".")).join(", ")),i=new t.WebKitCSSMatrix("none"===n?"":n)):(i=c.MozTransform||c.OTransform||c.MsTransform||c.msTransform||c.transform||c.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),a=i.toString().split(",")),"x"===s&&(n=t.WebKitCSSMatrix?i.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===s&&(n=t.WebKitCSSMatrix?i.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),n||0}(i[0],e);return a&&(c=-c),c||0},setTranslate:function Kt(e,s){const t=this,{rtlTranslate:a,params:n,$wrapperEl:i,wrapperEl:c,progress:f}=t;let r,l=0,d=0;t.isHorizontal()?l=a?-e:e:d=e,n.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),n.cssMode?c[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-l:-d:n.virtualTranslate||i.transform(`translate3d(${l}px, ${d}px, 0px)`),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?l:d;const u=t.maxTranslate()-t.minTranslate();r=0===u?0:(e-t.minTranslate())/u,r!==f&&t.updateProgress(e),t.emit("setTranslate",t.translate,s)},minTranslate:function Qt(){return-this.snapGrid[0]},maxTranslate:function Jt(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function es(e=0,s=this.params.speed,t=!0,a=!0,n){const i=this,{params:c,wrapperEl:f}=i;if(i.animating&&c.preventInteractionOnTransition)return!1;const l=i.minTranslate(),d=i.maxTranslate();let o;if(o=a&&e>l?l:a&&e<d?d:e,i.updateProgress(o),c.cssMode){const r=i.isHorizontal();if(0===s)f[r?"scrollLeft":"scrollTop"]=-o;else{if(!i.support.smoothScroll)return Ce({swiper:i,targetPosition:-o,side:r?"left":"top"}),!0;f.scrollTo({[r?"left":"top"]:-o,behavior:"smooth"})}return!0}return 0===s?(i.setTransition(0),i.setTranslate(o),t&&(i.emit("beforeTransitionStart",s,n),i.emit("transitionEnd"))):(i.setTransition(s),i.setTranslate(o),t&&(i.emit("beforeTransitionStart",s,n),i.emit("transitionStart")),i.animating||(i.animating=!0,i.onTranslateToWrapperTransitionEnd||(i.onTranslateToWrapperTransitionEnd=function(u){!i||i.destroyed||u.target===this&&(i.$wrapperEl[0].removeEventListener("transitionend",i.onTranslateToWrapperTransitionEnd),i.$wrapperEl[0].removeEventListener("webkitTransitionEnd",i.onTranslateToWrapperTransitionEnd),i.onTranslateToWrapperTransitionEnd=null,delete i.onTranslateToWrapperTransitionEnd,t&&i.emit("transitionEnd"))}),i.$wrapperEl[0].addEventListener("transitionend",i.onTranslateToWrapperTransitionEnd),i.$wrapperEl[0].addEventListener("webkitTransitionEnd",i.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function ss(e,s){const t=this;t.params.cssMode||t.$wrapperEl.transition(e),t.emit("setTransition",e,s)},transitionStart:function as(e=!0,s){const t=this,{params:a}=t;a.cssMode||(a.autoHeight&&t.updateAutoHeight(),_e({swiper:t,runCallbacks:e,direction:s,step:"Start"}))},transitionEnd:function ns(e=!0,s){const t=this,{params:a}=t;t.animating=!1,!a.cssMode&&(t.setTransition(0),_e({swiper:t,runCallbacks:e,direction:s,step:"End"}))}},slide:{slideTo:function ls(e=0,s=this.params.speed,t=!0,a,n){if("number"!=typeof e&&"string"!=typeof e)throw new Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof e}] given.`);if("string"==typeof e){const E=parseInt(e,10);if(!isFinite(E))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=E}const i=this;let c=e;c<0&&(c=0);const{params:f,snapGrid:l,slidesGrid:d,previousIndex:o,activeIndex:r,rtlTranslate:u,wrapperEl:m,enabled:g}=i;if(i.animating&&f.preventInteractionOnTransition||!g&&!a&&!n)return!1;const p=Math.min(i.params.slidesPerGroupSkip,c);let h=p+Math.floor((c-p)/i.params.slidesPerGroup);h>=l.length&&(h=l.length-1);const b=-l[h];if(f.normalizeSlideIndex)for(let E=0;E<d.length;E+=1){const M=-Math.floor(100*b),T=Math.floor(100*d[E]),P=Math.floor(100*d[E+1]);typeof d[E+1]<"u"?M>=T&&M<P-(P-T)/2?c=E:M>=T&&M<P&&(c=E+1):M>=T&&(c=E)}if(i.initialized&&c!==r&&(!i.allowSlideNext&&b<i.translate&&b<i.minTranslate()||!i.allowSlidePrev&&b>i.translate&&b>i.maxTranslate()&&(r||0)!==c))return!1;let v;if(c!==(o||0)&&t&&i.emit("beforeSlideChangeStart"),i.updateProgress(b),v=c>r?"next":c<r?"prev":"reset",u&&-b===i.translate||!u&&b===i.translate)return i.updateActiveIndex(c),f.autoHeight&&i.updateAutoHeight(),i.updateSlidesClasses(),"slide"!==f.effect&&i.setTranslate(b),"reset"!==v&&(i.transitionStart(t,v),i.transitionEnd(t,v)),!1;if(f.cssMode){const E=i.isHorizontal(),M=u?b:-b;if(0===s){const T=i.virtual&&i.params.virtual.enabled;T&&(i.wrapperEl.style.scrollSnapType="none",i._immediateVirtual=!0),m[E?"scrollLeft":"scrollTop"]=M,T&&requestAnimationFrame(()=>{i.wrapperEl.style.scrollSnapType="",i._swiperImmediateVirtual=!1})}else{if(!i.support.smoothScroll)return Ce({swiper:i,targetPosition:M,side:E?"left":"top"}),!0;m.scrollTo({[E?"left":"top"]:M,behavior:"smooth"})}return!0}return i.setTransition(s),i.setTranslate(b),i.updateActiveIndex(c),i.updateSlidesClasses(),i.emit("beforeTransitionStart",s,a),i.transitionStart(t,v),0===s?i.transitionEnd(t,v):i.animating||(i.animating=!0,i.onSlideToWrapperTransitionEnd||(i.onSlideToWrapperTransitionEnd=function(M){!i||i.destroyed||M.target===this&&(i.$wrapperEl[0].removeEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.$wrapperEl[0].removeEventListener("webkitTransitionEnd",i.onSlideToWrapperTransitionEnd),i.onSlideToWrapperTransitionEnd=null,delete i.onSlideToWrapperTransitionEnd,i.transitionEnd(t,v))}),i.$wrapperEl[0].addEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.$wrapperEl[0].addEventListener("webkitTransitionEnd",i.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function rs(e=0,s=this.params.speed,t=!0,a){if("string"==typeof e){const c=parseInt(e,10);if(!isFinite(c))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=c}const n=this;let i=e;return n.params.loop&&(i+=n.loopedSlides),n.slideTo(i,s,t,a)},slideNext:function os(e=this.params.speed,s=!0,t){const a=this,{animating:n,enabled:i,params:c}=a;if(!i)return a;let f=c.slidesPerGroup;"auto"===c.slidesPerView&&1===c.slidesPerGroup&&c.slidesPerGroupAuto&&(f=Math.max(a.slidesPerViewDynamic("current",!0),1));const l=a.activeIndex<c.slidesPerGroupSkip?1:f;if(c.loop){if(n&&c.loopPreventsSlide)return!1;a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft}return a.slideTo(c.rewind&&a.isEnd?0:a.activeIndex+l,e,s,t)},slidePrev:function ds(e=this.params.speed,s=!0,t){const a=this,{params:n,animating:i,snapGrid:c,slidesGrid:f,rtlTranslate:l,enabled:d}=a;if(!d)return a;if(n.loop){if(i&&n.loopPreventsSlide)return!1;a.loopFix(),a._clientLeft=a.$wrapperEl[0].clientLeft}function r(h){return h<0?-Math.floor(Math.abs(h)):Math.floor(h)}const u=r(l?a.translate:-a.translate),m=c.map(h=>r(h));let g=c[m.indexOf(u)-1];if(typeof g>"u"&&n.cssMode){let h;c.forEach((b,v)=>{u>=b&&(h=v)}),typeof h<"u"&&(g=c[h>0?h-1:h])}let p=0;return typeof g<"u"&&(p=f.indexOf(g),p<0&&(p=a.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(p=p-a.slidesPerViewDynamic("previous",!0)+1,p=Math.max(p,0))),a.slideTo(n.rewind&&a.isBeginning?a.params.virtual&&a.params.virtual.enabled&&a.virtual?a.virtual.slides.length-1:a.slides.length-1:p,e,s,t)},slideReset:function cs(e=this.params.speed,s=!0,t){return this.slideTo(this.activeIndex,e,s,t)},slideToClosest:function fs(e=this.params.speed,s=!0,t,a=.5){const n=this;let i=n.activeIndex;const c=Math.min(n.params.slidesPerGroupSkip,i),f=c+Math.floor((i-c)/n.params.slidesPerGroup),l=n.rtlTranslate?n.translate:-n.translate;if(l>=n.snapGrid[f]){const d=n.snapGrid[f];l-d>(n.snapGrid[f+1]-d)*a&&(i+=n.params.slidesPerGroup)}else{const d=n.snapGrid[f-1];l-d<=(n.snapGrid[f]-d)*a&&(i-=n.params.slidesPerGroup)}return i=Math.max(i,0),i=Math.min(i,n.slidesGrid.length-1),n.slideTo(i,e,s,t)},slideToClickedSlide:function us(){const e=this,{params:s,$wrapperEl:t}=e,a="auto"===s.slidesPerView?e.slidesPerViewDynamic():s.slidesPerView;let i,n=e.clickedIndex;if(s.loop){if(e.animating)return;i=parseInt(H(e.clickedSlide).attr("data-swiper-slide-index"),10),s.centeredSlides?n<e.loopedSlides-a/2||n>e.slides.length-e.loopedSlides+a/2?(e.loopFix(),n=t.children(`.${s.slideClass}[data-swiper-slide-index="${i}"]:not(.${s.slideDuplicateClass})`).eq(0).index(),oe(()=>{e.slideTo(n)})):e.slideTo(n):n>e.slides.length-a?(e.loopFix(),n=t.children(`.${s.slideClass}[data-swiper-slide-index="${i}"]:not(.${s.slideDuplicateClass})`).eq(0).index(),oe(()=>{e.slideTo(n)})):e.slideTo(n)}else e.slideTo(n)}},loop:{loopCreate:function hs(){const e=this,s=W(),{params:t,$wrapperEl:a}=e,n=a.children().length>0?H(a.children()[0].parentNode):a;n.children(`.${t.slideClass}.${t.slideDuplicateClass}`).remove();let i=n.children(`.${t.slideClass}`);if(t.loopFillGroupWithBlank){const l=t.slidesPerGroup-i.length%t.slidesPerGroup;if(l!==t.slidesPerGroup){for(let d=0;d<l;d+=1){const o=H(s.createElement("div")).addClass(`${t.slideClass} ${t.slideBlankClass}`);n.append(o)}i=n.children(`.${t.slideClass}`)}}"auto"===t.slidesPerView&&!t.loopedSlides&&(t.loopedSlides=i.length),e.loopedSlides=Math.ceil(parseFloat(t.loopedSlides||t.slidesPerView,10)),e.loopedSlides+=t.loopAdditionalSlides,e.loopedSlides>i.length&&e.params.loopedSlidesLimit&&(e.loopedSlides=i.length);const c=[],f=[];i.each((l,d)=>{H(l).attr("data-swiper-slide-index",d)});for(let l=0;l<e.loopedSlides;l+=1){const d=l-Math.floor(l/i.length)*i.length;f.push(i.eq(d)[0]),c.unshift(i.eq(i.length-d-1)[0])}for(let l=0;l<f.length;l+=1)n.append(H(f[l].cloneNode(!0)).addClass(t.slideDuplicateClass));for(let l=c.length-1;l>=0;l-=1)n.prepend(H(c[l].cloneNode(!0)).addClass(t.slideDuplicateClass))},loopFix:function ms(){const e=this;e.emit("beforeLoopFix");const{activeIndex:s,slides:t,loopedSlides:a,allowSlidePrev:n,allowSlideNext:i,snapGrid:c,rtlTranslate:f}=e;let l;e.allowSlidePrev=!0,e.allowSlideNext=!0;const o=-c[s]-e.getTranslate();s<a?(l=t.length-3*a+s,l+=a,e.slideTo(l,0,!1,!0)&&0!==o&&e.setTranslate((f?-e.translate:e.translate)-o)):s>=t.length-a&&(l=-t.length+s+a,l+=a,e.slideTo(l,0,!1,!0)&&0!==o&&e.setTranslate((f?-e.translate:e.translate)-o)),e.allowSlidePrev=n,e.allowSlideNext=i,e.emit("loopFix")},loopDestroy:function gs(){const{$wrapperEl:s,params:t,slides:a}=this;s.children(`.${t.slideClass}.${t.slideDuplicateClass},.${t.slideClass}.${t.slideBlankClass}`).remove(),a.removeAttr("data-swiper-slide-index")}},grabCursor:{setGrabCursor:function bs(e){const s=this;if(s.support.touch||!s.params.simulateTouch||s.params.watchOverflow&&s.isLocked||s.params.cssMode)return;const t="container"===s.params.touchEventsTarget?s.el:s.wrapperEl;t.style.cursor="move",t.style.cursor=e?"grabbing":"grab"},unsetGrabCursor:function Es(){const e=this;e.support.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="")}},events:{attachEvents:function Ps(){const e=this,s=W(),{params:t,support:a}=e;e.onTouchStart=xs.bind(e),e.onTouchMove=Cs.bind(e),e.onTouchEnd=Ts.bind(e),t.cssMode&&(e.onScroll=$s.bind(e)),e.onClick=_s.bind(e),a.touch&&!Me&&(s.addEventListener("touchstart",Ms),Me=!0),Pe(e,"on")},detachEvents:function Is(){Pe(this,"off")}},breakpoints:{setBreakpoint:function zs(){const e=this,{activeIndex:s,initialized:t,loopedSlides:a=0,params:n,$el:i}=e,c=n.breakpoints;if(!c||c&&0===Object.keys(c).length)return;const f=e.getBreakpoint(c,e.params.breakpointsBase,e.el);if(!f||e.currentBreakpoint===f)return;const d=(f in c?c[f]:void 0)||e.originalParams,o=Ie(e,n),r=Ie(e,d),u=n.enabled;o&&!r?(i.removeClass(`${n.containerModifierClass}grid ${n.containerModifierClass}grid-column`),e.emitContainerClasses()):!o&&r&&(i.addClass(`${n.containerModifierClass}grid`),(d.grid.fill&&"column"===d.grid.fill||!d.grid.fill&&"column"===n.grid.fill)&&i.addClass(`${n.containerModifierClass}grid-column`),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(h=>{const b=n[h]&&n[h].enabled,v=d[h]&&d[h].enabled;b&&!v&&e[h].disable(),!b&&v&&e[h].enable()});const m=d.direction&&d.direction!==n.direction,g=n.loop&&(d.slidesPerView!==n.slidesPerView||m);m&&t&&e.changeDirection(),F(e.params,d);const p=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),u&&!p?e.disable():!u&&p&&e.enable(),e.currentBreakpoint=f,e.emit("_beforeBreakpoint",d),g&&t&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(s-a+e.loopedSlides,0,!1)),e.emit("breakpoint",d)},getBreakpoint:function ws(e,s="window",t){if(!e||"container"===s&&!t)return;let a=!1;const n=G(),i="window"===s?n.innerHeight:t.clientHeight,c=Object.keys(e).map(f=>{if("string"==typeof f&&0===f.indexOf("@")){const l=parseFloat(f.substr(1));return{value:i*l,point:f}}return{value:f,point:f}});c.sort((f,l)=>parseInt(f.value,10)-parseInt(l.value,10));for(let f=0;f<c.length;f+=1){const{point:l,value:d}=c[f];"window"===s?n.matchMedia(`(min-width: ${d}px)`).matches&&(a=l):d<=t.clientWidth&&(a=l)}return a||"max"}},checkOverflow:{checkOverflow:function Gs(){const e=this,{isLocked:s,params:t}=e,{slidesOffsetBefore:a}=t;if(a){const n=e.slides.length-1;e.isLocked=e.size>e.slidesGrid[n]+e.slidesSizesGrid[n]+2*a}else e.isLocked=1===e.snapGrid.length;!0===t.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===t.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),s&&s!==e.isLocked&&(e.isEnd=!1),s!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function Ls(){const e=this,{classNames:s,params:t,rtl:a,$el:n,device:i,support:c}=e,f=function Ds(e,s){const t=[];return e.forEach(a=>{"object"==typeof a?Object.keys(a).forEach(n=>{a[n]&&t.push(s+n)}):"string"==typeof a&&t.push(s+a)}),t}(["initialized",t.direction,{"pointer-events":!c.touch},{"free-mode":e.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:a},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:i.android},{ios:i.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);s.push(...f),n.addClass([...s].join(" ")),e.emitContainerClasses()},removeClasses:function As(){const{$el:s,classNames:t}=this;s.removeClass(t.join(" ")),this.emitContainerClasses()}},images:{loadImage:function Ns(e,s,t,a,n,i){const c=G();let f;function l(){i&&i()}H(e).parent("picture")[0]||e.complete&&n||!s?l():(f=new c.Image,f.onload=l,f.onerror=l,a&&(f.sizes=a),t&&(f.srcset=t),s&&(f.src=s))},preloadImages:function Hs(){const e=this;function s(){typeof e>"u"||null===e||!e||e.destroyed||(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(let t=0;t<e.imagesToLoad.length;t+=1){const a=e.imagesToLoad[t];e.loadImage(a,a.currentSrc||a.getAttribute("src"),a.srcset||a.getAttribute("srcset"),a.sizes||a.getAttribute("sizes"),!0,s)}}}},pe={};class X{constructor(...s){let t,a;if(1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?a=s[0]:[t,a]=s,a||(a={}),a=F({},a),t&&!a.el&&(a.el=t),a.el&&H(a.el).length>1){const f=[];return H(a.el).each(l=>{const d=F({},a,{el:l});f.push(new X(d))}),f}const n=this;n.__swiper__=!0,n.support=Te(),n.device=function Dt(e={}){return ce||(ce=function Ot({userAgent:e}={}){const s=Te(),t=G(),a=t.navigator.platform,n=e||t.navigator.userAgent,i={ios:!1,android:!1},c=t.screen.width,f=t.screen.height,l=n.match(/(Android);?[\s\/]+([\d.]+)?/);let d=n.match(/(iPad).*OS\s([\d_]+)/);const o=n.match(/(iPod)(.*OS\s([\d_]+))?/),r=!d&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),u="Win32"===a;let m="MacIntel"===a;return!d&&m&&s.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${c}x${f}`)>=0&&(d=n.match(/(Version)\/([\d.]+)/),d||(d=[0,1,"13_0_0"]),m=!1),l&&!u&&(i.os="android",i.android=!0),(d||r||o)&&(i.os="ios",i.ios=!0),i}(e)),ce}({userAgent:a.userAgent}),n.browser=function At(){return fe||(fe=function Lt(){const e=G();return{isSafari:function s(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}(),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}()),fe}(),n.eventsListeners={},n.eventsAnyListeners=[],n.modules=[...n.__modules__],a.modules&&Array.isArray(a.modules)&&n.modules.push(...a.modules);const i={};n.modules.forEach(f=>{f({swiper:n,extendParams:Ws(a,i),on:n.on.bind(n),once:n.once.bind(n),off:n.off.bind(n),emit:n.emit.bind(n)})});const c=F({},ke,i);return n.params=F({},c,pe,a),n.originalParams=F({},n.params),n.passedParams=F({},a),n.params&&n.params.on&&Object.keys(n.params.on).forEach(f=>{n.on(f,n.params.on[f])}),n.params&&n.params.onAny&&n.onAny(n.params.onAny),n.$=H,Object.assign(n,{enabled:n.params.enabled,el:t,classNames:[],slides:H(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===n.params.direction,isVertical:()=>"vertical"===n.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:n.params.allowSlideNext,allowSlidePrev:n.params.allowSlidePrev,touchEvents:function(){const l=["touchstart","touchmove","touchend","touchcancel"],d=["pointerdown","pointermove","pointerup"];return n.touchEventsTouch={start:l[0],move:l[1],end:l[2],cancel:l[3]},n.touchEventsDesktop={start:d[0],move:d[1],end:d[2]},n.support.touch||!n.params.simulateTouch?n.touchEventsTouch:n.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:n.params.focusableElements,lastClickTime:K(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:n.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),n.emit("_swiper"),n.params.init&&n.init(),n}enable(){const s=this;s.enabled||(s.enabled=!0,s.params.grabCursor&&s.setGrabCursor(),s.emit("enable"))}disable(){const s=this;s.enabled&&(s.enabled=!1,s.params.grabCursor&&s.unsetGrabCursor(),s.emit("disable"))}setProgress(s,t){const a=this;s=Math.min(Math.max(s,0),1);const n=a.minTranslate(),c=(a.maxTranslate()-n)*s+n;a.translateTo(c,typeof t>"u"?0:t),a.updateActiveIndex(),a.updateSlidesClasses()}emitContainerClasses(){const s=this;if(!s.params._emitClasses||!s.el)return;const t=s.el.className.split(" ").filter(a=>0===a.indexOf("swiper")||0===a.indexOf(s.params.containerModifierClass));s.emit("_containerClasses",t.join(" "))}getSlideClasses(s){const t=this;return t.destroyed?"":s.className.split(" ").filter(a=>0===a.indexOf("swiper-slide")||0===a.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){const s=this;if(!s.params._emitClasses||!s.el)return;const t=[];s.slides.each(a=>{const n=s.getSlideClasses(a);t.push({slideEl:a,classNames:n}),s.emit("_slideClass",a,n)}),s.emit("_slideClasses",t)}slidesPerViewDynamic(s="current",t=!1){const{params:n,slides:i,slidesGrid:c,slidesSizesGrid:f,size:l,activeIndex:d}=this;let o=1;if(n.centeredSlides){let u,r=i[d].swiperSlideSize;for(let m=d+1;m<i.length;m+=1)i[m]&&!u&&(r+=i[m].swiperSlideSize,o+=1,r>l&&(u=!0));for(let m=d-1;m>=0;m-=1)i[m]&&!u&&(r+=i[m].swiperSlideSize,o+=1,r>l&&(u=!0))}else if("current"===s)for(let r=d+1;r<i.length;r+=1)(t?c[r]+f[r]-c[d]<l:c[r]-c[d]<l)&&(o+=1);else for(let r=d-1;r>=0;r-=1)c[d]-c[r]<l&&(o+=1);return o}update(){const s=this;if(!s||s.destroyed)return;const{snapGrid:t,params:a}=s;function n(){const f=Math.min(Math.max(s.rtlTranslate?-1*s.translate:s.translate,s.maxTranslate()),s.minTranslate());s.setTranslate(f),s.updateActiveIndex(),s.updateSlidesClasses()}let i;a.breakpoints&&s.setBreakpoint(),s.updateSize(),s.updateSlides(),s.updateProgress(),s.updateSlidesClasses(),s.params.freeMode&&s.params.freeMode.enabled?(n(),s.params.autoHeight&&s.updateAutoHeight()):(i=s.slideTo(("auto"===s.params.slidesPerView||s.params.slidesPerView>1)&&s.isEnd&&!s.params.centeredSlides?s.slides.length-1:s.activeIndex,0,!1,!0),i||n()),a.watchOverflow&&t!==s.snapGrid&&s.checkOverflow(),s.emit("update")}changeDirection(s,t=!0){const a=this,n=a.params.direction;return s||(s="horizontal"===n?"vertical":"horizontal"),s===n||"horizontal"!==s&&"vertical"!==s||(a.$el.removeClass(`${a.params.containerModifierClass}${n}`).addClass(`${a.params.containerModifierClass}${s}`),a.emitContainerClasses(),a.params.direction=s,a.slides.each(i=>{"vertical"===s?i.style.width="":i.style.height=""}),a.emit("changeDirection"),t&&a.update()),a}changeLanguageDirection(s){const t=this;t.rtl&&"rtl"===s||!t.rtl&&"ltr"===s||(t.rtl="rtl"===s,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.$el.addClass(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.$el.removeClass(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(s){const t=this;if(t.mounted)return!0;const a=H(s||t.params.el);if(!(s=a[0]))return!1;s.swiper=t;const n=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let c=(()=>{if(s&&s.shadowRoot&&s.shadowRoot.querySelector){const f=H(s.shadowRoot.querySelector(n()));return f.children=l=>a.children(l),f}return a.children?a.children(n()):H(a).children(n())})();if(0===c.length&&t.params.createElements){const l=W().createElement("div");c=H(l),l.className=t.params.wrapperClass,a.append(l),a.children(`.${t.params.slideClass}`).each(d=>{c.append(d)})}return Object.assign(t,{$el:a,el:s,$wrapperEl:c,wrapperEl:c[0],mounted:!0,rtl:"rtl"===s.dir.toLowerCase()||"rtl"===a.css("direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===s.dir.toLowerCase()||"rtl"===a.css("direction")),wrongRTL:"-webkit-box"===c.css("display")}),!0}init(s){const t=this;return t.initialized||!1===t.mount(s)||(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.params.loop&&t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.preloadImages&&t.preloadImages(),t.slideTo(t.params.loop?t.params.initialSlide+t.loopedSlides:t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t}destroy(s=!0,t=!0){const a=this,{params:n,$el:i,$wrapperEl:c,slides:f}=a;return typeof a.params>"u"||a.destroyed||(a.emit("beforeDestroy"),a.initialized=!1,a.detachEvents(),n.loop&&a.loopDestroy(),t&&(a.removeClasses(),i.removeAttr("style"),c.removeAttr("style"),f&&f.length&&f.removeClass([n.slideVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),a.emit("destroy"),Object.keys(a.eventsListeners).forEach(l=>{a.off(l)}),!1!==s&&(a.$el[0].swiper=null,function Pt(e){const s=e;Object.keys(s).forEach(t=>{try{s[t]=null}catch{}try{delete s[t]}catch{}})}(a)),a.destroyed=!0),null}static extendDefaults(s){F(pe,s)}static get extendedDefaults(){return pe}static get defaults(){return ke}static installModule(s){X.prototype.__modules__||(X.prototype.__modules__=[]);const t=X.prototype.__modules__;"function"==typeof s&&t.indexOf(s)<0&&t.push(s)}static use(s){return Array.isArray(s)?(s.forEach(t=>X.installModule(t)),X):(X.installModule(s),X)}}Object.keys(ue).forEach(e=>{Object.keys(ue[e]).forEach(s=>{X.prototype[s]=ue[e][s]})}),X.use([function Rt({swiper:e,on:s,emit:t}){const a=G();let n=null,i=null;const c=()=>{!e||e.destroyed||!e.initialized||(t("beforeResize"),t("resize"))},d=()=>{!e||e.destroyed||!e.initialized||t("orientationchange")};s("init",()=>{e.params.resizeObserver&&typeof a.ResizeObserver<"u"?!e||e.destroyed||!e.initialized||(n=new ResizeObserver(o=>{i=a.requestAnimationFrame(()=>{const{width:r,height:u}=e;let m=r,g=u;o.forEach(({contentBoxSize:p,contentRect:h,target:b})=>{b&&b!==e.el||(m=h?h.width:(p[0]||p).inlineSize,g=h?h.height:(p[0]||p).blockSize)}),(m!==r||g!==u)&&c()})}),n.observe(e.el)):(a.addEventListener("resize",c),a.addEventListener("orientationchange",d))}),s("destroy",()=>{i&&a.cancelAnimationFrame(i),n&&n.unobserve&&e.el&&(n.unobserve(e.el),n=null),a.removeEventListener("resize",c),a.removeEventListener("orientationchange",d)})},function Nt({swiper:e,extendParams:s,on:t,emit:a}){const n=[],i=G(),c=(d,o={})=>{const u=new(i.MutationObserver||i.WebkitMutationObserver)(m=>{if(1===m.length)return void a("observerUpdate",m[0]);const g=function(){a("observerUpdate",m[0])};i.requestAnimationFrame?i.requestAnimationFrame(g):i.setTimeout(g,0)});u.observe(d,{attributes:typeof o.attributes>"u"||o.attributes,childList:typeof o.childList>"u"||o.childList,characterData:typeof o.characterData>"u"||o.characterData}),n.push(u)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),t("init",()=>{if(e.params.observer){if(e.params.observeParents){const d=e.$el.parents();for(let o=0;o<d.length;o+=1)c(d[o])}c(e.$el[0],{childList:e.params.observeSlideChildren}),c(e.$wrapperEl[0],{attributes:!1})}}),t("destroy",()=>{n.forEach(d=>{d.disconnect()}),n.splice(0,n.length)})}]);["init","enabled","_direction","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_preloadImages","updateOnImagesReady","_loop","_loopAdditionalSlides","_loopedSlides","_loopedSlidesLimit","_loopFillGroupWithBlank","loopPreventsSlide","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideBlankClass","slideActiveClass","slideDuplicateActiveClass","slideVisibleClass","slideDuplicateClass","slideNextClass","slideDuplicateNextClass","slidePrevClass","slideDuplicatePrevClass","wrapperClass","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","lazy","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","on"].map(e=>e.replace(/_/,""));let Qs=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=be.$C({type:e}),e.\u0275inj=be.G2t({imports:[[He.MD]]}),e})()}}]);