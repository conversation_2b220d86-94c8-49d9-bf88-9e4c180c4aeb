"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4519],{26801:($,S,a)=>{a.r(S),a.d(S,{OrderDetailMarketPlacePageModule:()=>U});var d=a(56610),E=a(37222),_=a(77897),g=a(77575),u=a(73308),m=a(39893),I=a(92882),h=a(92533),D=a(91285),j=a(62049),A=a(28863),R=a(58133),O=a(88233),x=a(82571),c=a(99987),n=a(2978),o=a(11244),l=a(94440),s=a(74657);function M(i,P){if(1&i){const e=n.RV6();n.j41(0,"ion-buttons",23)(1,"ion-button",24),n.bIt("click",function(t){n.eBV(e);const k=n.XpG();return n.Njj(k.presentPopover(t))}),n.nrm(2,"ion-icon",25),n.k0s()()}}function v(i,P){1&i&&(n.j41(0,"ion-label",2),n.EFF(1),n.nI1(2,"capitalize"),n.nI1(3,"translate"),n.k0s()),2&i&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,n.bMT(3,3,"order.detail.validated-at"))," :"))}function f(i,P){1&i&&(n.j41(0,"ion-label",2),n.EFF(1),n.nI1(2,"capitalize"),n.nI1(3,"translate"),n.k0s()),2&i&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,n.bMT(3,3,"history-page.ref"))," :"))}function C(i,P){if(1&i&&(n.j41(0,"ion-label",13),n.EFF(1),n.nI1(2,"date"),n.k0s()),2&i){const e=n.XpG();n.R7$(1),n.SpI(" ",n.i5U(2,1,null==e.marketService||null==e.marketService.order||null==e.marketService.order.validation?null:e.marketService.order.validation.date,"dd/MM/YY")," ")}}function p(i,P){if(1&i&&(n.j41(0,"ion-label",13),n.EFF(1),n.nI1(2,"capitalize"),n.k0s()),2&i){const e=n.XpG();n.R7$(1),n.SpI(" ",n.bMT(2,1,null==e.marketService||null==e.marketService.order?null:e.marketService.order.appReference)," ")}}function b(i,P){if(1&i&&(n.j41(0,"div",33)(1,"div",27)(2,"div",34),n.nrm(3,"ion-img",35),n.k0s()(),n.j41(4,"div",36),n.EFF(5),n.k0s(),n.j41(6,"div",37),n.EFF(7),n.nI1(8,"number"),n.k0s(),n.j41(9,"div",38),n.EFF(10),n.nI1(11,"number"),n.k0s()()),2&i){const e=n.XpG(2);n.R7$(5),n.Lme(" ",null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart?null:e.marketService.order.cart.quantity," ",null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart||null==e.marketService.order.cart.items?null:e.marketService.order.cart.items.name," "),n.R7$(2),n.SpI(" ",n.i5U(8,4,null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart?null:e.marketService.order.cart.items.price,"")," POINTS "),n.R7$(3),n.SpI(" ",n.i5U(11,7,(null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart||null==e.marketService.order.cart.items?null:e.marketService.order.cart.items.price)*(null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart?null:e.marketService.order.cart.quantity),"")," XAF")}}function y(i,P){if(1&i&&(n.j41(0,"div")(1,"ion-title",8),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",26),n.nrm(5,"div",27),n.j41(6,"div",28),n.EFF(7,"Quantit\xe9"),n.k0s(),n.j41(8,"div",29),n.EFF(9,"PU"),n.k0s(),n.j41(10,"div",30),n.EFF(11,"Montant"),n.k0s()(),n.j41(12,"div",31),n.DNE(13,b,12,10,"div",32),n.k0s()()),2&i){const e=n.XpG();n.R7$(2),n.JRh(n.bMT(3,2,"order.detail.product-list")),n.R7$(11),n.Y8G("ngIf",null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart?null:e.marketService.order.cart.items)}}function T(i,P){if(1&i&&(n.j41(0,"div")(1,"li",33)(2,"div",39)(3,"ion-label"),n.EFF(4),n.nI1(5,"capitalize"),n.k0s(),n.j41(6,"ion-label"),n.EFF(7),n.k0s()(),n.j41(8,"ion-label",18),n.EFF(9),n.k0s(),n.j41(10,"ion-label",18),n.EFF(11),n.nI1(12,"number"),n.k0s()(),n.nrm(13,"li",40),n.k0s()),2&i){const e=n.XpG();n.R7$(4),n.JRh(n.bMT(5,4,null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart||null==e.marketService.order.cart.items?null:e.marketService.order.cart.items.name)),n.R7$(3),n.JRh(null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart||null==e.marketService.order.cart.items?null:e.marketService.order.cart.items.name),n.R7$(2),n.JRh(null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart?null:e.marketService.order.cart.quantity),n.R7$(2),n.SpI("",n.brH(12,6,null==e.marketService||null==e.marketService.order||null==e.marketService.order.cart||null==e.marketService.order.cart.items?null:e.marketService.order.cart.items.price,"","fr")," XAF")}}function F(i,P){if(1&i){const e=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalConfirmValidation())}),n.j41(1,"ion-label"),n.EFF(2," VALIDER"),n.k0s()()}}function L(i,P){if(1&i){const e=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalConfirmValidation())}),n.j41(1,"ion-label"),n.EFF(2," VALIDER"),n.k0s()()}}function V(i,P){if(1&i){const e=n.RV6();n.j41(0,"ion-button",42),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalRejectOrder())}),n.j41(1,"ion-label"),n.EFF(2," REJETER"),n.k0s()()}}function B(i,P){if(1&i){const e=n.RV6();n.j41(0,"ion-button",42),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalRejectOrder())}),n.j41(1,"ion-label"),n.EFF(2," REJETER"),n.k0s()()}}function N(i,P){1&i&&(n.j41(0,"ion-label",46),n.EFF(1),n.k0s()),2&i&&(n.R7$(1),n.SpI(" ","VALIDER LA COMMANDE"," "))}function Y(i,P){if(1&i){const e=n.RV6();n.j41(0,"div",43),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalConfirmValidation())}),n.j41(1,"ion-button",44),n.DNE(2,N,2,1,"ion-label",45),n.k0s()()}if(2&i){const e=n.XpG();n.R7$(2),n.Y8G("ngIf",!e.isLoading)}}const w=function(i){return[i]},z=[{path:"",component:(()=>{class i{constructor(){this.slideProductOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:10},this.isValidate=!1,this.isReject=!1,this.isLoading=!1,this.isPopoverOpen=!1,this.orderStatus=O.Re,this.userCategory=R.s,this.orderAction=O.T3,this.employeeType=R.P,this.actionHandlers={modifier:()=>this.modifyOrder(),telecharger:()=>this.generatePurchaseOrder(this.marketService?.order?._id),annuler:()=>this.handleCancellation()},this.location=(0,n.WQX)(d.aZ),this.marketService=(0,n.WQX)(A.j),this.alertController=(0,n.WQX)(_.hG),this.popoverCtrl=(0,n.WQX)(_.IE),this.commonSrv=(0,n.WQX)(x.h),this.modalCtrl=(0,n.WQX)(_.W3),this.translateService=(0,n.WQX)(j.E),this.router=(0,n.WQX)(g.Ix),this.route=(0,n.WQX)(g.nX),this.trackByFn=(e,r)=>e}ionViewWillEnter(){var e=this;return(0,u.A)(function*(){if(!e.marketService.order&&!e.route.snapshot.params.idOrder)return e.back();if(!e.marketService.order){const r=e.route.snapshot.params.idOrder;e.marketService.order=yield e.marketService.find(r)}e.order=e.marketService.order,console.log(e.order)})()}back(){this.marketService.order=null,this.location.back()}presentPopover(e){var r=this;return(0,u.A)(function*(){const t=yield r.popoverCtrl.create({component:h.c,event:e,cssClass:"custom-popover",mode:"md",componentProps:{actions:[{label:r.getTranslatedText("Modifier la commande","Edit order"),action:"modifier"},{label:r.getTranslatedText("T\xe9l\xe9charger Bon de co..","Download PO"),action:"telecharger"},{label:r.getTranslatedText("Demande d'annulation","Cancellation request"),action:"annuler"}]}});yield t.present();const{data:k}=yield t.onWillDismiss();k&&r.handlePopoverAction(k)})()}handlePopoverAction(e){const r=this.actionHandlers[e];r&&r()}modifyOrder(){this.canModifyOrder()?this.navigateToOrderUpdate():this.commonSrv.showToast({message:this.translateService.currentLang===c.T.French?"Une erreur est survenue.":"An error occurred.",color:"warning"})}canModifyOrder(){return this.marketService?.order?.status!==O.Re.VALIDATED&&this.commonSrv.user.category!==R.s.Commercial}navigateToOrderUpdate(){const e=this.marketService?.order?._id;this.router.navigate([`order/detail/${e}/order-update`]).then(r=>console.log(r?"Navigation r\xe9ussie":"\xc9chec de la navigation")).catch(r=>console.error("Erreur lors de la navigation",r))}cancelOrder(){this.commonSrv.showToast({message:this.translateService.currentLang===c.T.French?"Une erreur est survenue.":"An error occurred.",color:"warning"})}showModalConfirmValidation(){var e=this;return(0,u.A)(function*(){const r=yield e.modalCtrl.create({component:m.d,cssClass:"modal",initialBreakpoint:.4,breakpoints:[0,.75,.5],mode:"ios",componentProps:{statusToUpdate:O.Re.PAID,order:e.marketService?.order}});yield r.present();const{data:t}=yield r.onWillDismiss();t&&e.updateOrderStatus(t)})()}updateOrderStatus(e){}showModalRejectOrder(){var e=this;return(0,u.A)(function*(){yield(yield e.alertController.create({header:e.getTranslatedText("Rejet de commande","Reject Order"),message:e.getTranslatedText("Vous \xeates sur le point de rejeter cette commande.\n Confirmez vous cette action ?","You are about to reject this order.\n Do you confirm this action ?"),cssClass:"custom-loading",buttons:[{text:e.getTranslatedText("Annuler","Cancel"),cssClass:"alert-button-cancel"},{text:e.getTranslatedText("Rejeter","Reject"),cssClass:"alert-button-confirm",handler:()=>e.rejectOrder()}]})).present()})()}rejectOrder(){return(0,u.A)(function*(){})()}showDetail(e){var r=this;return(0,u.A)(function*(){yield(yield r.modalCtrl.create({component:I.F,cssClass:"modal",initialBreakpoint:.35,breakpoints:[0,.75,.8,.35,.9,.95,1],mode:"ios",componentProps:{item:e,packaging:e?.packaging}})).present()})()}generatePurchaseOrder(e){var r=this;return(0,u.A)(function*(){e?r.isLoading=!0:console.log("ID de commande non disponible")})()}getTranslatedText(e,r){return this.translateService.currentLang===c.T.French?e:r}handleCancellation(){var e=this;return(0,u.A)(function*(){if(!e.order?._id)return void(yield e.commonSrv.showToast({message:e.translateService.currentLang===c.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"}));let r=e.translateService.currentLang===c.T.French?"\xc0 la demande du client":"At the customers request";const t=yield e.modalCtrl.create({component:D.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:"Envoyer",cancelButton:"Annuler",text:"Demmande d annulation de la commande",message:"Renseigner le motif de la demande",isAnnulation:!0,handler:X=>{r=X||r,e.processCancellationRequest(r)}}}});yield t.present();const{role:k}=yield t.onWillDismiss();"confirm"===k&&e.showFinalConfirmation(r)})()}processCancellationRequest(e){var r=this;return(0,u.A)(function*(){r.order?._id||(yield r.commonSrv.showToast({message:r.translateService.currentLang===c.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"}))})()}sendCancellationRequest(e){var r=this;return(0,u.A)(function*(){r.order?._id||(yield r.commonSrv.showToast({message:r.translateService.currentLang===c.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"}))})()}showFinalConfirmation(e){var r=this;return(0,u.A)(function*(){yield(yield r.modalCtrl.create({component:D.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:"Confirmer",cancelButton:"Annuler",text:"\xcates-vous s\xfbr de vouloir envoyer cette demande d'annulation ?",handler:()=>{r.sendCancellationRequest(e)}}}})).present()})()}static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-order-detail"]],decls:80,vars:82,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"color-primary"],["slot","end",4,"ngIf"],["id","container",1,"order-history-page"],[1,"scroller-container","historic-bill-detail-container","containers"],[1,"order-detail"],[1,"h3-title"],[1,"bill-info"],[1,"right-block"],["class","title",4,"ngIf"],[1,"left-block"],[1,"value"],["class","value",4,"ngIf"],[4,"ngIf"],[1,"product-list"],[1,"list-elt","head"],[1,"col"],[1,"flex-dir"],["class","btn--meduim btn--upper","color","primary","expand","block",3,"click",4,"ngIf"],["class","btn--meduim btn--upper","color","danger","expand","block",3,"click",4,"ngIf"],["class","btn-validate",3,"click",4,"ngIf"],["slot","end"],[3,"click"],["name","ellipsis-vertical"],[1,"list-elt-header"],[1,"col","col-desc"],[1,"col","col-qdt","title"],[1,"col","col-price","title"],[1,"col","col-amount","title"],[1,"list-elt-contain"],["class"," list-elt ",4,"ngIf"],[1,"list-elt"],[1,"col-desc-elt"],["src","../../../assets/images/cimencam.png"],[1,"col","col-qdt"],[1,"col","col-price"],[1,"col","col-amount"],[1,"col","product"],[1,"list-elt","line"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"],["color","danger","expand","block",1,"btn--meduim","btn--upper",3,"click"],[1,"btn-validate",3,"click"],["color","primary","expand","block",1,"btn--meduim","btn--upper"],["class","green-btn",4,"ngIf"],[1,"green-btn"]],template:function(r,t){1&r&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return t.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"capitalize"),n.nI1(6,"translate"),n.j41(7,"span",3),n.EFF(8),n.k0s()(),n.DNE(9,M,3,0,"ion-buttons",4),n.k0s()(),n.j41(10,"ion-content")(11,"div",5)(12,"div",6)(13,"div",7)(14,"ion-title",8),n.EFF(15),n.nI1(16,"capitalize"),n.nI1(17,"translate"),n.k0s(),n.j41(18,"div",9)(19,"div",10)(20,"ion-label",2),n.EFF(21),n.nI1(22,"capitalize"),n.nI1(23,"translate"),n.k0s(),n.j41(24,"ion-label",2),n.EFF(25),n.nI1(26,"capitalize"),n.nI1(27,"translate"),n.k0s(),n.j41(28,"ion-label",2),n.EFF(29),n.nI1(30,"capitalize"),n.nI1(31,"translate"),n.k0s(),n.j41(32,"ion-label",2),n.EFF(33),n.nI1(34,"capitalize"),n.nI1(35,"translate"),n.k0s(),n.DNE(36,v,4,5,"ion-label",11),n.DNE(37,f,4,5,"ion-label",11),n.k0s(),n.j41(38,"div",12)(39,"ion-label",13),n.EFF(40),n.k0s(),n.j41(41,"ion-label",13),n.EFF(42),n.nI1(43,"date"),n.j41(44,"span",13),n.EFF(45),n.nI1(46,"capitalize"),n.nI1(47,"translate"),n.nI1(48,"date"),n.k0s()(),n.j41(49,"ion-label",13),n.EFF(50),n.nI1(51,"capitalize"),n.k0s(),n.j41(52,"ion-label",13),n.EFF(53),n.nI1(54,"capitalize"),n.nI1(55,"truncateString"),n.k0s(),n.DNE(56,C,3,4,"ion-label",14),n.DNE(57,p,3,3,"ion-label",14),n.k0s()()(),n.DNE(58,y,14,4,"div",15),n.j41(59,"ul",16)(60,"li",17)(61,"ion-label",18)(62,"strong"),n.EFF(63),n.nI1(64,"translate"),n.k0s()(),n.j41(65,"ion-label",18)(66,"strong"),n.EFF(67),n.nI1(68,"translate"),n.k0s()(),n.j41(69,"ion-label",18)(70,"strong"),n.EFF(71),n.nI1(72,"translate"),n.k0s()()(),n.DNE(73,T,14,10,"div",15),n.k0s(),n.j41(74,"div",19),n.DNE(75,F,3,0,"ion-button",20),n.DNE(76,L,3,0,"ion-button",20),n.DNE(77,V,3,0,"ion-button",21),n.DNE(78,B,3,0,"ion-button",21),n.k0s(),n.DNE(79,Y,3,1,"div",22),n.k0s()()()),2&r&&(n.R7$(4),n.SpI(" ",n.bMT(5,27,n.bMT(6,29,"order.detail.reference"))," "),n.R7$(4),n.SpI(" ",null==t.marketService||null==t.marketService.order?null:t.marketService.order.appReference," "),n.R7$(1),n.Y8G("ngIf",t.commonSrv.user.category!==t.userCategory.Commercial&&t.commonSrv.user.category!==t.userCategory.Particular),n.R7$(6),n.JRh(n.bMT(16,31,n.bMT(17,33,"order.detail.title"))),n.R7$(6),n.SpI("",n.bMT(22,35,n.bMT(23,37,"order.detail.reference"))," : "),n.R7$(4),n.SpI("",n.bMT(26,39,n.bMT(27,41,"order.detail.created-at"))," :"),n.R7$(4),n.SpI("",n.bMT(30,43,n.bMT(31,45,"order.detail.name"))," :"),n.R7$(4),n.SpI("",n.bMT(34,47,n.bMT(35,49,"order.detail.email"))," :"),n.R7$(3),n.Y8G("ngIf",null==t.marketService||null==t.marketService.order||null==t.marketService.order.validation?null:t.marketService.order.validation.date),n.R7$(1),n.Y8G("ngIf",null==t.marketService||null==t.marketService.order?null:t.marketService.order.appReference),n.R7$(3),n.JRh(null==t.marketService||null==t.marketService.order?null:t.marketService.order.appReference),n.R7$(2),n.SpI("",n.i5U(43,51,null==t.marketService||null==t.marketService.order?null:t.marketService.order.created_at,"dd/MM/YY")," \xa0 "),n.R7$(3),n.JRh(n.bMT(46,54,n.bMT(47,56,"preposition.to"))+n.i5U(48,58,null==t.marketService||null==t.marketService.order?null:t.marketService.order.created_at,"HH:mm:ss")),n.R7$(5),n.SpI("",n.bMT(51,61,(null==t.marketService||null==t.marketService.order||null==t.marketService.order.user?null:t.marketService.order.user.firstName)||"")," "),n.R7$(3),n.JRh(n.bMT(54,63,n.i5U(55,65,(null==t.marketService||null==t.marketService.order||null==t.marketService.order.user?null:t.marketService.order.user.email)||"N/A",18))),n.R7$(3),n.Y8G("ngIf",null==t.marketService||null==t.marketService.order||null==t.marketService.order.validation?null:t.marketService.order.validation.date),n.R7$(1),n.Y8G("ngIf",null==t.marketService||null==t.marketService.order?null:t.marketService.order.appReference),n.R7$(1),n.Y8G("ngIf",!1),n.R7$(5),n.JRh(n.bMT(64,68,"order-new-page.third-step.product")),n.R7$(4),n.JRh(n.bMT(68,70,"order-new-page.third-step.qte")),n.R7$(4),n.JRh(n.bMT(72,72,"order-new-page.third-step.unitPrice")),n.R7$(2),n.Y8G("ngIf",null==t.marketService||null==t.marketService.order||null==t.marketService.order.cart?null:t.marketService.order.cart.items),n.R7$(2),n.Y8G("ngIf",!t.isValidate&&n.eq3(74,w,null==t.orderStatus?null:t.orderStatus.CREDIT_IN_VALIDATION).includes(null==t.marketService||null==t.marketService.order?null:t.marketService.order.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.DRH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",!t.isValidate&&n.eq3(76,w,t.orderStatus.CREATED).includes(null==t.marketService||null==t.marketService.order?null:t.marketService.order.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.CORDO_RH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",!t.isReject&&n.eq3(78,w,null==t.orderStatus?null:t.orderStatus.CREDIT_IN_VALIDATION).includes(null==t.marketService||null==t.marketService.order?null:t.marketService.order.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.DRH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",!t.isReject&&n.eq3(80,w,null==t.orderStatus?null:t.orderStatus.CREATED).includes(null==t.marketService||null==t.marketService.order?null:t.marketService.order.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.CORDO_RH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",(null==t.marketService||null==t.marketService.order?null:t.marketService.order.status)===t.orderStatus.CREDIT_IN_AWAIT_VALIDATION&&t.commonSrv.user.category===t.userCategory.Commercial))},dependencies:[d.bT,_.Jm,_.QW,_.W9,_.eU,_.iq,_.KW,_.he,_.BC,_.ai,d.QX,d.vh,o.F,l.c,s.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background:#cfcfcf;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   app-purchase-summary[_ngcontent-%COMP%]{margin:1rem 0}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]{background-color:#e5e5e5;height:100%;text-align:-webkit-center;width:100%;display:flex;align-items:center;align-items:baseline}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]{background:var(--clr-default-400);background:#ffffff;border-radius:1rem;width:100%;padding-bottom:.2rem;margin-top:10px;box-sizing:content-box}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:1rem calc(41 * var(--res));border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{width:95%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .green-btn[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .flex-dir[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:95%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .flex-dir[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:48%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]{width:95%;padding:0 .5em;background:#ffffff;margin-bottom:1rem!important;border-radius:5px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .form--header[_ngcontent-%COMP%]{text-transform:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{box-shadow:0 4px 25px #00000017;border-radius:1px;overflow-x:hidden;overflow-y:auto;max-height:calc(25 * var(--resH))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{padding:5px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]{font-size:14px;display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{height:100%;padding:5px 0;display:flex;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:calc(100% - 300px)}@media only screen and (max-width: 530px){ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:80px}}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%]{width:80px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%]{width:105px;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%]{width:105px;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:ce}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%]{width:25px;height:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{margin-left:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]{height:30px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#000000a6;height:100%;text-align:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{text-align:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,0,0,.24)}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-bottom:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:0 calc(41 * var(--res)) 1rem;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .reason[_ngcontent-%COMP%]{margin-bottom:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:1rem calc(41 * var(--res));border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de;font-size:59%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]{margin-top:calc(50 * var(--res));margin-bottom:calc(50 * var(--res));flex:1}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100%;width:100%;padding:calc(25 * var(--res));display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .mWidth[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(8.3 * var(--resH));width:100px!important}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{padding:0;height:100%;box-shadow:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:space-between;gap:2px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-around}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(29 * var(--res))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   .sac[_ngcontent-%COMP%]{height:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:55%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .form--footer[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{overflow-y:auto;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]{gap:.2em;display:flex;align-items:center;align-items:flex-start;margin:0 calc(41 * var(--res));padding:calc(20 * var(--res)) calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#3c597d;text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap;font-size:calc(35 * var(--res));font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:first-child{width:41%}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:first-child   ion-label[_ngcontent-%COMP%]:first-child{text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap;font-family:Mont SemiBold;font-size:calc(35 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:nth-child(2){width:23%;min-width:27px}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:last-child{flex-grow:1;text-align:end}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]{gap:5px;flex-wrap:wrap;display:flex;align-items:center;justify-content:flex-start}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:last-child{display:flex;align-items:center;font-size:calc(29.1 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:first-child{width:100%;text-align:start}@media only screen and (max-width: 530px){ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]{gap:0}}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .equal[_ngcontent-%COMP%]{width:32%!important}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.line[_ngcontent-%COMP%]{padding:0;margin:0 calc(2 * 41 * var(--res));border-bottom:#CCDEF1 solid 1px}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.head[_ngcontent-%COMP%]{background-color:#f1f8ff;border-radius:calc(12 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.head[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#000}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.footer[_ngcontent-%COMP%]{background-color:#f5f6f6;border-radius:calc(12 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.footer[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#0b305c;font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]{margin-top:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:first-child   ion-text[_ngcontent-%COMP%]:first-child{color:#303950}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:first-child   ion-text[_ngcontent-%COMP%]:last-child{color:#103a5a}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child   ion-text[_ngcontent-%COMP%]{color:#419cfb}"]})}}return i})()}];let W=(()=>{class i{static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[g.iI.forChild(z),g.iI]})}}return i})();var G=a(93887);let U=(()=>{class i{static{this.\u0275fac=function(r){return new(r||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[d.MD,E.YN,_.bv,G.G,s.h,W]})}}return i})()},81559:($,S,a)=>{a.d(S,{Q:()=>R});var d=a(73308),E=a(35025),_=a.n(E),g=a(94934),u=a(56610),m=a(45312),I=a(26409),h=a(2978),D=a(82571),j=a(33607),A=a(14599);let R=(()=>{class O{constructor(c,n,o,l){this.http=c,this.commonSrv=n,this.baseUrlService=o,this.storageSrv=l,this.url=this.baseUrlService.getOrigin()+m.c.basePath}create(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.post(`${n.url}orders`,c))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}createOrderByCommercialForClient(c,n){var o=this;return(0,d.A)(function*(){try{return yield(0,g.s)(o.http.post(`${o.url}orders/${n}`,c))}catch(l){const M={message:o.commonSrv.getError("",l).message,color:"danger"};return yield o.commonSrv.showToast(M),l}})()}getAllOrder(c){var n=this;return(0,d.A)(function*(){try{let o=new I.Nl;const{num:l,commercialId:s,status:M,offset:v,limit:f,startDate:C,endDate:p,customerReference:b,selectedCompanyId:y}=c;return C&&p&&(o=o.append("startDate",new u.vh("fr").transform(C,"YYYY-MM-dd"))),p&&C&&(o=o.append("endDate",new u.vh("fr").transform(p,"YYYY-MM-dd"))),b&&(o=o.append("appReference",b)),y&&(o=o.append("selectedCompanyId",y)),s&&(o=o.append("commercial",s)),void 0!==v&&(o=o.append("offset",v)),f&&(o=o.append("limit",f)),M&&(o=o.append("status",M)),l&&(o=o.append("appReference",l)),yield(0,g.s)(n.http.get(`${n.url}orders/history`,{params:o}))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}getOrders(c){var n=this;return(0,d.A)(function*(){try{let o=new I.Nl;const{status:l,appReference:s,offset:M,limit:v,userCategory:f,paymentMode:C,validation:p,customer:b,product:y,date:T,enable:F=!0}=c;return M&&(o=o.append("offset",M)),v&&(o=o.append("limit",v)),l&&(o=o.append("status",l)),s&&(o=o.append("appReference",`${s}`)),C&&(o=o.append("payment.mode.id",C)),f&&(o=o.append("user.category",f)),b&&(o=o.append("user.email",b)),y&&(o=o.append("cart.items.product.label",y)),p&&(o=o.append("validation",p)),T.start&&T.end&&(o=o.append("startDate",_()(T.start).format("YYYY-MM-DD")),o=o.append("endDate",_()(T.end).format("YYYY-MM-DD"))),o=o.append("enable",F),yield(0,g.s)(n.http.get(`${n.url}orders`,{params:o}))}catch(o){return o}})()}updateOrders(c,n){var o=this;return(0,d.A)(function*(){try{return yield(0,g.s)(o.http.patch(`${o.url}orders/${c}`,n))}catch(l){const M={message:o.commonSrv.getError("",l).message,color:"danger"};return yield o.commonSrv.showToast(M),l}})()}RhValidatedOrder(c,n){var o=this;return(0,d.A)(function*(){try{return yield(0,g.s)(o.http.patch(`${o.url}orders/${c._id}/validate`,n))}catch(l){const M={message:o.commonSrv.getError("",l).message,color:"danger"};return yield o.commonSrv.showToast(M),l}})()}RhRejectOrder(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.patch(`${n.url}orders/${c._id}/reject`,{}))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}sendOtp(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.post(`${n.url}callback/afriland`,c))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}sendWallet(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.post(`${n.url}orders/verify-Wallet-Nber`,c))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}ubaPayment(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.post(`${n.url}orders/m2u-paymentRequest`,c))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}find(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.get(n.url+"orders/"+c))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}getCardToken(){var c=this;return(0,d.A)(function*(){try{return yield(0,g.s)(c.http.post(`${c.url}orders/order-generate-visa-key`,{}))}catch(n){const l={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(l),n}})()}setupPayerAuthentication(c,n){var o=this;return(0,d.A)(function*(){try{return yield(0,g.s)(o.http.post(`${o.url}orders/order-setup-payer-auth`,{transientTokenJwt:c,order:n}))}catch(l){const M={message:o.commonSrv.getError("",l).message,color:"danger"};return yield o.commonSrv.showToast(M),l}})()}authorizationWithPAEnroll(c,n){var o=this;return(0,d.A)(function*(){try{return yield(0,g.s)(o.http.post(`${o.url}orders/order-authorization-pay-enroll`,{order:c,options:n}))}catch(l){const M={message:o.commonSrv.getError("",l).message,color:"danger"};return yield o.commonSrv.showToast(M),l}})()}checkIfOrderExist(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.get(`${n.url}orders/${c}/exist`))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}generatePurchaseOrder(c){var n=this;return(0,d.A)(function*(){try{return yield(0,g.s)(n.http.get(`${n.url}orders/${c}/generate-purchase`))}catch(o){const s={message:n.commonSrv.getError("",o).message,color:"danger"};return yield n.commonSrv.showToast(s),o}})()}cancellationOrder(c,n){var o=this;return(0,d.A)(function*(){try{return yield(0,g.s)(o.http.patch(`${o.url}orders/${c}/cancellation-order`,n))}catch(l){const M={message:o.commonSrv.getError("",l).message,color:"danger"};return yield o.commonSrv.showToast(M),l}})()}updateCarrier(c,n){var o=this;return(0,d.A)(function*(){try{return yield(0,g.s)(o.http.patch(`${o.url}orders/${c}/add-carrier`,{carrier:n}))}catch(l){const M={message:o.commonSrv.getError("",l).message,color:"danger"};return yield o.commonSrv.showToast(M),l}})()}static{this.\u0275fac=function(n){return new(n||O)(h.KVO(I.Qq),h.KVO(D.h),h.KVO(j.K),h.KVO(A.n))}}static{this.\u0275prov=h.jDH({token:O,factory:O.\u0275fac,providedIn:"root"})}}return O})()},94440:($,S,a)=>{a.d(S,{c:()=>E});var d=a(2978);let E=(()=>{class _{transform(u,...m){return u?u.length>m[0]?`${u.substring(0,m[0]-3)}...`:u:""}static{this.\u0275fac=function(m){return new(m||_)}}static{this.\u0275pipe=d.EJ8({name:"truncateString",type:_,pure:!0})}}return _})()}}]);