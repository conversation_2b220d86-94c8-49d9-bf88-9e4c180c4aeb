"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4870],{54870:(f,l,r)=>{r.r(l),r.d(l,{HistoricMenuPageModule:()=>O});var i=r(56610),m=r(37222),c=r(77897),a=r(77575),s=r(58133),n=r(2978),p=r(14599);const u=function(t){return[t]};function M(t,h){if(1&t){const o=n.RV6();n.j41(0,"a",6),n.bIt("click",function(){const d=n.eBV(o).$implicit,_=n.XpG();return n.Njj(_.handleCategory(d))}),n.nrm(1,"ion-img",7),n.j41(2,"ion-label"),n.<PERSON>(3),n.k0s(),n.nrm(4,"ion-icon",8),n.k0s()}if(2&t){const o=h.$implicit;n.Y8G("routerLink",n.eq3(2,u,o.link)),n.R7$(3),n.SpI(" ",o.title," ")}}const C=[{path:"",component:(()=>{class t{constructor(o,e){this.location=o,this.storagService=e,this.clients=[{title:"Clients directs",link:"/navigation/market-place/historic-market-place",category:s.s.CompanyUser},{title:"Clients indirects",link:"/navigation/market-place/historic-market-place",category:s.s.Particular}],this.category=s.s.CompanyUser}ngOnInit(){}handleCategory(o){console.log(o.category),this.storagService.store("category",o.category)}back(){this.location.back()}static{this.\u0275fac=function(e){return new(e||t)(n.rXU(i.aZ),n.rXU(p.n))}}static{this.\u0275cmp=n.VBU({type:t,selectors:[["app-historic-menu"]],decls:8,vars:3,consts:[[3,"translucent"],[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[3,"fullscreen"],[1,"container"],["class","item",3,"routerLink","click",4,"ngFor","ngForOf"],[1,"item",3,"routerLink","click"],["src","/assets/images/man.png"],["slot","end","name","chevron-forward"]],template:function(e,g){1&e&&(n.j41(0,"ion-header",0)(1,"div",1)(2,"ion-img",2),n.bIt("click",function(){return g.back()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4," Historiques des achats "),n.k0s()()(),n.j41(5,"ion-content",3)(6,"div",4),n.DNE(7,M,5,4,"a",5),n.k0s()()),2&e&&(n.Y8G("translucent",!0),n.R7$(5),n.Y8G("fullscreen",!0),n.R7$(2),n.Y8G("ngForOf",g.clients))},dependencies:[i.Sq,c.W9,c.eU,c.iq,c.KW,c.he,c.BC,c.oY,a.Wk],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-button[_ngcontent-%COMP%]{margin:1em 0}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700;margin:1em 0}.header[_ngcontent-%COMP%]{--background: var(--ion-color-secondary-contrast);width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]{--background: var(--ion-color-secondary-contrast)}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:var(--ion-color-secondary-contrast);border-radius:5px;margin:10px 0;box-shadow:0 4px 4px #00000017;width:calc(100% - 48px);padding:1em;display:flex;justify-content:space-between}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-semibold);text-align:center;margin:auto}ion-content[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-weight:700}ion-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{padding:8px;border-radius:50px;border:1px solid var(--clr-primary-750)}ion-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}"]})}}return t})()}];let P=(()=>{class t{static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[a.iI.forChild(C),a.iI]})}}return t})();var b=r(74657);let O=(()=>{class t{static{this.\u0275fac=function(e){return new(e||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[i.MD,m.YN,c.bv,b.h,P]})}}return t})()}}]);