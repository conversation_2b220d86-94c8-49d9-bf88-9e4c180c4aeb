"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[130],{60130:(M,n,l)=>{l.r(n),l.d(n,{OrderProcessModule:()=>a});var s=l(56610),h=l(77575),t=l(2978);const P=[{path:"new",loadChildren:()=>Promise.all([l.e(4608),l.e(4390),l.e(1468),l.e(132),l.e(5083)]).then(l.bind(l,5083)).then(d=>d.NewOrderPageModule)},{path:"order-reseller",loadChildren:()=>Promise.all([l.e(4608),l.e(2076),l.e(7664)]).then(l.bind(l,7664)).then(d=>d.OrderResellerPageModule)},{path:"particular-order",loadChildren:()=>Promise.all([l.e(4390),l.e(1468),l.e(132),l.e(2076),l.e(8699)]).then(l.bind(l,78699)).then(d=>d.ParticularOrdersPageModule)},{path:"products",loadChildren:()=>l.e(7410).then(l.bind(l,7410)).then(d=>d.ProductsPageModule)},{path:"product-detail/:id",loadChildren:()=>l.e(3378).then(l.bind(l,93378)).then(d=>d.ItemDetailPageModule)},{path:"history",loadChildren:()=>Promise.all([l.e(2076),l.e(1605)]).then(l.bind(l,71605)).then(d=>d.HistoryPageModule)},{path:"all-orders",loadChildren:()=>l.e(7086).then(l.bind(l,47086)).then(d=>d.AllOrdersPageModule)},{path:"employees-orders",loadChildren:()=>Promise.all([l.e(2076),l.e(8931)]).then(l.bind(l,98931)).then(d=>d.EmployeesOrdersPageModule)},{path:"commercial",loadChildren:()=>Promise.all([l.e(2076),l.e(5543)]).then(l.bind(l,65543)).then(d=>d.HistoryCommercialPageModule)},{path:"validate-order",loadChildren:()=>Promise.all([l.e(2076),l.e(2904)]).then(l.bind(l,32904)).then(d=>d.ValidateOrderPageModule)},{path:"detail/:idOrder",loadChildren:()=>Promise.all([l.e(4608),l.e(4390),l.e(1468),l.e(132),l.e(7784),l.e(5083),l.e(2076),l.e(8759)]).then(l.bind(l,88759)).then(d=>d.OrderDetailPageModule)},{path:"order-detail",loadChildren:()=>Promise.all([l.e(4608),l.e(4390),l.e(1468),l.e(132),l.e(7784),l.e(5083),l.e(2076),l.e(8759)]).then(l.bind(l,88759)).then(d=>d.OrderDetailPageModule)},{path:"products",loadChildren:()=>l.e(7410).then(l.bind(l,7410)).then(d=>d.ProductsPageModule)},{path:"validate-order",loadChildren:()=>Promise.all([l.e(2076),l.e(2904)]).then(l.bind(l,32904)).then(d=>d.ValidateOrderPageModule)},{path:"product-scan",loadChildren:()=>Promise.all([l.e(2126),l.e(1142),l.e(2076),l.e(2729)]).then(l.bind(l,52729)).then(d=>d.ProductScanPageModule)},{path:"choice-suppliers",loadChildren:()=>Promise.all([l.e(2076),l.e(7572)]).then(l.bind(l,17572)).then(d=>d.ChoiceSuppliersPageModule)},{path:"recap-scan",loadChildren:()=>Promise.all([l.e(2126),l.e(4390),l.e(1142),l.e(8006)]).then(l.bind(l,6239)).then(d=>d.RecapScanPageModule)},{path:"validate-client-order-via-qrcode",loadChildren:()=>Promise.all([l.e(4608),l.e(2076),l.e(1175)]).then(l.bind(l,61175)).then(d=>d.ValidateClientOrderViaQrcodePageModule)},{path:"history-particular",loadChildren:()=>Promise.all([l.e(2076),l.e(6880)]).then(l.bind(l,86880)).then(d=>d.HistoryParticularPageModule)}];let m=(()=>{class d{static{this.\u0275fac=function(o){return new(o||d)}}static{this.\u0275mod=t.$C({type:d})}static{this.\u0275inj=t.G2t({imports:[h.iI.forChild(P),h.iI]})}}return d})(),a=(()=>{class d{static{this.\u0275fac=function(o){return new(o||d)}}static{this.\u0275mod=t.$C({type:d})}static{this.\u0275inj=t.G2t({imports:[s.MD,m]})}}return d})()}}]);