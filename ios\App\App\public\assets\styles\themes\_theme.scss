$colors: (
  primary: (0: rgb(231, 234, 239), // #e7eaef
    10: hsla(210, 20%, 84%, 1), // #ced6de
    20: hsla(213, 20%, 76%, 1),
    100: hsla(213, 20%, 68%, 1),
    200: hsla(213, 20%, 60%, 1),
    300: hsla(212, 20%, 52%, 1),
    400: hsla(213, 25%, 44%, 1), // #546d8c
    500: hsla(213, 35%, 36%, 1), // #3c597c
    600: hsla(212, 51%, 28%, 1), // #23456c
    700: hsla(213, 63%, 24%, 1), // #173964
    750: hsla(213, 79%, 20%, 1),
    800: hsla(213, 63%, 14%, 1), // #0d213a
    900: hsla(213, 79%, 20%),
  ),
  dark: (0: hsla(0, 0%, 0%, 0.05),
    10: hsla(0, 0%, 0%, 0.2),
    100: hsla(0, 0%, 0%, 0.3),
    200: hsla(0, 0%, 0%, 0.4),
    300: hsla(0, 0%, 0%, 0.5),
    400: hsla(0, 0%, 0%, 0.6),
    500: hsla(0, 0%, 0%, 0.7),
    600: hsla(0, 0%, 0%, 0.8),
    700: hsla(0, 0%, 0%, 0.9),
  ),
  secondary: (100: hsl(211, 96%, 92%),
    200: hsl(211, 96%, 82%),
    300: hsl(211, 96%, 72%),
    400: hsl(211, 96%, 62%),
    500: hsl(211, 96%, 52%),
    600: hsl(211, 96%, 42%),
  ),
  tertiary: (0: hsl(0, 10%, 100%),
    50: hsl(0, 20%, 100%),
    100: hsl(0, 30%, 100%),
    200: hsl(0, 10%, 100%),
    300: hsl(0, 0%, 100%),
    400: rgb(217, 217, 217),
    500: hsl(0, 0%, 95%),
    600: hsl(0, 0%, 100%),
  ),
  danger: (0: hsl(10, 100%, 80%),
    50: hsl(10, 100%, 70%),
    100: hsl(10, 100%, 60%),
    200: hsl(10, 100%, 50%),
    300: hsl(0, 98%, 45%),
    400: hsl(10, 88%, 45%),
    500: hsl(0, 0%, 95%),
    600: hsl(0, 0%, 100%),
  ),
  premium: (50: hsl(49, 100%, 90%),
    100: hsl(49, 100%, 80%),
    200: hsl(49, 100%, 70%),
    300: hsl(49, 100%, 60%),
    400: hsl(49, 100%, 50%),
    500: hsl(49, 100%, 40%),
  )
);