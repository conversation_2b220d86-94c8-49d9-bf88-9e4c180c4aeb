"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2729],{52729:(S,p,c)=>{c.r(p),c.d(p,{ProductScanPageModule:()=>tn});var u=c(56610),C=c(37222),i=c(77897),P=c(77575),l=c(73308),n=c(2978),O=c(68896),_=c(45312),x=c(94934),k=c(26409),w=c(33607),m=c(82571);let I=(()=>{class e{constructor(t,o,r){this.http=t,this.baseUrlService=o,this.commonSrv=r,this.url=`${this.baseUrlService.getOrigin()}${_.c.basePath}manual-order`}getManualOrderSetting(){var t=this;return(0,l.A)(function*(){try{return!0===(yield(0,x.s)(t.http.get(t.url)))}catch(o){const a={message:t.commonSrv.getError("",o).message,color:"danger"};return yield t.commonSrv.showToast(a),o}})()}static{this.\u0275fac=function(o){return new(o||e)(n.KVO(k.Qq),n.KVO(w.K),n.KVO(m.h))}}static{this.\u0275prov=n.jDH({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var g=c(99987),f=function(e){return e[e.ACTIVE=100]="ACTIVE",e[e.SCANNED=200]="SCANNED",e[e.USED=300]="USED",e[e.INACTIVE=99]="INACTIVE",e}(f||{}),F=c(23985),M=c(44444),h=c(94761),v=c(22126),R=c(17709),E=c(39316),T=c(14599),j=c(62049),z=c(68953),D=c(58133),$=c(11244);const B=function(e){return{"card-content":e}};let G=(()=>{class e{constructor(t){this.commonSrv=t,this.modalIsOpen=new n.bkB,this.CompanyCategory=z.kJ,this.userCategory=D.s}ngOnInit(){}static{this.\u0275fac=function(o){return new(o||e)(n.rXU(m.h))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-product-cart-qr-orders"]],inputs:{item:"item",isEdit:"isEdit",isCatalogue:"isCatalogue"},outputs:{modalIsOpen:"modalIsOpen"},decls:15,vars:11,consts:[[1,"product-card-container"],[1,"containerize",3,"ngClass"],[1,"product-container"],[1,"image-container"],[3,"src"],[1,"price-container"],[1,"packaging"],[1,"quantity-container"]],template:function(o,r){1&o&&(n.j41(0,"div",0)(1,"ion-card")(2,"div",1)(3,"div",2)(4,"div",3),n.nrm(5,"ion-img",4),n.k0s(),n.j41(6,"div",5)(7,"ion-label"),n.EFF(8),n.nI1(9,"capitalize"),n.k0s(),n.j41(10,"span",6),n.EFF(11),n.nI1(12,"capitalize"),n.k0s()(),n.j41(13,"div",7),n.EFF(14),n.k0s()()()()()),2&o&&(n.R7$(2),n.Y8G("ngClass",n.eq3(9,B,r.isEdit&&(null==r.commonSrv||null==r.commonSrv.user?null:r.commonSrv.user.category)!==r.userCategory.Particular)),n.R7$(3),n.Y8G("src",null==r.item||null==r.item.product?null:r.item.product.image),n.R7$(3),n.SpI(" ",n.bMT(9,5,null==r.item||null==r.item.product?null:r.item.product.label)," "),n.R7$(3),n.SpI(" ",n.bMT(12,7,null==r.item||null==r.item.packaging?null:r.item.packaging.label)," "),n.R7$(3),n.JRh(r.item.quantity))},dependencies:[i.b_,i.KW,i.he,u.YU,$.F],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.product-card-container[_ngcontent-%COMP%]{height:calc(550 * var(--res));width:calc(420 * var(--res));border-radius:1rem}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]{height:100%;background-color:#ebf5ff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;position:relative;height:100%;gap:.5rem}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{display:flex;width:50%;height:50%;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:100%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{height:20px;width:25%;display:flex;justify-content:center;align-self:flex-start}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]{display:flex;width:100%;color:#0b305c;padding-left:calc(25 * var(--res));margin-bottom:10px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-size:calc(35 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]   ion-label.text-content[_ngcontent-%COMP%]{color:#fff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]{width:100%;display:flex;color:#0b305c;text-align:center;margin-bottom:10px;flex-direction:column}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(35 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:10px;color:#6d839d}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]{text-align:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--mont-bold);color:#0b305c}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#6d839d;font-weight:300;margin-bottom:1rem}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{width:70%;color:#fff;border-radius:10px 0 0;background-color:#419cfb;display:flex;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(37 * var(--res));font-family:Mont SemiBold;margin-bottom:10%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-particular[_ngcontent-%COMP%]{width:100%;color:#fff;background-color:#143c5d;display:flex;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-particular[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(37 * var(--res));font-family:Mont SemiBold;margin-bottom:7%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .edit-product[_ngcontent-%COMP%]{background-color:#419cfb!important}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]{width:100%;justify-content:center;background-color:#419cfb}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{width:0%;visibility:hidden}.quantity-container[_ngcontent-%COMP%]{position:absolute;top:0;right:0;background-color:var(--clr-secondary-400);color:var(--clr-tertiary-100);font-family:var(--mont-bold);padding:.5em;border-bottom-left-radius:.5rem}@media only screen and (max-width: 400px){.product-card-container[_ngcontent-%COMP%]{height:calc(450 * var(--res));width:calc(390 * var(--res))}}"]})}}return e})();var N=c(45381),U=c(71333),b=c(74657);function X(e,d){1&e&&n.nrm(0,"app-progress-spinner")}function Q(e,d){if(1&e){const t=n.RV6();n.j41(0,"ion-img",6),n.bIt("click",function(){n.eBV(t);const r=n.XpG(2);return n.Njj(r.openManuelOrder())}),n.k0s()}}function V(e,d){if(1&e){const t=n.RV6();n.j41(0,"ion-header")(1,"ion-toolbar",2)(2,"ion-img",3),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.back())}),n.k0s(),n.j41(3,"ion-title",4),n.EFF(4),n.nI1(5,"translate"),n.k0s(),n.DNE(6,Q,1,0,"ion-img",5),n.k0s()()}if(2&e){const t=n.XpG();n.R7$(4),n.SpI(" ",t.user?"Scanner les produits ":n.bMT(5,2,"qr-orders.title")," "),n.R7$(2),n.Y8G("ngIf",t.manualOrderEnabled&&!t.user)}}function A(e,d){if(1&e&&(n.j41(0,"div",17),n.nrm(1,"app-product-cart-qr-orders",18),n.k0s()),2&e){const t=d.$implicit;n.R7$(1),n.Y8G("item",t)("isEdit",(null==t?null:t.quantity)>0)}}function J(e,d){if(1&e&&(n.j41(0,"div",15),n.DNE(1,A,2,2,"div",16),n.k0s()),2&e){const t=n.XpG(2);n.R7$(1),n.Y8G("ngForOf",null==t.productSrv?null:t.productSrv.currentDataProductScan)("ngForTrackBy",t.trackByFn)}}function L(e,d){if(1&e&&(n.j41(0,"div",24)(1,"p"),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",25)(5,"label",26),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"div",27),n.EFF(9),n.k0s()(),n.j41(10,"div",25)(11,"label",26),n.EFF(12,"Type de client"),n.k0s(),n.j41(13,"div",27),n.EFF(14),n.k0s()(),n.j41(15,"div",25)(16,"label",26),n.EFF(17),n.nI1(18,"translate"),n.k0s(),n.j41(19,"div",27),n.EFF(20),n.k0s()(),n.j41(21,"div",25)(22,"label",26),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.j41(25,"div",27),n.EFF(26),n.k0s()(),n.j41(27,"div",25)(28,"label",26),n.EFF(29),n.nI1(30,"translate"),n.k0s(),n.j41(31,"div",27),n.EFF(32),n.k0s()(),n.j41(33,"div",25)(34,"label",26),n.EFF(35,"Quartier"),n.k0s(),n.j41(36,"div",27),n.EFF(37),n.k0s()()()),2&e){const t=n.XpG(3);n.R7$(2),n.SpI(" ",n.bMT(3,11,"qr-orders.text")," "),n.R7$(4),n.JRh(n.bMT(7,13,"user-info.full-name")),n.R7$(3),n.SpI(" ",null==t.user?null:t.user.firstName,""),n.R7$(5),n.JRh(t.getCategory(null==t.user?null:t.user.categoryType)),n.R7$(3),n.JRh(n.bMT(18,15,"user-info.phone")),n.R7$(3),n.JRh(null==t.user?null:t.user.tel),n.R7$(3),n.JRh(n.bMT(24,17,"user-info.region")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.region),n.R7$(3),n.JRh(n.bMT(30,19,"indirect-clients.ville")),n.R7$(3),n.JRh(null==t.user?null:t.user.address.city),n.R7$(5),n.JRh(null==t.user||null==t.user.address?null:t.user.address.neighborhood)}}function Y(e,d){if(1&e&&(n.j41(0,"div",19),n.DNE(1,L,38,21,"div",20),n.j41(2,"div",21),n.nrm(3,"img",22),n.k0s(),n.j41(4,"p",23),n.EFF(5),n.nI1(6,"translate"),n.k0s()()),2&e){const t=n.XpG(2);n.R7$(1),n.Y8G("ngIf",t.user),n.R7$(4),n.SpI(" ",n.bMT(6,2,"qr-orders.scan-text")," ")}}function W(e,d){if(1&e){const t=n.RV6();n.j41(0,"ion-button",28),n.bIt("click",function(){n.eBV(t);const r=n.XpG(2);return n.Njj(r.nextStep())}),n.j41(1,"ion-label"),n.EFF(2),n.nI1(3,"translate"),n.k0s()()}if(2&e){const t=n.XpG(2);n.Y8G("disabled",!(null!=t.productSrv.currentDataProductScan&&t.productSrv.currentDataProductScan.length)),n.R7$(2),n.SpI(" ",n.bMT(3,2,"order-new-page.second-step.next-button-label")," ")}}function K(e,d){if(1&e){const t=n.RV6();n.j41(0,"section",7),n.DNE(1,J,2,2,"div",8),n.j41(2,"div",9),n.DNE(3,Y,7,4,"div",10),n.j41(4,"div",11)(5,"ion-button",12),n.bIt("click",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.openScan())}),n.nrm(6,"ion-icon",13),n.j41(7,"ion-label"),n.EFF(8),n.nI1(9,"translate"),n.k0s()(),n.DNE(10,W,4,4,"ion-button",14),n.k0s()()()}if(2&e){const t=n.XpG();n.R7$(1),n.Y8G("ngIf",null==t.productSrv||null==t.productSrv.currentDataProductScan?null:t.productSrv.currentDataProductScan.length),n.R7$(2),n.Y8G("ngIf",!(null!=t.productSrv&&null!=t.productSrv.currentDataProductScan&&t.productSrv.currentDataProductScan.length)),n.R7$(5),n.SpI(" ",n.bMT(9,4,"qr-orders.text-button")," "),n.R7$(2),n.Y8G("ngIf",null==t.productSrv.currentDataProductScan?null:t.productSrv.currentDataProductScan.length)}}function q(e,d){1&e&&n.nrm(0,"app-qr-code-scanner")}let y=(()=>{class e{constructor(t,o,r,a,s,en,on,rn){this.router=t,this.location=o,this.qrCodeSrv=r,this.commonSrv=a,this.productSrv=s,this.storageService=en,this.loadingCtrl=on,this.translateService=rn,this.scannerSrv=(0,n.WQX)(O.I),this.platform=(0,n.WQX)(i.OD),this.userSrv=(0,n.WQX)(F.D),this.user=null,this.route=(0,n.WQX)(P.nX),this.manualOrderService=(0,n.WQX)(I)}ngOnInit(){var t=this;return(0,l.A)(function*(){t.route.snapshot.params.id&&t.getUser(t.route.snapshot.params.id),yield t.fetchManualOrderSetting()})()}ionViewWillEnter(){this.commonSrv.showNav=!1}back(){this.location.back()}trackByFn(t,o){return t}showDetail(){this.router.navigate(["/item-detail"])}showScan(){var t=this;return(0,l.A)(function*(){try{if("android"===v.Ii.getPlatform()){const a=yield h.vi.installGoogleBarcodeScannerModule();console.log("res of installGoogleBarcodeScannerModule::::::",a)}else console.log(`Platform is not Android, skipping installation of Google Barcode Scanner module on ${v.Ii.getPlatform()} .`)}catch(a){console.error("Erreur lors de l\u2019installation du module Google Barcode Scanner:",a)}yield t.platform.ready();const{supported:o}=yield h.vi.isSupported();if(!o)return t.commonSrv.showToast({message:t.translateService.currentLang===g.T.French?"Scanner non pris en charge sur cet appareil":"Scanner not supported on this device",color:"warning"});t.scannerSrv.currDisplay=!0;const r=yield t.scannerSrv.startScan();return console.log("result first level::::",r),t.scannerSrv.currDisplay=!1,r})()}openScan(){var t=this;return(0,l.A)(function*(){let o;try{const r=_.c?.dev?'{"code":"a123456789"}':yield t.showScan();if(!r)return void(yield t.commonSrv.showToast({color:"danger",message:t.translateService.currentLang===g.T.French?"Aucun r\xe9sultat de scan":"No scan result"}));let a;try{a=JSON.parse(r)}catch{return void(yield t.commonSrv.showToast({color:"danger",message:t.translateService.currentLang===g.T.French?"QR code invalide":"Invalid QR code"}))}if(!a?.code)return void(yield t.commonSrv.showToast({color:"danger",message:t.translateService.currentLang===g.T.French?"Code absent du QR code":"Code is missing from the QR code"}));if(o=yield t.loadingCtrl.create({message:t.translateService.currentLang===g.T.French?`V\xe9rification du scan...\nCode: ${a?.code}`:`Checking scan...\nCode: ${a?.code}`}),yield o.present(),yield t.verifyScan(a))return;const s=yield t.qrCodeSrv.getQrCodeDataAnUpDateStateToScanned({code:a?.code});if(console.log(`==========QR Code data: ${JSON.stringify(s)}=================`),s instanceof Error||!s?.code)return console.log(`==========QR Code not found or error: ${JSON.stringify(s)}=================`),void(yield t.commonSrv.showToast({message:t.translateService.currentLang===g.T.French?"QR Code non actif ou invalide":"QR Code is inactive or invalid",color:"danger"}));if(s?.status===f.USED)return console.log(`==========QR Code already used: ${JSON.stringify(s)}=================`),void(yield t.commonSrv.showToast({message:t.translateService.currentLang===g.T.French?"QR Code d\xe9j\xe0 utilis\xe9":"QR Code is already used",color:"danger"}));yield t.addProduct(s,a)}catch(r){return console.error("Error in openScan:",r),yield t.commonSrv.showToast({message:t.translateService.currentLang===g.T.French?"Une erreur est survenue lors du scan":"An error occurred during scan",color:"danger"}),r}finally{o&&(yield o.dismiss())}})()}verifyScan(t){var o=this;return(0,l.A)(function*(){return-1!==o.productSrv.dataQrCode.findIndex(a=>a.code===t?.code)?(console.log(`==========QR Code already scanned: ${t?.code}=================`),yield o.commonSrv.showToast({message:o.translateService.currentLang===g.T.French?"QR Code d\xe9j\xe0 scann\xe9":"QR Code is already scanned",color:"danger"}),!0):(o.productSrv.dataQrCode.push(t),!1)})()}addProduct(t,o){var r=this;return(0,l.A)(function*(){const a=r.productSrv.currentDataProductScan.findIndex(s=>s?.product?._id===t?.product?._id&&s?.packaging?._id===t?.packaging?._id);-1!==a?r.productSrv.currentDataProductScan[a].quantity=(r.productSrv.currentDataProductScan[a].quantity||1)+1:r.productSrv.currentDataProductScan.unshift({...t,code:o?.code,quantity:1})})()}nextStep(){const t=this.productSrv.currentDataProductScan.filter(o=>o.quantity&&o.quantity>0);this.storageService.store("items",JSON.stringify(t)),this.storageService.store("qrCodeData",JSON.stringify(this.productSrv.dataQrCode)),this.router.navigate(["/order/choice-suppliers"])}openManuelOrder(){this.router.navigate(["/order/particular-order/first-step"])}fetchManualOrderSetting(){var t=this;return(0,l.A)(function*(){try{const o=yield t.manualOrderService.getManualOrderSetting();t.manualOrderEnabled=o}catch(o){console.error("Erreur lors de la r\xe9cup\xe9ration de la configuration :",o)}})()}getUser(t){var o=this;return(0,l.A)(function*(){o.user=yield o.userSrv.find(t),o.qrCodeSrv.currenUser=o.user})()}getCategory(t){switch(t){case M.iL.BHB:return"BHB";case M.iL.BS:return"BS";case M.iL.BPI:return"BPI";default:return"Unknown Category"}}static{this.\u0275fac=function(o){return new(o||e)(n.rXU(P.Ix),n.rXU(u.aZ),n.rXU(R.Q),n.rXU(m.h),n.rXU(E.b),n.rXU(T.n),n.rXU(i.Xi),n.rXU(j.E))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-product-scan"]],decls:4,vars:4,consts:[[4,"ngIf"],["id","container","class","scroller-container",4,"ngIf"],[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/touch.svg",3,"click",4,"ngIf"],["slot","end","src","/assets/icons/touch.svg",3,"click"],["id","container",1,"scroller-container"],["class","products",4,"ngIf"],[1,"scan-container"],["class","scan-content",4,"ngIf"],[1,"bottom-buttons"],["expand","block","color","secondary",1,"btn--meduim","btn--upper","bg-secondary-400",3,"click"],["name","scan-outline","slot","start"],["class","btn--meduim btn--upper","color","primary","expand","block",3,"disabled","click",4,"ngIf"],[1,"products"],["class","item",4,"ngFor","ngForOf","ngForTrackBy"],[1,"item"],[1,"elt",3,"item","isEdit"],[1,"scan-content"],["class","user-info-container",4,"ngIf"],[1,"qr-placeholder"],["src","assets/images/qr-scanner.png"],[1,"scan-text"],[1,"user-info-container"],[1,"user-info-item"],[1,"user-info-label"],[1,"user-info-value"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled","click"]],template:function(o,r){1&o&&(n.DNE(0,X,1,0,"app-progress-spinner",0),n.DNE(1,V,7,4,"ion-header",0),n.DNE(2,K,11,6,"section",1),n.DNE(3,q,1,0,"app-qr-code-scanner",0)),2&o&&(n.Y8G("ngIf",r.isLoading),n.R7$(1),n.Y8G("ngIf",!(null!=r.scannerSrv&&r.scannerSrv.currDisplay)),n.R7$(1),n.Y8G("ngIf",!(null!=r.scannerSrv&&r.scannerSrv.currDisplay)),n.R7$(1),n.Y8G("ngIf",null==r.scannerSrv?null:r.scannerSrv.currDisplay))},dependencies:[u.Sq,u.bT,i.Jm,i.eU,i.iq,i.KW,i.he,i.BC,i.ai,G,N.k,U._,b.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:calc(41 * var(--res));--border-color: transparent;--background: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;margin-bottom:0!important;color:var(--clr-primary-700);font-family:var(--mont-regular)}.ion-fab[_ngcontent-%COMP%]{bottom:14%!important}ion-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{--background: var(--clr-secondary-0);width:calc(16 * var(--resW));height:calc(16 * var(--resW))}ion-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{rotate:90deg}ion-content[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]{margin-bottom:calc(20 * var(--resW));gap:.75rem}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;padding-top:0;height:100%;background-color:transparent;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));overflow-x:hidden;overflow-y:auto}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));height:max-content;flex-direction:row;justify-content:flex-start;overflow:hidden;display:flex;flex-wrap:wrap;gap:30px;justify-content:center;width:100%}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 24px;flex:1}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]   .qr-placeholder[_ngcontent-%COMP%]{width:120px;height:120px;margin-bottom:24px;opacity:.5}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]   .qr-placeholder[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .scan-content[_ngcontent-%COMP%]   .scan-text[_ngcontent-%COMP%]{text-align:center;color:#666;font-size:16px;line-height:1.4;max-width:280px;margin:0 auto}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .bottom-buttons[_ngcontent-%COMP%]{padding:16px;display:flex;flex-direction:column;background:#fff;position:fixed;bottom:0;gap:.5em;left:0;right:0}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .bottom-buttons[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%]{--color: #666;--border-color: #ddd;margin:0}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]{--background: #f3f4f6}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #fff}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;align-items:center;gap:5px;margin-bottom:20px}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{text-align:center;color:#e70a1e;font-size:14px;line-height:1.4;max-width:280px;margin:0 auto}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;border-bottom:.6px solid var(--clr-primary-700);border-radius:3px;width:83%;font-weight:400;height:2em;padding:16px}#container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], #container[_ngcontent-%COMP%]   .scan-container[_ngcontent-%COMP%]   .user-info-container[_ngcontent-%COMP%]   .user-info-item[_ngcontent-%COMP%]   .user-info-value[_ngcontent-%COMP%]{font-weight:600;color:#0b305c;font-size:15px}.title[_ngcontent-%COMP%]{text-align:center;color:#143c5d;margin-bottom:1em}.title[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:22px;font-weight:700}.header[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}.header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont Bold;font-weight:700;font-size:calc(45 * var(--res));margin-bottom:13%}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));position:fixed;bottom:2%;width:calc(100% - 10 * var(--res))}app-qr-code-scanner[_ngcontent-%COMP%]{height:100%!important}"]})}}return e})();const H=[{path:"",component:y},{path:"user-scan/:id",component:y}];let Z=(()=>{class e{static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[P.iI.forChild(H),P.iI]})}}return e})();var nn=c(93887);let tn=(()=>{class e{static{this.\u0275fac=function(o){return new(o||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[u.MD,C.YN,i.bv,nn.G,b.h,Z]})}}return e})()},11244:(S,p,c)=>{c.d(p,{F:()=>C});var u=c(2978);let C=(()=>{class i{transform(l){return console.log(),`${l?.slice(0,1)?.toLocaleUpperCase()+l?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275pipe=u.EJ8({name:"capitalize",type:i,pure:!0})}}return i})()}}]);