"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7664,1095],{74916:(j,E,a)=>{a.d(E,{B:()=>M});var m=a(44912),d=a(9867),l=a(96111);function M(u,T=m.E){return(0,d.N)((f,t)=>{let P=null,y=null,F=null;const k=()=>{if(P){P.unsubscribe(),P=null;const v=y;y=null,t.next(v)}};function C(){const v=F+u,p=T.now();if(p<v)return P=this.schedule(void 0,v-p),void t.add(P);k()}f.subscribe((0,l._)(t,v=>{y=v,F=T.now(),P||(P=T.schedule(C,u),t.add(P))},()=>{k(),t.complete()},void 0,()=>{y=P=null}))})}},61095:(j,E,a)=>{a.d(E,{l:()=>v});var m=a(73308),d=a(94934),l=a(56610),M=a(45312),u=a(99987),T=a(26409),f=a(37222),t=a(73014),P=a(2978),y=a(82571),F=a(77897),k=a(33607),C=a(62049);let v=(()=>{class p{constructor(n,s,g,h,w){this.http=n,this.commonSrv=s,this.modalCtrl=g,this.baseUrlService=h,this.translateService=w,this.schedules=[],this.removals=[],this.remainingQtyTonne=0,this.remainingQtyTonneInStock=0,this.dailyCeiling=300,this.maxRemovalLine=10,this.minCapacityToRemove=.1,this.datePipe=new l.vh("fr"),this.scheduleForm=new f.gE({quantity:new f.MJ(0,[f.k0.required,f.k0.min(.1)]),tonneQuantity:new f.MJ(0,[f.k0.required,f.k0.min(.025)]),collection_date:new f.MJ("",[f.k0.required])}),this.url=this.baseUrlService.getOrigin()+M.c.basePath}editSchedule(){var n=this;return(0,m.A)(function*(){if(!n.remainingQtyTonne)return n.commonSrv.showToast({color:"warning",message:n.translateService.currentLang===u.T.French?"Renseignez au pr\xe9alable les quantit\xe9s":"Fill in the quantities as required"});const s=n.scheduleForm.get("quantity").value;return Number.isInteger(s)?void 0:(n.commonSrv.showToast({color:"warning",message:n.translateService.currentLang===u.T.French?"Veuillez renseigner une valeur de sac enti\xe8re":"Please enter a integer value of sac"}),void n.scheduleForm.patchValue({quantity:null,tonneQuantity:null}))})()}getScheduleDates(){const n=new Date,s=new Date(this.removalObject?.end)>n?new Date(new Date(this.removalObject?.end).setDate(new Date(this.removalObject?.end).getDate()+1)):this.removalObject?.start?new Date(this.removalObject?.start):new Date,g=this.remainingQtyTonneInStock?new Date(this.removalObject?.start)>n?new Date(this.removalObject?.start):new Date:s,h=new Date;return{minDate:g,maxDate:this.remainingQtyTonneInStock?new Date(this.removalObject.end):new Date(h.setMonth(h?.getMonth()+3))}}closeForm(){this.modalCtrl.dismiss()}getTotalTonnage(){let s=this.schedules.map(g=>g.nbrTruck?g?.nbrTonnage*g?.nbrTruck:+g.nbrTonnage).reduce((g,h)=>g+h,0);return Math.round(1e3*s)/1e3}getRemovals(){this?.cartItem?.quantity>0?this.removals?.filter((n,s)=>n.itemId===this.cartItem?.product?._id?(this.schedules=n.schedules,this.schedulesTypeSelected=n.removalType,this.currentRemoval=n,this.removals.splice(s,1)):this.removals):this.schedules=[]}verifyDailyCeiling(n,s){if(n=new l.vh("fr").transform(n,"dd/MM/yy"),s>this.dailyCeiling)return this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===u.T.French?"Vous avez exc\xe9d\xe9 votre capacit\xe9 de tonnage journali\xe8re qui est de 300 tonnes":"You have exceeded your daily tonnage capacity which is 300 tonnes"}),!0;let g=0;return this.schedules.forEach(h=>{new l.vh("fr").transform(h.removalDate,"dd/MM/yy")===n&&(g+=parseInt(`${this.schedulesTypeSelected===t.JN.perDate?h.nbrTonnage:h.nbrTonnage*h.nbrTruck}`))}),this.dailyCeiling<g+parseInt(`${s}`)&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===u.T.French?"Vous avez exc\xe9d\xe9 votre capacit\xe9 de tonnage pour ce jour qui est de 300 tonnes, s\xe9lectionnez un autre jour.":"You have exceeded your tonnage capacity for that day which is 300 tonnes, select another day."}),!0)}saveSchedule(n){if(this?.schedules?.length>=this?.maxRemovalLine)return this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===u.T.French?`Vous ne pouvez pas pas planifier plus de ${this?.maxRemovalLine} lignes d'enl\xe8vements`:`You have exceeded your daily tonnage capacity ${this?.maxRemovalLine} removal lines`}),new Error("maxRemovalLine");this.schedules.push(n),this.remainingQtyTonne-=this.schedulesTypeSelected===t.JN.perDate?n?.nbrTonnage:parseFloat((n?.nbrTonnage*n?.nbrTruck).toFixed(3)),this.scheduleForm.get("collection_date").reset(),this.commonSrv.showToast({icon:"checkmark-circle-outline",color:"success",message:this.translateService.currentLang===u.T.French?"Vous avez ajout\xe9 une ligne \xe0 votre planification":"You have added a line for your planning"}),0===this.remainingQtyTonne&&(this.isGoodPlannify=!0),this.remainingQtyTonne=Math.round(1e3*this.remainingQtyTonne)/1e3,this.closeForm()}getPlanificationByStores(n){var s=this;return(0,m.A)(function*(){try{let g=new T.Nl;const{storeId:h,today:w}=n;return h&&(g=g.append("storeId",h)),w&&(g=g.append("today",w)),yield(0,d.s)(s.http.get(`${s.url}planifications/custom`,{params:g}))}catch(g){const w={message:s.commonSrv.getError("",g).message,color:"danger"};return yield s.commonSrv.showToast(w),g}})()}getRemovalsObject(){var n=this;return(0,m.A)(function*(){try{const s={storeId:n.cart.store._id,today:(new Date).valueOf()};if(n.availableRemovals=yield n.getPlanificationByStores(s),!(n.availableRemovals instanceof Array)){const h=n.commonSrv.getError("Une erreur s'est produite",n.availableRemovals);throw new Error(JSON.stringify({title:h?.data,message:h?.message}))}const g=n.availableRemovals?.find(h=>h?.data?.product?._id===n.cartItem?.product?._id);if(!g)throw new Error(JSON.stringify({title:n.translateService.currentLang===u.T.French?"Indisponible":"Unavailable",message:n.translateService.currentLang===u.T.French?"Aucune planification en production n'est disponible pour ce produit. ":"No production planning is available for this product."}));return n.removalObject=g.data}catch(s){return s}})()}verifyCanRemoval(n){return!!(this.remainingQtyTonneInStock&&n>this.remainingQtyTonneInStock)&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===u.T.French?`Vous avez exc\xe9d\xe9 la quantit\xe9 en stock (${this.remainingQtyTonneInStock} tonnes)`:`You have exceeded the stock quantityLeft (${this.remainingQtyTonneInStock} tonnes)`}),!0)}getRemainingQuantityStock(){const n=this.removalObject?.clientShare?.find(g=>g?.company?._id===this.commonSrv?.user?.company?._id);if(n?.quantity)return this.remainingQtyTonneInStock=n?.quantity??null;const s=this.removalObject?.categoryShare?.find(g=>g?.category===this.commonSrv?.user?.company?.category);return this.remainingQtyTonneInStock=s?.quantity?s?.quantity:this.removalObject?.quantityLeft??null}static{this.\u0275fac=function(s){return new(s||p)(P.KVO(T.Qq),P.KVO(y.h),P.KVO(F.W3),P.KVO(k.K),P.KVO(C.E))}}static{this.\u0275prov=P.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})}}return p})()},7664:(j,E,a)=>{a.r(E),a.d(E,{OrderResellerPageModule:()=>ut});var m=a(56610),d=a(37222),l=a(77897),M=a(77575),u=a(73308),T=a(28653),f=a(58133),t=a(2978),P=a(14599),y=a(82571),F=a(95908),k=a(94934),C=a(45312),v=a(26409);let p=(()=>{class r{constructor(e,i){this.http=e,this.commonSrv=i,this.url=C.c.apiUrl,this.basepath=C.c.basePath}getResller(){var e=this;return(0,u.A)(function*(){e.isLoading=!0;try{return yield(0,k.s)(e.http.get(`${e.url}${e.basepath}companies?category=102&projection=name,address`))}catch(i){const _={message:e.commonSrv.getError("",i)?.message,color:"danger"};return yield e.commonSrv.showToast(_),i}})()}create(e){var i=this;return(0,u.A)(function*(){try{return yield(0,k.s)(i.http.post(`${i.url}${i.basepath}order-retaill`,e))}catch(o){const O={message:i.commonSrv.getError("",o).message,color:"danger"};return yield i.commonSrv.showToast(O),o}})()}static{this.\u0275fac=function(i){return new(i||r)(t.KVO(v.Qq),t.KVO(y.h))}}static{this.\u0275prov=t.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();var c=a(71333),n=a(74657);function s(r,b){1&r&&t.nrm(0,"app-progress-spinner")}function g(r,b){if(1&r&&(t.j41(0,"ion-select-option",12),t.EFF(1),t.k0s()),2&r){const e=b.$implicit;t.Y8G("value",e),t.R7$(1),t.SpI(" ",e,"")}}function h(r,b){if(1&r&&(t.j41(0,"ion-select-option",12),t.EFF(1),t.k0s()),2&r){const e=b.$implicit;t.Y8G("value",e),t.R7$(1),t.SpI(" ",e,"")}}function w(r,b){if(1&r&&(t.j41(0,"ion-select-option",12),t.EFF(1),t.k0s()),2&r){const e=b.$implicit;t.Y8G("value",e),t.R7$(1),t.SpI(" ",null==e?null:e.name," ")}}function I(r,b){if(1&r&&(t.j41(0,"ion-select-option",12),t.EFF(1),t.k0s()),2&r){const e=b.$implicit;t.Y8G("value",e),t.R7$(1),t.SpI(" ",null==e?null:e.label,"")}}let R=(()=>{class r{constructor(e,i,o,_,O){this.router=e,this.storageSrv=i,this.commonService=o,this.packagingSrv=_,this.resellerService=O,this.isLoading=!0,this.resellerFormPasserBy=new d.gE({regions:new d.MJ("",[d.k0.required]),city:new d.MJ("",[d.k0.required]),distributors:new d.MJ("",[d.k0.required]),packagings:new d.MJ("",[d.k0.required])}),this.orderFormCompany=new d.gE({shipPoint:new d.MJ("",[d.k0.required]),packaging:new d.MJ("",[d.k0.required]),address:new d.MJ("",[d.k0.required])}),this.storageSrv.getUserConnected(),this.viewpackagin=this.commonService.user?.authorizations.includes(T.L.VIEW)}ngOnInit(){var e=this;return(0,u.A)(function*(){e.getFormType(),e.regions=e.commonService.getRegions();const i=yield e.resellerService.getResller();e.distributors=i?.data,e.packagings=(yield e.packagingSrv.getPackagings())?.data})()}initData(){this.resellerForm.patchValue({distributors:null}),this.resellerForm.patchValue({packagings:null})}populateForm(){const e=this.storageSrv.load("resellerForm");if(e){const i=JSON.parse(e);this.resellerForm.patchValue(i)}}getRegion(e){this.cities=this.commonService.getCities(e.detail.value),this.initData()}ionViewWillEnter(){var e=this;return(0,u.A)(function*(){e.isLoading=!1})()}getFormType(){this.resellerForm=this.commonService?.user?.category===f.s.CompanyUser?this.orderFormCompany:this.resellerFormPasserBy}nextStep(){let e={...this.resellerForm.value};this.user=JSON.parse(this.storageSrv.load("USER_INFO")),this.cart={packaging:e.packagings,adresse:{region:e.regions,city:e.city},user:this.user,distributors:e.distributors,items:[]},this.storageSrv.store("resellerForm",JSON.stringify(this.resellerForm.value)),this.storageSrv.store("cart",JSON.stringify(this.cart)),this.router.navigate(["/order/order-reseller/second-step"])}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(M.Ix),t.rXU(P.n),t.rXU(y.h),t.rXU(F.L),t.rXU(p))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-first-step"]],decls:40,vars:34,consts:[[4,"ngIf"],["id","container",1,"scroller-container"],[1,"input-group",3,"formGroup"],["position","floating"],["r","","mode","ios","formControlName","regions","interface","action-sheet",3,"cancelText","ionChange"],["stores",""],[3,"value",4,"ngFor","ngForOf"],["mode","ios","formControlName","city","interface","action-sheet",3,"cancelText"],["mode","ios","formControlName","distributors","interface","action-sheet",3,"cancelText"],["mode","ios","formControlName","packagings","interface","action-sheet",3,"cancelText"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled","click"],[3,"value"]],template:function(i,o){1&i&&(t.DNE(0,s,1,0,"app-progress-spinner",0),t.j41(1,"section",1)(2,"form",2)(3,"ion-item")(4,"ion-label",3),t.EFF(5),t.nI1(6,"translate"),t.k0s(),t.j41(7,"ion-select",4,5),t.bIt("ionChange",function(O){return o.getRegion(O)}),t.nI1(9,"translate"),t.DNE(10,g,2,2,"ion-select-option",6),t.k0s()(),t.j41(11,"ion-item")(12,"ion-label",3),t.EFF(13),t.nI1(14,"translate"),t.k0s(),t.j41(15,"ion-select",7,5),t.nI1(17,"translate"),t.DNE(18,h,2,2,"ion-select-option",6),t.k0s()(),t.j41(19,"ion-item")(20,"ion-label",3),t.EFF(21),t.nI1(22,"translate"),t.k0s(),t.j41(23,"ion-select",8,5),t.nI1(25,"translate"),t.DNE(26,w,2,2,"ion-select-option",6),t.k0s()(),t.j41(27,"ion-item")(28,"ion-label",3),t.EFF(29),t.nI1(30,"translate"),t.k0s(),t.j41(31,"ion-select",9,5),t.nI1(33,"translate"),t.DNE(34,I,2,2,"ion-select-option",6),t.k0s()()(),t.j41(35,"div",10)(36,"ion-button",11),t.bIt("click",function(){return o.nextStep()}),t.j41(37,"ion-label"),t.EFF(38),t.nI1(39,"translate"),t.k0s()()()()),2&i&&(t.Y8G("ngIf",o.isLoading),t.R7$(2),t.Y8G("formGroup",o.resellerForm),t.R7$(3),t.SpI(" ",t.bMT(6,16,"reseller-new-page.first-step.select-region-label")," "),t.R7$(2),t.FS9("cancelText",t.bMT(9,18,"button.cancel")),t.R7$(3),t.Y8G("ngForOf",o.regions),t.R7$(3),t.SpI(" ",t.bMT(14,20,"reseller-new-page.first-step.select-city-label")," "),t.R7$(2),t.FS9("cancelText",t.bMT(17,22,"button.cancel")),t.R7$(3),t.Y8G("ngForOf",o.cities),t.R7$(3),t.SpI(" ",t.bMT(22,24,"reseller-new-page.first-step.select-distributors-label")," "),t.R7$(2),t.FS9("cancelText",t.bMT(25,26,"button.cancel")),t.R7$(3),t.Y8G("ngForOf",o.distributors),t.R7$(3),t.SpI(" ",t.bMT(30,28,"reseller-new-page.first-step.select-packaging-label")," "),t.R7$(2),t.FS9("cancelText",t.bMT(33,30,"button.cancel")),t.R7$(3),t.Y8G("ngForOf",o.packagings),t.R7$(2),t.Y8G("disabled",o.resellerForm.invalid),t.R7$(2),t.SpI(" ",t.bMT(39,32,"reseller-new-page.first-step.next-button-label")," "))},dependencies:[m.Sq,m.bT,d.qT,d.BC,d.cb,l.Jm,l.uz,l.he,l.Nm,l.Ip,l.Je,c._,d.j4,d.JD,n.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:calc(75 * var(--res)) 0;background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{height:calc(55 * var(--resH));overflow-x:hidden;overflow-y:auto;padding:0 calc(41 * var(--res));margin-top:0;flex:1}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .tonnage-employee[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(26 * var(--res))}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .tonnage-employee[_ngcontent-%COMP%]   .primary-text[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:calc(10.4 * var(--resH));margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-width:0px 0px .55px 0px;border-style:solid}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .btn-groups[_ngcontent-%COMP%]{display:flex}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .btn-groups[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{margin-bottom:0;--border-color: transparent}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .btn-groups[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-left:5px;width:16px;height:16px}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .toggle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item-group[_ngcontent-%COMP%]   .toggle[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:Mont Light}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px}"]})}}return r})();var D=a(51049),S=a(99987),$=a(74916),L=a(73793),U=a(38503),G=a(93527),B=a(62049);function K(r,b){1&r&&t.nrm(0,"ion-checkbox",13),2&r&&t.Y8G("checked",!0)("disabled",!0)}function N(r,b){if(1&r&&(t.j41(0,"div",14)(1,"ion-label"),t.EFF(2),t.k0s()()),2&r){const e=t.XpG().index;t.R7$(2),t.JRh(e+1)}}function Y(r,b){if(1&r&&(t.j41(0,"div",10),t.DNE(1,K,1,2,"ion-checkbox",11),t.DNE(2,N,3,1,"div",12),t.k0s()),2&r){const e=b.$implicit;t.R7$(1),t.Y8G("ngIf",1===e),t.R7$(1),t.Y8G("ngIf",0===e)}}function V(r,b){if(1&r&&(t.j41(0,"ion-label",15),t.EFF(1),t.k0s()),2&r){const e=t.XpG();t.R7$(1),t.SpI(" ",e.action," ")}}function W(r,b){1&r&&(t.j41(0,"ion-button",16)(1,"ion-label"),t.EFF(2),t.nI1(3,"translate"),t.k0s()()),2&r&&(t.R7$(2),t.SpI(" ",t.bMT(3,1,"order-new-page.information-label")," "))}let J=(()=>{class r{constructor(e,i,o,_){this.router=e,this.location=i,this.translateService=o,this.route=_,this.steps=[0,0,0],this.action="Nouvelle commande",this.stepTitle="",this.lastRoute="",this.formatter=O=>O.label+(O._id?" ( "+O.description+" )":""),this.search=O=>O.pipe((0,$.B)(200),(0,L.F)(),(0,U.p)(x=>x.length>=2),(0,G.T)(x=>this.product.filter(A=>new RegExp(x,"mi").test(A?.label+" "+A?.description)).slice(0,10)))}ngOnInit(){}onOutletLoaded(e){this.route.params.subscribe(i=>{this.action=this.translateService?.currentLang===S.T.French?"Nouvelle commande":"New order";let o={"/order/order-reseller/first-step":{step:0,url:"/navigation/home",title:this.translateService?.currentLang===S.T.French?"Nouvelle commande":"New Order",label:this.translateService?.currentLang===S.T.French?"Informations g\xe9n\xe9rales":"general information",description:"",steps:[0,0,0]},"/order/order-reseller/second-step":{step:1,title:this.translateService?.currentLang===S.T.French?"S\xe9lection des produits":"Product selection",label:this.translateService?.currentLang===S.T.French?"Rechercher":"search",url:"first-step",description:this.translateService?.currentLang===S.T.French?"S\xe9lectionner un produit pour l\u2019ajouter au panier ":"Select a product to add to cart",steps:[1,0,0]},"/order/order-reseller/third-step":{step:2,title:this.translateService?.currentLang===S.T.French?"R\xe9capitulatif":"Summary",url:"first-step",label:this.translateService?.currentLang===S.T.French?"Informations de votre commande":"Information of your order",description:"",steps:[1,1,0]},"/order/order-reseller/foor-step":{step:3,title:this.translateService?.currentLang===S.T.French?"Op\xe9ration r\xe9ussie":"Successful Operation",url:"third-step",label:"",description:"",steps:[1,1,1]}};this.stepTitle=o[this.router.url].title,this.steps=o[this.router.url].steps,this.action=o[this.router.url].label,this.lastRoute=o[this.router.url].url})}back(){if("/order/order-reseller/four-step"===this.router.url)return this.router.navigate(["navigation/home"]);this.location.back()}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(M.Ix),t.rXU(m.aZ),t.rXU(B.E),t.rXU(M.nX))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-order-reseller"]],decls:13,vars:5,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"hidden","click"],[1,"title"],[1,"step-container"],[1,"step-content"],["class","step-item",4,"ngFor","ngForOf"],[1,"space-h-v"],["class","subtitle",4,"ngIf"],["class","btn btn--meduim","color","primary","expand","block",4,"ngIf"],[3,"activate"],[1,"step-item"],["mode","ios",3,"checked","disabled",4,"ngIf"],["class","checkbox",4,"ngIf"],["mode","ios",3,"checked","disabled"],[1,"checkbox"],[1,"subtitle"],["color","primary","expand","block",1,"btn","btn--meduim"]],template:function(i,o){1&i&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),t.bIt("click",function(){return o.back()}),t.k0s(),t.j41(3,"ion-title",2),t.EFF(4),t.k0s()(),t.j41(5,"ion-toolbar",3)(6,"div",4),t.DNE(7,Y,3,2,"div",5),t.k0s()(),t.j41(8,"div",6),t.DNE(9,V,2,1,"ion-label",7),t.DNE(10,W,4,3,"ion-button",8),t.k0s()(),t.j41(11,"ion-content")(12,"ion-router-outlet",9),t.bIt("activate",function(O){return o.onOutletLoaded(O)}),t.k0s()()),2&i&&(t.R7$(2),t.Y8G("hidden",1==o.steps[2]),t.R7$(2),t.JRh(o.stepTitle),t.R7$(3),t.Y8G("ngForOf",o.steps),t.R7$(2),t.Y8G("ngIf","search"!=o.action&&"Rechercher"!=o.action&&"Informations de votre commande"!==o.action&&"Information of your order"!==o.action),t.R7$(1),t.Y8G("ngIf","Informations de votre commande"===o.action||"Information of your order"===o.action))},dependencies:[m.Sq,m.bT,l.Jm,l.eY,l.W9,l.eU,l.KW,l.he,l.BC,l.ai,l.hB,l.Rg,n.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{background:#eef2f9}ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#000;font-family:Mont Light}ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-size:calc(45 * var(--res))}ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{margin:calc(41 * var(--res)) 0;padding:0 calc(41 * var(--res))}ion-header[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}ion-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{text-transform:initial}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--border-color: transparent;padding:calc(41 * var(--res)) calc(41 * var(--res)) 0 calc(41 * var(--res));--background: white}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]{padding-top:0;border-bottom-left-radius:calc(50 * var(--res));border-bottom-right-radius:calc(50 * var(--res))}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;padding-top:calc(41 * var(--res));padding-bottom:calc(41 * var(--res));width:50%;margin-left:auto;margin-right:auto}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{opacity:1!important;font-family:Mont Bold;--background-checked: transparent;--checkmark-color: var(--ion-color-primary)}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%], ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{width:1.4em;height:1.4em}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{background:#ffffff;border:1px solid #ebebeb;box-shadow:0 2.99018px 18.6886px #00000016;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#bebcbc}ion-header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}ion-content[_ngcontent-%COMP%]{--background: #eef2f9}"]})}}return r})(),X=(()=>{class r{constructor(e,i,o,_){this.modalCtrl=e,this.storageSrv=i,this.commonSrv=o,this.translateService=_,this.quantityForm=new d.gE({qtyKg:new d.MJ("",[d.k0.required,d.k0.min(0)]),qtyTonne:new d.MJ("",[d.k0.required,d.k0.min(0)])})}ngOnInit(){}ionViewWillEnter(){this.cart=JSON.parse(this.storageSrv.load("cart")),this.quantityForm.get("qtyKg").patchValue(this.currentCartElt?.quantity??null),this.quantityForm.get("qtyTonne").patchValue(this.currentCartElt?.quantity/this.cart?.packaging?.unit?.ratioToTone)}ConvertKgtoTone(e){this.quantityForm.get("qtyKg").value<0&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===S.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),this.quantityForm.patchValue({qtykg:null}));let i=e.detail.value;this.quantityForm.get("qtyTonne").patchValue(i/this.cart.packaging?.unit?.ratioToTone)}ConvertTonnetoKg(e){this.quantityForm.get("qtyTonne").value<0&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===S.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),this.quantityForm.patchValue({qtyTonne:null}));let i=e.detail.value;this.quantityForm.get("qtyKg").patchValue(i*this.cart.packaging?.unit?.ratioToTone)}cancel(){return this.modalCtrl.dismiss(null,"annuler")}confirm(){this.modalCtrl.dismiss(this.quantityForm.get("qtyKg").value,"valider"),this.quantityForm.patchValue({qtykg:null})}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(l.W3),t.rXU(P.n),t.rXU(y.h),t.rXU(B.E))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-modal-quantity"]],inputs:{currentCartElt:"currentCartElt"},decls:25,vars:18,consts:[["id","container",1,"scroller-container"],[1,"tilte-quantite"],[1,"input-group",3,"formGroup"],["position","floating"],["placeholder","Enter number","type","number","formControlName","qtyKg","placeholder","0",3,"ionChange"],["placeholder","Enter number","type","number","formControlName","qtyTonne","placeholder","0",3,"ionChange"],[1,"btn-validate"],["fill","solid","color","secondary",3,"click"],["fill","solid","color","primary",3,"disabled","click"]],template:function(i,o){1&i&&(t.j41(0,"section",0)(1,"div",1)(2,"ion-title"),t.EFF(3),t.nI1(4,"translate"),t.k0s()(),t.j41(5,"form",2)(6,"ion-item")(7,"ion-label",3),t.EFF(8),t.nI1(9,"translate"),t.k0s(),t.j41(10,"ion-input",4),t.bIt("ionChange",function(O){return o.ConvertKgtoTone(O)}),t.k0s()(),t.j41(11,"ion-item")(12,"ion-label",3),t.EFF(13),t.nI1(14,"translate"),t.k0s(),t.j41(15,"ion-input",5),t.bIt("ionChange",function(O){return o.ConvertTonnetoKg(O)}),t.k0s()()(),t.j41(16,"div",6)(17,"ion-button",7),t.bIt("click",function(){return o.cancel()}),t.j41(18,"ion-label"),t.EFF(19),t.nI1(20,"translate"),t.k0s()(),t.j41(21,"ion-button",8),t.bIt("click",function(){return o.confirm()}),t.j41(22,"ion-label"),t.EFF(23),t.nI1(24,"translate"),t.k0s()()()()),2&i&&(t.R7$(3),t.SpI(" ",t.bMT(4,8,"reseller-new-page.first-step.enter-qdty")," "),t.R7$(2),t.Y8G("formGroup",o.quantityForm),t.R7$(3),t.Lme(" ",t.bMT(9,10,"reseller-new-page.first-step.qdty-bag")," ",null==o.cart||null==o.cart.packaging?null:o.cart.packaging.label," "),t.R7$(5),t.SpI("",t.bMT(14,12,"reseller-new-page.first-step.qdty-tons")," "),t.R7$(6),t.SpI(" ",t.bMT(20,14,"reseller-new-page.first-step.cancel")," "),t.R7$(2),t.Y8G("disabled",o.quantityForm.get("qtyKg").value<1),t.R7$(2),t.SpI(" ",t.bMT(24,16,"reseller-new-page.first-step.valid")," "))},dependencies:[d.qT,d.BC,d.cb,l.Jm,l.$w,l.uz,l.he,l.BC,l.su,d.j4,d.JD,n.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:calc(75 * var(--res)) 0;background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .tilte-quantite[_ngcontent-%COMP%]{margin-bottom:1em;padding-left:.5em}#container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{height:calc(55 * var(--resH));overflow-x:hidden;overflow-y:auto;padding:0 calc(41 * var(--res));margin-top:0;flex:1}#container[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:space-around}"]})}}return r})();var Q=a(39316),H=a(61095);function q(r,b){1&r&&t.nrm(0,"app-progress-spinner")}function Z(r,b){if(1&r&&(t.j41(0,"div"),t.EFF(1),t.nI1(2,"translate"),t.k0s()),2&r){const e=t.XpG().$implicit,i=t.XpG();let o;t.R7$(1),t.Lme(" Quantity: ",null==(o=i.verifyProductInCart(e))?null:o.quantity," ",t.bMT(2,2,"reseller-new-page.detail.bags")," ")}}function tt(r,b){if(1&r){const e=t.RV6();t.j41(0,"div",20)(1,"div",21),t.bIt("click",function(){t.eBV(e);const o=t.XpG().$implicit,_=t.XpG();return t.Njj(_.addQuantity(o))}),t.nrm(2,"ion-icon",22),t.k0s(),t.j41(3,"div",23),t.bIt("click",function(){t.eBV(e);const o=t.XpG().$implicit,_=t.XpG();return t.Njj(_.presentAlert(o))}),t.nrm(4,"ion-icon",24),t.k0s()()}}function nt(r,b){if(1&r){const e=t.RV6();t.j41(0,"ion-card",11)(1,"ion-card-content",12)(2,"div",13)(3,"div",14),t.bIt("click",function(){const _=t.eBV(e).$implicit,O=t.XpG();return t.Njj(O.addQuantity(_))}),t.j41(4,"div",15),t.nrm(5,"ion-img",16),t.k0s(),t.j41(6,"div",17)(7,"ion-label",18),t.EFF(8),t.k0s(),t.DNE(9,Z,3,4,"div",0),t.k0s()(),t.DNE(10,tt,5,0,"div",19),t.k0s()()()}if(2&r){const e=b.$implicit,i=t.XpG();let o,_;t.R7$(5),t.Y8G("src",null==e?null:e.image),t.R7$(3),t.Lme(" ",null==e?null:e.label," "," "+(null==e?null:e.normLabel)," "),t.R7$(1),t.Y8G("ngIf",null==(o=i.verifyProductInCart(e))?null:o.quantity),t.R7$(1),t.Y8G("ngIf",null==(_=i.verifyProductInCart(e))?null:_.product)}}let et=(()=>{class r{constructor(e,i,o,_,O,x){this.router=e,this.productSrv=i,this.modalCtrl=o,this.scheduleSrv=_,this.storageService=O,this.alertController=x,this.nbrOfProductSelect=0}ngOnInit(){var e=this;return(0,u.A)(function*(){yield e.getProducts()})()}getProducts(){var e=this;return(0,u.A)(function*(){e.isLoading=!0,e.cart=JSON.parse(e.storageService.load("cart")),e.products=(yield e.productSrv.getProducts())?.data,e.allCartItems=[...e.products],e.isLoading=!1})()}delete(e){const i=this.cart?.items.findIndex(o=>e._id===o.product._id);this.cart?.items?.splice(i,1)}presentAlert(e){var i=this;return(0,u.A)(function*(){const o=yield i.alertController.create({header:"Suppression de produit",mode:"ios",message:"Vous \xeates sur le point de supprimer ce produit de votre panier. \xeates vous sure de cela ? ",buttons:[{text:"Annuler",role:"annuler",handler:()=>{}},{text:"Supprimer",role:"supprimer",handler:()=>{i.delete(e)}}]});yield o.present(),yield o.onDidDismiss()})()}addQuantity(e){var i=this;return(0,u.A)(function*(){const o=i.cart?.items?.find(x=>x?.product?._id===e?._id),_=yield i.modalCtrl.create({component:X,cssClass:"modalClass",componentProps:{currentCartElt:o}});yield _.present();const{data:O}=yield _.onWillDismiss();if(O){if(o){o.quantity=O;const x=i.cart?.items.findIndex(A=>e._id===A.product._id);i.cart?.items.splice(x,1,o),i.storageService.store("cart",JSON.stringify(i.cart))}else i.cart.items.push({product:e,quantity:O});i.storageService.store("cart",JSON.stringify(i.cart))}})()}verifyProductInCart(e){return this.cart?.items.find(i=>e?._id===i?.product._id)}nextStep(){this.router.navigate(["/order/order-reseller/third-step"])}handleInput(e){const i=`${e.target.value}`.toLowerCase();return this.products=this.allCartItems.filter(o=>o?.label?.toLowerCase().includes(i))?.sort((o,_)=>o.toString().toLowerCase().indexOf(i.toLowerCase())-_.toString().toLowerCase().indexOf(i.toLowerCase()))}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(M.Ix),t.rXU(Q.b),t.rXU(l.W3),t.rXU(H.l),t.rXU(P.n),t.rXU(l.hG))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-second-step"]],decls:17,vars:14,consts:[[4,"ngIf"],[1,"part-search"],[1,"space-h-v"],[1,"searchbar",3,"placeholder","autocomplete","ionInput"],["id","container",1,"scroller-container"],[1,"header"],[1,"title"],[1,"products"],["class","item",4,"ngFor","ngForOf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"readonly","disabled","click"],[1,"item"],[1,"item-block"],[1,"item-content"],[1,"product",3,"click"],[1,"image"],[3,"src"],[1,"detai"],["color","primary",1,"title"],["class","icon",4,"ngIf"],[1,"icon"],[1,"btn","btn--round","btn--border-primary",3,"click"],["src","/assets/icons/edit-green.svg"],[1,"btn","btn--round","btn--border-secondary",3,"click"],["src","/assets/icons/trash-danger.svg"]],template:function(i,o){1&i&&(t.DNE(0,q,1,0,"app-progress-spinner",0),t.j41(1,"div",1)(2,"div",2)(3,"ion-searchbar",3),t.bIt("ionInput",function(O){return o.handleInput(O)}),t.nI1(4,"translate"),t.k0s()()(),t.j41(5,"section",4)(6,"div",5)(7,"ion-label",6),t.EFF(8),t.nI1(9,"translate"),t.k0s()(),t.j41(10,"div",7),t.DNE(11,nt,11,5,"ion-card",8),t.k0s(),t.j41(12,"div",9)(13,"ion-button",10),t.bIt("click",function(){return o.nextStep()}),t.j41(14,"ion-label"),t.EFF(15),t.nI1(16,"translate"),t.k0s()()()()),2&i&&(t.Y8G("ngIf",o.isLoading),t.R7$(3),t.FS9("placeholder",t.bMT(4,8,"order-new-page.searchbar-label")),t.Y8G("autocomplete",!0),t.R7$(5),t.SpI(" ",t.bMT(9,10,"reseller-new-page.second-step.title")," "),t.R7$(3),t.Y8G("ngForOf",o.products),t.R7$(2),t.Y8G("readonly",(null==o.cart||null==o.cart.items?null:o.cart.items.length)<=0)("disabled",(null==o.cart||null==o.cart.items?null:o.cart.items.length)<=0),t.R7$(2),t.SpI(" ",t.bMT(16,12,"reseller-new-page.second-step.next-button-label")," "))},dependencies:[m.Sq,m.bT,l.Jm,l.b_,l.I9,l.iq,l.KW,l.he,l.S1,l.Gw,c._,n.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;height:100%;background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));overflow-x:hidden;overflow-y:auto}#container[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{margin-bottom:5px}#container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));padding-bottom:calc(25 * var(--res))}#container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res))}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));height:85%;overflow:scroll;overflow-x:hidden}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:#ffffff;border:.79375px solid #ebebeb;border-radius:calc(25 * var(--res));margin-bottom:calc(31.25 * var(--res));box-shadow:0 2.37346px 14.8341px #00000016}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]{position:relative}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res));width:100%}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;width:20%;align-items:center}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:calc(41 * var(--res));height:calc(41 * var(--res))}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:calc(75 * var(--res));background:transparent;display:flex;align-items:center;justify-content:center;height:calc(75 * var(--res));border-radius:50%}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:first-child{margin-bottom:calc(15 * var(--res))}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   .btn--border-primary[_ngcontent-%COMP%]{border-color:var(--ion-color-primary);border-style:solid;border-width:2px}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   .btn--border-secondary[_ngcontent-%COMP%]{border-width:2px;border-color:var(--ion-color-secondary);border-style:solid}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .product[_ngcontent-%COMP%]{display:flex;align-items:center;width:85%}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .product[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{width:18%;margin-right:1em}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .product[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:auto}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .product[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{margin-left:calc(50 * var(--res));width:80%}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .product[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{line-height:initial;text-transform:uppercase;font-family:Mont Bold;font-size:calc(40 * var(--res))}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .product[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont Light;color:#000;font-size:calc(36 * var(--res))}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .product[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   .disable[_ngcontent-%COMP%]{line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont Regular;color:#aeaeae;font-size:calc(36 * var(--res))}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .disable-block[_ngcontent-%COMP%]{height:100%;width:100%;position:absolute;background:gray;opacity:.5;top:0}#container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}#container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(25 * var(--res));position:relative;width:calc(100% - 10 * var(--res))}.part-search[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{margin-bottom:calc(41 * var(--res));padding:0 calc(41 * var(--res))}.part-search[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}.my-modal-class[_ngcontent-%COMP%]   .modal-wrapper[_ngcontent-%COMP%]{--height: 90%;position:absolute;top:5%;--width: 90%;border-radius:25px;display:block}"]})}}return r})();var ot=a(81559),rt=a(94440);function it(r,b){1&r&&t.nrm(0,"ion-progress-bar",15)}function at(r,b){if(1&r&&(t.j41(0,"ul",16)(1,"li")(2,"ion-text",5),t.EFF(3),t.nI1(4,"translate"),t.k0s(),t.j41(5,"ion-text",17),t.EFF(6),t.k0s()(),t.j41(7,"li")(8,"ion-text",5),t.EFF(9),t.nI1(10,"translate"),t.k0s(),t.j41(11,"ion-text",17),t.EFF(12),t.k0s()(),t.j41(13,"li")(14,"ion-text",5),t.EFF(15),t.nI1(16,"translate"),t.k0s(),t.j41(17,"ion-text",17),t.EFF(18),t.k0s()(),t.j41(19,"li")(20,"ion-text",5),t.EFF(21),t.nI1(22,"translate"),t.k0s(),t.j41(23,"ion-text",17),t.EFF(24),t.k0s()()()),2&r){const e=t.XpG();t.R7$(3),t.SpI("",t.bMT(4,8,"reseller-new-page.third-step.region")," "),t.R7$(3),t.SpI(" ",null==e.cart||null==e.cart.adresse?null:e.cart.adresse.region,""),t.R7$(3),t.SpI("",t.bMT(10,10,"reseller-new-page.third-step.city")," "),t.R7$(3),t.JRh(null==e.cart||null==e.cart.adresse?null:e.cart.adresse.city),t.R7$(3),t.SpI("",t.bMT(16,12,"reseller-new-page.third-step.distributors")," "),t.R7$(3),t.JRh(null==e.cart||null==e.cart.distributors?null:e.cart.distributors.name),t.R7$(3),t.SpI("",t.bMT(22,14,"reseller-new-page.third-step.packaging")," "),t.R7$(3),t.SpI(" ",null==e.cart||null==e.cart.packaging?null:e.cart.packaging.label,"")}}function ct(r,b){1&r&&(t.j41(0,"ion-text",18),t.EFF(1),t.nI1(2,"translate"),t.k0s()),2&r&&(t.R7$(1),t.SpI(" ",t.bMT(2,1,"order-new-page.third-step.swipe-to-show-element")," "))}function st(r,b){if(1&r&&(t.j41(0,"ion-slide")(1,"ion-card")(2,"ion-card-content"),t.nrm(3,"ion-img",19),t.j41(4,"div",20)(5,"ion-label"),t.EFF(6),t.nI1(7,"truncateString"),t.k0s(),t.j41(8,"ion-label"),t.EFF(9),t.nI1(10,"number"),t.k0s()()()()()),2&r){const e=b.$implicit,i=t.XpG();t.R7$(3),t.Y8G("src",null==e||null==e.product?null:e.product.image),t.R7$(3),t.JRh(t.i5U(7,3,null==e||null==e.product?null:e.product.label,9)),t.R7$(3),t.SpI(" ",t.brH(10,6,(null==e?null:e.quantity)/(null==i.cart||null==i.cart.packaging||null==i.cart.packaging.unit?null:i.cart.packaging.unit.ratioToTone),"","fr")," T")}}function lt(r,b){1&r&&t.nrm(0,"ion-spinner",21)}const gt=[{path:"history",loadChildren:()=>a.e(713).then(a.bind(a,20713)).then(r=>r.HistoryPageModule)},{path:"order-detail",loadChildren:()=>Promise.all([a.e(3037),a.e(2126),a.e(194),a.e(2076),a.e(6580)]).then(a.bind(a,16580)).then(r=>r.OrderDetailPageModule)},{path:"order-distrb-detail",loadChildren:()=>Promise.all([a.e(3037),a.e(194),a.e(8593)]).then(a.bind(a,78593)).then(r=>r.OrderDistrbDetailPageModule)},{path:"",component:J,children:[{path:"first-step",component:R},{path:"second-step",component:et},{path:"third-step",component:(()=>{class r{constructor(e,i,o,_,O,x,A){this.router=e,this.orderservice=i,this.storageService=o,this.modalCtrl=_,this.resellerService=O,this.commonSrv=x,this.translateService=A,this.isLoading=!1,this.appForm=new d.gE({text:new d.MJ("",[d.k0.required])}),this.slideProductOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:10}}ngOnInit(){this.cart=JSON.parse(this.storageService.load("cart"))}confirmOrder(){var e=this;return(0,u.A)(function*(){e.isLoading=!0;try{let i={...e.appForm.value};e.cart=JSON.parse(e.storageService.load("cart")),e.cart.custumerRef=i.text;const o=yield e.resellerService.create(e.cart);e.commonSrv.showToast({color:o.status<400?"success":"danger",message:o?.message}),201===o.status&&e.router.navigate(["order/order-reseller/foor-step"])}catch{e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===S.T.French?"Echec de cr\xe9ation de la commande":"Failed to create the order"})}finally{e.isLoading=!1}})()}static{this.\u0275fac=function(i){return new(i||r)(t.rXU(M.Ix),t.rXU(ot.Q),t.rXU(P.n),t.rXU(l.W3),t.rXU(p),t.rXU(y.h),t.rXU(B.E))}}static{this.\u0275cmp=t.VBU({type:r,selectors:[["app-third-step"]],decls:26,vars:21,consts:[["type","indeterminate",4,"ngIf"],["id","container",1,"scroll-container"],[1,"padding-horizontal"],[1,"title","recap-title"],["class","detail-order padding-horizontal",4,"ngIf"],[1,"title"],["color","primary","class","subtitle",4,"ngIf"],[1,"padding-horizontal","slide-container","product-container",3,"options"],[4,"ngFor","ngForOf"],[1,"btn-validate"],[1,"input-group",3,"formGroup"],["position","floating"],["type","text","placeholder","Reference de la commande","formControlName","text",3,"autoGrow"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled","click"],["name","bubbles",4,"ngIf"],["type","indeterminate"],[1,"detail-order","padding-horizontal"],[1,"fMeduim"],["color","primary",1,"subtitle"],[1,"mWidth",3,"src"],[1,"detail"],["name","bubbles"]],template:function(i,o){1&i&&(t.DNE(0,it,1,0,"ion-progress-bar",0),t.j41(1,"section",1)(2,"div",2)(3,"ion-label",3),t.EFF(4),t.nI1(5,"translate"),t.k0s()(),t.DNE(6,at,25,16,"ul",4),t.j41(7,"div",2)(8,"ion-label",5),t.EFF(9),t.nI1(10,"translate"),t.k0s(),t.DNE(11,ct,3,3,"ion-text",6),t.k0s(),t.j41(12,"ion-slides",7),t.DNE(13,st,11,10,"ion-slide",8),t.k0s(),t.j41(14,"div",9)(15,"form",10)(16,"ion-item")(17,"ion-label",11),t.EFF(18),t.nI1(19,"translate"),t.k0s(),t.nrm(20,"ion-input",12),t.k0s()(),t.j41(21,"ion-button",13),t.bIt("click",function(){return o.confirmOrder()}),t.j41(22,"ion-label"),t.EFF(23),t.nI1(24,"translate"),t.k0s(),t.DNE(25,lt,1,0,"ion-spinner",14),t.k0s()()()),2&i&&(t.Y8G("ngIf",o.isLoading),t.R7$(4),t.JRh(t.bMT(5,13,"reseller-new-page.third-step.title")),t.R7$(2),t.Y8G("ngIf",(null==o.cart?null:o.cart.items.length)>0),t.R7$(3),t.SpI(" ",t.bMT(10,15,"reseller-new-page.third-step.product-order")," "),t.R7$(2),t.Y8G("ngIf",(null==o.cart?null:o.cart.items.length)>4),t.R7$(1),t.Y8G("options",o.slideProductOpts),t.R7$(1),t.Y8G("ngForOf",null==o.cart?null:o.cart.items),t.R7$(2),t.Y8G("formGroup",o.appForm),t.R7$(3),t.SpI(" ",t.bMT(19,17,"reseller-new-page.third-step.label"),""),t.R7$(2),t.Y8G("autoGrow",!0),t.R7$(1),t.Y8G("disabled",o.isLoading),t.R7$(2),t.SpI(" ",t.bMT(24,19,"reseller-new-page.third-step.confirm the order")," "),t.R7$(2),t.Y8G("ngIf",o.isLoading))},dependencies:[m.Sq,m.bT,d.qT,d.BC,d.cb,l.Jm,l.b_,l.I9,l.KW,l.$w,l.uz,l.he,l.FH,l.q3,l.tR,l.w2,l.IO,l.Gw,d.j4,d.JD,m.QX,rt.c,n.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;height:100%;background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));overflow-x:hidden;overflow-y:auto;align-items:center}#container[_ngcontent-%COMP%]   .fextrabold[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(35 * var(--res))}#container[_ngcontent-%COMP%]   .fMeduim[_ngcontent-%COMP%]{font-family:Mont Light}#container[_ngcontent-%COMP%]   .recap-title[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));margin-bottom:1rem}.padding-horizontal[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.padding-horizontal[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(40 * var(--res))}ul[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));list-style-type:none}ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding-bottom:calc(25 * var(--res))}.title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res))}.product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(8.3 * var(--resH))}.product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{padding:0;height:100%;box-shadow:none}.product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:space-between;gap:2px}.product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{align-items:flex-start;width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-around}.product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(29 * var(--res))}.product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:55%}.product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000}.btn-validate[_ngcontent-%COMP%]{width:calc(100% - 100 * var(--res));margin-left:1rem;text-align:center}"]})}}return r})()},{path:"foor-step",component:D.I},{path:"",redirectTo:"first-step",pathMatch:"full"},{path:"**",redirectTo:"first-step"}]},{path:"**",redirectTo:""}];let dt=(()=>{class r{static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({imports:[M.iI.forChild(gt),M.iI]})}}return r})();var z=a(93887),pt=a(24608);let ut=(()=>{class r{static{this.\u0275fac=function(i){return new(i||r)}}static{this.\u0275mod=t.$C({type:r})}static{this.\u0275inj=t.G2t({imports:[m.MD,d.YN,l.bv,z.G,dt,l.bv,m.MD,pt.vj,z.G,n.h,d.X1]})}}return r})()},81559:(j,E,a)=>{a.d(E,{Q:()=>k});var m=a(73308),d=a(35025),l=a.n(d),M=a(94934),u=a(56610),T=a(45312),f=a(26409),t=a(2978),P=a(82571),y=a(33607),F=a(14599);let k=(()=>{class C{constructor(p,c,n,s){this.http=p,this.commonSrv=c,this.baseUrlService=n,this.storageSrv=s,this.url=this.baseUrlService.getOrigin()+T.c.basePath}create(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.post(`${c.url}orders`,p))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}createOrderByCommercialForClient(p,c){var n=this;return(0,m.A)(function*(){try{return yield(0,M.s)(n.http.post(`${n.url}orders/${c}`,p))}catch(s){const h={message:n.commonSrv.getError("",s).message,color:"danger"};return yield n.commonSrv.showToast(h),s}})()}getAllOrder(p){var c=this;return(0,m.A)(function*(){try{let n=new f.Nl;const{num:s,commercialId:g,status:h,offset:w,limit:I,startDate:R,endDate:D,customerReference:S,selectedCompanyId:$}=p;return R&&D&&(n=n.append("startDate",new u.vh("fr").transform(R,"YYYY-MM-dd"))),D&&R&&(n=n.append("endDate",new u.vh("fr").transform(D,"YYYY-MM-dd"))),S&&(n=n.append("appReference",S)),$&&(n=n.append("selectedCompanyId",$)),g&&(n=n.append("commercial",g)),void 0!==w&&(n=n.append("offset",w)),I&&(n=n.append("limit",I)),h&&(n=n.append("status",h)),s&&(n=n.append("appReference",s)),yield(0,M.s)(c.http.get(`${c.url}orders/history`,{params:n}))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}getOrders(p){var c=this;return(0,m.A)(function*(){try{let n=new f.Nl;const{status:s,appReference:g,offset:h,limit:w,userCategory:I,paymentMode:R,validation:D,customer:S,product:$,date:L,enable:U=!0}=p;return h&&(n=n.append("offset",h)),w&&(n=n.append("limit",w)),s&&(n=n.append("status",s)),g&&(n=n.append("appReference",`${g}`)),R&&(n=n.append("payment.mode.id",R)),I&&(n=n.append("user.category",I)),S&&(n=n.append("user.email",S)),$&&(n=n.append("cart.items.product.label",$)),D&&(n=n.append("validation",D)),L.start&&L.end&&(n=n.append("startDate",l()(L.start).format("YYYY-MM-DD")),n=n.append("endDate",l()(L.end).format("YYYY-MM-DD"))),n=n.append("enable",U),yield(0,M.s)(c.http.get(`${c.url}orders`,{params:n}))}catch(n){return n}})()}updateOrders(p,c){var n=this;return(0,m.A)(function*(){try{return yield(0,M.s)(n.http.patch(`${n.url}orders/${p}`,c))}catch(s){const h={message:n.commonSrv.getError("",s).message,color:"danger"};return yield n.commonSrv.showToast(h),s}})()}RhValidatedOrder(p,c){var n=this;return(0,m.A)(function*(){try{return yield(0,M.s)(n.http.patch(`${n.url}orders/${p._id}/validate`,c))}catch(s){const h={message:n.commonSrv.getError("",s).message,color:"danger"};return yield n.commonSrv.showToast(h),s}})()}RhRejectOrder(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.patch(`${c.url}orders/${p._id}/reject`,{}))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}sendOtp(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.post(`${c.url}callback/afriland`,p))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}sendWallet(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.post(`${c.url}orders/verify-Wallet-Nber`,p))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}ubaPayment(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.post(`${c.url}orders/m2u-paymentRequest`,p))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}find(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.get(c.url+"orders/"+p))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}getCardToken(){var p=this;return(0,m.A)(function*(){try{return yield(0,M.s)(p.http.post(`${p.url}orders/order-generate-visa-key`,{}))}catch(c){const s={message:p.commonSrv.getError("",c).message,color:"danger"};return yield p.commonSrv.showToast(s),c}})()}setupPayerAuthentication(p,c){var n=this;return(0,m.A)(function*(){try{return yield(0,M.s)(n.http.post(`${n.url}orders/order-setup-payer-auth`,{transientTokenJwt:p,order:c}))}catch(s){const h={message:n.commonSrv.getError("",s).message,color:"danger"};return yield n.commonSrv.showToast(h),s}})()}authorizationWithPAEnroll(p,c){var n=this;return(0,m.A)(function*(){try{return yield(0,M.s)(n.http.post(`${n.url}orders/order-authorization-pay-enroll`,{order:p,options:c}))}catch(s){const h={message:n.commonSrv.getError("",s).message,color:"danger"};return yield n.commonSrv.showToast(h),s}})()}checkIfOrderExist(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.get(`${c.url}orders/${p}/exist`))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}generatePurchaseOrder(p){var c=this;return(0,m.A)(function*(){try{return yield(0,M.s)(c.http.get(`${c.url}orders/${p}/generate-purchase`))}catch(n){const g={message:c.commonSrv.getError("",n).message,color:"danger"};return yield c.commonSrv.showToast(g),n}})()}cancellationOrder(p,c){var n=this;return(0,m.A)(function*(){try{return yield(0,M.s)(n.http.patch(`${n.url}orders/${p}/cancellation-order`,c))}catch(s){const h={message:n.commonSrv.getError("",s).message,color:"danger"};return yield n.commonSrv.showToast(h),s}})()}updateCarrier(p,c){var n=this;return(0,m.A)(function*(){try{return yield(0,M.s)(n.http.patch(`${n.url}orders/${p}/add-carrier`,{carrier:c}))}catch(s){const h={message:n.commonSrv.getError("",s).message,color:"danger"};return yield n.commonSrv.showToast(h),s}})()}static{this.\u0275fac=function(c){return new(c||C)(t.KVO(f.Qq),t.KVO(P.h),t.KVO(y.K),t.KVO(F.n))}}static{this.\u0275prov=t.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})}}return C})()},39316:(j,E,a)=>{a.d(E,{b:()=>P});var m=a(73308),d=a(26409),l=a(94934),M=a(45312),u=a(2978),T=a(82571),f=a(33607),t=a(77897);let P=(()=>{class y{constructor(k,C,v,p){this.http=k,this.commonSrv=C,this.baseUrlService=v,this.toastController=p,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+M.c.basePath+"products"}getProducts(k){var C=this;return(0,m.A)(function*(){try{let v=new d.Nl;return k?.limit&&(v=v.append("limit",k?.limit)),yield(0,l.s)(C.http.get(C.url,{params:v}))}catch(v){const c={message:C.commonSrv.getError("",v).message,color:"danger"};return yield C.commonSrv.showToast(c),v}})()}getProduct(k){var C=this;return(0,m.A)(function*(){try{return yield(0,l.s)(C.http.get(`${C.url}/${k}`))}catch(v){const c={message:C.commonSrv.getError("",v).message,color:"danger"};return yield C.commonSrv.showToast(c),v}})()}static{this.\u0275fac=function(C){return new(C||y)(u.KVO(d.Qq),u.KVO(T.h),u.KVO(f.K),u.KVO(t.K_))}}static{this.\u0275prov=u.jDH({token:y,factory:y.\u0275fac,providedIn:"root"})}}return y})()},73014:(j,E,a)=>{a.d(E,{J7:()=>m,JN:()=>d,iB:()=>M});class m{}var d=function(u){return u[u.PerQuantity=1]="PerQuantity",u[u.perDate=2]="perDate",u}(d||{});class M{}},94440:(j,E,a)=>{a.d(E,{c:()=>d});var m=a(2978);let d=(()=>{class l{transform(u,...T){return u?u.length>T[0]?`${u.substring(0,T[0]-3)}...`:u:""}static{this.\u0275fac=function(T){return new(T||l)}}static{this.\u0275pipe=m.EJ8({name:"truncateString",type:l,pure:!0})}}return l})()}}]);