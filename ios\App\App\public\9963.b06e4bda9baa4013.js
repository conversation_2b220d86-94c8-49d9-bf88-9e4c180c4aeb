"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9963],{39963:(X,r,c)=>{c.d(r,{t:()=>w});var s=c(99987),n=c(2978),i=c(77897),_=c(62049),u=c(82571),m=c(56610),p=c(74657);function d(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",t.user.firstName),n.Y8G("readonly",!0)}}function g(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",t.user.tel),n.Y8G("readonly",!0)}}function b(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",t.user.cni),n.Y8G("readonly",!0)}}function C(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",t.user.nui),n.Y8G("readonly",!0)}}function M(o,e){if(1&o&&n.nrm(0,"ion-input",15),2&o){const t=n.XpG(2);n.FS9("value",t.user.email),n.Y8G("readonly",!0)}}function h(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",null==t.user.address?null:t.user.address.region),n.Y8G("readonly",!0)}}function f(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",null==t.user.address?null:t.user.address.commercialRegion),n.Y8G("readonly",!0)}}function P(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",null==t.user.address?null:t.user.address.city),n.Y8G("readonly",!0)}}function O(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(2);n.FS9("value",null==t.user||null==t.user.address?null:t.user.address.district),n.Y8G("readonly",!0)}}function v(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(3);n.FS9("value",null==t.user?null:t.user.profession),n.Y8G("readonly",!0)}}function E(o,e){if(1&o&&(n.j41(0,"ion-item")(1,"ion-label",10),n.EFF(2,"Profession *"),n.k0s(),n.DNE(3,v,1,2,"ion-input",12),n.k0s()),2&o){const t=n.XpG(2);n.R7$(3),n.Y8G("ngIf",null==t.user?null:t.user.profession)}}function k(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(3);n.FS9("value",null==t.user?null:t.user.socialReason),n.Y8G("readonly",!0)}}function x(o,e){if(1&o&&(n.j41(0,"ion-item")(1,"ion-label",10),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.DNE(4,k,1,2,"ion-input",12),n.k0s()),2&o){const t=n.XpG(2);n.R7$(2),n.SpI("",n.bMT(3,2,"bottom-sheet-validation.socialReasean")," *"),n.R7$(2),n.Y8G("ngIf",null==t.user?null:t.user.socialReason)}}function F(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(3);n.FS9("value",null==t.user?null:t.user.direction),n.Y8G("readonly",!0)}}function y(o,e){if(1&o&&(n.j41(0,"ion-item")(1,"ion-label",10),n.EFF(2,"Direction *"),n.k0s(),n.DNE(3,F,1,2,"ion-input",12),n.k0s()),2&o){const t=n.XpG(2);n.R7$(3),n.Y8G("ngIf",null==t.user?null:t.user.direction)}}function I(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(3);n.FS9("value",null==t.user?null:t.user.service),n.Y8G("readonly",!0)}}function G(o,e){if(1&o&&(n.j41(0,"ion-item")(1,"ion-label",10),n.EFF(2,"Service *"),n.k0s(),n.DNE(3,I,1,2,"ion-input",12),n.k0s()),2&o){const t=n.XpG(2);n.R7$(3),n.Y8G("ngIf",null==t.user?null:t.user.service)}}function S(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(3);n.FS9("value",null==t.user?null:t.user.position),n.Y8G("readonly",!0)}}function R(o,e){if(1&o&&(n.j41(0,"ion-item")(1,"ion-label",10),n.EFF(2,"Position *"),n.k0s(),n.DNE(3,S,1,2,"ion-input",12),n.k0s()),2&o){const t=n.XpG(2);n.R7$(3),n.Y8G("ngIf",null==t.user?null:t.user.position)}}function T(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(3);n.FS9("value",null==t.user?null:t.user.matricule),n.Y8G("readonly",!0)}}function Y(o,e){if(1&o&&(n.j41(0,"ion-item")(1,"ion-label",10),n.EFF(2,"Matricule *"),n.k0s(),n.DNE(3,T,1,2,"ion-input",12),n.k0s()),2&o){const t=n.XpG(2);n.R7$(3),n.Y8G("ngIf",null==t.user?null:t.user.matricule)}}function $(o,e){if(1&o&&n.nrm(0,"ion-input",11),2&o){const t=n.XpG(3);n.FS9("value",null==t.user?null:t.user.company),n.Y8G("readonly",!0)}}function D(o,e){if(1&o&&(n.j41(0,"ion-item")(1,"ion-label",10),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.DNE(4,$,1,2,"ion-input",12),n.k0s()),2&o){const t=n.XpG(2);n.R7$(2),n.SpI("",n.bMT(3,2,"bottom-sheet-validation.Company ")," *"),n.R7$(2),n.Y8G("ngIf",null==t.user?null:t.user.company)}}function B(o,e){if(1&o&&(n.j41(0,"ion-list",9)(1,"ion-list-header")(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()(),n.j41(5,"ion-item")(6,"ion-label",10),n.EFF(7),n.nI1(8,"translate"),n.k0s(),n.nrm(9,"ion-input",11),n.k0s(),n.j41(10,"ion-item")(11,"ion-label",10),n.EFF(12),n.nI1(13,"translate"),n.k0s(),n.DNE(14,d,1,2,"ion-input",12),n.k0s(),n.j41(15,"ion-item")(16,"ion-label",10),n.EFF(17),n.nI1(18,"translate"),n.k0s(),n.DNE(19,g,1,2,"ion-input",12),n.k0s(),n.j41(20,"ion-item")(21,"ion-label",10),n.EFF(22),n.nI1(23,"translate"),n.k0s(),n.DNE(24,b,1,2,"ion-input",12),n.k0s(),n.j41(25,"ion-item")(26,"ion-label",10),n.EFF(27),n.nI1(28,"translate"),n.k0s(),n.DNE(29,C,1,2,"ion-input",12),n.k0s(),n.j41(30,"ion-item")(31,"ion-label",10),n.EFF(32,"Email *"),n.k0s(),n.DNE(33,M,1,2,"ion-input",13),n.k0s(),n.j41(34,"ion-item")(35,"ion-label",10),n.EFF(36,"Region *"),n.k0s(),n.DNE(37,h,1,2,"ion-input",12),n.k0s(),n.j41(38,"ion-item")(39,"ion-label",10),n.EFF(40),n.nI1(41,"translate"),n.k0s(),n.DNE(42,f,1,2,"ion-input",12),n.k0s(),n.j41(43,"ion-item")(44,"ion-label",10),n.EFF(45),n.nI1(46,"translate"),n.k0s(),n.DNE(47,P,1,2,"ion-input",12),n.k0s(),n.j41(48,"ion-item")(49,"ion-label",10),n.EFF(50),n.nI1(51,"translate"),n.k0s(),n.DNE(52,O,1,2,"ion-input",12),n.k0s(),n.DNE(53,E,4,1,"ion-item",14),n.DNE(54,x,5,4,"ion-item",14),n.DNE(55,y,4,1,"ion-item",14),n.DNE(56,G,4,1,"ion-item",14),n.DNE(57,R,4,1,"ion-item",14),n.DNE(58,Y,4,1,"ion-item",14),n.DNE(59,D,5,4,"ion-item",14),n.k0s()),2&o){const t=n.XpG();n.R7$(3),n.JRh(n.bMT(4,27,"bottom-sheet-validation.accountInformation")),n.R7$(4),n.SpI("",n.bMT(8,29,"bottom-sheet-validation.name")," *"),n.R7$(2),n.FS9("value",t.user.lastName),n.Y8G("readonly",!0),n.R7$(3),n.SpI("",n.bMT(13,31,"bottom-sheet-validation.lastName")," *"),n.R7$(2),n.Y8G("ngIf",t.user.firstName),n.R7$(3),n.SpI("",n.bMT(18,33,"tel")," *"),n.R7$(2),n.Y8G("ngIf",t.user.tel),n.R7$(3),n.SpI("",n.bMT(23,35,"signup-page.first-step.numberNui")," *"),n.R7$(2),n.Y8G("ngIf",t.user.cni),n.R7$(3),n.SpI("",n.bMT(28,37,"signup-page.first-step.numberNui")," *"),n.R7$(2),n.Y8G("ngIf",t.user.nui),n.R7$(4),n.Y8G("ngIf",t.user.email),n.R7$(4),n.Y8G("ngIf",null==t.user.address?null:t.user.address.region),n.R7$(3),n.SpI("",n.bMT(41,39,"signup-page.second-step.select-commercialRegion-label")," *"),n.R7$(2),n.Y8G("ngIf",null==t.user.address?null:t.user.address.commercialRegion),n.R7$(3),n.SpI("",n.bMT(46,41,"signup-page.second-step.select-city-label")," *"),n.R7$(2),n.Y8G("ngIf",null==t.user.address?null:t.user.address.city),n.R7$(3),n.SpI("",n.bMT(51,43,"bottom-sheet-validation.Quartier")," *"),n.R7$(2),n.Y8G("ngIf",null==t.user.address?null:t.user.address.district),n.R7$(1),n.Y8G("ngIf","particular"===t.userType),n.R7$(1),n.Y8G("ngIf","retailer"===t.userType),n.R7$(1),n.Y8G("ngIf","employeeCimencam"===t.userType),n.R7$(1),n.Y8G("ngIf","employeeCimencam"===t.userType),n.R7$(1),n.Y8G("ngIf","employeeCimencam"===t.userType),n.R7$(1),n.Y8G("ngIf","employeeCimencam"===t.userType),n.R7$(1),n.Y8G("ngIf","CompanyEmployee"===t.userType)}}function A(o,e){1&o&&(n.j41(0,"p")(1,"ion-text"),n.EFF(2,"En cliquant sur Valider l'Op\xe9ration, vous accepter les "),n.k0s(),n.j41(3,"a",17),n.EFF(4," Politiques de confidentialit\xe9s"),n.k0s(),n.j41(5,"ion-text"),n.EFF(6," et les "),n.k0s(),n.j41(7,"a",18),n.EFF(8," Mentions L\xe9gales"),n.k0s(),n.j41(9,"ion-text"),n.EFF(10," li\xe9s \xe0 l'utilisation de "),n.k0s(),n.j41(11,"ion-text",19),n.EFF(12," La Pasta "),n.k0s()())}function j(o,e){1&o&&(n.j41(0,"p")(1,"ion-text"),n.EFF(2,"By clicking on Valid Operation , you accept the "),n.k0s(),n.j41(3,"a",17),n.EFF(4," Privacy Policies "),n.k0s(),n.j41(5,"ion-text"),n.EFF(6," and "),n.k0s(),n.j41(7,"a",18),n.EFF(8," Legal Notices "),n.k0s(),n.j41(9,"ion-text"),n.EFF(10," related to the use of "),n.k0s(),n.j41(11,"ion-text",19),n.EFF(12," La Pasta"),n.k0s()())}function N(o,e){if(1&o&&(n.j41(0,"div",16),n.DNE(1,A,13,0,"p",14),n.DNE(2,j,13,0,"p",14),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("ngIf","fr"===t.translateService.currentLang),n.R7$(1),n.Y8G("ngIf","fr"!==t.translateService.currentLang)}}let w=(()=>{class o{constructor(t,l,a){this.modalCtrl=t,this.translateService=l,this.commonSrv=a,this.label="ANNULER L\u2019OP\xc9RATION",this.action="validate",this.operation="create account",this.userType=""}ngOnInit(){this.description=s.T.French===this.translateService.currentLang?"Vous \xeates sur le point de valider la cr\xe9ation de votre compte  ":"You are about to validate the creation of your account"}closeModal(t){this.modalCtrl.dismiss({user:this.user,userType:this.userType},t)}static{this.\u0275fac=function(l){return new(l||o)(n.rXU(i.W3),n.rXU(_.E),n.rXU(u.h))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-bottom-sheet-validation-action"]],inputs:{label:"label",action:"action",operation:"operation",userType:"userType",user:"user",description:"description"},decls:18,vars:8,consts:[[1,"bottom-sheet-validation","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close.svg"],[1,"container"],[1,"ion-text-center"],["class","input-group",4,"ngIf"],["class","auth-check-condition",4,"ngIf"],["expand","block","size","large",3,"color","click"],[1,"input-group"],["position","floating"],["clearInput","",3,"value","readonly"],["clearInput","",3,"value","readonly",4,"ngIf"],["type","email","clearInput","",3,"value","readonly",4,"ngIf"],[4,"ngIf"],["type","email","clearInput","",3,"value","readonly"],[1,"auth-check-condition"],["color","primary","href","https://www.holcim.com/privacy-policy","target","_blank"],["color","primary","href","https://www.holcim.com/legal-mentions","target","_blank"],["color","primary"]],template:function(l,a){1&l&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return a.closeModal("close")}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"div",4)(10,"div",5)(11,"ion-text"),n.EFF(12),n.k0s()(),n.DNE(13,B,60,45,"ion-list",6),n.DNE(14,N,3,2,"div",7),n.j41(15,"div")(16,"ion-button",8),n.bIt("click",function(){return a.closeModal(a.action)}),n.EFF(17),n.k0s()()()()()),2&l&&(n.R7$(6),n.JRh(n.bMT(7,6,"bottom-sheet-validation.validateOparation")),n.R7$(6),n.JRh(a.description),n.R7$(1),n.Y8G("ngIf","create account"===a.operation),n.R7$(1),n.Y8G("ngIf","create account"===a.operation),n.R7$(2),n.Y8G("color","cancel"===a.action?"secondary":"primary"),n.R7$(1),n.SpI(" ",a.label," "))},dependencies:[i.Jm,i.W9,i.eU,i.KW,i.$w,i.uz,i.he,i.nf,i.AF,i.IO,i.Zx,i.ai,i.Gw,m.bT,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.bottom-sheet-validation[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.bottom-sheet-validation[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:10px}.bottom-sheet-validation[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px gray;border-radius:10px}.bottom-sheet-validation[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#0a692b;border-radius:10px}.bottom-sheet-validation[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#0a692b}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{margin-bottom:calc(31.25 * var(--res))}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));max-height:calc(55 * var(--resH));overflow:scroll;overflow-x:hidden}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-list-header[_ngcontent-%COMP%]{min-height:16px!important;padding-bottom:calc(75 * var(--res))}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-list-header[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:600}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{color:#000;font-size:calc(42 * var(--res))}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .auth-check-condition[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .auth-check-condition[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{text-align:justify}.bottom-sheet-validation[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .auth-check-condition[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;font-weight:500;font-family:Mont Regular;font-size:calc(40 * var(--res))}"]})}}return o})()}}]);