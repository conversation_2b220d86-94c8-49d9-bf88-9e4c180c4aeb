"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1907],{71907:(w,y,r)=>{r.r(y),r.d(y,{ReportingTopProductsPageModule:()=>F});var O=r(56610),M=r(37222),_=r(71604),f=r(60787),c=r(77897),h=r(77575),C=r(73308),D=r(20120),t=r(2978),b=r(93368),x=r(32327),l=r(82571),a=r(11244),n=r(21295),s=r(74657);function g(d,T){if(1&d&&(t.qex(0),t.j41(1,"ion-row")(2,"ion-col",18),t.EFF(3),t.k0s(),t.j41(4,"ion-col",18),t.<PERSON>(5),t.nI1(6,"tonne"),t.k0s(),t.j41(7,"ion-col",19),t.<PERSON><PERSON>(8),t.nI1(9,"translate"),t.k0s()(),t.bVm()),2&d){const e=T.$implicit,i=T.index,o=t.XpG();t.R7$(3),t.JRh(e),t.R7$(2),t.SpI("",t.bMT(6,4,o.dataStateProduct[i]||"0.00")," T"),t.R7$(3),t.Lme("",o.dataStateProduct[i]||0," ",t.bMT(9,6,"reporting.bags"),"")}}const p=[{path:"",component:(()=>{class d{constructor(e,i,o,v){this.reportingSrv=e,this.reportingRetailSrv=i,this.commonSrv=o,this.modalCtrl=v,this.filterData={userId:"",product:"",startDate:new Date((new Date).getFullYear(),0,1),endDate:new Date},this.date=new Date,this.months=["Jan","Fev","Mar","Avr","Mai","Jun","Jul","Aou","Sep","Oct","Nov","Dec"]}ngOnInit(){var e=this;return(0,C.A)(function*(){yield e.geTotalProduct(),e.chartData()})()}loadData(){var e=this;return(0,C.A)(function*(){yield e.geTotalProduct(),e.chartData()})()}geTotalProduct(){var e=this;return(0,C.A)(function*(){e.dataStateProduct=e.filterData?.product?e.reportingSrv.dataSumProduct[e.filterData?.product]:e.reportingSrv.dataSaleEvolutionsProduct,e.totalProduct=e.dataStateProduct?.reduce((i,o)=>i+(o?.totalQuantity||o?.totalValue||0),0)})()}chartData(){var e=this;return(0,C.A)(function*(){e.isLoading=!0,e.totalProduct=e.dataStateProduct?.reduce((o,v)=>o+v),e.dataSaleEvolutionsBar={labels:["J","F","M","A","M","J","J","A","S","O","N","D"],datasets:[{data:e.dataStateProduct,backgroundColor:"#419CFB",borderColor:"#419CFB",borderRadius:10}]},e.basicOptions={plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!1,ticks:{callback:function(o){return(o=>o>=1e9?(o/1e9).toFixed(1)+"B":o>=1e6?(o/1e6).toFixed(1)+"M":o>=1e3?(o/1e3).toFixed(1)+"k":o)(o)},color:"none"},grid:{display:!0,color:"#dadada",drawBorder:!1}},x:{ticks:{color:"none"},grid:{display:!1,drawBorder:!1}}}},e.isLoading=!1})()}generateHexColor(){const e=Math.floor(16215*Math.random()).toString(16);return"#"+"9".repeat(6-e.length)+e}doRefresh(e){var i=this;return(0,C.A)(function*(){i.filterData={userId:"",product:"",startDate:new Date((new Date).getFullYear(),0,1),endDate:new Date},yield i.loadData(),e.target.complete()})()}resetFilter(){var e=this;return(0,C.A)(function*(){e.filterData={userId:"",product:"",startDate:new Date((new Date).getFullYear(),0,1),endDate:new Date},yield e.loadData()})()}showFilter(){var e=this;return(0,C.A)(function*(){const i=yield e.modalCtrl.create({component:D.$,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.5],mode:"ios",componentProps:{filterData:{...e.filterData},isShow:!0,products:Object.keys(e.reportingSrv.dataStateProduct)}});i.present();const o=(yield i.onWillDismiss()).data;e.filterData={...e.filterData,...o},e.filterData&&(yield e.loadData())})()}static{this.\u0275fac=function(i){return new(i||d)(t.rXU(b.k),t.rXU(x.k),t.rXU(l.h),t.rXU(c.W3))}}static{this.\u0275cmp=t.VBU({type:d,selectors:[["app-reporting-top-products"]],decls:44,vars:41,consts:[[1,"header"],["mode","md","slot","start","text",""],["defaultHref","./"],[1,"title-and-filter-container"],[1,"title"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],[1,"ion-content"],[1,"p-card"],[1,"p-card-header"],[1,"filter"],["src","/assets/icons/refresh-green.png",1,"img-refresh",3,"click"],["expand","block","fill","outline","color","primary","size","small",3,"click"],["slot","start","src","/assets/icons/filtre-icon.png",1,"img-filter"],["type","bar",1,"p-card",3,"data","options","height"],["size","3",1,"row-title"],["size","4",1,"row-title"],[4,"ngFor","ngForOf"],["size","3"],["size","4"]],template:function(i,o){1&i&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-buttons",1),t.nrm(3,"ion-back-button",2),t.k0s(),t.j41(4,"div",3)(5,"h1",4),t.EFF(6),t.nI1(7,"capitalize"),t.nI1(8,"translate"),t.k0s()()()(),t.j41(9,"ion-content")(10,"ion-refresher",5),t.bIt("ionRefresh",function(R){return o.doRefresh(R)}),t.nrm(11,"ion-refresher-content",6),t.nI1(12,"translate"),t.nI1(13,"translate"),t.k0s(),t.j41(14,"div",7)(15,"p-card",8)(16,"div",9)(17,"div"),t.EFF(18),t.nI1(19,"tonne"),t.k0s(),t.j41(20,"div",10)(21,"ion-img",11),t.bIt("click",function(){return o.resetFilter()}),t.k0s(),t.j41(22,"ion-button",12),t.bIt("click",function(){return o.showFilter()}),t.nrm(23,"ion-img",13),t.EFF(24),t.nI1(25,"capitalize"),t.nI1(26,"translate"),t.k0s()()(),t.nrm(27,"p-chart",14),t.k0s(),t.j41(28,"ion-grid")(29,"ion-row")(30,"ion-col",15),t.EFF(31),t.nI1(32,"titlecase"),t.nI1(33,"translate"),t.k0s(),t.j41(34,"ion-col",15),t.EFF(35),t.nI1(36,"titlecase"),t.nI1(37,"translate"),t.k0s(),t.j41(38,"ion-col",16),t.EFF(39),t.nI1(40,"titlecase"),t.nI1(41,"translate"),t.nI1(42,"translate"),t.k0s()(),t.DNE(43,g,10,8,"ng-container",17),t.k0s()()()),2&i&&(t.R7$(6),t.JRh(t.bMT(7,13,t.bMT(8,15,"reporting.top-products"))),t.R7$(5),t.FS9("pullingText",t.bMT(12,17,"refresher.pull")),t.Mz_("refreshingText","",t.bMT(13,19,"refresher.refreshing"),"..."),t.R7$(7),t.SpI("Total : ",t.bMT(19,21,o.totalProduct)," T"),t.R7$(6),t.SpI(" ",t.bMT(25,23,t.bMT(26,25,"reporting.btn-filter"))," "),t.R7$(3),t.Y8G("data",o.dataSaleEvolutionsBar)("options",o.basicOptions)("height","12rem"),t.R7$(4),t.JRh(t.bMT(32,27,t.bMT(33,29,"reporting.month"))),t.R7$(4),t.SpI("",t.bMT(36,31,t.bMT(37,33,"reporting.quantity")),"(T)"),t.R7$(4),t.Lme("",t.bMT(40,35,t.bMT(41,37,"reporting.quantity")),"(",t.bMT(42,39,"reporting.bags"),")50KG"),t.R7$(4),t.Y8G("ngForOf",o.months))},dependencies:[O.Sq,c.el,c.Jm,c.QW,c.hU,c.W9,c.lO,c.eU,c.KW,c.To,c.Ki,c.ln,c.ai,c.tY,f.X,_.Z,O.PV,a.F,n.t,s.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--border-color: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{width:4rem}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-size:calc(55 * var(--res));text-align:start;flex-grow:1}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]{display:flex;align-items:flex-start;flex-direction:column;height:4em;padding:1em var(--container-padding);background-position:center;background-size:cover;border-radius:16px;width:100%;background-image:url(/assets/images/Total-card.png)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]{display:flex;flex-direction:column;font-size:1em;font-family:Mont Regular}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%]{font-size:var(--fs-32-px);color:var(--clr-tertiary-600);font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:var(--fs-15-px);color:var(--clr-tertiary-600)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-size:var(--fs-10-px)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .icon-home[_ngcontent-%COMP%]{height:48px;width:48px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]{padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{width:100%;margin:20px .5em;background-color:#d9d9d9}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:0 0 calc(25 * var(--res)) 0;font-family:var(--mont-semibold);font-size:calc(42 * var(--res));color:#143c5d}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{display:flex;align-items:center;gap:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   .img-refresh[_ngcontent-%COMP%]{width:calc(50 * var(--res));cursor:pointer}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{text-transform:capitalize;--padding-top: calc(35 * var(--res));--padding-bottom: calc(35 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .img-filter[_ngcontent-%COMP%]{margin-right:calc(15 * var(--res));width:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-chart[_ngcontent-%COMP%]{height:12rem}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{background:#CEE5FE;padding:1em;border-radius:10px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{padding-bottom:10px;margin-bottom:10px;border-bottom:1px dashed var(--ion-color-dark);display:flex;flex-direction:row;justify-content:space-between;font-size:calc(30 * var(--res));text-align:center}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-title[_ngcontent-%COMP%]{font-weight:500;background:hwb(211 53% 4%);border-radius:10px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .semi[_ngcontent-%COMP%]{font-family:Mont SemiBold!important}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-data[_ngcontent-%COMP%]{color:#143c5d;font-size:12px;font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .row-header[_ngcontent-%COMP%]{border-style:none!important}ion-content[_ngcontent-%COMP%]   .product-contain[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between}"]})}}return d})()}];let P=(()=>{class d{static{this.\u0275fac=function(i){return new(i||d)}}static{this.\u0275mod=t.$C({type:d})}static{this.\u0275inj=t.G2t({imports:[h.iI.forChild(p),h.iI]})}}return d})();var m=r(93887);let F=(()=>{class d{static{this.\u0275fac=function(i){return new(i||d)}}static{this.\u0275mod=t.$C({type:d})}static{this.\u0275inj=t.G2t({imports:[O.MD,M.YN,c.bv,f.F,_.D,P,m.G,s.h]})}}return d})()},32327:(w,y,r)=>{r.d(y,{k:()=>t});var O=r(73308),M=r(56610),_=r(26409),f=r(94934),c=r(45312),h=r(2978),C=r(33607),D=r(82571);let t=(()=>{class b{constructor(l,a,n){this.http=l,this.baseUrl=a,this.commonSrv=n,this.base_url=`${this.baseUrl.getOrigin()}${c.c.basePath}`}geTotalQuantityOrder(l){var a=this;return(0,O.A)(function*(){try{let n=new _.Nl;const{status:s,startDate:g,endDate:u,enable:p=!0,userId:P}=l;return s&&(n=n.append("status",s)),g&&u&&(n=n.append("startDate",new M.vh("fr").transform(g,"YYYY-MM-dd")),n=n.append("endDate",new M.vh("fr").transform(u,"YYYY-MM-dd"))),n=n.append("enable",p),P&&(n=n.append("user",P)),yield(0,f.s)(a.http.get(`${a.base_url}reporting/total-quantity-evolution`,{params:n}))}catch(n){return a.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}geTotalProduct(l){var a=this;return(0,O.A)(function*(){try{let n=new _.Nl;const{status:s,startDate:g,endDate:u,product:p,enable:P=!0,userId:m}=l;return g&&u&&(n=n.append("startDate",new M.vh("fr").transform(g,"YYYY-MM-dd")),n=n.append("endDate",new M.vh("fr").transform(u,"YYYY-MM-dd"))),s&&(n=n.append("status",s)),p&&(n=n.append("items.product._id",p)),m&&(n=n.append("user",m)),n=n.append("enable",P),yield(0,f.s)(a.http.get(`${a.base_url}reporting/product-quantity`,{params:n}))}catch(n){return yield a.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}getTotalEarnPoint(l){var a=this;return(0,O.A)(function*(){try{let n=new _.Nl;const{startDate:s,endDate:g,region:u,enable:p=!0}=l;return u&&(n=n.append("region",u)),s&&g&&(n=n.append("startDate",new M.vh("fr").transform(s,"YYYY-MM-dd")),n=n.append("endDate",new M.vh("fr").transform(g,"YYYY-MM-dd"))),n=n.append("enable",p),yield(0,f.s)(a.http.get(`${a.base_url}reporting/points-evolution`,{params:n}))}catch(n){return yield a.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}getDistributorVolume(l){var a=this;return(0,O.A)(function*(){try{let n=new _.Nl;const{status:s,startDate:g,endDate:u,product:p,enable:P=!0,userId:m}=l;return s&&(n=n.append("status",s)),p&&(n=n.append("items.product._id",p)),m&&(n=n.append("user",m)),g&&u&&(n=n.append("startDate",new M.vh("fr").transform(g,"YYYY-MM-dd")),n=n.append("endDate",new M.vh("fr").transform(u,"YYYY-MM-dd"))),n=n.append("enable",P),yield(0,f.s)(a.http.get(`${a.base_url}reporting/distributor-volume`,{params:n}))}catch(n){return yield a.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}getTotalPoint(l){var a=this;return(0,O.A)(function*(){try{let n=new _.Nl;const{status:s,product:p,enable:P=!0,region:m}=l;return s&&(n=n.append("status",s)),p&&(n=n.append("items.product._id",p)),m&&(n=n.append("payment.mode.id",m)),n=n.append("enable",P),yield(0,f.s)(a.http.get(`${a.base_url}reporting/retail-points`,{params:n}))}catch(n){return yield a.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}static{this.\u0275fac=function(a){return new(a||b)(h.KVO(_.Qq),h.KVO(C.K),h.KVO(D.h))}}static{this.\u0275prov=h.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})}}return b})()}}]);