"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7410],{7410:(v,_,t)=>{t.r(_),t.d(_,{ProductsPageModule:()=>w});var g=t(56610),P=t(37222),o=t(77897),p=t(77575),c=t(73308),n=t(2978),m=t(39316),b=t(94440);function h(e,C){1&e&&n.nrm(0,"ion-progress-bar",12)}const u=function(e){return{"background-image":e}};function f(e,C){if(1&e){const r=n.RV6();n.j41(0,"ion-slide",13)(1,"div",14),n.bIt("click",function(){n.eBV(r),n.XpG();const l=n.sdS(11);return n.Njj(l.slidePrev())}),n.k0s(),n.j41(2,"div",15),n.bIt("click",function(){n.eBV(r),n.XpG();const l=n.sdS(11);return n.Njj(l.slideNext())}),n.k0s(),n.nrm(3,"div",16),n.k0s()}2&e&&n.Y8G("ngStyle",n.eq3(1,u,"url("+C.$implicit.img+")"))}function d(e,C){if(1&e&&(n.j41(0,"ion-card",17)(1,"ion-card-content"),n.nrm(2,"ion-img",18),n.j41(3,"ion-label",19),n.EFF(4),n.nI1(5,"truncateString"),n.k0s()()()),2&e){const r=C.$implicit;n.FS9("routerLink","/order/product-detail/"+r._id),n.R7$(2),n.FS9("src",null==r?null:r.image),n.R7$(2),n.JRh(n.i5U(5,3,null==r?null:r.label,12))}}const a=[{path:"",component:(()=>{class e{constructor(r){this.productSrv=r,this.isLoading=!1,this.slideOpts={initialSlide:0,speed:500,spaceBetween:16,autoplay:!0,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}},this.slideOfferOpts={initialSlide:0,speed:4e3,spaceBetween:10,slidesPerView:2,autoplay:!0,grabCursor:!0,cubeEffect:{shadow:!0,slideShadows:!0,shadowOffset:20,shadowScale:.94}},this.slides=[{img:"assets/images/Group611.png"},{img:"assets/images/Group612.png"},{img:"assets/images/Group475.png"},{img:"assets/images/Group612.png"}],this.products=[]}ngOnInit(){var r=this;return(0,c.A)(function*(){yield r.getProducts()})()}getProducts(){var r=this;return(0,c.A)(function*(){r.isLoading=!0,r.products=(yield r.productSrv.getProducts()).data,r.isLoading=!1})()}static{this.\u0275fac=function(s){return new(s||e)(n.rXU(m.b))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-products"]],decls:15,vars:6,consts:[["slot","start","defaultHref","#","text"," "],[1,"title"],[1,"products-container",3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],[1,"card","slide-container"],[1,"card-content"],[1,"slide-content",3,"pager","options"],["mySlider",""],["class","slide",3,"ngStyle",4,"ngFor","ngForOf"],[1,"rows-products"],["class","card-product",3,"routerLink",4,"ngFor","ngForOf"],["type","indeterminate"],[1,"slide",3,"ngStyle"],[1,"swiper-button-prev",3,"click"],[1,"swiper-button-next",3,"click"],[1,"background-opacity"],[1,"card-product",3,"routerLink"],[3,"src"],[1,"product-name"]],template:function(s,l){1&s&&(n.j41(0,"ion-header")(1,"ion-toolbar"),n.nrm(2,"ion-back-button",0),n.j41(3,"ion-title",1),n.EFF(4,"Tous les produits"),n.k0s()()(),n.j41(5,"ion-content",2),n.DNE(6,h,1,0,"ion-progress-bar",3),n.j41(7,"div",4)(8,"ion-card",5)(9,"ion-card-content",6)(10,"ion-slides",7,8),n.DNE(12,f,4,3,"ion-slide",9),n.k0s()()(),n.j41(13,"div",10),n.DNE(14,d,6,6,"ion-card",11),n.k0s()()()),2&s&&(n.R7$(5),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",l.isLoading),n.R7$(4),n.Y8G("pager",!0)("options",l.slideOpts),n.R7$(2),n.Y8G("ngForOf",l.slides),n.R7$(2),n.Y8G("ngForOf",l.products))},dependencies:[g.Sq,g.bT,g.B3,o.el,o.b_,o.I9,o.W9,o.eU,o.KW,o.he,o.FH,o.q3,o.tR,o.BC,o.ai,o.tY,o.N7,p.Wk,b.c],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{color:#000}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:var(--main-title);text-align:start}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:100}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.products-container[_ngcontent-%COMP%]{background-color:#eef2f9}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(37.5 * var(--res))}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:100;z-index:1}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .background-opacity[_ngcontent-%COMP%]{position:absolute;width:100%;height:100%;top:0;z-index:1}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]{width:100%;border-radius:9px;height:calc(30 * var(--resH))}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]{border-radius:9px;height:100%;--bullet-background: white;--bullet-background-active: red}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .slide[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .slide[_ngcontent-%COMP%]{background-size:100%;background-repeat:no-repeat;background-position:center}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%]{width:19px;height:19px;background-size:19px 19px}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%]{background-image:url(arrow-back-white.e97e691d01c56343.svg)}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%]{background-image:url(arrow-forward-white.a589d59d565b95fe.svg)}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%], .products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{background-repeat:no-repeat;background-size:cover}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .rows-products[_ngcontent-%COMP%]{margin-top:1em;display:grid;gap:.3em;grid-template-columns:auto auto auto}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .rows-products[_ngcontent-%COMP%]   .card-product[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;padding:calc(20 * var(--res)) 0}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .rows-products[_ngcontent-%COMP%]   .card-product[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:70%}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .rows-products[_ngcontent-%COMP%]   .card-product[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{text-align:center}.products-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .rows-products[_ngcontent-%COMP%]   .card-product[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{margin-top:calc(30 * var(--res));font-family:Mont SemiBold}"]})}}return e})()}];let O=(()=>{class e{static{this.\u0275fac=function(s){return new(s||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[p.iI.forChild(a),p.iI]})}}return e})();var M=t(93887),y=t(74657);let w=(()=>{class e{static{this.\u0275fac=function(s){return new(s||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[g.MD,P.YN,o.bv,y.h,O,M.G]})}}return e})()},39316:(v,_,t)=>{t.d(_,{b:()=>h});var g=t(73308),P=t(26409),o=t(94934),p=t(45312),c=t(2978),n=t(82571),m=t(33607),b=t(77897);let h=(()=>{class u{constructor(d,i,a,O){this.http=d,this.commonSrv=i,this.baseUrlService=a,this.toastController=O,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+p.c.basePath+"products"}getProducts(d){var i=this;return(0,g.A)(function*(){try{let a=new P.Nl;return d?.limit&&(a=a.append("limit",d?.limit)),yield(0,o.s)(i.http.get(i.url,{params:a}))}catch(a){const M={message:i.commonSrv.getError("",a).message,color:"danger"};return yield i.commonSrv.showToast(M),a}})()}getProduct(d){var i=this;return(0,g.A)(function*(){try{return yield(0,o.s)(i.http.get(`${i.url}/${d}`))}catch(a){const M={message:i.commonSrv.getError("",a).message,color:"danger"};return yield i.commonSrv.showToast(M),a}})()}static{this.\u0275fac=function(i){return new(i||u)(c.KVO(P.Qq),c.KVO(n.h),c.KVO(m.K),c.KVO(b.K_))}}static{this.\u0275prov=c.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})}}return u})()},94440:(v,_,t)=>{t.d(_,{c:()=>P});var g=t(2978);let P=(()=>{class o{transform(c,...n){return c?c.length>n[0]?`${c.substring(0,n[0]-3)}...`:c:""}static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275pipe=g.EJ8({name:"truncateString",type:o,pure:!0})}}return o})()}}]);