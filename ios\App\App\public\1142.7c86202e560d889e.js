"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1142],{92391:(d,O,r)=>{r.d(O,{Nn:()=>D,gH:()=>c});var c=function(s){return s.CalendarEvent="CALENDAR_EVENT",s.ContactInfo="CONTACT_INFO",s.DriversLicense="DRIVERS_LICENSE",s.Email="EMAIL",s.Geo="GEO",s.Isbn="ISBN",s.Phone="PHONE",s.Product="PRODUCT",s.Sms="SMS",s.Text="TEXT",s.Url="URL",s.Wifi="WIFI",s.Unknown="UNKNOWN",s}(c||{}),D=function(s){return s.Front="FRONT",s.Back="BACK",s}(D||{})},94761:(d,O,r)=>{r.d(O,{vi:()=>a});var i=r(22126);r(92391);const a=(0,i.F3)("BarcodeScanner",{web:()=>r.e(5499).then(r.bind(r,85499)).then(D=>new D.BarcodeScannerWeb)})},39316:(d,O,r)=>{r.d(O,{b:()=>v});var i=r(73308),c=r(26409),a=r(94934),D=r(45312),E=r(2978),m=r(82571),u=r(33607),P=r(77897);let v=(()=>{class s{constructor(t,o,n,_){this.http=t,this.commonSrv=o,this.baseUrlService=n,this.toastController=_,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+D.c.basePath+"products"}getProducts(t){var o=this;return(0,i.A)(function*(){try{let n=new c.Nl;return t?.limit&&(n=n.append("limit",t?.limit)),yield(0,a.s)(o.http.get(o.url,{params:n}))}catch(n){const e={message:o.commonSrv.getError("",n).message,color:"danger"};return yield o.commonSrv.showToast(e),n}})()}getProduct(t){var o=this;return(0,i.A)(function*(){try{return yield(0,a.s)(o.http.get(`${o.url}/${t}`))}catch(n){const e={message:o.commonSrv.getError("",n).message,color:"danger"};return yield o.commonSrv.showToast(e),n}})()}static{this.\u0275fac=function(o){return new(o||s)(E.KVO(c.Qq),E.KVO(m.h),E.KVO(u.K),E.KVO(P.K_))}}static{this.\u0275prov=E.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})()},44444:(d,O,r)=>{r.d(O,{PB:()=>v,cs:()=>a,iL:()=>h}),r(68953);class c{constructor(o){this.email="",this.firstName="",this.lastName="",this.tel="",this.password="",this.cni="",this.nui="",this.address={region:"",city:"",district:""},this.category=o}}class a extends c{}var v=function(t){return t[t.NORMAL=100]="NORMAL",t[t.CORDO_RH=101]="CORDO_RH",t[t.DRH=102]="DRH",t}(v||{}),h=function(t){return t[t.BHB=101]="BHB",t[t.BS=102]="BS",t[t.BPI=103]="BPI",t}(h||{})},17709:(d,O,r)=>{r.d(O,{Q:()=>s});var i=r(73308),c=r(26409),a=r(2978),D=r(45312),E=r(94934),m=r(74657),u=r(99987),P=r(82571),v=r(33607);let s=(()=>{class h{constructor(o,n,_){this.http=o,this.commonSrv=n,this.baseUrlService=_,this.translateService=(0,a.WQX)(m.c$),this.url=this.baseUrlService.getOrigin()+D.c.basePath}getQrCodeDataAnUpDateStateToScanned(o){var n=this;return(0,i.A)(function*(){try{let _=new c.Nl;const{code:e}=o;if(!e)throw yield n.commonSrv.showToast({color:"danger",message:u.T.French===n.translateService.currentLang?"Code absent du QR code":"Code is missing from the QR code"}),new Error("Code is required");return e&&(_=_.append("code",e)),yield(0,E.s)(n.http.get(`${n.url}qr-code/search-update/${e}`,{params:_}))}catch(_){return yield n.commonSrv.showToast({color:"danger",message:""+_?.error?.message}),_}})()}static{this.\u0275fac=function(n){return new(n||h)(a.KVO(c.Qq),a.KVO(P.h),a.KVO(v.K))}}static{this.\u0275prov=a.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()},68896:(d,O,r)=>{r.d(O,{I:()=>h});var i=r(73308),c=r(2978),a=r(94761),D=r(82571),E=r(45312),m=r(26409),u=r(33607),P=r(14599),v=r(94934),s=r(56610);let h=(()=>{class t{constructor(){this.commonSrv=(0,c.WQX)(D.h),this.http=(0,c.WQX)(m.Qq),this.baseUrl=(0,c.WQX)(u.K),this.storageSrv=(0,c.WQX)(P.n),this.base_url=`${this.baseUrl.getOrigin()}${E.c.basePath}`}validateScanData(n){var _=this;return(0,i.A)(function*(){try{return yield(0,v.s)(_.http.post(`${_.base_url}scanner-data`,n))}catch(e){const M={message:_.commonSrv.getError("",e).message,color:"danger"};return yield _.commonSrv.showToast(M),e}})()}checkPermission(){return(0,i.A)(function*(){try{const{camera:n}=yield a.vi.requestPermissions();return"granted"===n}catch(n){return console.log(n),!1}})()}stopScan(){var n=this;return(0,i.A)(function*(){n.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(_=>{_.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,i.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var n=this;return(0,i.A)(function*(){try{if(!(yield n.checkPermission()))return void n.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"});yield n.prepareScanner();const{barcodes:e}=yield a.vi.scan();if(n.restoreUI(),e&&e.length>0)return e[0].displayValue;n.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(_){console.error("Erreur lors du scan",_),n.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{n.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(n){var _=this;return(0,i.A)(function*(){let e=new m.Nl;const{status:A=300,offset:M,limit:N,enable:f=!0,associatedCommercialId:L,startDate:U,endDate:g,customerName:l}=n;void 0!==M&&(e=e.append("offset",M)),N&&(e=e.append("limit",N)),A&&(e=e.append("status",A)),L&&(e=e.append("user.associatedCommercial._id",L)),e=e.append("enable",f),U&&g&&(e=e.append("startDate",new s.vh("fr").transform(U,"YYYY-MM-dd")),e=e.append("endDate",new s.vh("fr").transform(g,"YYYY-MM-dd"))),l&&(e=e.append("user.firstName",l));try{return yield(0,v.s)(_.http.get(`${_.base_url}scanner-data/volume-order-by-particular-client`,{params:e}))}catch(C){const K={message:_.commonSrv.getError("",C).message,color:"danger"};return yield _.commonSrv.showToast(K),C}})()}static{this.\u0275fac=function(_){return new(_||t)}}static{this.\u0275prov=c.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})()}}]);