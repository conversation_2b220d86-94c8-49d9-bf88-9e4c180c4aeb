$color-primary: #143c5d;
$color-secondary: #419CFB;
$color-third: hsl(0, 0%, 85%);
$color-fouth: #cfcfcf;
$color-fith: #dadada;
$color-six: #aeaeae;
$color-seven: #757474;
$color-eight: #1e1e1e;
$color-nine: #ffffff;
$color-ten: #858484;
$color-eleven: #fdfdfe;
$color-twelve: #ebebeb;
$color-thirtheen: #dedede;

//couleur retrouve sur le figma de ce projet
$color-fourtheen: #6D839D;
$color-fiftheen: #0B305C;
$color-sixtheen: #6D839D;
$color-seventheen: #303950;
$color-eighteen: #E4F1FF;
$color-nineteen: #e7eaef;
$color-twenty: #546e8d;
$color-twenty-one: hsl(210, 68%, 89%);


$background-page: #EEF2F9;
$font-regular: "Mont Regular";
$font-regular-italic: "Mont Regular Italic";
$font-medium-italic: "Mont Light Italic";
$font-medium: "Mont Light";
$font-demiBold: "Mont SemiBold";
$font-demiBold-italic: "Mont SemiBold Italic ";
$font-bold-italic: "Mont Bold Italic";
$font-bold: "Mont Bold";

$font-size-1: 2rem; //32px
$font-size-2: calc(70 * var(--res));
$font-size-3: 1.25rem; //20px
$font-size-4: 1.125rem; //18px
$font-size-5: calc(42 * var(--res));
$font-size-6: 0.875rem; //14px

$font-size-small: calc(37.5 * var(--res)); //12px
$font-size-extra-small: 0.625rem; //10px

$margin-1: 3rem; //48px
$margin-2: calc(112.5 * var(--res)); //32px
$margin-3: calc(75 * var(--res)); //24px
$margin-4: calc(50 * var(--res)); //16px
$margin-5: calc(37.5 * var(--res));

$padding-1: 3rem; //48px
$padding-2: calc(112.5 * var(--res)); // 2rem; //32px
$padding-3: calc(75 * var(--res)); //24px //1.5rem
$padding-4: calc(50 * var(--res));
$padding-5: calc(41 * var(--res));

$padding-small: calc(25 * var(--res)); //0.625rem; //10px
$margin-small: calc(31.25 * var(--res)); //10px
$margin-large: 3.625rem; //58px

$line-height-1: 1.75rem; //28px
$line-height-2: 1.125rem; //18px
$line-height-3: 1rem; //16px
$line-height-4: 0.875rem; //14px
$line-height-5: 0.75rem; //12px

* {
    margin: 0;
    padding: 0;
}

.header-md::after {
    background-image: none !important;
}

.modal {
    --border-radius: calc(60 * var(--res));
}

.input-group {
    margin-top: $margin-3;

    ion-item {
        --padding-start: 0;
        margin-bottom: $margin-4;
        --placeholder-color: $color-six;

        ion-input,
        ion-label,
        ion-select {
            font-family: $font-regular;
            font-weight: 400;
            font-size: calc(42 * var(--res));
            color: $color-eight;
        }

        ion-select {
            padding-top: calc(25 * var(--res));
            padding-bottom: calc(25 * var(--res));
        }
    }

    .password-input {
        margin-bottom: $margin-4;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom-color: var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));
        border-bottom-width: 0.55px;
        border-bottom-style: solid;

        ion-item {
            width: 90%;
            margin-bottom: 0;
            --border-color: transparent;
            --inner-border-width: 0px 0px 0px 0px;
            --border-style: none;
        }

        ion-checkbox {
            margin-top: 5%;
        }
    }
}

.bg-primary {
    color: $color-primary;
}

.message {
    margin-bottom: $margin-3;

    ion-title {
        font-weight: 800;
        font-size: $font-size-2;
        padding-bottom: calc(25 * var(--res));
    }

    ion-text {
        font-weight: 400;
        color: $color-seven;
        display: block;
        line-height: 1.5em !important;
    }
}

.btn {

    &--meduim {
        height: calc(130 * var(--res));
        font-size: calc(50 * var(--res));
        font-family: var(--mont-bold);

        ion-label {
            font-family: var(--mont-bold);
        }
    }

    &--small {
        height: calc(100 * var(--res));
    }

    &--round {
        --border-radius: 50%;
        padding-inline-start: 0;
        padding-inline-end: 0;
        --padding-start: 0;
        --padding-end: 0;
    }

    &--border-primary {
        --background: transparent;
        --background-activated: #c4e6d0;
        --border-color: var(--ion-color-primary);
        --border-style: solid;
        --border-width: 2px;
    }

    &--border-secondary {
        --border-width: 2px;
        --background: transparent;
        --background-activated: #ddabb2;
        --border-color: var(--ion-color-secondary);
        --border-style: solid;
    }

    &--upper {
        text-transform: uppercase;
    }
}

.scroller-container {
    * {
        overflow: hidden;
    }

    /* width */
    *::-webkit-scrollbar {
        width: 5px;
    }

    /* Track */
    *::-webkit-scrollbar-track {
        box-shadow: inset 0 0 5px $color-twenty;
        border-radius: 10px;
    }

    /* Handle */
    *::-webkit-scrollbar-thumb {
        background: $color-primary;
        border-radius: 10px;
    }

    /* Handle on hover */
    *::-webkit-scrollbar-thumb:hover {
        background: $color-primary;
    }
}

.bottom-sheet-content {
    background: $color-eleven;
    height: 100%;

    ion-header {
        ion-toolbar {
            ion-thumbnail {
                margin-left: $margin-small;
                background-color: $color-eight;
                --border-radius: 50%;
                --size: 23;
            }

            ion-label {
                font-family: $font-bold;
                font-size: calc(45 * var(--res));
                text-align: center;
                color: $color-seven;
            }
        }
    }

    ion-container {

        .container,
        #container {
            padding: $padding-3;
        }
    }
}

.font-size-5 {
    font-size: calc(42 * var(--res));
}