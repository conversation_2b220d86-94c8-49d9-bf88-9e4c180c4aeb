(self.webpackChunkapp=self.webpackChunkapp||[]).push([[132,1095],{18707:(H,x,o)=>{"use strict";o.d(x,{A:()=>t});var r=o(99987),a=o(2978),s=o(62049),m=o(77897);let t=(()=>{class _{constructor(e,u){this.translateService=e,this.modalCtrl=u}ngOnInit(){console.log(this.cart),this.confirmation=this.translateService.currentLang===r.T.French?"Annuler":"Cancel",this.confirmation1=this.translateService.currentLang===r.T.French?"Confirmer":"Validate",this.clientSer=this.translateService.currentLang===r.T.French?"Suppression de produit":"Delete product",this.text=this.translateService.currentLang===r.T.French?"Vous \xeates sur le point de supprimer ce produit de votre panier. \xeates vous sure de cela ? ":"You are about to remove this product from your basket. Are you sure? "}cancel(){return this.modalCtrl.dismiss("annuler")}confirm(){return this.modalCtrl.dismiss("valider")}static{this.\u0275fac=function(u){return new(u||_)(a.rXU(s.E),a.rXU(m.W3))}}static{this.\u0275cmp=a.VBU({type:_,selectors:[["delete-product"]],inputs:{cart:"cart"},decls:13,vars:3,consts:[["id","container",1,"scroller-container"],[1,"tilte-quantite"],[1,"contain-text"],[1,"btn-validate"],["fill","solid",1,"cancel",3,"click"],["fill","solid","color","primary",1,"yes",3,"click"]],template:function(u,M){1&u&&(a.j41(0,"section",0)(1,"div",1),a.nrm(2,"ion-title"),a.k0s(),a.j41(3,"div",2)(4,"label"),a.EFF(5),a.k0s()(),a.j41(6,"div",3)(7,"ion-button",4),a.bIt("click",function(){return M.cancel()}),a.j41(8,"ion-label"),a.EFF(9),a.k0s()(),a.j41(10,"ion-button",5),a.bIt("click",function(){return M.confirm()}),a.j41(11,"ion-label"),a.EFF(12),a.k0s()()()()),2&u&&(a.R7$(5),a.SpI(" ",M.text," "),a.R7$(4),a.SpI(" ",M.confirmation,""),a.R7$(3),a.SpI(" ",M.confirmation1,""))},dependencies:[m.Jm,m.he,m.BC],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:calc(75 * var(--res)) 0;background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]{margin-left:1em;text-align:center;line-height:1;margin-right:1em}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#6D839D #6D839D;font-size:14px;font-weight:600;text-align:center}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:center;margin-top:13px;gap:1em}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{--background: #fff;width:8rem;border-radius:50px;border:1px solid #143c5d;color:#143c5d}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{border-radius:50px}"]})}}return _})()},21635:(H,x,o)=>{"use strict";o.d(x,{X:()=>C});var r=o(73308),a=o(26409),s=o(37222),m=o(5141),t=o(2978),_=o(77575),F=o(81559),e=o(82571),u=o(77897),M=o(14599),N=o(56610),D=o(74657);function v(p,l){if(1&p){const d=t.RV6();t.j41(0,"ion-datetime",18,19),t.bIt("ionChange",function(){t.eBV(d);const b=t.sdS(1);return t.Njj(b.confirm(!0))}),t.nI1(2,"date"),t.k0s()}if(2&p){const d=t.XpG();t.FS9("min",t.i5U(2,1,d.currentDate,"yyyy-MM-dd"))}}function f(p,l){1&p&&t.nrm(0,"ion-spinner",20)}let C=(()=>{class p{ngOnInit(){}constructor(d,O,b,L,V){this.router=d,this.orderSrv=O,this.commonSrv=b,this.modalCtrl=L,this.storageService=V,this.title="",this.type="",this.isLoading=!1,this.currentDate=new Date,this.paiementForm=new s.gE({paiement:new s.MJ(null,"pay_cimencam"===this.type?s.k0.required:[]),monthlyPayment:new s.MJ(null,[s.k0.required,s.k0.min(0)]),startDate:new s.MJ(null,[s.k0.required])})}ionViewWillEnter(){this.storageService.getUserConnected()}closeModal(){this.modalCtrl.dismiss()}doPaiement(){var d=this;return(0,r.A)(function*(){d.isLoading=!0;let O=d.paiementForm.value,b={payment:{tel:O?.compte,mode:m.b7[d.type]?.mode,clientOption:m.b7[d.type].clientOption,...O},cart:d.cart,customerReference:O.customerReference,removals:JSON.parse(d.storageService.load("removals"))};d.orderSrv.response=yield d.orderSrv.create(b),d.isLoading=!1,d.orderSrv.response instanceof a.yz?d.commonSrv.showToast({color:"danger",message:d.orderSrv.response.message}):(d.modalCtrl.dismiss({...d.paiementForm.value}),d.router.navigate(["order/new/four-step"]))})()}verifyMounthNber(){}static{this.\u0275fac=function(O){return new(O||p)(t.rXU(_.Ix),t.rXU(F.Q),t.rXU(e.h),t.rXU(u.W3),t.rXU(M.n))}}static{this.\u0275cmp=t.VBU({type:p,selectors:[["app-paiement-on-credit"]],inputs:{title:"title",type:"type",amount:"amount",cart:"cart"},decls:48,vars:39,consts:[[1,"paiement-container","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close.svg"],["id","container"],[1,"input-group",3,"formGroup"],[1,"title"],["color","primary",1,"fbold"],["position","floating"],["formControlName","monthlyPayment","clearInput","",3,"readonly","keyup","onchange"],[1,"date-time"],["slot","start","src","assets/icons/calendar.svg",1,"ion-text-center"],["id","date",1,"ion-text-start",3,"placeholder","value"],["trigger","date","size","cover","side","top","alignment","center"],[1,"last-title"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled","click"],["name","bubbles",4,"ngIf"],["formControlName","startDate","presentation","date","locale","fr-FR",3,"min","ionChange"],["popoverDatetime",""],["name","bubbles"]],template:function(O,b){1&O&&(t.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),t.bIt("click",function(){return b.closeModal()}),t.nrm(4,"ion-img",3),t.k0s(),t.j41(5,"ion-label"),t.EFF(6),t.k0s()()(),t.j41(7,"ion-content")(8,"div",4)(9,"form",5)(10,"ion-label",6),t.EFF(11),t.nI1(12,"translate"),t.j41(13,"ion-text",7),t.EFF(14),t.nI1(15,"number"),t.k0s(),t.EFF(16),t.nI1(17,"translate"),t.k0s(),t.j41(18,"ion-item")(19,"ion-label",8),t.EFF(20),t.nI1(21,"translate"),t.k0s(),t.j41(22,"ion-input",9),t.bIt("keyup",function(){return b.verifyMounthNber()})("onchange",function(){return b.verifyMounthNber()}),t.k0s()(),t.j41(23,"ion-item",10),t.nrm(24,"ion-icon",11)(25,"ion-input",12),t.nI1(26,"translate"),t.nI1(27,"date"),t.j41(28,"ion-popover",13),t.DNE(29,v,3,4,"ng-template"),t.k0s()(),t.j41(30,"ion-label",8),t.EFF(31),t.nI1(32,"translate"),t.j41(33,"ion-text",7),t.EFF(34),t.k0s()(),t.j41(35,"ion-label",14),t.EFF(36),t.nI1(37,"translate"),t.j41(38,"ion-text",7),t.EFF(39," 500 000 FCFA"),t.k0s(),t.EFF(40),t.nI1(41,"translate"),t.k0s()(),t.j41(42,"div",15)(43,"ion-button",16),t.bIt("click",function(){return b.doPaiement()}),t.j41(44,"ion-label"),t.EFF(45),t.nI1(46,"translate"),t.k0s(),t.DNE(47,f,1,0,"ion-spinner",17),t.k0s()()()()()),2&O&&(t.R7$(6),t.JRh(b.title),t.R7$(3),t.Y8G("formGroup",b.paiementForm),t.R7$(2),t.SpI(" ",t.bMT(12,16,"bottom-sheet.payment.label")," "),t.R7$(3),t.SpI("",t.brH(15,18,b.amount,"","fr")," FCFA"),t.R7$(2),t.SpI(" ",t.bMT(17,22,"preposition.to")," Cadyst Grain ? "),t.R7$(4),t.JRh(t.bMT(21,24,"bottom-sheet.payment.monthlyPayment")),t.R7$(2),t.Y8G("readonly",b.isLoading),t.R7$(3),t.FS9("placeholder",t.bMT(26,26,"bottom-sheet.payment.startDatePaiement")),t.FS9("value",t.i5U(27,28,b.paiementForm.get("startDate").value,"dd/MM/YYYY")),t.R7$(6),t.SpI("",t.bMT(32,31,"bottom-sheet.payment.amount-per-month"),": "),t.R7$(3),t.SpI(" ",b.amount/(b.paiementForm.get("monthlyPayment").value||1)," FCA "),t.R7$(2),t.SpI(" ",t.bMT(37,33,"bottom-sheet.payment.limitation")," "),t.R7$(4),t.SpI(" ",t.bMT(41,35,"bottom-sheet.payment.plateform")," ? "),t.R7$(3),t.Y8G("disabled",b.paiementForm.invalid||b.isLoading),t.R7$(2),t.SpI(" ",t.bMT(46,37,"bottom-sheet.payment.send-validate-button-label"),""),t.R7$(2),t.Y8G("ngIf",b.isLoading))},dependencies:[s.qT,s.BC,s.cb,u.Jm,u.W9,u.A9,u.eU,u.iq,u.KW,u.$w,u.uz,u.he,u.w2,u.IO,u.Zx,u.ai,u.CF,u.Je,u.Gw,N.bT,s.j4,s.JD,N.QX,N.vh,D.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.paiement-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(25 * var(--res)) 0}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{display:block;text-align:center;margin-bottom:calc(37.5 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .last-title[_ngcontent-%COMP%]{display:block;text-align:center;margin-top:calc(112.5 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{padding:0 calc(75 * var(--res));margin-top:0;overflow:scroll;overflow-x:hidden;margin-bottom:calc(31.25 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{padding:calc(25 * var(--res));border-radius:50%;background:rgba(10,105,43,.35)}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{width:90%;margin-left:10%}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{padding-right:calc(75 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#143c5d}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{margin:0 calc(75 * var(--res))}"]})}}return p})()},67824:H=>{window,H.exports=function(x){var o={};function r(a){if(o[a])return o[a].exports;var s=o[a]={i:a,l:!1,exports:{}};return x[a].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=x,r.c=o,r.d=function(a,s,m){r.o(a,s)||Object.defineProperty(a,s,{enumerable:!0,get:m})},r.r=function(a){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},r.t=function(a,s){if(1&s&&(a=r(a)),8&s||4&s&&"object"==typeof a&&a&&a.__esModule)return a;var m=Object.create(null);if(r.r(m),Object.defineProperty(m,"default",{enumerable:!0,value:a}),2&s&&"string"!=typeof a)for(var t in a)r.d(m,t,function(_){return a[_]}.bind(null,t));return m},r.n=function(a){var s=a&&a.__esModule?function(){return a.default}:function(){return a};return r.d(s,"a",s),s},r.o=function(a,s){return Object.prototype.hasOwnProperty.call(a,s)},r.p="",r(r.s=16)}([function(x,o,r){"use strict";r.d(o,"j",function(){return _}),r.d(o,"i",function(){return F}),r.d(o,"f",function(){return e}),r.d(o,"k",function(){return u}),r.d(o,"e",function(){return M}),r.d(o,"h",function(){return N}),r.d(o,"b",function(){return D}),r.d(o,"p",function(){return v}),r.d(o,"s",function(){return f}),r.d(o,"d",function(){return C}),r.d(o,"t",function(){return p}),r.d(o,"l",function(){return l}),r.d(o,"g",function(){return d}),r.d(o,"o",function(){return O}),r.d(o,"q",function(){return b}),r.d(o,"r",function(){return L}),r.d(o,"m",function(){return V}),r.d(o,"c",function(){return K}),r.d(o,"a",function(){return Y}),r.d(o,"n",function(){return B}),r.d(o,"u",function(){return Z});var a=r(4),s=r.n(a);function t(h){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(R){return typeof R}:function(R){return R&&"function"==typeof Symbol&&R.constructor===Symbol&&R!==Symbol.prototype?"symbol":typeof R})(h)}function _(h){return null!=h&&"object"===t(h)}function F(h){var R=t(h);return null!=h&&("object"===R||"function"===R)}function e(h){var R=F(h)?Object.prototype.toString.call(h):"";return"[object Function]"===R||"[object GeneratorFunction]"===R}function u(h){return"string"==typeof h||!Array.isArray(h)&&_(h)&&"[object String]"===Object.prototype.toString.call(h)}function M(h){return!0===h||!1===h||"[object Boolean]"===Object.prototype.toString.call(h)}function N(h){return"[object Number]"===Object.prototype.toString.call(h)&&!isNaN(h)}function D(h){return String.fromCharCode.apply(null,new Uint8Array(h))}function v(h){for(var R=new ArrayBuffer(h.length),U=new Uint8Array(R),$=0,ee=h.length;$<ee;$+=1)U[$]=h.charCodeAt($);return R}function f(h){try{return atob(h.replace(/-/g,"+").replace(/_/g,"/"))}catch{throw new Error("Failed to decode base64 string")}}function C(){window.getSelection&&window.getSelection().empty?window.getSelection().empty():window.getSelection&&window.getSelection().removeAllRanges?window.getSelection().removeAllRanges():document.selection&&document.selection.empty()}function p(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(h){var R=16*Math.random()|0;return("x"===h?R:3&R|8).toString(16)})}function l(h){return u(h)&&/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(h)}function d(h){return u(h)&&/^([-\w]+\.){2}[-\w]+$/.test(h)}function O(h){return JSON.parse(JSON.stringify(h))}function b(h){return"string"==typeof h?h.replace(/\D/g,""):""}function L(h){return"string"==typeof h?h.replace(/\s/g,""):""}function V(h,R){h.setAttribute("autocomplete",R),h.setAttribute("autocompletetype",R),h.setAttribute("x-autocompletetype",R)}function K(h){if(!(h instanceof Set))return[];if(Array.from)return Array.from(h);var R=[];return h.forEach(function(U){return R.push(U)}),R}function Y(h,R){return h instanceof Set&&R instanceof Set&&h.size===R.size&&K(h).every(function(U){return R.has(U)})}function B(h){if(h instanceof Array&&h.length>0)for(var R,U,$=h.length;--$;)U=~~(Math.random()*($+1)),R=h[$],h[$]=h[U],h[U]=R}function Z(h){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).map(function(U){return U in h?function m(h,R,U){return R in h?Object.defineProperty(h,R,{value:U,enumerable:!0,configurable:!0,writable:!0}):h[R]=U,h}({},U,h[U]):{}}).reduce(function(U,$){return s()(U,$)},{})}},function(x,o,r){"use strict";function a(){var m=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!m.reason)throw new Error("Error reason required");if(!m.message)throw new Error("Error message required");this.name="MicroformError",this.reason=m.reason,this.message=m.message,this.informationLink=m.informationLink||"https://www.cybersource.com/products/payment_security/secure_acceptance",this.correlationId=m.correlationId,this.details=m.details}(a.prototype=Object.create(Error.prototype)).constructor=a;var s=a;o.a={CAPTURE_CONTEXT_INVALID:function(){return new s({reason:"CAPTURE_CONTEXT_INVALID",message:"You have not supplied a valid capture context."})},CAPTURE_CONTEXT_EXPIRED:function(){return new s({reason:"CAPTURE_CONTEXT_EXPIRED",message:"Your capture context has expired."})},BROWSER_ENCRYPTION_FAILED:function(){return new s({reason:"BROWSER_ENCRYPTION_FAILED",message:"In-browser encryption has failed."})},CREATE_FIELD_INVALID_FIELD_TYPE:function(m){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return new s({reason:"CREATE_FIELD_INVALID_FIELD_TYPE",message:'Invalid field "'.concat(m,'". Supported values are [ ').concat(t.join(", ")," ]."),details:{message:"Supported values are [ ".concat(t.join(", ")," ].")}})},CREATE_FIELD_DUPLICATE:function(m){return new s({reason:"CREATE_FIELD_DUPLICATE",message:'A "'.concat(m,'" field has already been created for this integration.')})},CREATE_TOKEN_NO_FIELDS_LOADED:function(){return new s({reason:"CREATE_TOKEN_NO_FIELDS_LOADED",message:"No fields have been loaded."})},CREATE_TOKEN_TIMEOUT:function(m){var t=new s({reason:"CREATE_TOKEN_TIMEOUT",message:"Create token request timed out.",details:{}});return m&&(t.details.fields=m),t},CREATE_TOKEN_XHR_ERROR:function(){return new s({reason:"CREATE_TOKEN_XHR_ERROR",message:"Could not create a token as the underlying XMLHttpRequest failed."})},CREATE_TOKEN_NO_FIELDS:function(){return new s({reason:"CREATE_TOKEN_NO_FIELDS",message:"No fields for collection."})},CREATE_TOKEN_VALIDATION_PARAMS:function(){return new s({reason:"CREATE_TOKEN_VALIDATION_PARAMS",message:"One or more params have a validation error.",details:(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map(function(t){return{message:"Validation error",location:t}})})},CREATE_TOKEN_VALIDATION_FIELDS:function(){return new s({reason:"CREATE_TOKEN_VALIDATION_FIELDS",message:"One or more fields have a validation error.",details:(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map(function(t){return{message:"Validation error",location:t}})})},CREATE_TOKEN_UNABLE_TO_START:function(){return new s({reason:"CREATE_TOKEN_UNABLE_TO_START",message:"Unable to trigger tokenization."})},FIELD_UNLOAD_ERROR:function(){return new s({reason:"FIELD_UNLOAD_ERROR",message:'Field is not loaded. Call "load" before attempting to unload.'})},FIELD_ALREADY_LOADED:function(){return new s({reason:"FIELD_ALREADY_LOADED",message:'Field has already been loaded. Call "unload" before trying to re-load.'})},FIELD_LOAD_CONTAINER_SELECTOR:function(){return new s({reason:"FIELD_LOAD_CONTAINER_SELECTOR",message:"Unable to locate a DOM node using the supplied selector string."})},FIELD_LOAD_INVALID_CONTAINER:function(){return new s({reason:"FIELD_LOAD_INVALID_CONTAINER",message:"Invalid argument supplied to load. Supported values are [HTMLElement or a CSS selector string]."})},FIELD_SUBSCRIBE_UNSUPPORTED_EVENT:function(){return new s({reason:"FIELD_SUBSCRIBE_UNSUPPORTED_EVENT",message:"Unsupported event name."})},FIELD_SUBSCRIBE_INVALID_CALLBACK:function(){return new s({reason:"FIELD_SUBSCRIBE_INVALID_CALLBACK",message:"The supplied callback is not a function."})},IFRAME_JWT_VALIDATION_FAILED:function(){return new s({reason:"IFRAME_JWT_VALIDATION_FAILED",message:"Invalid JWT."})},IFRAME_UNSUPPORTED_FIELD_TYPE:function(m){return new s({reason:"IFRAME_UNSUPPORTED_FIELD_TYPE",message:"Unsupported field type:  ".concat(m,".")})},MALFORMED_TOKENS_RESPONSE:function(){return new s({reason:"MALFORMED_TOKENS_RESPONSE",message:"JWT returned on tokens request does not match expected format."})},UNKNOWN_REASON:function(m){return new s({reason:"UNKNOWN_REASON",message:"Microform could not handle the recieved Flex API reason code.",details:m})},API_INTERNAL_ERROR:function(){return new s({reason:"API_INTERNAL_ERROR",message:"An unknown error has occurred."})},API_JSON_PARSER_ERROR:function(){return new s({reason:"API_JSON_PARSER_ERROR",message:"There was a problem processing your request."})},API_JWE_PARSING_ERROR:function(){return new s({reason:"API_JWE_PARSING_ERROR",message:"Unable create a token as the JWE contents could not be parsed."})},API_GATEWAY_ERROR:function(){return new s({reason:"API_GATEWAY_ERROR",message:"The API gateway rejected your request."})},CREATE_TOKEN_DECRYPTION:function(){return new s({reason:"CREATE_TOKEN_DECRYPTION",message:"Unable create a token as the JWE contents could not be decrypted."})},CREATE_TOKEN_PLATFORM_ERROR:function(){return new s({reason:"CREATE_TOKEN_PLATFORM_ERROR",message:"There was a problem communicating with the tokenization service."})},CREATE_TOKEN_CAPTURE_CONTEXT_USED_TOO_MANY_TIMES:function(){return new s({reason:"CREATE_TOKEN_CAPTURE_CONTEXT_USED_TOO_MANY_TIMES",message:"The supplied capture context has exceeded its usage limits. Please request another before trying again."})},CREATE_TOKEN_CAPTURE_CONTEXT_NOT_FOUND:function(){return new s({reason:"CREATE_TOKEN_CAPTURE_CONTEXT_NOT_FOUND",message:"The capture context could not be found. Please request another before trying again."})},CREATE_TOKEN_NO_DATA_SUBMITTED:function(){return new s({reason:"CREATE_TOKEN_NO_DATA_SUBMITTED",message:"At least one non-empty field must be supplied for tokenization."})},CREATE_TOKEN_CARD_TYPE_NOT_ALLOWED:function(m){var t=new s({reason:"CREATE_TOKEN_CARD_TYPE_NOT_ALLOWED",message:"Card Type is not in this session's allowedCardNetworks.",details:{message:"Validation error",location:"type"}});return m&&(t.details.message+=": supported values [".concat(m.join(", "),"]")),t},CREATE_TOKEN_VALIDATION_SERVERSIDE:function(m){var t=new s({reason:"CREATE_TOKEN_VALIDATION_SERVERSIDE",message:"Server-side validation has rejected your request.",details:[]}),_={"captureData.number":"number","captureData.type":"type","captureData.expirationMonth":"expirationMonth","captureData.expirationYear":"expirationYear","captureData.securityCode":"securityCode"};return Array.isArray(m)&&m.forEach(function(F){F.location&&_[F.location]&&t.details.push({location:_[F.location],message:F.message})}),t}}},function(x,o,r){"use strict";r.r(o),r.d(o,"AUTOCOMPLETE",function(){return a}),r.d(o,"BLUR",function(){return s}),r.d(o,"CHANGE",function(){return m}),r.d(o,"ERROR",function(){return t}),r.d(o,"FOCUS",function(){return _}),r.d(o,"INPUT_SUBMIT_REQUEST",function(){return F}),r.d(o,"LOAD",function(){return e}),r.d(o,"UNLOAD",function(){return u}),r.d(o,"UPDATE",function(){return M});var a="autocomplete",s="blur",m="change",t="error",_="focus",F="inputSubmitRequest",e="load",u="unload",M="update"},function(x,o,r){"use strict";r.r(o),r.d(o,"NUMBER",function(){return a}),r.d(o,"SECURITY_CODE",function(){return s});var a="number",s="securityCode"},function(x,o,r){"use strict";var a=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;x.exports=function(){try{if(!Object.assign)return!1;var _=new String("abc");if(_[5]="de","5"===Object.getOwnPropertyNames(_)[0])return!1;for(var F={},e=0;e<10;e++)F["_"+String.fromCharCode(e)]=e;if("0123456789"!==Object.getOwnPropertyNames(F).map(function(M){return F[M]}).join(""))return!1;var u={};return"abcdefghijklmnopqrst".split("").forEach(function(M){u[M]=M}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},u)).join("")}catch{return!1}}()?Object.assign:function(_,F){for(var e,u,M=function t(_){if(null==_)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(_)}(_),N=1;N<arguments.length;N++){for(var D in e=Object(arguments[N]))s.call(e,D)&&(M[D]=e[D]);if(a){u=a(e);for(var v=0;v<u.length;v++)m.call(e,u[v])&&(M[u[v]]=e[u[v]])}}return M}},function(x,o,r){"use strict";r.d(o,"a",function(){return a}),r.d(o,"b",function(){return s}),r.d(o,"d",function(){return m}),r.d(o,"c",function(){return t});var a="autocompleteEdit",s="clear",m="tokenize",t="error"},function(x,o,r){"use strict";r.d(o,"a",function(){return e});var a=r(0);function s(u){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(M){return typeof M}:function(M){return M&&"function"==typeof Symbol&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M})(u)}function e(u){var M=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},N={};return Object.keys(M).forEach(function(D){var v=M[D],f=u[D];if(void 0===f){if(v.required)throw function m(u){return new Error('Required parameter "'.concat(u,'" was not supplied'))}(D);if(void 0===v.default)return;f=v.default}if(s(f)!==v.type)throw function _(u,M,N){return new Error('Invalid value typeof "'.concat(M,'" supplied for parameter "').concat(u,'". Expected typeof "').concat(N,'".'))}(D,s(f),v.type);if(v.supportedValues&&v.supportedValues.indexOf(f)<0)throw function t(u,M,N){return new Error('Invalid value of "'.concat(M,'" for "').concat(u,'" parameter. Supported values are [').concat(N.join(", "),"]"))}(D,f,v.supportedValues);if(Object(a.f)(v.expression)&&!0!==v.expression.call(null,f,u,M))throw function F(u){return new Error('Failed expression evaluation for parameter "'.concat(u,'".'))}(D);N[D]=f}),N}},function(x,o,r){"use strict";r.d(o,"a",function(){return m});var a=r(0),s=/^http(s)?:\/\/.+(:\d+)?$/;function m(t,_,F){if(!s.test(F))throw new Error('Invalid origin "'.concat(F,'"'));var e={},u=_,M=t,N=F;function D(l,d){return!(!Object(a.k)(l)||!Object(a.f)(d)||(e[l]||(e[l]=[]),e[l].push(d),0))}function v(l,d){if(!Object(a.k)(l)||!Object(a.f)(d)||!e[l])return!1;for(var O=e[l],b=0;b<O.length;b+=1)if(O[b]===d)return O.splice(b,1),!0;return!1}function f(l,d,O){var L,V,b={event:l,channel:u,contentType:"application/x-telegram+json",data:d||{}};return"function"==typeof O&&(b.reply=(L=O,D(V=Object(a.t)(),function K(Y){v(V,K),L(Y)}),V)),b}function C(l){var b;if((N===(l.origin||l.originalEvent.origin)||"*"===N)&&Object(a.i)(l.data)&&"application/x-telegram+json"===l.data.contentType&&"event"in l.data&&!(!"application/x-telegram+json".indexOf(l.data.event)<0||void 0!==l.data.channel&&l.data.channel!==u&&"*"!==l.data.channel)){var O=[l.data.data||{}];l.data.reply&&O.push((b=l,function(L,V){var K=f(b.data.reply,L,V);b.source.postMessage(K,b.origin)})),function(b,L,V){if(e[b])for(var K=e[b].length,Y=0;Y<K;Y+=1)e[b][Y].apply(V,L)}(l.data.event,O,l)}}function p(){return e={},!0}return window.addEventListener("message",C,!1),{publish:function(l,d,O){var b=f(l,d,O);try{(M instanceof HTMLIFrameElement?M.contentWindow:M).postMessage(b,N)}catch{throw new Error("Frame communication failed.")}},subscribe:D,unsubscribe:v,unsubscribeAll:p,teardown:function(){window.removeEventListener("message",C,!1),p()}}}},,,,,,,,,function(x,o,r){"use strict";r.r(o),r.d(o,"Flex",function(){return ae});var a=r(1),s=r(0),m=r(3),t=r(5),_=r(6),F=r(4),e=r.n(F),u=r(2),M=r(7);function N(){var y={};return{subscribe:function(E,A){if(!Object(s.f)(A))throw new Error("handler not a function.");return y.hasOwnProperty.call(y,E)||(y[E]=[]),y[E].push(A),this},unsubscribe:function(E,A){if(!Object(s.f)(A))throw new Error("handler not a function.");if(y.hasOwnProperty.call(y,E)){var P=y[E].indexOf(A);P>=0&&y[E].splice(P,1)}return this},unsubscribeAll:function(){return y={},this},publish:function(E){var A=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return y.hasOwnProperty.call(y,E)&&y[E].forEach(function(P){return P(A)}),this}}}var D=["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","object","embed","iframe","*[tabindex]","*[contenteditable]"].join(","),v={base:"flex-microform",focused:"flex-microform-focused",valid:"flex-microform-valid",invalid:"flex-microform-invalid",disabled:"flex-microform-disabled",autocomplete:"flex-microform-autocomplete"};function C(y,E){for(var A=0;A<E.length;A++){var P=E[A];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(y,P.key,P)}}var p=Object.keys(v).map(function(y){return v[y]}),l=Object.keys(m).map(function(y){return m[y]}),d={placeholder:{type:"string",required:!1},title:{type:"string",required:!1},description:{type:"string",required:!1},"aria-label":{type:"string",required:!1},"aria-required":{type:"boolean",required:!1},disabled:{type:"boolean",required:!1},styles:{type:"object",required:!1}},O=e()({},d,{autoformat:{type:"boolean",required:!1}}),b=e()({},d,{maxLength:{type:"number",required:!1,supportedValues:[3,4]}}),L=Object.keys(u).map(function(y){return u[y]}),V="/microform/bundle/v".concat("2.1.1","/iframe.html");var Y=function(y){return L.indexOf(y)>=0};function B(y,E,A){Y(E)&&y.publish(E,A)}function Z(y,E,A,P){var S=this;E.subscribe(u.LOAD,function(T){return B(A,u.LOAD,T)}),E.subscribe(u.FOCUS,function(){Object(s.d)(),P.parentElement.classList.add(v.focused),B(A,u.FOCUS)}),E.subscribe(u.BLUR,function(T){(function(j,k){if(k&&("next"===k.focusShift||"previous"===k.focusShift)){var G=Array.prototype.slice.call(document.querySelectorAll(D)).filter(function(Q){var z=Q.getAttribute("tabindex"),J=Q.getBoundingClientRect();return(!z||parseInt(z,10)>=0)&&(J.width>0&&J.height>0||"AREA"===Q.tagName)}),X=G.indexOf(j)+("previous"===k.focusShift?-1:1);X>=0&&G[X].focus()}})(P,T),P.parentElement.classList.remove(v.focused),B(A,u.BLUR)}),E.subscribe(u.INPUT_SUBMIT_REQUEST,function(){B(A,u.INPUT_SUBMIT_REQUEST)}),E.subscribe(u.CHANGE,function(T){T.valid?(P.parentElement.classList.remove(v.invalid),P.parentElement.classList.add(v.valid)):T.couldBeValid?(P.parentElement.classList.remove(v.invalid),P.parentElement.classList.remove(v.valid)):(P.parentElement.classList.remove(v.valid),P.parentElement.classList.add(v.invalid)),B(A,u.CHANGE,T)}),E.subscribe(u.UPDATE,function(T){e()(S._config,T),!0===T.disabled?P.parentElement.classList.add(v.disabled):P.parentElement.classList.remove(v.disabled),B(A,u.UPDATE,T)}),E.subscribe(t.c,function(T){B(A,u.ERROR,T)}),y===m.NUMBER&&(E.subscribe(u.AUTOCOMPLETE,function(T){P.parentElement.classList.add(v.autocomplete),B(A,u.AUTOCOMPLETE,T)}),E.subscribe(t.a,function(){P.parentElement.classList.remove(v.autocomplete)}))}function h(y,E){switch(y){case m.NUMBER:return Object(_.a)(E,O);case m.SECURITY_CODE:return Object(_.a)(E,b);default:return{}}}var R=function(){function y(S,T,j,k){var G=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(function f(y,E){if(!(y instanceof E))throw new TypeError("Cannot call a class as a function")}(this,y),l.indexOf(j)<0)throw a.a.CREATE_FIELD_INVALID_FIELD_TYPE(j,l);this._type=j,this._config=h(this._type,G);var X=Object(s.t)();this._iframe=function K(y,E,A,P,S,T){var j={microformId:E,fieldId:A,jwt:y.jwt,microformConfig:P,config:S,fieldType:T};void 0===document.location.ancestorOrigins&&(j.refererOrigin=window.location.origin);var k=document.createElement("iframe");return k.setAttribute("hspace","0"),k.setAttribute("vspace","0"),k.setAttribute("frameborder","0"),k.setAttribute("scrolling","no"),k.setAttribute("allowtransparency","true"),k.setAttribute("marginwidth","0"),k.setAttribute("marginheight","0"),k.setAttribute("title","secure payment field"),k.style.overflow="hidden",k.style.position="relative",k.style.border="none",k.style.width="100%",k.style.height="100%",k.setAttribute("src","".concat(y.origin).concat(V,"?keyId=").concat(y.keyId,"#").concat(JSON.stringify(j))),k}(S,T,X,k,this._config,j),this._telegram=new M.a(this._iframe,X,S.origin),this._pubSub=new N,this._container=null,this._loaded=!1,Z.call(this,j,this._telegram,this._pubSub,this._iframe)}var E,A;return E=y,(A=[{key:"clear",value:function(){!0===this._loaded&&this._telegram.publish(t.b)}},{key:"dispose",value:function(){!0===this._loaded&&this.unload()}},{key:"focus",value:function(){!0===this._loaded&&this._telegram.publish(u.FOCUS)}},{key:"blur",value:function(){!0===this._loaded&&this._telegram.publish(u.BLUR)}},{key:"on",value:function(S,T){if(!Y(S))throw a.a.FIELD_SUBSCRIBE_UNSUPPORTED_EVENT();if(!Object(s.f)(T))throw a.a.FIELD_SUBSCRIBE_INVALID_CALLBACK();this._pubSub.subscribe(S,T)}},{key:"off",value:function(S,T){this._pubSub.unsubscribe(S,T)}},{key:"unload",value:function(){var S=this;if(!0!==this._loaded)throw a.a.FIELD_UNLOAD_ERROR();if(this._loaded=!1,this._iframe.onload=null,this._container){for(;this._container.lastChild;)this._container.removeChild(this._container.lastChild);p.forEach(function(T){return S._container.classList.remove(T)}),this._container=null}B(this._pubSub,u.UNLOAD)}},{key:"update",value:function(S){var T=h(this._type,S);Object.keys(T).length<1||(e()(this._config,T),!0===this._loaded&&this._telegram.publish(u.UPDATE,T))}},{key:"load",value:function(S){var T=this;if(!0===this._loaded)throw a.a.FIELD_ALREADY_LOADED();if(S instanceof Element)this._container=S;else{if("string"!=typeof S)throw a.a.FIELD_LOAD_INVALID_CONTAINER();var j=document.querySelector(S);if(!j)throw a.a.FIELD_LOAD_CONTAINER_SELECTOR();this._container=j}var k=function(G,X){try{var Q=G.indexOf("#"),z=JSON.parse(G.substr(Q+1));return e()(z.config,X),"".concat(G.substring(0,Q),"#").concat(JSON.stringify(z))}catch{return G}}(this._iframe.getAttribute("src"),this._config);for(this._iframe.setAttribute("src",k);this._container.lastChild;)this._container.removeChild(this._container.lastChild);this._iframe.onload=function(){T._loaded=!0},this._container.appendChild(this._iframe),this._container.classList.add(v.base)}}])&&C(E.prototype,A),y}();function $(y,E){for(var A=0;A<E.length;A++){var P=E[A];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(y,P.key,P)}}var ee=Object.keys(m).map(function(y){return m[y]}),re={styles:{type:"object",required:!1,default:{}}},ie=function(){function y(S){var T=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(function U(y,E){if(!(y instanceof E))throw new TypeError("Cannot call a class as a function")})(this,y),T=Object(_.a)(T,re),this._microformId=Object(s.t)(),this._flexConfig=S,this._baseConfig={styles:T.styles},this.fields={}}var E,A;return E=y,(A=[{key:"createField",value:function(S,T){if("string"!=typeof S||ee.indexOf(S)<0)throw a.a.CREATE_FIELD_INVALID_FIELD_TYPE(S,ee);if(void 0!==this.fields[S])throw a.a.CREATE_FIELD_DUPLICATE(S);return this.fields[S]=new R(this._flexConfig,this._microformId,S,this._baseConfig,T),this.fields[S]}},{key:"createToken",value:function(S,T){var j=this,k=[],G=Object.keys(this.fields).reduce(function(J,ne){return j.fields[ne]._loaded&&(k.push(ne),J.push(j.fields[ne]._telegram.publish)),J},[]);if(G.length<1)T(a.a.CREATE_TOKEN_NO_FIELDS_LOADED());else{Object(s.n)(G);for(var X={options:Object(s.u)(S,["type","expirationMonth","expirationYear"]),fields:k},Q=function(J){Object(s.g)(J)?T(null,J):T(J)},z=0;z<G.length;z+=1)try{return void G[z](t.d,X,Q)}catch{}T(a.a.CREATE_TOKEN_UNABLE_TO_START())}}},{key:"teardown",value:function(){var S=this;Object.keys(this.fields).forEach(function(T){return S.fields[T].dispose()}),this.fields={}}}])&&$(E.prototype,A),y}();function te(y,E){for(var A=0;A<E.length;A++){var P=E[A];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(y,P.key,P)}}var ae=function(){function y(S){(function(T,j){if(!(T instanceof j))throw new TypeError("Cannot call a class as a function")})(this,y),this._config=function(T){var j;try{if(!Object(s.g)(T))throw new Error;if(!(j=JSON.parse(Object(s.s)(T.split(".")[1]))).jti||!j.flx.origin)throw new Error}catch{throw a.a.CAPTURE_CONTEXT_INVALID()}return Object.freeze({jwt:T,keyId:j.flx.jwk.kid,origin:j.flx.origin})}(S)}var E,A;return E=y,(A=[{key:"microform",value:function(S){return new ie(this._config,S)}}])&&te(E.prototype,A),y}()}])},765:(H,x,o)=>{"use strict";o.d(x,{c:()=>ce});var r=o(73308),a=o(5141),s=o(26409),m=o(99987),t=o(88233),_=o(37222),F=o(67824),e=o(2978),u=o(77897);const M=["iframeContainer"];let N=(()=>{class g{constructor(n){this.modalCrl=n,this.context="",this.confirm=()=>this.modalCrl.dismiss(!0),this.close=()=>this.modalCrl.dismiss(!1)}ngOnInit(){}ngAfterViewInit(){this.iframeContainer.nativeElement.innerHTML=this.context,document.paForm.submit()}static{this.\u0275fac=function(i){return new(i||g)(e.rXU(u.W3))}}static{this.\u0275cmp=e.VBU({type:g,selectors:[["app-dialog-step-up-iframe-component"]],viewQuery:function(i,c){if(1&i&&e.GBs(M,7),2&i){let I;e.mGM(I=e.lsd())&&(c.iframeContainer=I.first)}},inputs:{context:"context"},decls:5,vars:0,consts:[[2,"height","48em","width","98%"],[2,"display","flex","justify-content","flex-end","cursor","pointer"],["name","close","size","large",2,"color","black",3,"click"],[2,"height","100%","width","100%"],["iframeContainer",""]],template:function(i,c){1&i&&(e.j41(0,"div",0)(1,"h1",1)(2,"ion-icon",2),e.bIt("click",function(){return c.close()}),e.k0s()(),e.nrm(3,"div",3,4),e.k0s())},dependencies:[u.iq]})}}return g})();var D=o(77575),v=o(81559),f=o(82571),C=o(43556),p=o(14599),l=o(62049),d=o(56610),O=o(71333),b=o(74657);const L=["collectionDataContainer"],V=["setUpIframe"];function K(g,w){1&g&&e.nrm(0,"app-progress-spinner")}function Y(g,w){1&g&&(e.j41(0,"ion-label",10)(1,"ion-text",13),e.EFF(2),e.nI1(3,"translate"),e.k0s()()),2&g&&(e.R7$(2),e.SpI(" ",e.bMT(3,1,"bottom-sheet.payment.error-balance-insufficient-amount")," "))}function B(g,w){1&g&&(e.j41(0,"ion-label",14),e.EFF(1),e.nI1(2,"translate"),e.j41(3,"ion-text",11),e.EFF(4," 500 000 FCFA"),e.k0s(),e.EFF(5),e.nI1(6,"translate"),e.k0s()),2&g&&(e.R7$(1),e.SpI(" ",e.bMT(2,2,"bottom-sheet.payment.limitation")," "),e.R7$(4),e.SpI(" ",e.bMT(6,4,"bottom-sheet.payment.plateform")," ? "))}function Z(g,w){1&g&&(e.j41(0,"ion-label",14),e.EFF(1),e.nI1(2,"translate"),e.j41(3,"ion-text",11),e.EFF(4," M2U"),e.k0s()()),2&g&&(e.R7$(1),e.SpI(" ",e.bMT(2,1,"bottom-sheet.payment.specification")," "))}function h(g,w){if(1&g&&(e.j41(0,"ion-select-option",19),e.EFF(1),e.k0s()),2&g){const n=w.$implicit;e.Y8G("value",n),e.R7$(1),e.SpI(" ",null==n?null:n.label," ")}}function R(g,w){if(1&g){const n=e.RV6();e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-input",21),e.bIt("keyup",function(c){e.eBV(n);const I=e.XpG(2);return e.Njj(I.removeSpecialCharacters(c,"codeCheck"))}),e.k0s()()}if(2&g){const n=e.XpG(2);e.R7$(2),e.JRh(e.bMT(3,2,"bottom-sheet.payment.ref-check")),e.R7$(2),e.Y8G("readonly",n.isLoading)}}function U(g,w){if(1&g&&(e.j41(0,"ion-select-option",19),e.EFF(1),e.k0s()),2&g){const n=w.$implicit;e.Y8G("value",n),e.R7$(1),e.SpI(" ",null==n?null:n.label," ")}}function $(g,w){if(1&g&&(e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-select",22),e.nI1(5,"translate"),e.DNE(6,U,2,2,"ion-select-option",18),e.k0s()()),2&g){const n=e.XpG(2);e.R7$(2),e.SpI(" ",e.bMT(3,3,"bottom-sheet.payment.select-bank")," "),e.R7$(2),e.FS9("cancelText",e.bMT(5,5,"button.cancel")),e.R7$(2),e.Y8G("ngForOf",n.banks)}}function ee(g,w){if(1&g){const n=e.RV6();e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-input",23),e.bIt("keyup",function(c){e.eBV(n);const I=e.XpG(2);return e.Njj(I.removeSpecialCharacters(c,"transactionRef"))}),e.k0s()()}if(2&g){const n=e.XpG(2);e.R7$(2),e.JRh(e.bMT(3,2,"bottom-sheet.payment.num-transaction")),e.R7$(2),e.Y8G("readonly",n.isLoading)}}function re(g,w){if(1&g&&(e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.nrm(4,"ion-input",24),e.k0s()),2&g){const n=e.XpG(2);e.R7$(2),e.JRh(e.bMT(3,3,"bottom-sheet.payment.amount-num")),e.R7$(2),e.Y8G("readonly",n.isLoading)("disabled",!0)}}function ie(g,w){if(1&g){const n=e.RV6();e.j41(0,"div")(1,"ion-label"),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-item")(5,"div",15),e.nrm(6,"ion-img",16),e.k0s(),e.j41(7,"ion-select",17),e.bIt("ionChange",function(c){e.eBV(n);const I=e.XpG();return e.Njj(I.verifyClientOption(c))}),e.nI1(8,"translate"),e.DNE(9,h,2,2,"ion-select-option",18),e.k0s()(),e.DNE(10,R,5,4,"ion-item",4),e.DNE(11,$,7,7,"ion-item",4),e.DNE(12,ee,5,4,"ion-item",4),e.DNE(13,re,5,5,"ion-item",4),e.k0s()}if(2&g){const n=e.XpG();e.R7$(2),e.JRh(e.bMT(3,7,"bottom-sheet.payment.mode")),e.R7$(5),e.FS9("cancelText",e.bMT(8,9,"button.cancel")),e.R7$(2),e.Y8G("ngForOf",n.clientOptions),e.R7$(1),e.Y8G("ngIf",1===(null==n.paiementForm||null==n.paiementForm.value||null==n.paiementForm.value.clientOption?null:n.paiementForm.value.clientOption.id)),e.R7$(1),e.Y8G("ngIf",2===(null==n.paiementForm||null==n.paiementForm.value||null==n.paiementForm.value.clientOption?null:n.paiementForm.value.clientOption.id)),e.R7$(1),e.Y8G("ngIf",2===(null==n.paiementForm||null==n.paiementForm.value||null==n.paiementForm.value.clientOption?null:n.paiementForm.value.clientOption.id)),e.R7$(1),e.Y8G("ngIf",2===(null==n.paiementForm||null==n.paiementForm.value||null==n.paiementForm.value.clientOption?null:n.paiementForm.value.clientOption.id))}}function te(g,w){if(1&g&&(e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.nrm(4,"ion-input",25),e.k0s()),2&g){const n=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,"bottom-sheet.payment.account-ref")),e.R7$(2),e.Y8G("readonly",n.isLoading)}}function ae(g,w){if(1&g&&(e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.nrm(4,"ion-input",26),e.k0s()),2&g){const n=e.XpG();e.R7$(2),e.JRh(e.bMT(3,3,"bottom-sheet.payment.walletNber")),e.R7$(2),e.Y8G("readonly",n.isLoading)("disabled",n.isValidWallet)}}function y(g,w){if(1&g&&(e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.nrm(4,"ion-input",27),e.k0s()),2&g){const n=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,"bottom-sheet.payment.otpInsert")),e.R7$(2),e.Y8G("readonly",n.isLoading)}}function E(g,w){if(1&g){const n=e.RV6();e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-input",28),e.bIt("keyup",function(c){e.eBV(n);const I=e.XpG();return e.Njj(I.removeSpecialCharacters(c,"customerReference"))}),e.k0s()()}if(2&g){const n=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,"bottom-sheet.payment.reference")),e.R7$(2),e.Y8G("readonly",n.isLoading)}}function A(g,w){if(1&g&&(e.j41(0,"ion-select-option",19),e.EFF(1),e.k0s()),2&g){const n=w.$implicit;e.FS9("value",n.code),e.R7$(1),e.SpI(" ",n.label," ")}}function P(g,w){if(1&g&&(e.j41(0,"ion-select-option",19),e.EFF(1),e.k0s()),2&g){const n=w.$implicit;e.FS9("value",n),e.R7$(1),e.SpI("",n," ")}}function S(g,w){if(1&g){const n=e.RV6();e.j41(0,"div",29)(1,"ion-text",13),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-item")(5,"ion-label",20),e.EFF(6),e.nI1(7,"translate"),e.k0s(),e.nrm(8,"ion-input",30),e.k0s(),e.j41(9,"ion-item")(10,"ion-label",20),e.EFF(11),e.nI1(12,"translate"),e.k0s(),e.j41(13,"ion-select",31),e.nI1(14,"translate"),e.DNE(15,A,2,2,"ion-select-option",18),e.k0s()(),e.j41(16,"ion-item")(17,"ion-label",20),e.EFF(18),e.nI1(19,"translate"),e.k0s(),e.j41(20,"ion-select",32),e.DNE(21,P,2,2,"ion-select-option",18),e.k0s()(),e.j41(22,"ion-text"),e.EFF(23),e.nI1(24,"translate"),e.k0s(),e.j41(25,"ion-item",33),e.nrm(26,"div",34),e.k0s(),e.j41(27,"ion-text",20),e.EFF(28),e.nI1(29,"translate"),e.k0s(),e.j41(30,"ion-item",33),e.nrm(31,"div",35),e.k0s(),e.j41(32,"ion-item",36)(33,"ion-label",20),e.EFF(34),e.nI1(35,"translate"),e.k0s(),e.j41(36,"ion-input",37),e.bIt("keyup",function(c){e.eBV(n);const I=e.XpG();return e.Njj(I.removeSpecialCharacters(c,"customerReference"))}),e.k0s()(),e.nrm(37,"div",null,38),e.k0s()}if(2&g){const n=e.XpG();e.R7$(2),e.SpI(" ",e.bMT(3,12,"bottom-sheet.payment.visa-label")," "),e.R7$(4),e.SpI("",e.bMT(7,14,"bottom-sheet.payment.visa-name")," "),e.R7$(2),e.Y8G("readonly",n.isLoading),e.R7$(3),e.SpI("",e.bMT(12,16,"bottom-sheet.payment.visa-mouth")," "),e.R7$(2),e.FS9("cancelText",e.bMT(14,18,"button.cancel")),e.R7$(2),e.Y8G("ngForOf",n.mounths),e.R7$(3),e.JRh(e.bMT(19,20,"bottom-sheet.payment.visa-Year")),e.R7$(3),e.Y8G("ngForOf",n.years),e.R7$(2),e.JRh(e.bMT(24,22,"bottom-sheet.payment.visa-cart-number")),e.R7$(5),e.JRh(e.bMT(29,24,"bottom-sheet.payment.visa-security-code")),e.R7$(6),e.JRh(e.bMT(35,26,"bottom-sheet.payment.reference")),e.R7$(2),e.Y8G("readonly",n.isLoading)}}function T(g,w){if(1&g){const n=e.RV6();e.j41(0,"ion-item")(1,"ion-label",20),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-input",39),e.bIt("keyup",function(c){e.eBV(n);const I=e.XpG();return e.Njj(I.removeSpecialCharacters(c,"otpRef"))}),e.k0s()()}if(2&g){const n=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,"bottom-sheet.payment.enter-otp")),e.R7$(2),e.Y8G("readonly",n.isLoading)}}function j(g,w){1&g&&e.nrm(0,"ion-spinner",43)}function k(g,w){if(1&g){const n=e.RV6();e.j41(0,"div",40)(1,"ion-button",41),e.bIt("click",function(){e.eBV(n);const c=e.XpG();return e.Njj(c.doPayment())}),e.j41(2,"ion-label"),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.DNE(5,j,1,0,"ion-spinner",42),e.k0s()()}if(2&g){const n=e.XpG();e.R7$(1),e.Y8G("disabled",n.paiementForm.invalid||n.isLoading),e.R7$(2),e.SpI(" ",e.bMT(4,3,n.balanceAccountOK?"bottom-sheet.payment.validate-button-label":"bottom-sheet.payment.send-in-validation-btn"),""),e.R7$(2),e.Y8G("ngIf",n.isLoading)}}function G(g,w){1&g&&e.nrm(0,"ion-spinner",43)}function X(g,w){if(1&g){const n=e.RV6();e.j41(0,"div",40)(1,"ion-button",41),e.bIt("click",function(){e.eBV(n);const c=e.XpG();return e.Njj(c.doUbaPayment())}),e.j41(2,"ion-label"),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.DNE(5,G,1,0,"ion-spinner",42),e.k0s()()}if(2&g){const n=e.XpG();e.R7$(1),e.Y8G("disabled",n.paiementForm.invalid||n.isLoading),e.R7$(2),e.SpI(" ",e.bMT(4,3,n.balanceAccountOK?"bottom-sheet.payment.validate-button-label":"bottom-sheet.payment.send-in-validation-btn"),""),e.R7$(2),e.Y8G("ngIf",n.isLoading)}}function Q(g,w){1&g&&e.nrm(0,"ion-spinner",43)}function z(g,w){if(1&g){const n=e.RV6();e.j41(0,"div",40)(1,"ion-button",41),e.bIt("click",function(){e.eBV(n);const c=e.XpG();return e.Njj(c.sendOtp(null==c.paiementForm||null==c.paiementForm.value?null:c.paiementForm.value.otpRef))}),e.j41(2,"ion-label"),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.DNE(5,Q,1,0,"ion-spinner",42),e.k0s()()}if(2&g){const n=e.XpG();e.R7$(1),e.Y8G("disabled",n.paiementForm.invalid||n.isLoading),e.R7$(2),e.SpI(" ",e.bMT(4,3,"bottom-sheet.payment.send-otp"),""),e.R7$(2),e.Y8G("ngIf",n.isLoading)}}function J(g,w){1&g&&e.nrm(0,"ion-spinner",43)}function ne(g,w){if(1&g){const n=e.RV6();e.j41(0,"div",40)(1,"ion-button",41),e.bIt("click",function(){e.eBV(n);const c=e.XpG();return e.Njj(c.sendWallet())}),e.j41(2,"ion-label"),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.DNE(5,J,1,0,"ion-spinner",42),e.k0s()()}if(2&g){const n=e.XpG();e.R7$(1),e.Y8G("disabled",n.isLoading),e.R7$(2),e.SpI(" ",e.bMT(4,3,"bottom-sheet.payment.send-wallet"),""),e.R7$(2),e.Y8G("ngIf",n.isLoading)}}const se=function(g,w){return[g,w]};let ce=(()=>{class g{constructor(n,i,c,I,W,q,oe){this.router=n,this.orderSrv=i,this.commonSrv=c,this.modalCtrl=I,this.companySrv=W,this.storageService=q,this.translateService=oe,this.title="",this.type="",this.isLoading=!1,this.paymentAction=t.ru,this.sendotp=!1,this.disable=!0,this.years=[],this.collectionDataUrl="",this.collectionDataAccessToken="",this.paiementForm=new _.gE({customerReference:new _.MJ("",[]),mounthExp:new _.MJ("",[]),yearExp:new _.MJ("",[]),name:new _.MJ("",[]),tel:new _.MJ("",[]),walletNumber:new _.MJ("",[]),otpWallet:new _.MJ("",[])}),this.clientOptions=[{id:0,label:this.translateService.currentLang===m.T.French?"Mon compte CIMENCAM":"Account CIMENCAM",icon:"/assets/icon/account-option.svg",destination:"account-management"},{id:1,label:this.translateService.currentLang===m.T.French?"R\xe9gler par ch\xe8que":"Check",icon:"/assets/icon/account-option.svg",destination:"claim-form"},{id:2,label:this.translateService.currentLang===m.T.French?"Versement \xe0 la Banque":"bank payment",icon:"/assets/icon/account-option.svg",destination:"contact-form"}],this.banks=[{id:0,label:"Afriland First Bank"},{id:1,label:"BICEC"},{id:2,label:"SCB Cameroun"},{id:3,label:"Soci\xe9t\xe9 G\xe9n\xe9rale Cameroun"},{id:4,label:"VISA"}],this.balanceAccountOK=!0}ngOnInit(){var n=this;return(0,r.A)(function*(){n.setValidators(),n.mounths=n.commonSrv.getMounth(),n.years=n.commonSrv.getyear(),n.storageService.getUserConnected(),yield n.initializeCardCapture()})()}onMessage(n){n.origin===this.captureContext.dataCollectionOrigin&&(this.collectionDataResponse=JSON.parse(n.data))}setValidators(){(this.type===t.ru.MOBILE_MONEY||this.type===t.ru.ORANGE_MONEY||this.type===t.ru.EU)&&this.paiementForm.addControl("tel",new _.MJ(null,[_.k0.required,_.k0.pattern("^[0-9]*$"),_.k0.pattern("^((\\+91-?)|0)?[0-9]{9}$")])),this.type===t.ru.MY_ACCOUNT&&this.paiementForm.addControl("clientOption",new _.MJ(null,[_.k0.required]))}removeSpecialCharacters(n,i){var c=this;return(0,r.A)(function*(){const I=c.paiementForm.get(i).value;if(I.length>25)return c.paiementForm.get(i).patchValue(I.substring(0,25)),c.paiementForm.get(i).updateValueAndValidity(),yield c.commonSrv.showToast({color:"warning",icon:"close",message:c.translateService.currentLang===m.T.French?"La taille de la valeur ne doit pas \xeatre sup\xe9rieure \xe0 25 caract\xe8res":"The length of the reference should not be greater than 25 characters"});c.paiementForm.get(i).patchValue(n.target.value.replace(/[`\xa3\xb5\xa7\xb2\xf9!@#$%^&*()+\=\[\]{};':"\\|,.<>\/?~]/,"")),c.paiementForm.get(i).updateValueAndValidity()})()}closeModal(){this.modalCtrl.dismiss()}verifyClientOption(n){var i=this;return(0,r.A)(function*(){i.isLoading=!0,0===n.detail.value.id&&(yield i.companySrv.getBalance(),i.paiementForm.removeControl("codeCheck"),i.paiementForm.removeControl("transactionRef"),i.paiementForm.removeControl("amount"),i.paiementForm.removeControl("bank")),1===n.detail.value.id&&(i.paiementForm.addControl("codeCheck",new _.MJ("",[_.k0.required])),i.paiementForm?.get("codeCheck")?.updateValueAndValidity(),i.paiementForm.removeControl("transactionRef"),i.paiementForm.removeControl("amount"),i.paiementForm.removeControl("bank")),2===n.detail.value.id&&(i.paiementForm.addControl("bank",new _.MJ("",[_.k0.required])),i.paiementForm?.get("bank")?.updateValueAndValidity(),i.paiementForm.addControl("transactionRef",new _.MJ("",[_.k0.required])),i.paiementForm?.get("transactionRef")?.updateValueAndValidity(),i.paiementForm.addControl("amount",new _.MJ(i.cart.amount.TTC,[_.k0.required])),i.paiementForm?.get("amount")?.updateValueAndValidity(),i.paiementForm.removeControl("codeCheck")),i.isLoading=!1})()}doPayment(){var n=this;return(0,r.A)(function*(){try{n.isLoading=!0;const i={payment:{mode:a.b7[n.type].mode,...n.paiementForm.value},cart:n.cart,customerReference:n.paiementForm.value.customerReference,removals:JSON.parse(n.storageService.load("removals"))};if(n.order=i,n.type===t.ru.VISA)return void n.proceed();(n.type===t.ru.MOBILE_MONEY||n.type===t.ru.ORANGE_MONEY||n.type===t.ru.MOBILE_MONEY)&&(i.payment.tel=n.paiementForm.value.tel),(n.type===t.ru.MY_ACCOUNT||n.type===t.ru.LOW_BALANCE)&&(i.payment.clientOption=n.paiementForm.value?.clientOption),n.orderSrv.response=yield n.orderSrv.create(i),n.type===n.paymentAction.AFRILAND&&(n.paiementForm.addControl("otpRef",new _.MJ(null,[_.k0.required])),n.orderSrv.response instanceof s.yz||(n.sendotp=!0,n.disable=!1)),n.type!==n.paymentAction.AFRILAND&&!(n.orderSrv.response instanceof s.yz)&&(n.modalCtrl.dismiss({...n.paiementForm.value}),n.router.navigate(["order/new/four-step"]))}catch(i){console.error(i)}finally{n.isLoading=!1}})()}doUbaPayment(){var n=this;return(0,r.A)(function*(){n.isLoading=!0,n.paiementForm.addControl("otpWallet",new _.MJ(null,[_.k0.required]));const i=n.paiementForm?.value?.otpWallet,c=n.paiementForm?.value?.walletNumber,I={OTP:i,SourceWallet:c,Amount:n.cart.amount.TTC,walletInfo:n.walletInfo},W={payment:{mode:a.b7[n.type].mode,...n.paiementForm?.value},cart:n.cart,customerReference:n.paiementForm.value.customerReference,removals:JSON.parse(n.storageService.load("removals"))};n.orderSrv.response=yield n.orderSrv.ubaPayment({paymentRequest:I,order:W}),n.orderSrv.response instanceof s.yz||(n.modalCtrl.dismiss({...n.paiementForm.value}),n.router.navigate(["order/new/four-step"])),n.isLoading=!1})()}sendWallet(){var n=this;return(0,r.A)(function*(){if(n.isLoading=!0,n.paiementForm.addControl("walletNumber",new _.MJ(null,[_.k0.required])),!new RegExp(/^XAF-[0-9]+-[A-Z]+$/,"g").test(n.paiementForm?.value?.walletNumber)&&n.paiementForm?.value?.walletNumber.includes("XAF"))return n.isLoading=!1,n.commonSrv.showToast({color:"warning",message:n.translateService.currentLang===m.T.French?"Num\xe9ro de wallet invalide":"Invalid wallet number"});n.sendwalletNber=!0,n.walletInfo=yield n.orderSrv.sendWallet({WalletNumber:n.paiementForm?.value?.walletNumber}),n.isLoading=!1,(302===n.walletInfo.status||n.walletInfo?.data?.WalletNumber)&&(n.isValidWallet=!0)})()}sendOtp(n){var i=this;return(0,r.A)(function*(){i.sendotp=!0;const c={orderId:i.orderSrv.response.data,OTP:n},I=yield i.orderSrv.sendOtp(c);201===I.status?(i.commonSrv.showToast({color:"warning",message:i.translateService.currentLang===m.T.French?"Payment effectue avec success":"Successful Payment"}),i.modalCtrl.dismiss({...i.paiementForm.value}),i.router.navigate(["order/new/four-step"])):i.commonSrv.showToast({color:"warning",message:""+I.message})})()}proceed(){var n=this;return(0,r.A)(function*(){if(yield n.customerReferenceValidator(n.paiementForm))try{n.isLoading=!0,n.transientTokenRes=yield n.payButton(),yield n.initSetupPayerAuth()}catch(i){console.error(i)}finally{n.isLoading=!1}})()}initializeCardCapture(){var n=this;return(0,r.A)(function*(){if(n.type===t.ru.VISA){n.isLoading=!0;try{n.captureContext=yield n.orderSrv.getCardToken();const i=new F.Flex(n.captureContext?.value);n.microform=i.microform({});const c=n.microform?.createField("number",{placeholder:"Enter card number"}),I=n.microform?.createField("securityCode",{placeholder:"Enter security code"});c?.load("#number-container"),I?.load("#securityCode-container")}catch(i){console.error("Error:",i),n.handleError(i),n.modalCtrl.dismiss()}finally{n.isLoading=!1}}})()}initSetupPayerAuth(){var n=this;return(0,r.A)(function*(){n.isLoading=!0;try{if(n.setupResult??=(yield n.orderSrv.setupPayerAuthentication(n.transientTokenRes,n.order)),!("status"in n.setupResult)||"COMPLETED"!=n.setupResult.status)return n.setupResult=null,yield n.handlePaymentError();yield n.submitCollectionDataForm(),yield n.waitForCollectionDataResponse(),n.collectionDataResponse&&!0===n.collectionDataResponse.Status&&n.openConfirmModal()}catch(i){console.error("payment error",i),n.handlePaymentError()}finally{n.isLoading=!1}})()}submitCollectionDataForm(){var n=this;return(0,r.A)(function*(){n.collectionDataUrl=n.setupResult?.consumerAuthenticationInformation?.deviceDataCollectionUrl,n.collectionDataAccessToken=n.setupResult?.consumerAuthenticationInformation?.accessToken;const i=document.createElement("iframe");i.id="cardinal_collection_iframe",i.name="collectionIframe",i.height="10",i.width="10",i.style.display="none";const c=document.createElement("form");c.name="collectionDataForm",c.id="cardinal_collection_form",c.method="POST",c.target="collectionIframe",c.action=n.collectionDataUrl;const I=document.createElement("input");I.id="cardinal_collection_form_input",I.type="hidden",I.name="JWT",I.value=n.collectionDataAccessToken,c.appendChild(I),n.collectionDataContainer.nativeElement.innerHTML="",n.collectionDataContainer.nativeElement.appendChild(i),n.collectionDataContainer.nativeElement.appendChild(c);const W=n.collectionDataContainer.nativeElement.querySelector("form");W?W.submit():console.error("Form element not found")})()}delay(n){return(0,r.A)(function*(){return new Promise(i=>setTimeout(i,n))})()}waitForCollectionDataResponse(){var n=this;return(0,r.A)(function*(){let i=0;for(;!n.collectionDataResponse&&i<=30;)yield n.delay(1e3),i+=1})()}openConfirmModal(){var n=this;return(0,r.A)(function*(){try{n.isLoading=!0;const i={transientTokenJwt:n.transientTokenRes,clientReferenceCode:n.setupResult?.clientReferenceInformation.code,referenceId:n.collectionDataResponse?.SessionId};n.order.clientReferenceCode=n.setupResult?.clientReferenceInformation.code,n.isLoading=!0,n.enrollResult=yield n.orderSrv.authorizationWithPAEnroll(n.order,i);const c={PENDING_AUTHENTICATION:()=>n.setUpIframe(),AUTHORIZED:(W=(0,r.A)(function*(){n.isLoading=!0,yield n.commonSrv.showToast({color:"success",icon:"close",message:n.translateService.currentLang===m.T.French?"Votre paiement Visa a \xe9t\xe9 effectu\xe9 avec succ\xe8s. Paiement effectu\xe9":"Your Visa payment was successful."}),n.modalCtrl.dismiss(),n.router.navigate(["/order/new/four-step"]),n.isLoading=!1}),function(){return W.apply(this,arguments)}),AUTHENTICATION_FAILED:()=>{n.handlePaymentError(),n.modalCtrl.dismiss(!0)}},I=c[n.enrollResult?.status];I?I():c?.AUTHENTICATION_FAILED?.()}catch(i){return n.handlePaymentError(),console.error("error ::: ",i),i}var W})()}setUpIframe(){this.isLoading=!0;const{stepUpUrl:n,accessToken:i}=this.enrollResult.consumerAuthenticationInformation,c=this.getFormHtml(n,i);this.createStepUpIframeModal(c)}getFormHtml(n,i){return`<iframe name="step-up-iframe" height="100%" width="100%"></iframe>\n      <form name="paForm" id="step-up-form" target="step-up-iframe" method="post" action="${n}">\n        <input type="hidden" name="JWT" value="${i}" />\n        <input type="hidden" name="MD" value="${this.setupResult.transientTokenId}" />\n      </form>`}createStepUpIframeModal(n){var i=this;return(0,r.A)(function*(){i.modalCtrl.dismiss();const c=yield i.modalCtrl.create({component:N,initialBreakpoint:.8,cssClass:"modal",breakpoints:[0,.8,.85,1],mode:"ios",componentProps:{context:n,closeOnBackdropClick:!1,closeOnEsc:!1}});return c.onDidDismiss().then(function(){var I=(0,r.A)(function*(W){try{if(i.isLoading=!0,i.orderSrv.response=yield i.orderSrv.checkIfOrderExist(i.setupResult.transientTokenId),200!=i.orderSrv.response.status)return void(yield i.clearAndShowToast());yield i.commonSrv.showToast({color:"success",icon:"close",message:i.translateService.currentLang===m.T.French?"Votre paiement Visa a \xe9t\xe9 effectu\xe9 avec succ\xe8s Paiement effectu\xe9!!.":"Your Visa payment has been processed successfully Payment processed!."}),i.router.navigate(["order/new/four-step"])}catch(q){console.error(q)}});return function(W){return I.apply(this,arguments)}}()),yield c.present()})()}clearAndShowToast(){var n=this;return(0,r.A)(function*(){n.setupResult=null,n.enrollResult=null,n.collectionDataResponse=null,yield n.commonSrv.showToast({color:"warning",icon:"close",message:n.translateService.currentLang===m.T.French?"Pour relancer le processus de paiement, cliquez sur le bouton VALIDER TRANSACTION.":"To restart the payment process, click on the VALIDATE TRANSACTION button."})})()}payButton(){var n=this;return(0,r.A)(function*(){try{n.isLoading=!0;const i={expirationMonth:n.paiementForm.get("mounthExp")?.value,expirationYear:n.paiementForm.get("yearExp")?.value};return n.transientToken??=(yield n.createTransientToken(n.microform,i)),n.transientToken}catch(i){return console.error("\nException on calling the API : "+i),i}})()}createTransientToken(n,i){return(0,r.A)(function*(){try{return new Promise((c,I)=>{n.createToken(i,function(){var W=(0,r.A)(function*(q,oe,le){q?(console.error(q),I(q)):c(oe)});return function(q,oe,le){return W.apply(this,arguments)}}())})}catch(c){throw console.error("\nException on calling the API : "+c),c}})()}handleError(n){var i=this;return(0,r.A)(function*(){yield i.commonSrv.showToast({color:"warning",icon:"close",message:i.translateService.currentLang===m.T.French?"Erreur lors de l initialisation du contexte Veuillez v\xe9rifier votre connexion internet":"Error initializing context Please check your internet connection"}),console.error("Initialization error:",n)})()}handlePaymentError(){var n=this;return(0,r.A)(function*(){yield n.commonSrv.showToast({color:"warning",icon:"close",message:n.translateService.currentLang===m.T.French?"Votre paiement Visa n a pas pu \xeatre effectu\xe9e. Veuillez v\xe9rifier votre connexion internet et r\xe9essayer.":"Your Visa payment could not be completed. Please check your internet connection and try again."})})()}customerReferenceValidator(n){var i=this;return(0,r.A)(function*(){return!["mounthExp","yearExp","name"].some(I=>""===n.get(I).value)||(yield i.commonSrv.showToast({color:"warning",icon:"close",message:i.translateService.currentLang===m.T.French?"Les champs mounthExp, yearExp , customerReference et name ne doivent pas \xeatre vides.":"The fields mounthExp, yearExp, customerReference and name must not be empty."}),!1)})()}static{this.\u0275fac=function(i){return new(i||g)(e.rXU(D.Ix),e.rXU(v.Q),e.rXU(f.h),e.rXU(u.W3),e.rXU(C.B),e.rXU(p.n),e.rXU(l.E))}}static{this.\u0275cmp=e.VBU({type:g,selectors:[["app-paiement-validation"]],viewQuery:function(i,c){if(1&i&&(e.GBs(L,5),e.GBs(V,5)),2&i){let I;e.mGM(I=e.lsd())&&(c.collectionDataContainer=I.first),e.mGM(I=e.lsd())&&(c.setUpIFrame=I.first)}},hostBindings:function(i,c){1&i&&e.bIt("message",function(W){return c.onMessage(W)},!1,e.tSv)},inputs:{title:"title",type:"type",amount:"amount",cart:"cart"},decls:33,vars:31,consts:[[1,"paiement-container","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close.svg"],[4,"ngIf"],["id","container"],["class","title",4,"ngIf"],[1,"input-group",3,"formGroup"],["class","last-title",4,"ngIf"],["class","visa-input",4,"ngIf"],[1,"title"],["color","primary",1,"fbold"],["class","btn-validate",4,"ngIf"],["color","secondary",1,"fbold"],[1,"last-title"],[1,"icon"],["src","/assets/icons/institution.svg"],["mode","ios","formControlName","clientOption","interface","action-sheet",3,"cancelText","ionChange"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["position","floating"],["formControlName","codeCheck","clearInput","",3,"readonly","keyup"],["mode","ios","formControlName","bank","interface","action-sheet",3,"cancelText"],["formControlName","transactionRef","clearInput","",3,"readonly","keyup"],["formControlName","amount","clearInput","",3,"readonly","disabled"],["type","tel","min","0","formControlName","tel","clearInput","",3,"readonly"],["formControlName","walletNumber","clearInput","",3,"readonly","disabled"],["type","number","formControlName","otpWallet","clearInput","",3,"readonly"],["formControlName","customerReference","clearInput","",3,"readonly","keyup"],[1,"visa-input"],["type","text","formControlName","name","clearInput","",3,"readonly"],["mode","ios","formControlName","mounthExp","interface","action-sheet",3,"cancelText"],["mode","ios","formControlName","yearExp","cancelText","Annuler","interface","action-sheet",1,"select"],[1,"input-cart"],["id","number-container",1,"form-control"],["id","securityCode-container",1,"form-control"],[1,"ref"],["formControlName","customerReference","maxlength","25","clearInput","",3,"readonly","keyup"],["collectionDataContainer",""],["formControlName","otpRef","clearInput","",3,"readonly","keyup"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled","click"],["name","bubbles",4,"ngIf"],["name","bubbles"]],template:function(i,c){1&i&&(e.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),e.bIt("click",function(){return c.closeModal()}),e.nrm(4,"ion-img",3),e.k0s(),e.j41(5,"ion-label"),e.EFF(6),e.k0s()()(),e.DNE(7,K,1,0,"app-progress-spinner",4),e.j41(8,"ion-content")(9,"div",5),e.DNE(10,Y,4,3,"ion-label",6),e.j41(11,"form",7),e.DNE(12,B,7,6,"ion-label",8),e.DNE(13,Z,5,3,"ion-label",8),e.DNE(14,ie,14,11,"div",4),e.DNE(15,te,5,4,"ion-item",4),e.DNE(16,ae,5,5,"ion-item",4),e.DNE(17,y,5,4,"ion-item",4),e.DNE(18,E,5,4,"ion-item",4),e.DNE(19,S,39,28,"div",9),e.j41(20,"ion-label",10),e.EFF(21),e.nI1(22,"translate"),e.j41(23,"ion-text",11),e.EFF(24),e.nI1(25,"number"),e.k0s(),e.EFF(26),e.nI1(27,"translate"),e.k0s(),e.DNE(28,T,5,4,"ion-item",4),e.k0s(),e.DNE(29,k,6,5,"div",12),e.DNE(30,X,6,5,"div",12),e.DNE(31,z,6,5,"div",12),e.DNE(32,ne,6,5,"div",12),e.k0s()()()),2&i&&(e.R7$(6),e.JRh(c.title),e.R7$(1),e.Y8G("ngIf",c.isLoading),e.R7$(3),e.Y8G("ngIf",!c.balanceAccountOK),e.R7$(1),e.Y8G("formGroup",c.paiementForm),e.R7$(1),e.Y8G("ngIf",c.type!==c.paymentAction.MY_ACCOUNT&&c.type!==c.paymentAction.VISA&&c.type!==c.paymentAction.M2U&&c.type!==c.paymentAction.LOW_BALANCE),e.R7$(1),e.Y8G("ngIf",c.type===c.paymentAction.M2U),e.R7$(1),e.Y8G("ngIf",c.type===c.paymentAction.MY_ACCOUNT),e.R7$(1),e.Y8G("ngIf",c.type===c.paymentAction.ORANGE_MONEY||c.type===c.paymentAction.MOBILE_MONEY||c.type===c.paymentAction.EU),e.R7$(1),e.Y8G("ngIf",c.type===c.paymentAction.M2U),e.R7$(1),e.Y8G("ngIf",c.type===c.paymentAction.M2U&&c.isValidWallet),e.R7$(1),e.Y8G("ngIf",!e.l_i(28,se,c.paymentAction.M2U.toString(),c.paymentAction.VISA).includes(c.type)||c.isValidWallet),e.R7$(1),e.Y8G("ngIf",c.type===c.paymentAction.VISA),e.R7$(2),e.SpI(" ",e.bMT(22,20,"bottom-sheet.payment.label")," "),e.R7$(3),e.SpI("",e.brH(25,22,c.amount,"","fr")," FCFA"),e.R7$(2),e.SpI(" ",e.bMT(27,26,"preposition.to")," Cadyst Grain ? "),e.R7$(2),e.Y8G("ngIf",c.sendotp),e.R7$(1),e.Y8G("ngIf",c.disable&&c.type!==c.paymentAction.M2U),e.R7$(1),e.Y8G("ngIf",c.disable&&c.isValidWallet),e.R7$(1),e.Y8G("ngIf",c.sendotp),e.R7$(1),e.Y8G("ngIf",c.type===c.paymentAction.M2U&&!c.isValidWallet))},dependencies:[_.qT,_.BC,_.cb,_.tU,u.Jm,u.W9,u.eU,u.KW,u.$w,u.uz,u.he,u.Nm,u.Ip,u.w2,u.IO,u.Zx,u.ai,u.su,u.Je,u.Gw,d.Sq,d.bT,O._,_.j4,_.JD,d.QX,b.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.paiement-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(25 * var(--res)) 0;height:100%;overflow:hidden}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{display:block;text-align:center;margin-bottom:calc(37.5 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .last-title[_ngcontent-%COMP%]{display:block;text-align:center}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{padding:0 calc(75 * var(--res));margin-top:0;overflow:hidden;overflow-x:hidden;margin-bottom:calc(31.25 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{padding:calc(25 * var(--res));border-radius:50%;background:rgba(10,105,43,.35)}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .visa-input[_ngcontent-%COMP%]{margin-bottom:1em}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .visa-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{margin-bottom:1em}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .visa-input[_ngcontent-%COMP%]   .select[_ngcontent-%COMP%]{text-align:left}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .visa-input[_ngcontent-%COMP%]   .input-cart[_ngcontent-%COMP%]{height:2em;width:100%;border-bottom:1px solid rgb(218,218,214)}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .visa-input[_ngcontent-%COMP%]   .input-cart[_ngcontent-%COMP%]   #number-container[_ngcontent-%COMP%]{height:2em}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{padding-right:calc(75 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#143c5d}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{margin:0 calc(75 * var(--res))}.paiement-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .btn-otp[_ngcontent-%COMP%]{margin-top:5rem}"]})}}return g})()},61095:(H,x,o)=>{"use strict";o.d(x,{l:()=>f});var r=o(73308),a=o(94934),s=o(56610),m=o(45312),t=o(99987),_=o(26409),F=o(37222),e=o(73014),u=o(2978),M=o(82571),N=o(77897),D=o(33607),v=o(62049);let f=(()=>{class C{constructor(l,d,O,b,L){this.http=l,this.commonSrv=d,this.modalCtrl=O,this.baseUrlService=b,this.translateService=L,this.schedules=[],this.removals=[],this.remainingQtyTonne=0,this.remainingQtyTonneInStock=0,this.dailyCeiling=300,this.maxRemovalLine=10,this.minCapacityToRemove=.1,this.datePipe=new s.vh("fr"),this.scheduleForm=new F.gE({quantity:new F.MJ(0,[F.k0.required,F.k0.min(.1)]),tonneQuantity:new F.MJ(0,[F.k0.required,F.k0.min(.025)]),collection_date:new F.MJ("",[F.k0.required])}),this.url=this.baseUrlService.getOrigin()+m.c.basePath}editSchedule(){var l=this;return(0,r.A)(function*(){if(!l.remainingQtyTonne)return l.commonSrv.showToast({color:"warning",message:l.translateService.currentLang===t.T.French?"Renseignez au pr\xe9alable les quantit\xe9s":"Fill in the quantities as required"});const d=l.scheduleForm.get("quantity").value;return Number.isInteger(d)?void 0:(l.commonSrv.showToast({color:"warning",message:l.translateService.currentLang===t.T.French?"Veuillez renseigner une valeur de sac enti\xe8re":"Please enter a integer value of sac"}),void l.scheduleForm.patchValue({quantity:null,tonneQuantity:null}))})()}getScheduleDates(){const l=new Date,d=new Date(this.removalObject?.end)>l?new Date(new Date(this.removalObject?.end).setDate(new Date(this.removalObject?.end).getDate()+1)):this.removalObject?.start?new Date(this.removalObject?.start):new Date,O=this.remainingQtyTonneInStock?new Date(this.removalObject?.start)>l?new Date(this.removalObject?.start):new Date:d,b=new Date;return{minDate:O,maxDate:this.remainingQtyTonneInStock?new Date(this.removalObject.end):new Date(b.setMonth(b?.getMonth()+3))}}closeForm(){this.modalCtrl.dismiss()}getTotalTonnage(){let d=this.schedules.map(O=>O.nbrTruck?O?.nbrTonnage*O?.nbrTruck:+O.nbrTonnage).reduce((O,b)=>O+b,0);return Math.round(1e3*d)/1e3}getRemovals(){this?.cartItem?.quantity>0?this.removals?.filter((l,d)=>l.itemId===this.cartItem?.product?._id?(this.schedules=l.schedules,this.schedulesTypeSelected=l.removalType,this.currentRemoval=l,this.removals.splice(d,1)):this.removals):this.schedules=[]}verifyDailyCeiling(l,d){if(l=new s.vh("fr").transform(l,"dd/MM/yy"),d>this.dailyCeiling)return this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===t.T.French?"Vous avez exc\xe9d\xe9 votre capacit\xe9 de tonnage journali\xe8re qui est de 300 tonnes":"You have exceeded your daily tonnage capacity which is 300 tonnes"}),!0;let O=0;return this.schedules.forEach(b=>{new s.vh("fr").transform(b.removalDate,"dd/MM/yy")===l&&(O+=parseInt(`${this.schedulesTypeSelected===e.JN.perDate?b.nbrTonnage:b.nbrTonnage*b.nbrTruck}`))}),this.dailyCeiling<O+parseInt(`${d}`)&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===t.T.French?"Vous avez exc\xe9d\xe9 votre capacit\xe9 de tonnage pour ce jour qui est de 300 tonnes, s\xe9lectionnez un autre jour.":"You have exceeded your tonnage capacity for that day which is 300 tonnes, select another day."}),!0)}saveSchedule(l){if(this?.schedules?.length>=this?.maxRemovalLine)return this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===t.T.French?`Vous ne pouvez pas pas planifier plus de ${this?.maxRemovalLine} lignes d'enl\xe8vements`:`You have exceeded your daily tonnage capacity ${this?.maxRemovalLine} removal lines`}),new Error("maxRemovalLine");this.schedules.push(l),this.remainingQtyTonne-=this.schedulesTypeSelected===e.JN.perDate?l?.nbrTonnage:parseFloat((l?.nbrTonnage*l?.nbrTruck).toFixed(3)),this.scheduleForm.get("collection_date").reset(),this.commonSrv.showToast({icon:"checkmark-circle-outline",color:"success",message:this.translateService.currentLang===t.T.French?"Vous avez ajout\xe9 une ligne \xe0 votre planification":"You have added a line for your planning"}),0===this.remainingQtyTonne&&(this.isGoodPlannify=!0),this.remainingQtyTonne=Math.round(1e3*this.remainingQtyTonne)/1e3,this.closeForm()}getPlanificationByStores(l){var d=this;return(0,r.A)(function*(){try{let O=new _.Nl;const{storeId:b,today:L}=l;return b&&(O=O.append("storeId",b)),L&&(O=O.append("today",L)),yield(0,a.s)(d.http.get(`${d.url}planifications/custom`,{params:O}))}catch(O){const L={message:d.commonSrv.getError("",O).message,color:"danger"};return yield d.commonSrv.showToast(L),O}})()}getRemovalsObject(){var l=this;return(0,r.A)(function*(){try{const d={storeId:l.cart.store._id,today:(new Date).valueOf()};if(l.availableRemovals=yield l.getPlanificationByStores(d),!(l.availableRemovals instanceof Array)){const b=l.commonSrv.getError("Une erreur s'est produite",l.availableRemovals);throw new Error(JSON.stringify({title:b?.data,message:b?.message}))}const O=l.availableRemovals?.find(b=>b?.data?.product?._id===l.cartItem?.product?._id);if(!O)throw new Error(JSON.stringify({title:l.translateService.currentLang===t.T.French?"Indisponible":"Unavailable",message:l.translateService.currentLang===t.T.French?"Aucune planification en production n'est disponible pour ce produit. ":"No production planning is available for this product."}));return l.removalObject=O.data}catch(d){return d}})()}verifyCanRemoval(l){return!!(this.remainingQtyTonneInStock&&l>this.remainingQtyTonneInStock)&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===t.T.French?`Vous avez exc\xe9d\xe9 la quantit\xe9 en stock (${this.remainingQtyTonneInStock} tonnes)`:`You have exceeded the stock quantityLeft (${this.remainingQtyTonneInStock} tonnes)`}),!0)}getRemainingQuantityStock(){const l=this.removalObject?.clientShare?.find(O=>O?.company?._id===this.commonSrv?.user?.company?._id);if(l?.quantity)return this.remainingQtyTonneInStock=l?.quantity??null;const d=this.removalObject?.categoryShare?.find(O=>O?.category===this.commonSrv?.user?.company?.category);return this.remainingQtyTonneInStock=d?.quantity?d?.quantity:this.removalObject?.quantityLeft??null}static{this.\u0275fac=function(d){return new(d||C)(u.KVO(_.Qq),u.KVO(M.h),u.KVO(N.W3),u.KVO(D.K),u.KVO(v.E))}}static{this.\u0275prov=u.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})}}return C})()},43556:(H,x,o)=>{"use strict";o.d(x,{B:()=>N});var r=o(73308),a=o(94934),s=o(45312),m=o(26409),_=(o(99987),o(2978)),F=o(33607),e=o(82571),u=o(14599),M=o(74657);let N=(()=>{class D{constructor(f,C,p,l,d){this.baseUrl=f,this.http=C,this.commonSrv=p,this.storageSrv=l,this.translateService=d,this.base_url=`${this.baseUrl.getOrigin()}${s.c.basePath}`,this.base_url+="companies"}create(f){var C=this;return(0,r.A)(function*(){try{return delete f._id,yield(0,a.s)(C.http.post(C.base_url,f))}catch(p){return C.commonSrv.getError("Echec de cr\xe9ation de la compagnie",p)}})()}getCompanies(f){var C=this;return(0,r.A)(function*(){try{let p=new m.Nl;const{category:l,city:d,limit:O,name:b,regionCom:L,solToId:V,tel:K,users:Y,offset:B,enable:Z=!0,projection:h,isLoyaltyProgDistributor:R}=f;return void 0!==l&&(p=p.append("category",l)),d&&(p=p.append("address.city",d)),b&&(p=p.append("name",b)),V&&(p=p.append("erpSoldToId",V)),K&&(p=p.append("tel",`${K}`)),h&&(p=p.append("projection",`${h}`)),Y&&(p=p.append("users",`${Y}`)),L&&(p=p.append("address.commercialRegion",L)),R&&(p=p.append("isLoyaltyProgDistributor",R)),void 0!==O&&(p=p.append("limit",O)),void 0!==B&&(p=p.append("offset",B)),p=p.set("enable",Z),yield(0,a.s)(C.http.get(C.base_url,{params:p}))}catch(p){const d={message:C.commonSrv.getError("",p).message,color:"danger"};return yield C.commonSrv.showToast(d),p}})()}getParticularCompanies(f){var C=this;return(0,r.A)(function*(){let p=new m.Nl;const{limit:l,offset:d,enable:O=!0,commercialRegion:b}=f;return void 0!==l&&(p=p.append("limit",l)),void 0!==d&&(p=p.append("offset",d)),b&&(p=p.append("address.commercialRegion",b)),p=p.set("enable",O),yield(0,a.s)(C.http.get(C.base_url+"/particular-suppliers",{params:p}))})()}find(f){var C=this;return(0,r.A)(function*(){try{return yield(0,a.s)(C.http.get(C.base_url+"/"+f))}catch{return C.commonSrv.initCompany()}})()}getBalance(f){var C=this;return(0,r.A)(function*(){try{let p=new m.Nl;const{company:l}=C.storageSrv.getUserConnected();return p=p.set("_id",l?l?._id:f?.companyId),yield(0,a.s)(C.http.get(`${C.base_url}/balance`,{params:p}))}catch(p){return yield C.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),p}})()}getUsersCompany(f,C){var p=this;return(0,r.A)(function*(){try{let l=new m.Nl;const{email:d,enable:O=!0}=C;return d&&(l=l.append("email",d)),l=l.append("enable",O),yield(0,a.s)(p.http.get(`${p.base_url}/${f}/users`,{params:l}))}catch(l){return yield p.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),l}})()}static{this.\u0275fac=function(C){return new(C||D)(_.KVO(F.K),_.KVO(m.Qq),_.KVO(e.h),_.KVO(u.n),_.KVO(M.c$))}}static{this.\u0275prov=_.jDH({token:D,factory:D.\u0275fac,providedIn:"root"})}}return D})()},93387:(H,x,o)=>{"use strict";o.d(x,{A:()=>u});var r=o(73308),a=o(26409),s=o(94934),m=o(58133),t=o(45312),_=o(2978),F=o(82571),e=o(33607);let u=(()=>{class M{constructor(D,v,f){this.http=D,this.commonSrv=v,this.baseUrlService=f,this.url=this.baseUrlService.getOrigin()+t.c.basePath+"prices"}getStores(D){var v=this;return(0,r.A)(function*(){try{let f=new a.Nl;const{companyId:C}=D;return C&&(f=f.append("company._id",C)),f=f.append("enable",!0),yield(0,s.s)(v.http.get(`${v.url}/${v.commonSrv.user.category===m.s.Commercial?"stores-sales":"stores"}`,{params:f}))}catch(f){const p={message:v.commonSrv.getError("",f).message,color:"danger"};return yield v.commonSrv.showToast(p),f}})()}getPricesForOrder(D){var v=this;return(0,r.A)(function*(){try{let f=new a.Nl;const{store:C,packaging:p,companyId:l,enable:d=!0}=D;return f=f.append("enable",d),C&&(f=f.append("store",C)),p&&(f=f.append("packaging",p)),l&&(f=f.append("company._id",l)),yield(0,s.s)(v.http.get(`${v.url}/${v.commonSrv.user.category===m.s.Commercial?"products-sales":"products"}`,{headers:{Authorization:`Bearer ${v.commonSrv?.user?.accessToken}`},params:f}))}catch(f){const p={message:v.commonSrv.getError("",f).message,color:"danger"};return yield v.commonSrv.showToast(p),f}})()}getPricesForCurrentOrder(D){var v=this;return(0,r.A)(function*(){try{let f=new a.Nl;const{storeId:C,packagingId:p,userId:l,enable:d=!0}=D;return f=f.append("enable",d),C&&(f=f.append("store",C)),p&&(f=f.append("packaging",p)),l&&(f=f.append("user",l)),yield(0,s.s)(v.http.get(`${v.url}/products-change`,{params:f}))}catch(f){return f}})()}getShippingAddress(D){var v=this;return(0,r.A)(function*(){try{let f=new a.Nl;const{offset:C,limit:p,enable:l=!0}=D;return C&&(f=f.append("offset",C)),p&&(f=f.append("limit",p)),f=f.append("enable",l),yield(0,s.s)(v.http.get(`${v.url}`,{params:f}))}catch(f){return f}})()}static{this.\u0275fac=function(v){return new(v||M)(_.KVO(a.Qq),_.KVO(F.h),_.KVO(e.K))}}static{this.\u0275prov=_.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()},73014:(H,x,o)=>{"use strict";o.d(x,{J7:()=>r,JN:()=>a,iB:()=>m});class r{}var a=function(t){return t[t.PerQuantity=1]="PerQuantity",t[t.perDate=2]="perDate",t}(a||{});class m{}},94440:(H,x,o)=>{"use strict";o.d(x,{c:()=>a});var r=o(2978);let a=(()=>{class s{transform(t,..._){return t?t.length>_[0]?`${t.substring(0,_[0]-3)}...`:t:""}static{this.\u0275fac=function(_){return new(_||s)}}static{this.\u0275pipe=r.EJ8({name:"truncateString",type:s,pure:!0})}}return s})()}}]);