"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6580],{16580:(W,b,l)=>{l.r(b),l.d(b,{OrderDetailPageModule:()=>G});var m=l(56610),s=l(37222),i=l(77897),_=l(77575),g=l(73308),p=l(88233),h=l(32401),c=l(99987),n=l(2978),v=l(57870),M=l(82571),O=l(62049),P=l(74657);function k(r,u){1&r&&n.nrm(0,"ion-spinner",14)}let w=(()=>{class r{constructor(e,o,t,a,d){this.router=e,this.modal=o,this.orderRetailSrv=t,this.commonSrv=a,this.translateService=d,this.textForm=new s.gE({text:new s.MJ("",[s.k0.required])})}ngOnInit(){}closeModal(){this.modal.dismiss()}cancel(){this.modal.dismiss()}confirmRejet(){var e=this;return(0,g.A)(function*(){const o=e.textForm.get("text").value;if(""===o)return e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"Veuillez renseigner la raison.":"Please provide the reason."}),!1;e.isLoading=!0;try{const t=yield e.orderRetailSrv.rejectRetailOrder(e.orderRetailSrv.orderRetail,o);e.commonSrv.showToast({color:t?.status<400?"success":"danger",message:e.translateService.currentLang===c.T.French?t?.status<400?"Commande rejet\xe9e avec succ\xe8s.":"\xc9chec du rejet de la commande.":t?.status<400?"Order rejected successfully.":"Failed to reject the order."}),t?.status<400&&(e.isLoading=!1,e.closeModal(),e.router.navigate(["order/validate-order"]))}catch{e.commonSrv.showToast({color:"danger",message:e.translateService.currentLang===c.T.French?"Une erreur inattendue est survenue.":"An unexpected error occurred."})}finally{e.isLoading=!1}})()}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(_.Ix),n.rXU(i.W3),n.rXU(v.l),n.rXU(M.h),n.rXU(O.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-reject-order"]],inputs:{order:"order"},decls:31,vars:22,consts:[[1,"product-detail-container","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",1,"close-icon",3,"click"],["src","assets/icons/close.svg"],[1,"title"],[1,"color-primary"],[1,"container"],[1,"description"],[1,"input-group",3,"formGroup"],["labelPlacement","floating","formControlName","text","rows","6","fill","solid",3,"label","placeholder"],[1,"btn-validate"],["fill","solid","color","primary",3,"click"],["fill","solid","color","secondary",3,"disabled","click"],["name","bubbles",4,"ngIf"],["name","bubbles"]],template:function(o,t){1&o&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return t.closeModal()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-title",4),n.EFF(6),n.nI1(7,"translate"),n.j41(8,"span",5),n.EFF(9),n.k0s()()()(),n.j41(10,"ion-content")(11,"div",6)(12,"div",7)(13,"p"),n.EFF(14),n.nI1(15,"translate"),n.k0s()(),n.j41(16,"form",8)(17,"ion-item"),n.nrm(18,"ion-textarea",9),n.nI1(19,"translate"),n.nI1(20,"translate"),n.k0s(),n.j41(21,"div",10)(22,"ion-button",11),n.bIt("click",function(){return t.cancel()}),n.j41(23,"ion-label"),n.EFF(24),n.nI1(25,"translate"),n.k0s()(),n.j41(26,"ion-button",12),n.bIt("click",function(){return t.confirmRejet()}),n.j41(27,"ion-label"),n.EFF(28),n.nI1(29,"translate"),n.k0s(),n.DNE(30,k,1,0,"ion-spinner",13),n.k0s()()()()()()),2&o&&(n.R7$(6),n.SpI(" ",n.bMT(7,10,"order.detail.reference")," "),n.R7$(3),n.JRh(null==t.order?null:t.order.appReference),n.R7$(5),n.SpI(" ",n.bMT(15,12,"order.rejection.description")," "),n.R7$(2),n.Y8G("formGroup",t.textForm),n.R7$(2),n.FS9("label",n.bMT(19,14,"order.rejection.reasonLabel")),n.FS9("placeholder",n.bMT(20,16,"order.rejection.placeholder")),n.R7$(6),n.SpI(" ",n.bMT(25,18,"reseller-new-page.first-step.cancel")," "),n.R7$(2),n.Y8G("disabled",t.isLoading),n.R7$(2),n.SpI(" ",n.bMT(29,20,"reseller-new-page.history-page.tabs.rejected")," "),n.R7$(2),n.Y8G("ngIf",t.isLoading))},dependencies:[m.bT,s.qT,s.BC,s.cb,i.Jm,i.W9,i.eU,i.KW,i.uz,i.he,i.w2,i.nc,i.Zx,i.BC,i.ai,i.Gw,s.j4,s.JD,P.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.product-detail-container[_ngcontent-%COMP%]   ion-popover[_ngcontent-%COMP%]::part(content){overflow:hidden}.product-detail-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]{background-color:var(--ion-color-light);border-bottom:1px solid var(--ion-color-medium)}.product-detail-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{cursor:pointer;border-radius:50%;box-shadow:0 2px 5px #0003}.product-detail-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:700;color:var(--ion-color-primary)}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{height:100%;background:#f8f9fa;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));padding:1.5rem;display:flex;flex-direction:column;align-items:center}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{text-align:center;font-size:.9rem;color:var(--ion-color-medium);margin-bottom:1.5rem;line-height:1.5}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{width:100%;padding:1rem;--margin-top: 0;margin-bottom:1rem;background:white;border-radius:8px;box-shadow:0 4px 6px #0000001a}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;margin-bottom:1rem}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:100%}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{flex:1;margin:0 .5rem}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:first-child{background-color:var(--ion-color-light);color:var(--ion-color-primary);border-radius:1rem}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:last-child{background-color:var(--ion-color-secondary);color:#fff;border-radius:1rem}"]})}}return r})(),R=(()=>{class r{constructor(e,o,t,a){this.popoverController=e,this.modalCtrl=o,this.translateService=t,this.commonSrv=a,this.quantityForm=new s.gE({qdtysheep:new s.MJ("",[s.k0.required,s.k0.min(0)])})}ngOnInit(){}cancel(){this.modalCtrl.dismiss()}closeModal(){this.modalCtrl.dismiss()}confirm(){var e=this;return(0,g.A)(function*(){try{if(e.item.quantityShipped=e.quantityForm.get("qdtysheep").value,null==e.item.quantityShipped)return e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"Veuillez entrer une quantit\xe9.":"Please enter a quantity."}),!1;if(e.item.quantityShipped<=0)return e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"La quantit\xe9 entr\xe9e ne doit pas \xeatre z\xe9ro.":"The entered quantity must not be zero."}),!1;if(e.item.quantityShipped>e.item.quantity)return e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"La quantit\xe9 entr\xe9e ne doit pas \xeatre sup\xe9rieure \xe0 la quantit\xe9 actuelle.":"The entered quantity must not exceed the current quantity."}),!1;e.commonSrv.showToast({color:"success",message:e.translateService.currentLang===c.T.French?"Op\xe9ration effectu\xe9e avec succ\xe8s.":"Operation completed successfully."}),e.modalCtrl.dismiss()}catch(o){console.error("Error:",o)}})()}verificationQuantityShipped(e){return e.filter(o=>null==o.quantityShipped).length?(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===c.T.French?"Veuillez entrer une quantit\xe9.":"Please enter a quantity."}),!1):e.filter(o=>o.quantityShipped<=0).length?(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===c.T.French?"La quantit\xe9 entr\xe9e ne doit pas \xeatre z\xe9ro.":"The entered quantity must not be zero."}),!1):!e.filter(o=>o.quantityShipped>o.quantity).length||(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===c.T.French?"La quantit\xe9 entr\xe9e ne doit pas \xeatre sup\xe9rieure \xe0 la quantit\xe9 actuelle.":"The entered quantity must not exceed the current quantity."}),!1)}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(i.IE),n.rXU(i.W3),n.rXU(O.E),n.rXU(M.h))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-modal-product-detail"]],inputs:{item:"item"},decls:40,vars:25,consts:[[1,"product-detail-container","bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close.svg"],["color","primary",1,"title"],[1,"container"],[1,"recap"],[1,"input-group",3,"formGroup"],["position","floating"],["placeholder","Enter number","type","number","formControlName","qdtysheep",3,"value"],[1,"btn-validate"],["fill","solid","color","secondary",3,"click"],["fill","solid","color","primary",3,"disabled","click"]],template:function(o,t){1&o&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return t.closeModal()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-label",4),n.EFF(6),n.k0s()()(),n.j41(7,"ion-content")(8,"div",5)(9,"div",6)(10,"ion-label"),n.EFF(11),n.nI1(12,"translate"),n.j41(13,"strong"),n.EFF(14),n.k0s()(),n.j41(15,"ion-label"),n.EFF(16),n.nI1(17,"translate"),n.j41(18,"strong"),n.EFF(19),n.k0s()(),n.j41(20,"ion-label"),n.EFF(21),n.nI1(22,"translate"),n.j41(23,"strong"),n.EFF(24),n.k0s()()(),n.j41(25,"form",7)(26,"ion-item")(27,"ion-label",8),n.EFF(28),n.nI1(29,"translate"),n.k0s(),n.nrm(30,"ion-input",9),n.k0s(),n.j41(31,"div",10)(32,"ion-button",11),n.bIt("click",function(){return t.cancel()}),n.j41(33,"ion-label"),n.EFF(34),n.nI1(35,"translate"),n.k0s()(),n.j41(36,"ion-button",12),n.bIt("click",function(){return t.confirm()}),n.j41(37,"ion-label"),n.EFF(38),n.nI1(39,"translate"),n.k0s()()()()()()()),2&o&&(n.R7$(6),n.SpI(" "," "+(null==t.item||null==t.item.product?null:t.item.product.normLabel)," "),n.R7$(5),n.SpI("",n.bMT(12,13,"reseller-new-page.first-step.qdty-bag")," : "),n.R7$(3),n.JRh(null==t.item?null:t.item.quantity),n.R7$(2),n.SpI("",n.bMT(17,15,"reseller-new-page.detail.packaging"),": "),n.R7$(3),n.JRh(null==t.item||null==t.item.packaging?null:t.item.packaging.label),n.R7$(2),n.SpI("",n.bMT(22,17,"reseller-new-page.first-step.qdty-sheep"),": "),n.R7$(3),n.JRh((null==t.item?null:t.item.quantityShipped)||0),n.R7$(1),n.Y8G("formGroup",t.quantityForm),n.R7$(3),n.SpI(" ",n.bMT(29,19,"reseller-new-page.first-step.qdty-sheep")," "),n.R7$(2),n.FS9("value",t.item.quantity),n.R7$(4),n.SpI(" ",n.bMT(35,21,"reseller-new-page.first-step.cancel")," "),n.R7$(2),n.Y8G("disabled",t.quantityForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(39,23,"reseller-new-page.first-step.valid"),""))},dependencies:[s.qT,s.BC,s.cb,i.Jm,i.W9,i.eU,i.KW,i.$w,i.uz,i.he,i.Zx,i.ai,i.su,s.j4,s.JD,P.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.product-detail-container[_ngcontent-%COMP%]   ion-popover[_ngcontent-%COMP%]::part(content){overflow:hidden}.product-detail-container[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:var(--ion-color-primary)!important}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{height:100%;background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .recap[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding-left:4%;padding-top:3%}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .recap[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:1rem;color:#333;margin-bottom:.5rem}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .recap[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#0070c0}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .tilte-quantite[_ngcontent-%COMP%]{padding-left:4em;margin-bottom:1em}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{height:calc(55 * var(--resH));overflow-x:hidden;overflow-y:auto;padding:0 calc(41 * var(--res));margin-top:0;flex:1}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px}.product-detail-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:space-around}"]})}}return r})();var S=l(27453),F=l(58133),T=l(14599),f=l(63037),I=l(94440);function j(r,u){1&r&&(n.j41(0,"ion-text",36),n.EFF(1),n.nI1(2,"translate"),n.k0s()),2&r&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,"reseller-new-page.detail.para"),""))}function x(r,u){if(1&r){const e=n.RV6();n.j41(0,"button",41),n.bIt("click",function(){n.eBV(e);const t=n.XpG().$implicit,a=n.XpG();return n.Njj(a.showDetailAndEnterQty(t))}),n.j41(1,"ion-label"),n.nrm(2,"ion-icon",26),n.EFF(3),n.nI1(4,"translate"),n.k0s()()}2&r&&(n.R7$(3),n.JRh(n.bMT(4,1,"reseller-new-page.detail.modify")))}function E(r,u){if(1&r){const e=n.RV6();n.j41(0,"ion-slide")(1,"ion-card")(2,"ion-card-content")(3,"div",37)(4,"ion-img",38),n.bIt("click",function(){const a=n.eBV(e).$implicit,d=n.XpG();return n.Njj(d.showDetailAndEnterQty(a))}),n.k0s(),n.j41(5,"div",39)(6,"ion-label"),n.EFF(7),n.nI1(8,"truncateString"),n.k0s(),n.j41(9,"ion-label"),n.EFF(10),n.nI1(11,"translate"),n.j41(12,"strong"),n.EFF(13),n.k0s()(),n.j41(14,"ion-label"),n.EFF(15),n.nI1(16,"translate"),n.j41(17,"strong"),n.EFF(18),n.k0s()()()(),n.DNE(19,x,5,3,"button",40),n.k0s()()()}if(2&r){const e=u.$implicit,o=n.XpG();n.R7$(4),n.Y8G("src",null==e||null==e.product?null:e.product.image),n.R7$(3),n.JRh(n.i5U(8,7,null==e||null==e.product?null:e.product.label,9)),n.R7$(3),n.SpI("",n.bMT(11,10,"reseller-new-page.detail.quantity")," : "),n.R7$(3),n.SpI("",(null==e?null:e.quantity)/(null==e||null==e.packaging.unit?null:e.packaging.unit.ratioToTone),"T"),n.R7$(2),n.SpI("",n.bMT(16,12,"reseller-new-page.detail.quantity-ship")," : "),n.R7$(3),n.SpI("",(null==e?null:e.quantityShipped)/(null==e||null==e.packaging.unit?null:e.packaging.unit.ratioToTone)||0,"T"),n.R7$(1),n.Y8G("ngIf",(null==o.orderService.orderRetail?null:o.orderService.orderRetail.status)==o.orderStatusReseller.CREATED)}}function D(r,u){if(1&r){const e=n.RV6();n.j41(0,"div",42),n.bIt("click",function(){n.eBV(e),n.XpG();const t=n.sdS(62);return n.Njj(t.slidePrev())}),n.k0s()}}function $(r,u){if(1&r){const e=n.RV6();n.j41(0,"div",43),n.bIt("click",function(){n.eBV(e),n.XpG();const t=n.sdS(62);return n.Njj(t.slideNext())}),n.k0s()}}function L(r,u){if(1&r){const e=n.RV6();n.j41(0,"div",44)(1,"ion-icon",45),n.bIt("click",function(){const a=n.eBV(e).index,d=n.XpG();return n.Njj(d.deleteImage(a))}),n.k0s(),n.j41(2,"img",46),n.bIt("click",function(){const a=n.eBV(e).index,d=n.XpG();return n.Njj(d.OpenImage(a))}),n.k0s()()}if(2&r){const e=u.$implicit;n.R7$(2),n.Y8G("src",e,n.B4B)}}function z(r,u){1&r&&n.nrm(0,"ion-spinner",47)}function A(r,u){1&r&&n.nrm(0,"ion-spinner",47)}let y=(()=>{class r{constructor(e,o,t,a,d,C,N,X,Y,J,B){this.fb=e,this.location=o,this.route=t,this.modalCtrl=a,this.commonService=d,this.orderService=C,this.storageService=N,this.router=X,this.popoverController=Y,this.translateService=J,this.imageCompress=B,this.slideProductOpts={initialSlide:0,speed:1e3,slidesPerView:2,spaceBetween:5,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}},this.orderStatusReseller=p.Dp,this.orderAction=p.T3,this.takePictureForm=this.fb.group({images:[[],[]]}),this.isRejectLoading=!1,this.isValidateLoading=!1,this.totalize=6291456}ngOnInit(){var e=this;return(0,g.A)(function*(){yield e.getImagePrevalidated()})()}ionViewWillEnter(){var e=this;return(0,g.A)(function*(){e.user=e.storageService.getUserConnected(),e.orderService.orderRetail||(e.orderService.orderRetail=yield e.orderService.findOrderRetail(e.route.snapshot.params.id))})()}back(){this.location.back()}getImage(){return this.takePictureForm?.value?.images}getImagePrevalidated(){var e=this;return(0,g.A)(function*(){e.imageOrderValidateDto=yield e.orderService.getImageRetail({appRef:e.orderService.orderRetail?.appReference}),e.imageOrderValidateDto?.dataUrls?.forEach(o=>{e.takePictureForm.value.images.unshift(o)})})()}showDetailAndEnterQty(e){var o=this;return(0,g.A)(function*(){o.orderService.orderRetail.status==p.Dp.CREATED&&o.user?.category!=F.s.Commercial||o.orderService.orderRetail.status==p.Dp.VALIDATED||o.orderService.orderRetail.status==p.Dp.REJECTED?o.showDetail(e):yield(yield o.modalCtrl.create({component:R,initialBreakpoint:.5,cssClass:"modal",breakpoints:[0,.5],mode:"ios",componentProps:{item:e,order:o.orderService.orderRetail}})).present()})()}showDetail(e){var o=this;return(0,g.A)(function*(){yield(yield o.popoverController.create({component:S.U,componentProps:{item:e}})).present()})()}uploadImage(){var e=this;return(0,g.A)(function*(){if(e.orderService.orderRetail.status==p.Dp.PREVALIDATED&&!e.user?.authorizations.includes(p.T3.ADMIN_VALIDATION))return void(yield e.commonService.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"Vous avez pas l autorisation n\xe9cessaire pour effectuer cette .":"You do not have the necessary permission to perform this action."}));const o=yield h.i7.getPhoto({quality:100,allowEditing:!1,resultType:h.LK.DataUrl}),t=yield e.imageCompress.compressFile(o.dataUrl,0);return e.takePictureForm?.value?.images.length>=5?yield e.commonService.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"Vous avez atteint le montre maximum de photos.":"You have reached the maximum number of photos."}):e.takePictureForm?.value?.images.reduce((d,C)=>d+e.imageCompress.byteCount(C),e.imageCompress.byteCount(t))>e.totalize?yield e.commonService.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"La taille maximum des photos est sup\xe9rieure  a 6 Mo  veiller \xe0  r\xe9duire le nombre d images.":"The maximum size of photos is over 6 Mo, so please reduce the number of images."}):void e.takePictureForm.value.images.push(t)})()}rejected(){var e=this;return(0,g.A)(function*(){e.isRejectLoading=!0;try{yield(yield e.modalCtrl.create({component:w,initialBreakpoint:.8,cssClass:"modal",breakpoints:[0,.5],mode:"ios",componentProps:{order:e.orderService.orderRetail}})).present()}catch(o){console.error("Error while rejecting:",o),yield e.commonService.showToast({color:"danger",message:e.translateService.currentLang===c.T.French?"Une erreur est survenue lors du rejet.":"An error occurred while rejecting."})}finally{e.isRejectLoading=!1}})()}validated(){var e=this;return(0,g.A)(function*(){if(e.orderService.orderRetail.status==p.Dp.VALIDATED&&!e.user?.authorizations.includes(p.T3.ADMIN_VALIDATION))return yield e.commonService.showToast({color:"warning",message:e.translateService.currentLang===c.T.French?"Vous n'avez pas l'autorisation n\xe9cessaire pour effectuer cette action.":"You do not have the necessary permission to perform this action."}),!1;if(e.isValidateLoading=!0,e.imageOrderValidateDto={dataUrls:e.takePictureForm?.value?.images,appRef:e.orderService.orderRetail?.appReference},!e.verificationQuantityShipped(e.orderService.orderRetail.cart.items))return e.isValidateLoading=!1,!1;try{(yield e.orderService.imageOrderValidated(e.imageOrderValidateDto))?.status<400?(yield e.commonService.showToast({color:"success",message:e.translateService.currentLang===c.T.French?"Images envoy\xe9es avec succ\xe8s.":"Images uploaded successfully."}),(yield e.orderService.validateRetailOrder(e.orderService.orderRetail))?.status<400?(yield e.commonService.showToast({color:"success",message:e.translateService.currentLang===c.T.French?"Commande valid\xe9e avec succ\xe8s.":"Order validated successfully."}),e.router.navigate(["order/validate-order"],{queryParams:{reload:!0}})):yield e.commonService.showToast({color:"danger",message:e.translateService.currentLang===c.T.French?"Erreur lors de la validation de la commande.":"Error while validating the order."})):yield e.commonService.showToast({color:"danger",message:e.translateService.currentLang===c.T.French?"\xc9chec de l'envoi des images.":"Failed to upload images."})}catch(o){console.error("Error:",o),yield e.commonService.showToast({color:"danger",message:e.translateService.currentLang===c.T.French?"Une erreur inattendue est survenue.":"An unexpected error occurred."})}finally{e.isValidateLoading=!1}return!1})()}OpenImage(e){this.getImage()}deleteImage(e){const o=this.takePictureForm.get("images").value.filter((t,a)=>a!=e);this.takePictureForm.get("images").patchValue(o)}verificationQuantityShipped(e){return e.filter(o=>null==o.quantityShipped).length?(this.commonService.showToast({color:"warning",message:this.translateService.currentLang===c.T.French?"Veuillez entrer une quantit\xe9":"Please enter a quantity"}),!1):e.filter(o=>o.quantityShipped<=0).length?(this.commonService.showToast({color:"warning",message:this.translateService.currentLang===c.T.French?"La quandite entrer ne doit pas etre  zero":"Input speed must not be zero"}),!1):!e.filter(o=>o.quantityShipped>o.quantity).length||(this.commonService.showToast({color:"warning",message:this.translateService.currentLang===c.T.French?"La quandite entrer ne doit pas etre superieure a la quandite actuele":"The entry date must not be higher than the current date."}),!1)}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(s.ok),n.rXU(m.aZ),n.rXU(_.nX),n.rXU(i.W3),n.rXU(M.h),n.rXU(v.l),n.rXU(T.n),n.rXU(_.Ix),n.rXU(i.IE),n.rXU(O.E),n.rXU(f.ep))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-order-detail"]],decls:94,vars:67,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"color-primary"],["id","container",1,"order-history-page"],[1,"scroller-container","historic-bill-detail-container"],[1,"order-detail"],[1,"h3-title"],[1,"bill-info"],[1,"right-block"],[1,"left-block"],[1,"value"],[1,"order-summary"],[1,"list-products"],["class","para",4,"ngIf"],[1,"content-slide"],[1,"padding-horizontal","slide-container","product-container",3,"options"],["slider",""],[4,"ngFor","ngForOf"],["class","swiper-button-prev",3,"click",4,"ngIf"],["class","swiper-button-next",3,"click",4,"ngIf"],[1,"upload-image",3,"hidden"],[1,"upload-image-box"],["src","/assets/icons/upload-file.png","alt","upload-cloud"],[1,"button-upload-image"],["type","submit","expand","block",1,"btn","btn--meduim",3,"disabled","click"],["name","add"],[1,"show-image"],["class","image-col",4,"ngFor","ngForOf"],["slot","start",1,"button-validated",3,"hidden"],[1,"validate-rejected-order"],[1,"rejected-order"],["color","danger",1,"showAll",3,"disabled","click"],["name","bubbles",4,"ngIf"],[1,"validated-order"],["type","submit",1,"primary",3,"disabled","click"],[1,"para"],[1,"content"],[1,"mWidth",3,"src","click"],[1,"detail"],["class","btn btn--meduim btn-modify","color","secondary","expand","block",3,"click",4,"ngIf"],["color","secondary","expand","block",1,"btn","btn--meduim","btn-modify",3,"click"],[1,"swiper-button-prev",3,"click"],[1,"swiper-button-next",3,"click"],[1,"image-col"],["name","trash-outline",1,"img-delete-icon",3,"click"],["alt","image",3,"src","click"],["name","bubbles"]],template:function(o,t){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return t.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.j41(6,"span",3),n.EFF(7),n.k0s()()()(),n.j41(8,"ion-content")(9,"div",4)(10,"div",5)(11,"div",6)(12,"ion-title",7),n.EFF(13),n.nI1(14,"translate"),n.k0s(),n.j41(15,"div",8)(16,"div",9)(17,"ion-label",2),n.EFF(18),n.nI1(19,"translate"),n.k0s(),n.j41(20,"ion-label",2),n.EFF(21),n.nI1(22,"translate"),n.k0s()(),n.j41(23,"div",10)(24,"ion-label",11),n.EFF(25),n.k0s(),n.j41(26,"ion-label",11),n.EFF(27),n.nI1(28,"date"),n.j41(29,"span",11),n.EFF(30),n.nI1(31,"translate"),n.nI1(32,"date"),n.k0s()()()()(),n.j41(33,"div",12)(34,"ion-title",7),n.EFF(35),n.nI1(36,"translate"),n.k0s(),n.j41(37,"div",8)(38,"div",9)(39,"ion-label",2),n.EFF(40),n.nI1(41,"translate"),n.k0s(),n.j41(42,"ion-label",2),n.EFF(43),n.nI1(44,"translate"),n.k0s(),n.j41(45,"ion-label",2),n.EFF(46),n.nI1(47,"translate"),n.k0s()(),n.j41(48,"div",10)(49,"ion-label",11),n.EFF(50),n.k0s(),n.j41(51,"ion-label",11),n.EFF(52),n.k0s(),n.j41(53,"ion-label",11),n.EFF(54),n.k0s()()()(),n.j41(55,"div",13)(56,"ion-title",2),n.EFF(57),n.nI1(58,"translate"),n.k0s(),n.DNE(59,j,3,3,"ion-text",14),n.j41(60,"div",15)(61,"ion-slides",16,17),n.DNE(63,E,20,14,"ion-slide",18),n.k0s()(),n.DNE(64,D,1,0,"div",19),n.DNE(65,$,1,0,"div",20),n.k0s(),n.j41(66,"div",21)(67,"ion-title",7),n.EFF(68),n.nI1(69,"translate"),n.k0s(),n.j41(70,"div",22),n.nrm(71,"img",23),n.j41(72,"ion-text"),n.EFF(73),n.nI1(74,"translate"),n.k0s(),n.j41(75,"div",24)(76,"ion-button",25),n.bIt("click",function(){return t.uploadImage()}),n.nrm(77,"ion-icon",26),n.k0s()()(),n.j41(78,"div",27),n.DNE(79,L,3,1,"div",28),n.k0s()(),n.j41(80,"div",29)(81,"div",30)(82,"div",31)(83,"ion-button",32),n.bIt("click",function(){return t.rejected()}),n.j41(84,"ion-label"),n.EFF(85),n.nI1(86,"translate"),n.k0s(),n.DNE(87,z,1,0,"ion-spinner",33),n.k0s()(),n.j41(88,"div",34)(89,"ion-button",35),n.bIt("click",function(){return t.validated()}),n.j41(90,"ion-label"),n.EFF(91),n.nI1(92,"translate"),n.k0s(),n.DNE(93,A,1,0,"ion-spinner",33),n.k0s()()()()()()()),2&o&&(n.R7$(4),n.SpI("",n.bMT(5,33,"order.detail.reference")," "),n.R7$(3),n.JRh(null==t.orderService.orderRetail?null:t.orderService.orderRetail.appReference),n.R7$(6),n.JRh(n.bMT(14,35,"order.detail.title")),n.R7$(5),n.SpI("",n.bMT(19,37,"order.detail.reference")," : "),n.R7$(3),n.SpI("",n.bMT(22,39,"reseller-new-page.detail.created-at")," :"),n.R7$(4),n.JRh(null==t.orderService.orderRetail?null:t.orderService.orderRetail.appReference),n.R7$(2),n.SpI("",n.i5U(28,41,null==t.orderService.orderRetail?null:t.orderService.orderRetail.created_at,"dd/MM/YY")," "),n.R7$(3),n.JRh(n.bMT(31,44,"preposition.to")+n.i5U(32,46,null==t.orderService.orderRetail?null:t.orderService.orderRetail.created_at,"HH:mm:ss")),n.R7$(5),n.SpI("",n.bMT(36,49,"reseller-new-page.detail.order-recap")," "),n.R7$(5),n.SpI("",n.bMT(41,51,"reseller-new-page.detail.region"),":"),n.R7$(3),n.SpI("",n.bMT(44,53,"reseller-new-page.detail.distributors"),":"),n.R7$(3),n.SpI("",n.bMT(47,55,"reseller-new-page.detail.point-validate")," :"),n.R7$(4),n.JRh((null==t.orderService.orderRetail||null==t.orderService.orderRetail.user||null==t.orderService.orderRetail.user.address?null:t.orderService.orderRetail.user.address.commercialRegion)||"N/A"),n.R7$(2),n.JRh((null==t.orderService.orderRetail||null==t.orderService.orderRetail.supplier?null:t.orderService.orderRetail.supplier.name)||"N/A"),n.R7$(2),n.JRh((null==t.orderService.orderRetail||null==t.orderService.orderRetail.user||null==t.orderService.orderRetail.user.points?null:t.orderService.orderRetail.user.points.unValidated)||"N/A"),n.R7$(3),n.SpI("",n.bMT(58,57,"reseller-new-page.detail.produit")," "),n.R7$(2),n.Y8G("ngIf",!((null==t.orderService.orderRetail?null:t.orderService.orderRetail.status)==t.orderStatusReseller.VALIDATED||(null==t.orderService.orderRetail?null:t.orderService.orderRetail.status)==t.orderStatusReseller.REJECTED)),n.R7$(2),n.Y8G("options",t.slideProductOpts),n.R7$(2),n.Y8G("ngForOf",null==t.orderService.orderRetail||null==t.orderService.orderRetail.cart?null:t.orderService.orderRetail.cart.items),n.R7$(1),n.Y8G("ngIf",(null==t.orderService.orderRetail||null==t.orderService.orderRetail.cart||null==t.orderService.orderRetail.cart.items?null:t.orderService.orderRetail.cart.items.length)>2),n.R7$(1),n.Y8G("ngIf",(null==t.orderService.orderRetail||null==t.orderService.orderRetail.cart||null==t.orderService.orderRetail.cart.items?null:t.orderService.orderRetail.cart.items.length)>2),n.R7$(1),n.Y8G("hidden",(null==t.orderService.orderRetail?null:t.orderService.orderRetail.status)==t.orderStatusReseller.VALIDATED||t.orderService.orderRetail.status==t.orderStatusReseller.REJECTED&&!(null!=t.user&&t.user.authorizations.includes(t.orderAction.ADMIN_VALIDATION))),n.R7$(2),n.SpI("",n.bMT(69,59,"reseller-new-page.detail.image")," "),n.R7$(5),n.JRh(n.bMT(74,61,"reseller-new-page.detail.piece-join")),n.R7$(3),n.Y8G("disabled",(null==t.takePictureForm||null==t.takePictureForm.value||null==t.takePictureForm.value.images?null:t.takePictureForm.value.images.length)>=5),n.R7$(3),n.Y8G("ngForOf",null==t.takePictureForm||null==t.takePictureForm.value?null:t.takePictureForm.value.images),n.R7$(1),n.Y8G("hidden",(null==t.orderService.orderRetail?null:t.orderService.orderRetail.status)==t.orderStatusReseller.VALIDATED||t.orderService.orderRetail.status==t.orderStatusReseller.REJECTED&&!(null!=t.user&&t.user.authorizations.includes(t.orderAction.ADMIN_VALIDATION))),n.R7$(3),n.Y8G("disabled",t.isRejectLoading),n.R7$(2),n.SpI(" ",n.bMT(86,63,"reseller-new-page.detail.rejete")," "),n.R7$(2),n.Y8G("ngIf",t.isRejectLoading),n.R7$(2),n.Y8G("disabled",(null==t.takePictureForm||null==t.takePictureForm.value?null:t.takePictureForm.value.images.length)<=0||t.isValidateLoading),n.R7$(2),n.SpI(" ",n.bMT(92,65,"reseller-new-page.detail.valide")," "),n.R7$(2),n.Y8G("ngIf",t.isValidateLoading))},dependencies:[m.Sq,m.bT,i.Jm,i.b_,i.I9,i.W9,i.eU,i.iq,i.KW,i.he,i.q3,i.tR,i.w2,i.IO,i.BC,i.ai,m.vh,I.c,P.D9],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--background: #e5e5e5;padding:calc(41 * var(--res));display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:var(--mont-semibold)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]{--background: #e5e5e5}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]{background-color:#e5e5e5;text-align:-webkit-center;width:100%;align-items:center;display:flex;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]{border-radius:1rem;width:100%;padding-bottom:.2rem;box-sizing:content-box;display:flex;align-items:center;justify-content:center;gap:1em;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]{width:100%;padding:0 .5em;background:#ffffff;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px;display:flex;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d;text-align:left;font-family:var(--mont-semibold)}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:auto}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-transform:capitalize;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]{width:100%;padding:0 .5em;background:#ffffff;border-radius:5px;position:relative;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .content-slide[_ngcontent-%COMP%]{width:24em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]{margin-bottom:1em;display:flex;justify-content:space-around}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{width:37vw!important}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{padding:0;width:-moz-fit-content;width:fit-content;box-shadow:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{height:100%;width:35vw;display:flex;flex-direction:row}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{align-items:flex-start;width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-around}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(29 * var(--res));padding:0;text-align:start}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .btn-modify[_ngcontent-%COMP%]{width:70px;height:26px;display:flex;justify-content:center;align-items:center;border-radius:5px;background-color:#419cfb2b;margin-top:.9em;cursor:pointer;padding:0}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .btn-modify[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:4px;font-size:12px;vertical-align:middle}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .btn-modify[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:flex;color:#0b305c;font-size:8px;align-items:center;gap:4px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .para[_ngcontent-%COMP%]{margin-bottom:.5em;color:red;font-size:.5em;display:flex;align-items:flex-start}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .paras[_ngcontent-%COMP%]{margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:600;margin:2vh 0;margin-bottom:0vh!important;color:#143c5d;font-family:var(--mont-semibold);text-align:left}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]{width:100%;padding:0 .5em;background:#ffffff;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d;text-align:left;font-family:var(--mont-semibold)}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-transform:capitalize;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]{width:100%;padding:.5em;background:#ffffff;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px;display:flex;flex-direction:column;justify-content:center;align-items:flex-start}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .show-image[_ngcontent-%COMP%]{display:flex}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .show-image[_ngcontent-%COMP%]   .image-col[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;margin-top:1em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .show-image[_ngcontent-%COMP%]   .image-col[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:10vh;width:30vw;border-radius:12px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .upload-image-box[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column;border:1px dashed #143c5d;padding:.5em;gap:1em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .upload-image[_ngcontent-%COMP%]   .upload-image-box[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:14px;color:#143c5d;width:80%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .button-validated[_ngcontent-%COMP%]{width:100%;flex-direction:column;align-content:space-evenly;display:flex;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .button-validated[_ngcontent-%COMP%]   .button-save-image[_ngcontent-%COMP%]{margin:.5em 0;width:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .button-validated[_ngcontent-%COMP%]   .validate-rejected-order[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;align-items:center;width:100%;margin-top:.2em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .button-validated[_ngcontent-%COMP%]   .validate-rejected-order[_ngcontent-%COMP%]   .rejected-order[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .button-validated[_ngcontent-%COMP%]   .validate-rejected-order[_ngcontent-%COMP%]   .validated-order[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:50%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .button-validated[_ngcontent-%COMP%]   .validate-rejected-order[_ngcontent-%COMP%]   .rejected-order[_ngcontent-%COMP%] > ion-button[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .button-validated[_ngcontent-%COMP%]   .validate-rejected-order[_ngcontent-%COMP%]   .validated-order[_ngcontent-%COMP%] > ion-button[_ngcontent-%COMP%]{width:39vw;height:6vh}.swiper-button-prev[_ngcontent-%COMP%], .swiper-button-next[_ngcontent-%COMP%]{width:1em;height:1em;background-size:1em 1em;position:absolute;top:50%}.swiper-button-prev[_ngcontent-%COMP%]{background-image:url(arrow-ios-downward-outline\\ 9.bd5041918211cf00.svg)}.swiper-button-next[_ngcontent-%COMP%]{background-image:url(arrow-ios-downward-outline\\ 8.1d166daa5fdb0ae8.svg)}']})}}return r})();const V=[{path:"",component:y},{path:":id",component:y}];let q=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[_.iI.forChild(V),_.iI]})}}return r})();var U=l(93887);let G=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({providers:[f.ep],imports:[m.MD,s.YN,i.bv,U.G,P.h,s.X1,q]})}}return r})()}}]);