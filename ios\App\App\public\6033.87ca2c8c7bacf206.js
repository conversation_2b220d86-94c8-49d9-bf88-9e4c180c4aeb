"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6033],{16033:(k,b,r)=>{r.r(b),r.d(b,{ClaimFormPageModule:()=>G});var h=r(74657),e=r(56610),c=r(37222),s=r(77897),_=r(77575),p=r(73308),M=r(63037),P=r(99987),v=r(91285),y=r(58133),n=r(2978),x=r(82571),d=r(14599),F=r(28639),g=r(62049),u=r(11244);function l(i,C){if(1&i&&(n.j41(0,"ion-select-option",23),n.EFF(1),n.k0s()),2&i){const t=C.$implicit;n.Y8G("value",t),n.R7$(1),n.SpI("",null==t?null:t.label," ")}}function O(i,C){if(1&i&&(n.j41(0,"ion-select-option",23),n.EFF(1),n.k0s()),2&i){const t=C.$implicit;n.Y8G("value",t),n.R7$(1),n.SpI("",null==t?null:t.label," ")}}function w(i,C){if(1&i&&(n.j41(0,"ion-select",24),n.DNE(1,O,2,2,"ion-select-option",9),n.k0s()),2&i){const t=n.XpG();n.R7$(1),n.Y8G("ngForOf",t.subClaimTypes)}}function T(i,C){if(1&i&&(n.j41(0,"span",25),n.EFF(1),n.k0s()),2&i){const t=n.XpG();n.R7$(1),n.JRh(null==t.file?null:t.file.name)}}function E(i,C){if(1&i&&(n.j41(0,"span",26),n.EFF(1),n.k0s()),2&i){const t=n.XpG();n.R7$(1),n.JRh(null==t.file?null:t.file.name)}}function R(i,C){if(1&i){const t=n.RV6();n.j41(0,"div",27),n.bIt("click",function(){n.eBV(t);const a=n.XpG();return n.Njj(a.preventAlert())}),n.EFF(1),n.nI1(2,"capitalize"),n.nI1(3,"translate"),n.k0s()}2&i&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,n.bMT(3,3,"claim-form-page.add-piece-button-label"))," "))}function I(i,C){if(1&i&&(n.j41(0,"div",28),n.EFF(1),n.k0s()),2&i){const t=n.XpG();n.R7$(1),n.SpI(" ",null==t.file?null:t.file.name," ")}}function A(i,C){1&i&&n.nrm(0,"ion-spinner",29)}const z=[{path:"",component:(()=>{class i{constructor(t,o,a,m,f,D,$){this.location=t,this.commonSvr=o,this.storageSrv=a,this.claimSvr=m,this.imageCompress=f,this.translateService=D,this.modalCtrl=$,this.isLoading=!1,this.isAprove=!1,this.isContentShown=!1,this.attachment={file:"",name:"",contentType:""},this.isClaimSelected=!0,this.options={newClaim:1,myCleam:2},this.tabOption=this.options.newClaim,this.numberOfCharacters=0,this.MIN_CHARACTER_LENGTH=20,this.MAX_CHARACTER_LENGTH=1e3,this.LIMIT_SIZE=15e5,this.feedbackForm=new c.gE({message:new c.MJ("",[c.k0.required,c.k0.minLength(this.MIN_CHARACTER_LENGTH),c.k0.maxLength(this.MAX_CHARACTER_LENGTH)]),category:new c.MJ("",[c.k0.required]),subCategory:new c.MJ("",[c.k0.required])}),this.title="",this.claimFile=[],this.paths=[]}ngOnInit(){this.claimTypes=this.claimSvr.getClaimTypes(),this.storageSrv.getUserConnected(),this.user=this.commonSvr.user,this.isRetailer=1===this.user.category}newClaims(){this.displayOptions=!0}initData(){this.feedbackForm.patchValue({category:null}),this.feedbackForm.patchValue({subCategory:null}),this.feedbackForm.patchValue({message:null})}ionViewWillEnter(){this.isContentShown=!0}ionViewWillLeave(){this.isContentShown=!1}categoryChange(t){this.subClaimTypes=this.claimSvr.getSubCategoryTypesById(t.detail.value)}back(){this.location.back()}postFeedback(){var t=this;return(0,p.A)(function*(){if(t.isLoading=!0,!t.feedbackForm.value.category||!t.feedbackForm.value.subCategory||!t.feedbackForm.value.message)return void t.commonSvr.showToast({color:"danger",message:"Veuillez renseigner tous les champs"});const o={category:t.feedbackForm.value.category,subCategory:t.feedbackForm.value.subCategory,message:t.feedbackForm.value.message,attachment:t.attachment};try{t.commonSvr?.user?.category===y.s?.CompanyUser&&(o.user=t.commonSvr?.user),t.response=yield t.claimSvr.createFeedback(o),201===t.response?.status?(t.commonSvr.showToast({message:`${t.response.message}`,color:"success"}),t.initData(),t.location.back()):t.commonSvr.showToast({color:"danger",message:`${t.response.message}`})}catch(a){return a.message}finally{t.isLoading=!1}})()}countCharacters(){let t=this.trimMessageToMaxLength();this.updateFeedbackFormMessage(t),this.numberOfCharacters=t.length}trimMessageToMaxLength(){let t=this.feedbackForm.value.message;return t.length>this.MAX_CHARACTER_LENGTH?t.substring(0,this.MAX_CHARACTER_LENGTH):t}updateFeedbackFormMessage(t){this.feedbackForm.get("message").value!==t&&this.feedbackForm.get("message").patchValue({message:t},{emitEvent:!1})}setAttachment(t){var o=this;return(0,p.A)(function*(){try{o.file=o.getFileFromDataSet(t),o.attachment.file=yield o.getFileDataUrl(o.file);let a=yield o.getFileSize(o.file,o.attachment.file);o.validateFileSize(a,o.LIMIT_SIZE),[o.attachment.name,o.attachment.contentType]=[o.file.name,o.file.type]}catch(a){o.handleError(a)}})()}getFileFromDataSet(t){const o=t.target.files[0];return this.validate(o),o}getFileDataUrl(t){var o=this;return(0,p.A)(function*(){const a=yield o.convertFileToDataUrl(t);return o.validate(a),a})()}convertFileToDataUrl(t){return new Promise((o,a)=>{const m=new FileReader;m.readAsDataURL(t),m.onload=()=>o(m.result),m.onerror=f=>a(f)})}validate(t){if(!t)throw new Error("Une erreur est survenue, veuillez ressayer SVP !")}getFileSize(t,o){var a=this;return(0,p.A)(function*(){let m=t.size;return"application/pdf"!=t.type&&(o=yield a.imageCompress.compressFile(o,M._k.Up),m=a.imageCompress.byteCount(o)),m})()}validateFileSize(t,o){if(t>o)throw new Error("Veuillez choisir un fichier de moins de 1,5 MB SVP !")}handleError(t){this.file=null,this.commonSvr.showToast({color:"danger",message:`${t.message}`})}preventAlert(){var t=this;return(0,p.A)(function*(){const o=yield t.modalCtrl.create({component:v.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:t.translateService.currentLang===P.T.French?"Oui":"Yes",cancelButton:t.translateService.currentLang===P.T.French?"Non":"No",text:t.translateService.currentLang===P.T.French?"Acc\xe8s cam\xe9ra pour photos, vid\xe9os et scan de codes. Donn\xe9es stock\xe9es localement et non partag\xe9es sans consentement. Utilis\xe9es pour am\xe9liorer l exp\xe9rience utilisateur et traiter les r\xe9clamations de commande et les probl\xe8mes d application.":"Camera access for photos, videos and code scanning. Data stored locally and not shared without consent. Used to improve user experience and process order claims and app issues.",handler:()=>{document.getElementById("file").click(),t.isAprove=!0,t.modalCtrl.dismiss()}}}});yield o.present(),yield o.onDidDismiss(),t.modalCtrl.dismiss()})()}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(e.aZ),n.rXU(x.h),n.rXU(d.n),n.rXU(F.a),n.rXU(M.ep),n.rXU(g.E),n.rXU(s.W3))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-claim-form"]],decls:50,vars:34,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],[1,"ion-padding"],["slot","top",1,"tab-container","ion-no-border"],["id","container"],[1,"input-group",3,"formGroup"],["position","floating"],["mode","ios","formControlName","category","cancelText","Annuler","interface","action-sheet",3,"ionChange"],[3,"value",4,"ngFor","ngForOf"],["mode","ios","formControlName","subCategory","cancelText","Annuler","interface","action-sheet",4,"ngIf"],["formControlName","message","max","9","type","number","rows","4","cols","20","clearInput","",3,"maxlength","autoGrow","ngModelChange"],[2,"font-size","13px"],[1,"new-attachment"],[1,"file-section",3,"click"],["src","assets/icons/upload-file.png"],["class","filename",4,"ngif"],["class","empty",4,"ngIf"],["class","message",3,"click",4,"ngIf"],["type","file","id","file","accept","image/*,.pdf","multiple","",2,"display","none",3,"change"],["class","message",4,"ngIf"],["type","submit","color","primary","expand","block",1,"btn","mbottom250","btn--meduim","btn--upper",3,"disabled","click"],["name","bubbles",4,"ngIf"],[3,"value"],["mode","ios","formControlName","subCategory","cancelText","Annuler","interface","action-sheet"],[1,"filename"],[1,"empty"],[1,"message",3,"click"],[1,"message"],["name","bubbles"]],template:function(o,a){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return a.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content",3)(7,"ion-tab-bar",4)(8,"ion-tab-button")(9,"ion-title"),n.EFF(10," new claim"),n.k0s()(),n.j41(11,"ion-tab-button")(12,"ion-title"),n.EFF(13," my claim "),n.k0s()()(),n.j41(14,"div",5)(15,"form",6)(16,"ion-item")(17,"ion-label",7),n.EFF(18),n.nI1(19,"capitalize"),n.nI1(20,"translate"),n.k0s(),n.j41(21,"ion-select",8),n.bIt("ionChange",function(f){return a.categoryChange(f)}),n.DNE(22,l,2,2,"ion-select-option",9),n.k0s()(),n.j41(23,"ion-item")(24,"ion-label",7),n.EFF(25),n.nI1(26,"capitalize"),n.nI1(27,"translate"),n.k0s(),n.DNE(28,w,2,1,"ion-select",10),n.k0s(),n.j41(29,"ion-item")(30,"ion-label",7),n.EFF(31),n.nI1(32,"capitalize"),n.nI1(33,"translate"),n.k0s(),n.j41(34,"ion-textarea",11),n.bIt("ngModelChange",function(){return a.countCharacters()}),n.k0s(),n.j41(35,"span",12),n.EFF(36),n.k0s()(),n.j41(37,"div",13)(38,"div",14),n.bIt("click",function(){return a.preventAlert()}),n.nrm(39,"ion-img",15),n.DNE(40,T,2,1,"span",16),n.DNE(41,E,2,1,"span",17),n.k0s(),n.DNE(42,R,4,5,"div",18),n.j41(43,"input",19),n.bIt("change",function(f){return a.setAttachment(f)}),n.k0s(),n.DNE(44,I,2,1,"div",20),n.k0s()(),n.j41(45,"ion-button",21),n.bIt("click",function(){return a.postFeedback()}),n.j41(46,"ion-label"),n.EFF(47),n.nI1(48,"translate"),n.k0s(),n.DNE(49,A,1,0,"ion-spinner",22),n.k0s()()()),2&o&&(n.R7$(4),n.JRh(n.bMT(5,18,"claim-form-page.title")),n.R7$(11),n.Y8G("formGroup",a.feedbackForm),n.R7$(3),n.JRh(n.bMT(19,20,n.bMT(20,22,"claim-form-page.input-type-label"))),n.R7$(4),n.Y8G("ngForOf",a.claimTypes),n.R7$(3),n.JRh(n.bMT(26,24,n.bMT(27,26,"claim-form-page.input-motivation-label"))),n.R7$(3),n.Y8G("ngIf",a.isClaimSelected),n.R7$(3),n.SpI("",n.bMT(32,28,n.bMT(33,30,"claim-form-page.input-message-label"))," "),n.R7$(3),n.Y8G("maxlength",a.MAX_CHARACTER_LENGTH)("autoGrow",!0),n.R7$(2),n.Lme("",a.numberOfCharacters,"/",a.MAX_CHARACTER_LENGTH," caract\xe8res"),n.R7$(4),n.Y8G("ngif",null==a.file?null:a.file.name),n.R7$(1),n.Y8G("ngIf",!(null!=a.file&&a.file.name)),n.R7$(1),n.Y8G("ngIf",!(null!=a.file&&a.file.name||a.isAprove)),n.R7$(2),n.Y8G("ngIf",null==a.file?null:a.file.name),n.R7$(1),n.Y8G("disabled",a.feedbackForm.invalid||a.isLoading),n.R7$(2),n.SpI(" ",n.bMT(48,32,"claim-form-page.submit-button-label")," "),n.R7$(2),n.Y8G("ngIf",a.isLoading))},dependencies:[e.Sq,e.bT,c.qT,c.BC,c.cb,c.tU,s.Jm,s.W9,s.eU,s.KW,s.uz,s.he,s.Nm,s.Ip,s.w2,s.Jq,s.qW,s.nc,s.BC,s.ai,s.Je,s.Gw,c.j4,c.JD,h.D9,u.F],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:calc(41 * var(--res));padding-block:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#f4f4f4;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;color:var(--clr-primary-700);font-family:var(--mont-regular)}ion-content[_ngcontent-%COMP%]{--background: #f4f4f4}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:0}.item-interactive[_nghost-%COMP%]{--border-width: none}.disclaimer[_ngcontent-%COMP%]{font-size:14px;margin-bottom:25px!important;letter-spacing:.4px;text-align:justify;color:#000001;font-family:Mont Regular}.disclaimer[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{text-decoration:underline;color:red}.disclaimer[_ngcontent-%COMP%]   .contact-cimencam[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont Bold}.input-group[_ngcontent-%COMP%]{overflow:overlay;margin-bottom:24px;margin-top:0!important}.input-group[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{font-family:Mont Regular}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:has(ion-select){border:1px solid #d8d8d8;border-radius:calc(40 * var(--res));padding:0 calc(40 * var(--res)) calc(40 * var(--res));background-color:#fff;--box-shadow: 0 4px 14px 0 rgba(var(--ion-color-primary-rgb), .3)}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:has(ion-select)   ion-select[_ngcontent-%COMP%]{--border-width: none}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:has(ion-textarea){border:2px dashed #419CFB;border-radius:calc(40 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:has(ion-textarea)   span[_ngcontent-%COMP%]{padding:0 calc(40 * var(--res)) 0}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:has(ion-textarea)   textarea[_ngcontent-%COMP%]{padding:0 calc(40 * var(--res))!important}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:has(ion-textarea)   ion-label[_ngcontent-%COMP%]{padding:0 calc(40 * var(--res)) 0}.input-group[_ngcontent-%COMP%]   .upper[_ngcontent-%COMP%]{text-transform:capitalize}.input-group[_ngcontent-%COMP%]   .new-attachment[_ngcontent-%COMP%]{gap:calc(10 * var(--res));display:flex;align-items:center;justify-content:space-between;box-sizing:border-box}.input-group[_ngcontent-%COMP%]   .new-attachment[_ngcontent-%COMP%]   .file-section[_ngcontent-%COMP%]{width:50%;gap:calc(10 * var(--res));display:flex;align-items:center;justify-content:center;border-radius:calc(20 * var(--res));background-color:#ccdef1}.input-group[_ngcontent-%COMP%]   .new-attachment[_ngcontent-%COMP%]   .file-section[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:27%;height:calc(100 * var(--res));border-radius:calc(20 * var(--res))}.input-group[_ngcontent-%COMP%]   .new-attachment[_ngcontent-%COMP%]   .file-section[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{flex-grow:1;text-align:center}.input-group[_ngcontent-%COMP%]   .new-attachment[_ngcontent-%COMP%]   .file-section[_ngcontent-%COMP%]   span.filename[_ngcontent-%COMP%]{text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap;font-size:.9375rem}.input-group[_ngcontent-%COMP%]   .new-attachment[_ngcontent-%COMP%]   .file-section[_ngcontent-%COMP%]   span.empty[_ngcontent-%COMP%]{font-weight:100;color:#6d839d;font-family:Mont Regular;font-size:.8125rem}.input-group[_ngcontent-%COMP%]   .new-attachment[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{width:50%;margin:auto;display:flex;align-items:center;justify-content:center;color:#0b305c;text-decoration:underline;font-family:Mont SemiBold;font-size:.8125rem}.input-group[_ngcontent-%COMP%]   .attachment[_ngcontent-%COMP%]{display:flex;width:100%;margin-top:10px;margin-bottom:15px}.input-group[_ngcontent-%COMP%]   .attachment[_ngcontent-%COMP%]   .attachment-line[_ngcontent-%COMP%]{width:20%;height:2px;background-color:#dedede;margin-top:10px}.input-group[_ngcontent-%COMP%]   .attachment[_ngcontent-%COMP%]   .attachment-title[_ngcontent-%COMP%]{width:60%;font-family:Mont Regular;font-size:14px;margin-left:12px}.input-group[_ngcontent-%COMP%]   .attachment-btn[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;margin:20px 0}.input-group[_ngcontent-%COMP%]   .attachemnt-free[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;margin:20px}.input-group[_ngcontent-%COMP%]   .upload-btn[_ngcontent-%COMP%]{--background: transparent}.input-group[_ngcontent-%COMP%]   .upload-btn[_ngcontent-%COMP%]   .icon.icon_attachment[_ngcontent-%COMP%]{width:36px!important;height:36px!important}.input-group[_ngcontent-%COMP%]   .file-view[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:100%;height:auto;border:1px solid #dedede;border-radius:3px;font-family:Mont Bold;font-size:15px}"]})}}return i})()}];let S=(()=>{class i{static{this.\u0275fac=function(o){return new(o||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({imports:[_.iI.forChild(z),_.iI]})}}return i})();var j=r(93887);let G=(()=>{class i{static{this.\u0275fac=function(o){return new(o||i)}}static{this.\u0275mod=n.$C({type:i})}static{this.\u0275inj=n.G2t({providers:[M.ep],imports:[e.MD,c.YN,s.bv,S,c.X1,h.h,j.G]})}}return i})()},91285:(k,b,r)=>{r.d(b,{y:()=>x});var h=r(99987),e=r(2978),c=r(77897),s=r(62049),_=r(82571),p=r(37222),M=r(56610);function P(d,F){if(1&d){const g=e.RV6();e.j41(0,"div",6)(1,"label",7),e.EFF(2),e.k0s(),e.j41(3,"ion-item",8)(4,"ion-textarea",9),e.bIt("ngModelChange",function(l){e.eBV(g);const O=e.XpG();return e.Njj(O.annulationMessage=l)}),e.k0s()()()}if(2&d){const g=e.XpG();e.R7$(2),e.JRh(null==g.dataModal?null:g.dataModal.message),e.R7$(2),e.Y8G("ngModel",g.annulationMessage)}}function v(d,F){if(1&d){const g=e.RV6();e.j41(0,"ion-button",10),e.bIt("click",function(){e.eBV(g);const l=e.XpG();return e.Njj(l.cancel())}),e.j41(1,"ion-label"),e.EFF(2),e.nI1(3,"titlecase"),e.k0s()()}if(2&d){const g=e.XpG();e.R7$(2),e.JRh(e.bMT(3,1,null==g.dataModal?null:g.dataModal.cancelButton))}}const y=function(d){return{"annulation-mode":d}},n=function(d){return{"single-button":d}};let x=(()=>{class d{constructor(g,u,l){this.modalCtrl=g,this.translateService=u,this.commonSrv=l,this.annulationMessage=""}ngOnInit(){}cancel(){return this.modalCtrl.dismiss(null,"annuler")}confirm(){let g=this.annulationMessage;this.dataModal.isAnnulation&&!this.annulationMessage.trim()&&(g=this.translateService.currentLang===h.T.French?"\xc0 la demande du client":"At the customers request"),this.dataModal.handler(g),this.modalCtrl.dismiss(this.dataModal.isAnnulation?{message:g}:null,"confirm")}static{this.\u0275fac=function(u){return new(u||d)(e.rXU(c.W3),e.rXU(s.E),e.rXU(_.h))}}static{this.\u0275cmp=e.VBU({type:d,selectors:[["app-base-modal"]],inputs:{dataModal:"dataModal"},decls:12,vars:12,consts:[["id","container",1,"scroller-container",3,"ngClass"],[1,"contain-text"],["class","message-container",4,"ngIf"],[1,"btn-validate",3,"ngClass"],["fill","solid","class","cancel",3,"click",4,"ngIf"],["fill","solid","color","primary",1,"yes",3,"click"],[1,"message-container"],[1,"message"],[1,"message-input"],["rows","4",1,"custom-textarea",3,"ngModel","ngModelChange"],["fill","solid",1,"cancel",3,"click"]],template:function(u,l){1&u&&(e.j41(0,"section",0)(1,"div",1)(2,"label"),e.EFF(3),e.nrm(4,"span"),e.k0s()(),e.DNE(5,P,5,2,"div",2),e.j41(6,"div",3),e.DNE(7,v,4,3,"ion-button",4),e.j41(8,"ion-button",5),e.bIt("click",function(){return l.confirm()}),e.j41(9,"ion-label"),e.EFF(10),e.nI1(11,"titlecase"),e.k0s()()()()),2&u&&(e.Y8G("ngClass",e.eq3(8,y,null==l.dataModal?null:l.dataModal.isAnnulation)),e.R7$(3),e.SpI(" ",null==l.dataModal?null:l.dataModal.text," "),e.R7$(2),e.Y8G("ngIf",null==l.dataModal?null:l.dataModal.isAnnulation),e.R7$(1),e.Y8G("ngClass",e.eq3(10,n,null==l.dataModal?null:l.dataModal.isAnnulation)),e.R7$(1),e.Y8G("ngIf",!(null!=l.dataModal&&l.dataModal.isAnnulation)),e.R7$(3),e.JRh(e.bMT(11,6,null==l.dataModal?null:l.dataModal.confirmButton)))},dependencies:[p.BC,p.vS,c.Jm,c.uz,c.he,c.nc,c.Gw,M.YU,M.bT,M.PV],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:var(--container-padding);background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]{text-align:center;font-family:var(--mont-regular);color:#143c5d;line-height:1;margin-bottom:var(--container-margin)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:"#0B305C";font-size:20px;text-align:center;display:block;margin-bottom:16px;font-family:var(--mont-bold)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]{margin:14px 0}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-size:14px;font-weight:400;display:block;margin-bottom:8px;text-align:center;font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{--background: #f8f9fa;--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(20,60,93,.5);border-radius:8px}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]   .custom-textarea[_ngcontent-%COMP%]{--padding-top: 8px;--padding-bottom: 8px;min-height:100px;font-size:14px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:center;margin-top:13px;gap:1em}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]{gap:0}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;max-width:none}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{--background: #fff;width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);color:#143c5d;font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[disabled][_ngcontent-%COMP%]{opacity:.5}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 16px}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;font-size:16px}']})}}return d})()},11244:(k,b,r)=>{r.d(b,{F:()=>e});var h=r(2978);let e=(()=>{class c{transform(_){return console.log(),`${_?.slice(0,1)?.toLocaleUpperCase()+_?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(p){return new(p||c)}}static{this.\u0275pipe=h.EJ8({name:"capitalize",type:c,pure:!0})}}return c})()}}]);