@use '../themes/theme' as *;
@use './fonts' as *;

//TODO: CREATE MARKDOWN FOR THIS VARIABLE
:root {

  /**
  * Colors
  */
  @each $color, $shades in $colors {
    @each $shade, $value in $shades {
      --clr-#{$color}-#{$shade}: #{$value};
    }
  }

  // Font Famillies
  @each $font, $shades in $fonts {
    @each $shade, $value in $shades {
      --#{$font}-#{$shade}: "#{$value}";
    }
  }

  --clr-white: #ffffff;
  --clr-black: #000000;
  --clr-body-bg: #f6f9fc;

  /**
  * Font Famillies
  */
  // @each $font, $shades in $fonts {
  //   @each $shade, $value in $shades {
  //       --#{$font}-#{$shade}: '#{$value}';
  //   }
  // }

  /**
  * Typography
  */
  --scale: 1.250;
  --fs-base: 16px;
  --padding: var(--res);
  --margin: car(--res);
  --container-padding: calc(7.5% - var(--res));
  --container-margin: calc(7.5% - var(--res));
  --container-padding: 2rem;

  --fs-300: calc(var(--fs-base) / var(--scale));
  --fs-400: 1rem;
  --fs-500: calc(var(--fs-400) * var(--scale));
  --fs-600: calc(var(--fs-500) * var(--scale));
  --fs-700: calc(var(--fs-600) * var(--scale));
  --fs-800: calc(var(--fs-700) * var(--scale));
  --fs-900: calc(var(--fs-800) * var(--scale));
  --fs-8-px: 8px;
  --fs-10-px: 0.625rem;
  --fs-12-px: clamp(12px, 3.5vw, 20px);
  --fs-13-px: clamp(13px, 3.5vw, 20px);
  --fs-14-px: clamp(14px, 3.5vw, 20px);
  --fs-16-px: clamp(16px, 3.5vw, 20px);
  --fs-18-px: clamp(18px, 3.5vw, 20px);
  --fs-20-px: clamp(20px, 3.6vw, 30px);
  --fs-15-px: 0.9375rem;
  --fs-32-px: 2rem;


  /*
  --fs-400: clamp(0.75rem, 1vw + 0.5rem, 1rem);
  --fs-500: clamp(1.25rem, 1vw + 1rem, 1.563rem);
  --fs-600: clamp(1.563rem, 2vw + 1rem, 1.953rem);
  --fs-700: clamp(1.953rem, 2vw + 1rem, 2.441rem);
  --fs-800: clamp(2.441rem, 2vw + 1rem, 3.052rem);
  --fs-900: clamp(3.052rem, 2vw + 1rem, 3.815rem); */

  /**
  * Container size
  */
  --padding: 50px;
  --container-max: 1200px;
  --container: calc(100% - var(--padding));
}

@media (max-width: 350px) {
  :root {
    --scale: 1.200;
    --padding: 20px;
  }
}

@media (min-width: 900px) {
  :root {
    --scale: 1.333;
  }
}

@media only screen and (min-width: 1320px) {
  :root {
    --container: 1300px;
  }
}