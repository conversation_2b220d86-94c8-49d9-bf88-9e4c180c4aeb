"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2391],{22391:(R,C,a)=>{a.r(C),a.d(C,{IndirectUserPageModule:()=>N});var M=a(37222),e=a(77897),d=a(56610),b=a(77575),s=a(73308),n=a(2978),f=a(74657),k=a(91285),O=a(99987),y=a(12330),h=a(58133),v=a(23985),u=a(82571),w=a(94440);const g=["popover"];function m(r,p){1&r&&n.nrm(0,"ion-progress-bar",29)}function l(r,p){if(1&r){const t=n.RV6();n.j41(0,"div",30)(1,"ion-searchbar",31),n.bIt("ngModelChange",function(i){n.eBV(t);const c=n.XpG();return n.Njj(c.searchQuery=i)})("ngModelChange",function(){n.eBV(t);const i=n.XpG();return n.Njj(i.filterUsers())}),n.k0s()()}if(2&r){const t=n.XpG();n.R7$(1),n.Y8G("ngModel",t.searchQuery)}}function x(r,p){1&r&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",32),n.nrm(2,"ion-skeleton-text",33),n.k0s()()),2&r&&(n.R7$(2),n.Y8G("animated",!0))}const F=function(r){return{"empty-state":r}},T=function(r){return["/navigation/indirect-user/detail",r]};function j(r,p){if(1&r){const t=n.RV6();n.j41(0,"div",36)(1,"ion-label",37)(2,"h2"),n.EFF(3),n.nI1(4,"truncateString"),n.k0s(),n.j41(5,"span"),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"span"),n.EFF(9),n.nI1(10,"translate"),n.k0s()(),n.j41(11,"span",38),n.EFF(12),n.k0s(),n.j41(13,"div",39)(14,"ion-img",40),n.bIt("click",function(i){const _=n.eBV(t).$implicit,I=n.XpG(2);return n.Njj(I.presentPopover(i,_))}),n.k0s()()()}if(2&r){const t=p.$implicit,o=n.XpG(2);n.Y8G("ngClass",n.eq3(16,F,0===o.filteredUsers.length)),n.R7$(1),n.Y8G("routerLink",n.eq3(18,T,null==t?null:t._id)),n.R7$(2),n.JRh(n.i5U(4,9,null==t?null:t.firstName,11)),n.R7$(3),n.Lme("",n.bMT(7,12,"indirect-user.phone")," : ",null==t?null:t.tel,""),n.R7$(3),n.E5c("",n.bMT(10,14,"indirect-user.address")," : ",null==t||null==t.address?null:t.address.region," ",null==t||null==t.address?null:t.address.city,""),n.R7$(3),n.JRh(null==t?null:t.firstName)}}function D(r,p){if(1&r&&(n.j41(0,"div",34),n.DNE(1,j,15,20,"div",35),n.k0s()),2&r){const t=n.XpG();n.R7$(1),n.Y8G("ngForOf",t.filteredUsers)}}function E(r,p){1&r&&(n.j41(0,"div",41),n.nrm(1,"ion-img",42),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&r&&(n.R7$(3),n.JRh(n.bMT(4,1,"indirect-clients.empty")))}function z(r,p){if(1&r){const t=n.RV6();n.j41(0,"ion-item",44),n.bIt("click",function(){n.eBV(t);const i=n.XpG(2);return n.Njj(i.localisation())}),n.nrm(1,"ion-icon",50),n.j41(2,"ion-label",48),n.EFF(3),n.nI1(4,"translate"),n.k0s()()}2&r&&(n.R7$(3),n.JRh(n.bMT(4,1,"indirect-clients.localisation")))}function $(r,p){if(1&r){const t=n.RV6();n.j41(0,"ion-list",43)(1,"ion-item",44),n.bIt("click",function(){n.eBV(t);const i=n.XpG();return n.Njj(i.openModalDeletingUser())}),n.nrm(2,"ion-icon",45),n.j41(3,"ion-label",46),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"ion-item",44),n.bIt("click",function(){n.eBV(t);const i=n.XpG();return n.Njj(i.editProspect())}),n.nrm(7,"ion-icon",47),n.j41(8,"ion-label",48),n.EFF(9),n.nI1(10,"translate"),n.k0s()(),n.DNE(11,z,5,3,"ion-item",49),n.k0s()}if(2&r){const t=n.XpG();n.R7$(4),n.JRh(n.bMT(5,3,"indirect-clients.delete")),n.R7$(5),n.JRh(n.bMT(10,5,"indirect-clients.edit")),n.R7$(2),n.Y8G("ngIf",null==t.currentUser?null:t.currentUser.localisation)}}let B=(()=>{class r{constructor(t,o){this.userSrv=t,this.commonSrv=o,this.location=(0,n.WQX)(d.aZ),this.translateService=(0,n.WQX)(f.c$),this.modalCtrl=(0,n.WQX)(e.W3),this.router=(0,n.WQX)(b.Ix),this.isOpen=!1,this.users=[],this.skeletons=[1,2,3,4,5,6],this.filterData={category:0,limit:100,offset:0},this.filteredUsers=[],this.searchQuery="",this.isSearchVisible=!1,this.isMenuOpen={},this.userCategory=h.s,this.totalUsers=0}toggleSearch(){this.isSearchVisible=!this.isSearchVisible}ngOnInit(){var t=this;return(0,s.A)(function*(){t.isLoading=!0,yield t.getUsers(),t.router.events.subscribe(o=>{o instanceof b.wF&&"/navigation/indirect-user"===o.url&&t.getUsers()})})()}reset(){this.filterData.commercialRegion=null,this.ngOnInit()}presentPopover(t,o){this.popover?(this.popover.event=t,this.isOpen=!0,this.currentUser=o):console.error("Popover is not defined")}editProspect(){var t=this;return(0,s.A)(function*(){t.popover&&(yield t.popover.dismiss(),t.isOpen=!1),t.userSrv.currentUserParticular=t.currentUser,t.router.navigate(["/navigation/manage-user/create-indirect-user"])})()}onAddUser(){var t=this;return(0,s.A)(function*(){t.userSrv.currentUserParticular=null,t.router.navigate(["/navigation/manage-user/create-indirect-user"])})()}getUserCommercial(){var t=this;return(0,s.A)(function*(){t.filterData.commercialRegion=t.commonSrv.user?.address.commercialRegion,t.ngOnInit()})()}localisation(){var t=this;return(0,s.A)(function*(){t.popover&&(yield t.popover.dismiss(),t.isOpen=!1),t.userSrv.currentUserParticular=t.currentUser,t.router.navigate(["/navigation/indirect-user/location-view"])})()}localisations(){var t=this;return(0,s.A)(function*(){t.isLoading=!0;const o=t.commonSrv.user?.address.commercialRegion,i={...t.filterData,commercialRegion:o};try{const I=((yield t.userSrv.getUsers(i)).data||[]).filter(P=>P.localisation&&P?.localisation?.latitude&&P?.localisation?.longitude).map(P=>({latitude:P?.localisation?.latitude,longitude:P?.localisation?.longitude}));t.userSrv.currentUserLocalisation=I,t.router.navigate(["/navigation/indirect-user/location-view"])}catch(c){throw yield t.commonSrv.showToast({color:"danger",message:"Erreur lors de la r\xe9cup\xe9ration des utilisateurs"+c?.error?.message}),c}finally{t.isLoading=!1}})()}preventAlert(){var t=this;return(0,s.A)(function*(){t.isOpen=!1})()}back(){[h.s.DonutAnimator,h.s.Particular].includes(this.commonSrv.user?.category)?this.router.navigate(["/navigation/home-alt"]):this.router.navigate(["/navigation/home"])}openModalDeletingUser(){var t=this;return(0,s.A)(function*(){try{t.popover&&(yield t.popover.dismiss(),t.isOpen=!1);const o=yield t.modalCtrl.create({component:k.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:t.translateService.currentLang===O.T.French?"Supprimer":"Delete",cancelButton:t.translateService.currentLang===O.T.French?"Annuler":"Cancel",text:t.translateService.currentLang===O.T.French?`Vous \xeates sur le point de supprimer le compte ${t.currentUser?.firstName} dans l'application Clic Cadyst.\nConfirmez-vous cette action ?`:`You are about to delete the account ${t.currentUser?.firstName} from the Clic Cadyst application.\nDo you confirm this action?`,cssClass:"custom-loading",handler:(c=(0,s.A)(function*(){return yield t.deleteCurrentUser(t.currentUser)}),function(){return c.apply(this,arguments)})}}});yield o.present();const{role:i}=yield o.onDidDismiss();console.log("Modal dismissed with role:",i)}catch(o){console.error("Error opening delete user modal:",o)}finally{t.isOpen=!1}var c})()}deleteCurrentUser(t){var o=this;return(0,s.A)(function*(){try{t.enable=!1,yield o.userSrv.updateUserParticular(t),yield o.getUsers()}catch{}})()}showFilter(){var t=this;return(0,s.A)(function*(){const o=yield t.modalCtrl.create({component:y.f,initialBreakpoint:.7,cssClass:"modal",breakpoints:[0,.5,.7,1],mode:"ios",componentProps:{filterData:t.filterData,filteredUsersNames:t.filteredUsersNames}});o.present(),t.filterData=(yield o.onWillDismiss()).data,t.filterData&&(t.users=[],t.filterData.offset=0,yield t.getUsers())})()}getUsers(){var t=this;return(0,s.A)(function*(){try{t.skeletons=[1,2,3,4,5,6,7,8];const o=t.commonSrv.user?._id,i={...t.filterData,associatedCommercialId:o},c=yield t.userSrv.getUsers(i);t.users=c.data||[],t.filteredUsers=[...t.users],t.totalUsers=t.users.length,t.isLoading=!1,t.skeletons=[]}catch(o){console.error("Error fetching users:",o),t.users=[],t.filteredUsers=[],t.isLoading=!1,t.skeletons=[]}})()}filterUsers(){if(!this.searchQuery)return void(this.filteredUsers=[...this.users]);const t=this.searchQuery.toLowerCase().split("");this.filteredUsers=this.users.filter(o=>{const i=o.firstName.toLowerCase(),c=String(o.tel);return t.every(_=>i.includes(_)||c.includes(_))})}doRefresh(t){var o=this;return(0,s.A)(function*(){o.filterData={category:0,limit:50,offset:0},o.isLoading=!0,yield o.getUsers(),t.target.complete()})()}getFlowUsers(t){var o=this;return(0,s.A)(function*(){yield o.getUsers(),t.target.complete()})()}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(v.D),n.rXU(u.h))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-indirect-user"]],viewQuery:function(o,i){if(1&o&&n.GBs(g,5),2&o){let c;n.mGM(c=n.lsd())&&(i.popover=c.first)}},decls:54,vars:26,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"click"],[1,"buttons"],[1,"transparent",3,"click"],["name","search"],["name","funnel-outline"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["class","search",4,"ngIf"],[4,"ngFor","ngForOf"],["class","container",4,"ngIf"],["class","empty-list",4,"ngIf"],[3,"isOpen","didDismiss"],["popover",""],["vertical","bottom","horizontal","start","slot","fixed"],["name","menu"],["side","top"],[1,"fab-content",3,"click"],[1,"elts"],["color","secondary",3,"click"],["name","location-outline"],[1,"fab-tooltip"],["color","danger",3,"click"],["name","refresh-outline"],["color","primary",3,"click"],["name","add"],[3,"ionInfinite"],["type","indeterminate"],[1,"search"],[3,"ngModel","ngModelChange"],[1,"skeleton"],[3,"animated"],[1,"container"],["class","item",3,"ngClass",4,"ngFor","ngForOf"],[1,"item",3,"ngClass"],[3,"routerLink"],[1,"function"],[1,"icon-container"],["src","/assets/icons/info.svg",3,"click"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"],[1,"popover-list"],["button","",3,"click"],["slot","start","name","trash","color","danger"],["color","danger"],["slot","start","name","pencil","color","success"],["color","success"],["button","",3,"click",4,"ngIf"],["slot","start","name","home-outline","color","success",1,"blue"]],template:function(o,i){1&o&&(n.j41(0,"ion-header")(1,"div",0)(2,"ion-img",1),n.bIt("click",function(){return i.back()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",2)(7,"ion-button",3),n.bIt("click",function(){return i.toggleSearch()}),n.nrm(8,"ion-icon",4),n.k0s(),n.j41(9,"ion-button",3),n.bIt("click",function(){return i.showFilter()}),n.nrm(10,"ion-icon",5),n.k0s()()(),n.j41(11,"ion-content",6),n.DNE(12,m,1,0,"ion-progress-bar",7),n.j41(13,"ion-refresher",8),n.bIt("ionRefresh",function(_){return i.doRefresh(_)}),n.nrm(14,"ion-refresher-content",9),n.nI1(15,"translate"),n.nI1(16,"translate"),n.k0s(),n.DNE(17,l,2,1,"div",10),n.DNE(18,x,3,1,"ion-cart",11),n.DNE(19,D,2,1,"div",12),n.DNE(20,E,5,3,"div",13),n.j41(21,"ion-popover",14,15),n.bIt("didDismiss",function(){return i.isOpen=!1}),n.DNE(23,$,12,7,"ng-template"),n.k0s(),n.j41(24,"ion-fab",16)(25,"ion-fab-button"),n.nrm(26,"ion-icon",17),n.k0s(),n.j41(27,"ion-fab-list",18)(28,"div",19),n.bIt("click",function(){return i.localisations()}),n.j41(29,"div",20)(30,"ion-fab-button",21),n.bIt("click",function(){return i.localisations()}),n.nrm(31,"ion-icon",22),n.k0s()(),n.j41(32,"div",20)(33,"h2",23),n.EFF(34),n.nI1(35,"translate"),n.k0s()()(),n.j41(36,"div",19),n.bIt("click",function(){return i.reset()}),n.j41(37,"div",20)(38,"ion-fab-button",24),n.bIt("click",function(){return i.reset()}),n.nrm(39,"ion-icon",25),n.k0s()(),n.j41(40,"div",20)(41,"h2",23),n.EFF(42),n.nI1(43,"translate"),n.k0s()()(),n.j41(44,"div",19),n.bIt("click",function(){return i.onAddUser()}),n.j41(45,"div",20)(46,"ion-fab-button",26),n.bIt("click",function(){return i.onAddUser()}),n.nrm(47,"ion-icon",27),n.k0s()(),n.j41(48,"div",20)(49,"h2"),n.EFF(50),n.nI1(51,"translate"),n.k0s()()()()(),n.j41(52,"ion-infinite-scroll",28),n.bIt("ionInfinite",function(_){return i.getFlowUsers(_)}),n.nrm(53,"ion-infinite-scroll-content"),n.k0s()()),2&o&&(n.R7$(4),n.Lme("",n.bMT(5,14,"indirect-user.title")," (",i.totalUsers,")"),n.R7$(7),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",i.isLoading),n.R7$(2),n.FS9("pullingText",n.bMT(15,16,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(16,18,"refresher.refreshing"),"..."),n.R7$(3),n.Y8G("ngIf",i.isSearchVisible),n.R7$(1),n.Y8G("ngForOf",i.skeletons),n.R7$(1),n.Y8G("ngIf",!i.isLoading),n.R7$(1),n.Y8G("ngIf",!i.isLoading&&0===i.filteredUsers.length),n.R7$(1),n.Y8G("isOpen",i.isOpen),n.R7$(13),n.JRh(n.bMT(35,20,"indirect-clients.locations")),n.R7$(8),n.JRh(n.bMT(43,22,"indirect-clients.reset")),n.R7$(8),n.JRh(n.bMT(51,24,"indirect-clients.add")))},dependencies:[M.BC,M.vS,e.Jm,e.W9,e.Q8,e.YW,e.OL,e.eU,e.iq,e.KW,e.Ax,e.Hp,e.uz,e.he,e.nf,e.FH,e.To,e.Ki,e.S1,e.ds,e.Zx,e.BC,e.CF,e.Gw,e.N7,d.YU,d.Sq,d.bT,b.Wk,f.D9,w.c],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;align-items:center;background:var(--clr-white)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}.header[_ngcontent-%COMP%]{--background: #D5DFEB;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-label[_ngcontent-%COMP%]{display:grid!important}ion-label[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-weight:800}ion-label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.75rem;color:#8597ad;margin-top:8px}ion-content[_ngcontent-%COMP%]{--background: #D5DFEB}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:white;border-radius:5px;box-shadow:0 4px 4px #00000017;width:calc(100% - 48px);margin:13px auto;padding:10px 20px;display:flex;justify-content:space-between}ion-content[_ngcontent-%COMP%]   .function[_ngcontent-%COMP%]{display:inline-block;width:100px;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:700;color:#0b305c;background:rgba(65,156,251,.168627451);padding:5px 10px;border-radius:30px;margin:auto;font-size:14px;font-weight:500}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:10vh;display:flex;flex-direction:column;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]{margin-bottom:3.8rem;gap:.8rem;align-items:flex-start;background:#ffffff;width:8.6em;border-radius:5px;padding-left:10px;padding-bottom:10px;padding-top:10px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]{display:flex;gap:5px;align-items:center}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;color:#0b305c;text-align:center;font-size:14px;font-weight:800!important}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{position:relative;width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=primary][_ngcontent-%COMP%]{--background: #4caf50;--color: white}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=secondary][_ngcontent-%COMP%]{--background: #2196f3;--color: white}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=danger][_ngcontent-%COMP%]{--background: #f44336;--color: white}.buttons[_ngcontent-%COMP%]{display:flex;margin:6px 24px 6px 0;justify-content:space-between}.buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent !important;--box-shadow: none;color:#0b305c;border:none;--padding: auto}.buttons[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#419cfb}.dropdown-menu[_ngcontent-%COMP%]{position:absolute;background:#F1F2F4;--text-align: left;right:-6px;box-shadow:0 2px 10px #0003;z-index:10}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: transparent;text-align:left}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{padding:auto;margin:auto}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border: 1px solid #D2DBE7;--background: #F1F2F4}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover{background:#f0f0f0}.dropdown-menu[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{align-items:center;justify-content:flex-start;--background: #F1F2F4;--border: 1px solid #D2DBE7;text-align:left;--border-radius: none}.dropdown-menu[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}ion-list[_ngcontent-%COMP%]{display:grid;margin-right:24px}ion-list[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: #F1F2F4;--border: 1px solid #D2DBE7;text-align:left;--border-radius: none;width:100%}ion-list[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{text-align:left;padding:2px}.icon-container[_ngcontent-%COMP%]{position:relative;margin:auto 0}.custom-back-button[_ngcontent-%COMP%]::part(back-button-text){display:none}.success[_ngcontent-%COMP%]{color:green;text-transform:capitalize}.danger[_ngcontent-%COMP%]{color:red;text-transform:capitalize}.blue[_ngcontent-%COMP%]{color:#419cfb;text-transform:capitalize}.skeleton[_ngcontent-%COMP%]{height:3.5em;width:100%;margin-bottom:1rem;display:flex;align-items:center;justify-content:center}.skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:90%;height:100%}.custom-popover[_ngcontent-%COMP%]{--width: 150px}.small-icon[_ngcontent-%COMP%]{font-size:18px}.popover-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 8px}.popover[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.popover[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{margin-top:4px;font-size:14px;color:var(--ion-color-medium)}ion-searchbar[_ngcontent-%COMP%]{width:calc(100% - 48px);margin:auto}']})}}return r})();var U=a(36594);function S(r,p){1&r&&n.nrm(0,"map-marker",5),2&r&&n.Y8G("position",p.$implicit)}const G=[{path:"",component:B},{path:"detail/:id",loadChildren:()=>a.e(8206).then(a.bind(a,68206)).then(r=>r.UserDetailPageModule)},{path:"location-view",component:(()=>{class r{constructor(t){this.userSrv=t,this.location=(0,n.WQX)(d.aZ),this.userLocations=[],this.center={lat:0,lng:0},this.markers=[]}ngOnInit(){var t=this;return(0,s.A)(function*(){t.userLocations=t.userSrv.currentUserLocalisation,t.userLocations.length>0?(t.markers=t.userLocations.map(o=>({lat:o.latitude,lng:o.longitude})),t.center={lat:t.markers[0].lat,lng:t.markers[0].lng}):(t.latitude=t.userSrv.currentUserParticular?.localisation?.latitude,t.longitude=t.userSrv.currentUserParticular?.localisation?.longitude,t.latitude&&t.longitude&&(t.center={lat:t.latitude,lng:t.longitude},t.markers.push({lat:t.latitude,lng:t.longitude})))})()}closeModal(){this.userSrv.currentUserLocalisation=[],this.location.back()}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(v.D))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-location-view"]],decls:9,vars:3,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"click"],[2,"height","100%","width","100%"],["height","100%","width","100%",3,"center","zoom"],[3,"position",4,"ngFor","ngForOf"],[3,"position"]],template:function(o,i){1&o&&(n.j41(0,"ion-header")(1,"div",0)(2,"ion-img",1),n.bIt("click",function(){return i.closeModal()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4,"Localisation de l'utilisateur"),n.k0s()()(),n.j41(5,"ion-content")(6,"div",2)(7,"google-map",3),n.DNE(8,S,1,1,"map-marker",4),n.k0s()()()),2&o&&(n.R7$(7),n.Y8G("center",i.center)("zoom",15),n.R7$(1),n.Y8G("ngForOf",i.markers))},dependencies:[e.W9,e.eU,e.KW,e.BC,d.Sq,U.u6,U.fU],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;align-items:center;background:var(--clr-white);height:2em}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}.header[_ngcontent-%COMP%]{--background: #D5DFEB;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}"]})}}return r})()}];let L=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[b.iI.forChild(G),b.iI]})}}return r})();var A=a(93887);let N=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[M.YN,e.bv,d.MD,M.X1,f.h,L,U.rJ,A.G]})}}return r})()},91285:(R,C,a)=>{a.d(C,{y:()=>v});var M=a(99987),e=a(2978),d=a(77897),b=a(62049),s=a(82571),n=a(37222),f=a(56610);function k(u,w){if(1&u){const g=e.RV6();e.j41(0,"div",6)(1,"label",7),e.EFF(2),e.k0s(),e.j41(3,"ion-item",8)(4,"ion-textarea",9),e.bIt("ngModelChange",function(l){e.eBV(g);const x=e.XpG();return e.Njj(x.annulationMessage=l)}),e.k0s()()()}if(2&u){const g=e.XpG();e.R7$(2),e.JRh(null==g.dataModal?null:g.dataModal.message),e.R7$(2),e.Y8G("ngModel",g.annulationMessage)}}function O(u,w){if(1&u){const g=e.RV6();e.j41(0,"ion-button",10),e.bIt("click",function(){e.eBV(g);const l=e.XpG();return e.Njj(l.cancel())}),e.j41(1,"ion-label"),e.EFF(2),e.nI1(3,"titlecase"),e.k0s()()}if(2&u){const g=e.XpG();e.R7$(2),e.JRh(e.bMT(3,1,null==g.dataModal?null:g.dataModal.cancelButton))}}const y=function(u){return{"annulation-mode":u}},h=function(u){return{"single-button":u}};let v=(()=>{class u{constructor(g,m,l){this.modalCtrl=g,this.translateService=m,this.commonSrv=l,this.annulationMessage=""}ngOnInit(){}cancel(){return this.modalCtrl.dismiss(null,"annuler")}confirm(){let g=this.annulationMessage;this.dataModal.isAnnulation&&!this.annulationMessage.trim()&&(g=this.translateService.currentLang===M.T.French?"\xc0 la demande du client":"At the customers request"),this.dataModal.handler(g),this.modalCtrl.dismiss(this.dataModal.isAnnulation?{message:g}:null,"confirm")}static{this.\u0275fac=function(m){return new(m||u)(e.rXU(d.W3),e.rXU(b.E),e.rXU(s.h))}}static{this.\u0275cmp=e.VBU({type:u,selectors:[["app-base-modal"]],inputs:{dataModal:"dataModal"},decls:12,vars:12,consts:[["id","container",1,"scroller-container",3,"ngClass"],[1,"contain-text"],["class","message-container",4,"ngIf"],[1,"btn-validate",3,"ngClass"],["fill","solid","class","cancel",3,"click",4,"ngIf"],["fill","solid","color","primary",1,"yes",3,"click"],[1,"message-container"],[1,"message"],[1,"message-input"],["rows","4",1,"custom-textarea",3,"ngModel","ngModelChange"],["fill","solid",1,"cancel",3,"click"]],template:function(m,l){1&m&&(e.j41(0,"section",0)(1,"div",1)(2,"label"),e.EFF(3),e.nrm(4,"span"),e.k0s()(),e.DNE(5,k,5,2,"div",2),e.j41(6,"div",3),e.DNE(7,O,4,3,"ion-button",4),e.j41(8,"ion-button",5),e.bIt("click",function(){return l.confirm()}),e.j41(9,"ion-label"),e.EFF(10),e.nI1(11,"titlecase"),e.k0s()()()()),2&m&&(e.Y8G("ngClass",e.eq3(8,y,null==l.dataModal?null:l.dataModal.isAnnulation)),e.R7$(3),e.SpI(" ",null==l.dataModal?null:l.dataModal.text," "),e.R7$(2),e.Y8G("ngIf",null==l.dataModal?null:l.dataModal.isAnnulation),e.R7$(1),e.Y8G("ngClass",e.eq3(10,h,null==l.dataModal?null:l.dataModal.isAnnulation)),e.R7$(1),e.Y8G("ngIf",!(null!=l.dataModal&&l.dataModal.isAnnulation)),e.R7$(3),e.JRh(e.bMT(11,6,null==l.dataModal?null:l.dataModal.confirmButton)))},dependencies:[n.BC,n.vS,d.Jm,d.uz,d.he,d.nc,d.Gw,f.YU,f.bT,f.PV],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:var(--container-padding);background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]{text-align:center;font-family:var(--mont-regular);color:#143c5d;line-height:1;margin-bottom:var(--container-margin)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:"#0B305C";font-size:20px;text-align:center;display:block;margin-bottom:16px;font-family:var(--mont-bold)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]{margin:14px 0}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-size:14px;font-weight:400;display:block;margin-bottom:8px;text-align:center;font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{--background: #f8f9fa;--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(20,60,93,.5);border-radius:8px}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]   .custom-textarea[_ngcontent-%COMP%]{--padding-top: 8px;--padding-bottom: 8px;min-height:100px;font-size:14px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:center;margin-top:13px;gap:1em}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]{gap:0}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;max-width:none}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{--background: #fff;width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);color:#143c5d;font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[disabled][_ngcontent-%COMP%]{opacity:.5}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 16px}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;font-size:16px}']})}}return u})()},94440:(R,C,a)=>{a.d(C,{c:()=>e});var M=a(2978);let e=(()=>{class d{transform(s,...n){return s?s.length>n[0]?`${s.substring(0,n[0]-3)}...`:s:""}static{this.\u0275fac=function(n){return new(n||d)}}static{this.\u0275pipe=M.EJ8({name:"truncateString",type:d,pure:!0})}}return d})()}}]);