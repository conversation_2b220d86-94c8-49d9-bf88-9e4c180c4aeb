"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1001],{31001:(k,g,r)=>{r.r(g),r.d(g,{ion_loading:()=>_});var b=r(73308),i=r(29814),p=r(42673),h=r(69324),c=r(97255),x=r(35367),a=r(28775);r(94706);const t=n=>{const s=(0,a.c)(),o=(0,a.c)(),l=(0,a.c)();return o.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),l.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),s.addElement(n).easing("ease-in-out").duration(200).addAnimation([o,l])},d=n=>{const s=(0,a.c)(),o=(0,a.c)(),l=(0,a.c)();return o.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),l.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),s.addElement(n).easing("ease-in-out").duration(200).addAnimation([o,l])},u=n=>{const s=(0,a.c)(),o=(0,a.c)(),l=(0,a.c)();return o.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),l.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),s.addElement(n).easing("ease-in-out").duration(200).addAnimation([o,l])},f=n=>{const s=(0,a.c)(),o=(0,a.c)(),l=(0,a.c)();return o.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),l.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),s.addElement(n).easing("ease-in-out").duration(200).addAnimation([o,l])},_=class{constructor(n){(0,i.r)(this,n),this.didPresent=(0,i.e)(this,"ionLoadingDidPresent",7),this.willPresent=(0,i.e)(this,"ionLoadingWillPresent",7),this.willDismiss=(0,i.e)(this,"ionLoadingWillDismiss",7),this.didDismiss=(0,i.e)(this,"ionLoadingDidDismiss",7),this.customHTMLEnabled=p.c.get("innerHTMLTemplatesEnabled",h.E),this.presented=!1,this.keyboardClose=!0,this.duration=0,this.backdropDismiss=!1,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.onBackdropTap=()=>{this.dismiss(void 0,c.B)}}connectedCallback(){(0,c.e)(this.el)}componentWillLoad(){if(void 0===this.spinner){const n=(0,p.b)(this);this.spinner=p.c.get("loadingSpinner",p.c.get("spinner","ios"===n?"lines":"crescent"))}}present(){var n=this;return(0,b.A)(function*(){yield(0,c.d)(n,"loadingEnter",t,u),n.duration>0&&(n.durationTimeout=setTimeout(()=>n.dismiss(),n.duration+10))})()}dismiss(n,s){return this.durationTimeout&&clearTimeout(this.durationTimeout),(0,c.f)(this,n,s,"loadingLeave",d,f)}onDidDismiss(){return(0,c.g)(this.el,"ionLoadingDidDismiss")}onWillDismiss(){return(0,c.g)(this.el,"ionLoadingWillDismiss")}renderLoadingMessage(n){const{customHTMLEnabled:s,message:o}=this;return s?(0,i.h)("div",{class:"loading-content",id:n,innerHTML:(0,h.a)(o)}):(0,i.h)("div",{class:"loading-content",id:n},o)}render(){const{message:n,spinner:s,htmlAttributes:o,overlayIndex:l}=this,w=(0,p.b)(this),y=`loading-${l}-msg`;return(0,i.h)(i.H,Object.assign({role:"dialog","aria-modal":"true","aria-labelledby":void 0!==n?y:null,tabindex:"-1"},o,{style:{zIndex:`${4e4+this.overlayIndex}`},onIonBackdropTap:this.onBackdropTap,class:Object.assign(Object.assign({},(0,x.g)(this.cssClass)),{[w]:!0,"overlay-hidden":!0,"loading-translucent":this.translucent})}),(0,i.h)("ion-backdrop",{visible:this.showBackdrop,tappable:this.backdropDismiss}),(0,i.h)("div",{tabindex:"0"}),(0,i.h)("div",{class:"loading-wrapper ion-overlay-wrapper"},s&&(0,i.h)("div",{class:"loading-spinner"},(0,i.h)("ion-spinner",{name:s,"aria-hidden":"true"})),void 0!==n&&this.renderLoadingMessage(y)),(0,i.h)("div",{tabindex:"0"}))}get el(){return(0,i.i)(this)}};_.style={ios:".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, #f9f9f9));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, #666666);--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:14px}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;padding-left:34px;padding-right:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.loading-wrapper.sc-ion-loading-ios{padding-left:unset;padding-right:unset;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px}}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{margin-left:16px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{margin-left:unset;-webkit-margin-start:16px;margin-inline-start:16px}}",md:".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, #f2f2f2);--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #3880ff);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, #262626);font-size:14px}.loading-wrapper.sc-ion-loading-md{border-radius:2px;padding-left:24px;padding-right:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.loading-wrapper.sc-ion-loading-md{padding-left:unset;padding-right:unset;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px}}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{margin-left:16px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{margin-left:unset;-webkit-margin-start:16px;margin-inline-start:16px}}"}},35367:(k,g,r)=>{r.d(g,{c:()=>p,g:()=>c,h:()=>i,o:()=>a});var b=r(73308);const i=(e,t)=>null!==t.closest(e),p=(e,t)=>"string"==typeof e&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},t):t,c=e=>{const t={};return(e=>void 0!==e?(Array.isArray(e)?e:e.split(" ")).filter(d=>null!=d).map(d=>d.trim()).filter(d=>""!==d):[])(e).forEach(d=>t[d]=!0),t},x=/^[a-z][a-z0-9+\-.]*:/,a=function(){var e=(0,b.A)(function*(t,d,u,f){if(null!=t&&"#"!==t[0]&&!x.test(t)){const m=document.querySelector("ion-router");if(m)return d?.preventDefault(),m.push(t,u,f)}return!1});return function(d,u,f,m){return e.apply(this,arguments)}}()}}]);