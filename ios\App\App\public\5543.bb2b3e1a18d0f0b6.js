"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5543],{65543:($,I,s)=>{s.r(I),s.d(I,{HistoryCommercialPageModule:()=>v});var g=s(56610),y=s(37222),m=s(77897),h=s(77575),p=s(73308),R=s(88233),b=s(79898),t=s(2978),S=s(81559),P=s(82571),A=s(39316),O=s(14599),u=s(74657);function M(i,f){1&i&&t.nrm(0,"ion-progress-bar",16)}function o(i,f){if(1&i){const r=t.RV6();t.j41(0,"ion-card",17),t.bIt("click",function(){const C=t.eBV(r).$implicit,D=t.XpG();return t.Njj(D.orderService.order=C)}),t.j41(1,"ion-card-content")(2,"div",18)(3,"ion-label"),t.EFF(4),t.nI1(5,"translate"),t.j41(6,"strong"),t.EFF(7),t.k0s()(),t.j41(8,"ion-label"),t.EFF(9),t.nI1(10,"translate"),t.j41(11,"strong"),t.EFF(12),t.nI1(13,"number"),t.k0s()(),t.j41(14,"ion-label"),t.EFF(15,"Date: "),t.j41(16,"strong"),t.EFF(17),t.nI1(18,"date"),t.k0s()()(),t.j41(19,"div",19),t.nrm(20,"ion-icon",20),t.k0s()()()}if(2&i){const r=f.$implicit;t.Mz_("routerLink","/order/detail/",null==r?null:r._id,""),t.R7$(4),t.SpI("",t.bMT(5,6,"history-page.reference"),": "),t.R7$(3),t.JRh((null==r?null:r.customerReference)||(null==r?null:r.appReference)),t.R7$(2),t.SpI("",t.bMT(10,8,"history-page.amount"),": "),t.R7$(3),t.SpI("",t.bMT(13,10,null==r||null==r.cart||null==r.cart.amount?null:r.cart.amount.TTC)," FCFA"),t.R7$(5),t.JRh(t.i5U(18,12,null==r?null:r.created_at,"dd/MM/YYYY \xe0 HH:mm"))}}function n(i,f){1&i&&(t.j41(0,"ion-cart")(1,"ion-thumbnail",21),t.nrm(2,"ion-skeleton-text",22),t.k0s()()),2&i&&(t.R7$(2),t.Y8G("animated",!0))}function e(i,f){1&i&&(t.j41(0,"div",23),t.nrm(1,"ion-img",24),t.j41(2,"ion-label"),t.EFF(3),t.nI1(4,"translate"),t.k0s()()),2&i&&(t.R7$(3),t.SpI(" ",t.bMT(4,1,"history-page.empty-order")," "))}const a=function(i){return{active:i}},_=[{path:"",component:(()=>{class i{constructor(r,l,d,C,D,k){this.location=r,this.orderService=l,this.modalCtrl=d,this.commonService=C,this.productService=D,this.storageService=k,this.isLoading=!1,this.tabOption=R.Re.PAID,this.orderStatus=R.Re,this.orders=[],this.skeletons=[1,2,3,4,5,6],this.offset=0,this.limit=20}doRefresh(r){var l=this;return(0,p.A)(function*(){l.filterData=null,l.orders=[],yield l.getOrderByUser(),r.target.complete()})()}ionViewWillEnter(){var r=this;return(0,p.A)(function*(){r.isLoading=!0,r.user=r.storageService.getUserConnected(),r.orders=[],yield r.getOrderByUser()})()}ionViewDidEnter(){this.isLoading=!1}getFlowOrder(r){var l=this;return(0,p.A)(function*(){l.offset=l.offset+l.limit+1,yield l.getOrderByUser(),r.target.complete()})()}getOrderByUser(){var r=this;return(0,p.A)(function*(){r.skeletons=[1,2,3,4,5,6],r.isLoading=!0;const l={status:r.tabOption,limit:r.limit,offset:r.offset,...r.filterData,commercialId:r.user?._id},d=(yield r.orderService.getAllOrder(l)).data;r.orders=r.orders.concat(d),r.isLoading=!1,r.skeletons=[]})()}showFilter(){var r=this;return(0,p.A)(function*(){const l=yield r.modalCtrl.create({component:b.W,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios",componentProps:{filterData:r.filterData}});l.present(),r.filterData=(yield l.onWillDismiss()).data,r.filterData&&(r.orders=[],yield r.getOrderByUser())})()}showModalRemovalDetail(r){this.orders=r,this.orderService.modalDetailRemoval=!0}back(){this.location.back()}static{this.\u0275fac=function(l){return new(l||i)(t.rXU(g.aZ),t.rXU(S.Q),t.rXU(m.W3),t.rXU(P.h),t.rXU(A.b),t.rXU(O.n))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-history-commercial"]],decls:29,vars:26,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],["slot","end","src","/assets/icons/funnel-outline.svg",3,"click"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["id","container"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click"],[1,"order-list"],["class","order",3,"routerLink","click",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[3,"ionInfinite"],["class","empty-list",4,"ngIf"],["type","indeterminate"],[1,"order",3,"routerLink","click"],[1,"detail"],[1,"icon"],["src","/assets/icons/arrow-forward-green.svg"],[1,"skeleton"],[3,"animated"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(l,d){1&l&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),t.bIt("click",function(){return d.back()}),t.k0s(),t.j41(3,"ion-title",2),t.EFF(4),t.nI1(5,"translate"),t.k0s(),t.j41(6,"ion-img",3),t.bIt("click",function(){return d.showFilter()}),t.k0s()()(),t.j41(7,"ion-content",4),t.DNE(8,M,1,0,"ion-progress-bar",5),t.j41(9,"div",6)(10,"ion-refresher",7),t.bIt("ionRefresh",function(D){return d.doRefresh(D)}),t.nrm(11,"ion-refresher-content",8),t.nI1(12,"translate"),t.nI1(13,"translate"),t.k0s(),t.j41(14,"ion-tab-bar",9)(15,"ion-tab-button",10),t.bIt("click",function(){return d.tabOption=d.orderStatus.CREDIT_IN_AWAIT_VALIDATION,d.orders=[],d.getOrderByUser()}),t.j41(16,"ion-title"),t.EFF(17),t.nI1(18,"translate"),t.k0s()(),t.j41(19,"ion-tab-button",10),t.bIt("click",function(){return d.tabOption=d.orderStatus.PAID,d.orders=[],d.getOrderByUser()}),t.j41(20,"ion-title"),t.EFF(21),t.nI1(22,"translate"),t.k0s()()(),t.j41(23,"div",11),t.DNE(24,o,21,15,"ion-card",12),t.DNE(25,n,3,1,"ion-cart",13),t.j41(26,"ion-infinite-scroll",14),t.bIt("ionInfinite",function(D){return d.getFlowOrder(D)}),t.nrm(27,"ion-infinite-scroll-content"),t.k0s()(),t.DNE(28,e,5,3,"div",15),t.k0s()()),2&l&&(t.R7$(4),t.JRh(t.bMT(5,12,"history-page.title")),t.R7$(3),t.Y8G("fullscreen",!0),t.R7$(1),t.Y8G("ngIf",d.isLoading),t.R7$(3),t.FS9("pullingText",t.bMT(12,14,"refresher.pull")),t.Mz_("refreshingText","",t.bMT(13,16,"refresher.refreshing"),"..."),t.R7$(4),t.Y8G("ngClass",t.eq3(22,a,d.tabOption===d.orderStatus.CREDIT_IN_AWAIT_VALIDATION)),t.R7$(2),t.JRh(t.bMT(18,18,"reseller-new-page.history-page.tabs.Waiting")),t.R7$(2),t.Y8G("ngClass",t.eq3(24,a,d.tabOption===d.orderStatus.PAID)),t.R7$(2),t.JRh(t.bMT(22,20,"reseller-new-page.history-page.tabs.prevalidate")),t.R7$(3),t.Y8G("ngForOf",d.orders),t.R7$(1),t.Y8G("ngForOf",d.skeletons),t.R7$(3),t.Y8G("ngIf",(null==d.orders?null:d.orders.length)<=0&&!d.isLoading))},dependencies:[g.YU,g.Sq,g.bT,m.b_,m.I9,m.W9,m.eU,m.iq,m.KW,m.Ax,m.Hp,m.he,m.FH,m.To,m.Ki,m.ds,m.Jq,m.qW,m.Zx,m.BC,m.ai,m.N7,h.Wk,g.QX,g.vh,u.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;color:#0b305c;font-family:Mont Regular}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding-top:0}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]{margin:auto;min-height:35px;gap:1em;margin-bottom:calc(50 * var(--res));border:none}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-tab-button[_ngcontent-%COMP%]{min-width:-moz-fit-content;min-width:fit-content}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:calc(41 * var(--res));color:#6d839d;font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#6d839d;border-bottom:3px solid #419CFB}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .tab-container[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#0b305c}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;line-height:initial;margin-top:calc(15.7 * var(--res));font-family:Mont Light;color:#000;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .order-list[_ngcontent-%COMP%]   .order[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:2rem;height:2rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:20vh;display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-infinite-scroll-content[_ngcontent-%COMP%]{min-height:15px}"]})}}return i})()},{path:"order-removals-details",loadChildren:()=>Promise.all([s.e(148),s.e(1095)]).then(s.bind(s,80148)).then(i=>i.OrderRemovalsDetailsPageModule)}];let E=(()=>{class i{static{this.\u0275fac=function(l){return new(l||i)}}static{this.\u0275mod=t.$C({type:i})}static{this.\u0275inj=t.G2t({imports:[h.iI.forChild(_),h.iI]})}}return i})();var T=s(93887);let v=(()=>{class i{static{this.\u0275fac=function(l){return new(l||i)}}static{this.\u0275mod=t.$C({type:i})}static{this.\u0275inj=t.G2t({imports:[g.MD,y.YN,m.bv,T.G,u.h,y.X1,E]})}}return i})()},81559:($,I,s)=>{s.d(I,{Q:()=>O});var g=s(73308),y=s(35025),m=s.n(y),h=s(94934),p=s(56610),R=s(45312),b=s(26409),t=s(2978),S=s(82571),P=s(33607),A=s(14599);let O=(()=>{class u{constructor(o,n,e,a){this.http=o,this.commonSrv=n,this.baseUrlService=e,this.storageSrv=a,this.url=this.baseUrlService.getOrigin()+R.c.basePath}create(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.post(`${n.url}orders`,o))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}createOrderByCommercialForClient(o,n){var e=this;return(0,g.A)(function*(){try{return yield(0,h.s)(e.http.post(`${e.url}orders/${n}`,o))}catch(a){const _={message:e.commonSrv.getError("",a).message,color:"danger"};return yield e.commonSrv.showToast(_),a}})()}getAllOrder(o){var n=this;return(0,g.A)(function*(){try{let e=new b.Nl;const{num:a,commercialId:c,status:_,offset:E,limit:T,startDate:v,endDate:i,customerReference:f,selectedCompanyId:r}=o;return v&&i&&(e=e.append("startDate",new p.vh("fr").transform(v,"YYYY-MM-dd"))),i&&v&&(e=e.append("endDate",new p.vh("fr").transform(i,"YYYY-MM-dd"))),f&&(e=e.append("appReference",f)),r&&(e=e.append("selectedCompanyId",r)),c&&(e=e.append("commercial",c)),void 0!==E&&(e=e.append("offset",E)),T&&(e=e.append("limit",T)),_&&(e=e.append("status",_)),a&&(e=e.append("appReference",a)),yield(0,h.s)(n.http.get(`${n.url}orders/history`,{params:e}))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}getOrders(o){var n=this;return(0,g.A)(function*(){try{let e=new b.Nl;const{status:a,appReference:c,offset:_,limit:E,userCategory:T,paymentMode:v,validation:i,customer:f,product:r,date:l,enable:d=!0}=o;return _&&(e=e.append("offset",_)),E&&(e=e.append("limit",E)),a&&(e=e.append("status",a)),c&&(e=e.append("appReference",`${c}`)),v&&(e=e.append("payment.mode.id",v)),T&&(e=e.append("user.category",T)),f&&(e=e.append("user.email",f)),r&&(e=e.append("cart.items.product.label",r)),i&&(e=e.append("validation",i)),l.start&&l.end&&(e=e.append("startDate",m()(l.start).format("YYYY-MM-DD")),e=e.append("endDate",m()(l.end).format("YYYY-MM-DD"))),e=e.append("enable",d),yield(0,h.s)(n.http.get(`${n.url}orders`,{params:e}))}catch(e){return e}})()}updateOrders(o,n){var e=this;return(0,g.A)(function*(){try{return yield(0,h.s)(e.http.patch(`${e.url}orders/${o}`,n))}catch(a){const _={message:e.commonSrv.getError("",a).message,color:"danger"};return yield e.commonSrv.showToast(_),a}})()}RhValidatedOrder(o,n){var e=this;return(0,g.A)(function*(){try{return yield(0,h.s)(e.http.patch(`${e.url}orders/${o._id}/validate`,n))}catch(a){const _={message:e.commonSrv.getError("",a).message,color:"danger"};return yield e.commonSrv.showToast(_),a}})()}RhRejectOrder(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.patch(`${n.url}orders/${o._id}/reject`,{}))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}sendOtp(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.post(`${n.url}callback/afriland`,o))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}sendWallet(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.post(`${n.url}orders/verify-Wallet-Nber`,o))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}ubaPayment(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.post(`${n.url}orders/m2u-paymentRequest`,o))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}find(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.get(n.url+"orders/"+o))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}getCardToken(){var o=this;return(0,g.A)(function*(){try{return yield(0,h.s)(o.http.post(`${o.url}orders/order-generate-visa-key`,{}))}catch(n){const a={message:o.commonSrv.getError("",n).message,color:"danger"};return yield o.commonSrv.showToast(a),n}})()}setupPayerAuthentication(o,n){var e=this;return(0,g.A)(function*(){try{return yield(0,h.s)(e.http.post(`${e.url}orders/order-setup-payer-auth`,{transientTokenJwt:o,order:n}))}catch(a){const _={message:e.commonSrv.getError("",a).message,color:"danger"};return yield e.commonSrv.showToast(_),a}})()}authorizationWithPAEnroll(o,n){var e=this;return(0,g.A)(function*(){try{return yield(0,h.s)(e.http.post(`${e.url}orders/order-authorization-pay-enroll`,{order:o,options:n}))}catch(a){const _={message:e.commonSrv.getError("",a).message,color:"danger"};return yield e.commonSrv.showToast(_),a}})()}checkIfOrderExist(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.get(`${n.url}orders/${o}/exist`))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}generatePurchaseOrder(o){var n=this;return(0,g.A)(function*(){try{return yield(0,h.s)(n.http.get(`${n.url}orders/${o}/generate-purchase`))}catch(e){const c={message:n.commonSrv.getError("",e).message,color:"danger"};return yield n.commonSrv.showToast(c),e}})()}cancellationOrder(o,n){var e=this;return(0,g.A)(function*(){try{return yield(0,h.s)(e.http.patch(`${e.url}orders/${o}/cancellation-order`,n))}catch(a){const _={message:e.commonSrv.getError("",a).message,color:"danger"};return yield e.commonSrv.showToast(_),a}})()}updateCarrier(o,n){var e=this;return(0,g.A)(function*(){try{return yield(0,h.s)(e.http.patch(`${e.url}orders/${o}/add-carrier`,{carrier:n}))}catch(a){const _={message:e.commonSrv.getError("",a).message,color:"danger"};return yield e.commonSrv.showToast(_),a}})()}static{this.\u0275fac=function(n){return new(n||u)(t.KVO(b.Qq),t.KVO(S.h),t.KVO(P.K),t.KVO(A.n))}}static{this.\u0275prov=t.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})}}return u})()},39316:($,I,s)=>{s.d(I,{b:()=>S});var g=s(73308),y=s(26409),m=s(94934),h=s(45312),p=s(2978),R=s(82571),b=s(33607),t=s(77897);let S=(()=>{class P{constructor(O,u,M,o){this.http=O,this.commonSrv=u,this.baseUrlService=M,this.toastController=o,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+h.c.basePath+"products"}getProducts(O){var u=this;return(0,g.A)(function*(){try{let M=new y.Nl;return O?.limit&&(M=M.append("limit",O?.limit)),yield(0,m.s)(u.http.get(u.url,{params:M}))}catch(M){const n={message:u.commonSrv.getError("",M).message,color:"danger"};return yield u.commonSrv.showToast(n),M}})()}getProduct(O){var u=this;return(0,g.A)(function*(){try{return yield(0,m.s)(u.http.get(`${u.url}/${O}`))}catch(M){const n={message:u.commonSrv.getError("",M).message,color:"danger"};return yield u.commonSrv.showToast(n),M}})()}static{this.\u0275fac=function(u){return new(u||P)(p.KVO(y.Qq),p.KVO(R.h),p.KVO(b.K),p.KVO(t.K_))}}static{this.\u0275prov=p.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})}}return P})()}}]);