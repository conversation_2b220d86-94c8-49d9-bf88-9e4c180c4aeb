"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[225],{30225:(u,c,e)=>{e.r(c),e.d(c,{QrCodePageModule:()=>M});var g=e(56610),l=e(37222),o=e(77897),r=e(77575),n=e(2978);function d(t,O){1&t&&(n.j41(0,"div",20)(1,"div",21),n.nrm(2,"ion-spinner",22),n.j41(3,"p"),n.EFF(4,"Mise \xe0 jour de votre solde..."),n.k0s()()())}function p(t,O){1&t&&(n.j41(0,"ion-footer",1)(1,"ion-toolbar",23)(2,"ion-button",24),n.EFF(3," Retour \xe0 l'accueil "),n.k0s()()())}const C=[{path:"",component:(()=>{class t{constructor(){this.isLoading=!0,this.qrPoints=250,this.totalPoints=1500}ngOnInit(){setTimeout(()=>{this.isLoading=!1},2e3)}static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275cmp=n.VBU({type:t,selectors:[["app-qr-code"]],decls:29,vars:4,consts:[[1,"points-container"],[1,"ion-no-border"],["slot","start"],["defaultHref","/","text",""],[1,"main-content"],[1,"points-card"],[1,"award-icon"],["name","trophy"],[1,"points-display"],[1,"points-label"],[1,"points-value"],[1,"number"],["name","star",1,"star-icon"],[1,"points-subtitle"],[1,"divider"],[1,"total-points"],[1,"total-label"],[1,"total-value"],["class","loading-overlay",4,"ngIf"],["class","ion-no-border",4,"ngIf"],[1,"loading-overlay"],[1,"loader-content"],["name","circular"],["routerLink","/navigation/home-alt"],["expand","block",1,"return-button"]],template:function(i,a){1&i&&(n.j41(0,"ion-content")(1,"div",0)(2,"ion-header",1)(3,"ion-toolbar")(4,"ion-buttons",2),n.nrm(5,"ion-back-button",3),n.k0s(),n.j41(6,"ion-title"),n.EFF(7,"Points Gagn\xe9s"),n.k0s()()(),n.j41(8,"div",4)(9,"div",5)(10,"div",6),n.nrm(11,"ion-icon",7),n.k0s(),n.j41(12,"div",8)(13,"span",9),n.EFF(14,"Vous avez gagn\xe9"),n.k0s(),n.j41(15,"div",10)(16,"span",11),n.EFF(17),n.k0s(),n.nrm(18,"ion-icon",12),n.k0s(),n.j41(19,"span",13),n.EFF(20,"points"),n.k0s()(),n.nrm(21,"div",14),n.j41(22,"div",15)(23,"span",16),n.EFF(24,"Total points"),n.k0s(),n.j41(25,"span",17),n.EFF(26),n.k0s()()()(),n.DNE(27,d,5,0,"div",18),n.DNE(28,p,4,0,"ion-footer",19),n.k0s()()),2&i&&(n.R7$(17),n.JRh(a.qrPoints),n.R7$(9),n.JRh(a.totalPoints),n.R7$(1),n.Y8G("ngIf",a.isLoading),n.R7$(1),n.Y8G("ngIf",!a.isLoading))},dependencies:[g.bT,o.el,o.Jm,o.QW,o.W9,o.M0,o.eU,o.iq,o.w2,o.BC,o.ai,o.tY,o.N7,r.Wk],styles:["[_nghost-%COMP%]   .points-container[_ngcontent-%COMP%]{height:100%;background:linear-gradient(180deg,var(--ion-color-primary-tint) 0%,var(--ion-color-light) 100%)}[_nghost-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent}[_nghost-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600}[_nghost-%COMP%]   .main-content[_ngcontent-%COMP%]{padding:20px;display:flex;flex-direction:column;align-items:center}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]{background:white;border-radius:20px;padding:24px;width:90%;max-width:400px;box-shadow:0 4px 12px #0000001a}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .award-icon[_ngcontent-%COMP%]{text-align:center;margin-bottom:20px}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .award-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:48px;color:var(--ion-color-primary)}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-display[_ngcontent-%COMP%]{text-align:center;margin-bottom:24px}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-display[_ngcontent-%COMP%]   .points-label[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:1.1rem;display:block;margin-bottom:8px}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-display[_ngcontent-%COMP%]   .points-value[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-display[_ngcontent-%COMP%]   .points-value[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]{font-size:3.5rem;font-weight:700;color:var(--ion-color-primary)}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-display[_ngcontent-%COMP%]   .points-value[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%]{font-size:24px;color:gold}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .points-display[_ngcontent-%COMP%]   .points-subtitle[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:1rem}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;background:var(--ion-color-light-shade);margin:16px 0}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .total-points[_ngcontent-%COMP%]{background:var(--ion-color-light);border-radius:12px;padding:16px;text-align:center}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .total-points[_ngcontent-%COMP%]   .total-label[_ngcontent-%COMP%]{display:block;color:var(--ion-color-medium);margin-bottom:8px}[_nghost-%COMP%]   .points-card[_ngcontent-%COMP%]   .total-points[_ngcontent-%COMP%]   .total-value[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:600;color:var(--ion-color-primary)}[_nghost-%COMP%]   .loading-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(0,0,0,.5);display:flex;align-items:center;justify-content:center;z-index:1000}[_nghost-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   .loader-content[_ngcontent-%COMP%]{background:white;padding:24px;border-radius:16px;text-align:center}[_nghost-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   .loader-content[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{width:48px;height:48px;margin-bottom:16px}[_nghost-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   .loader-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);margin:0}[_nghost-%COMP%]   ion-footer[_ngcontent-%COMP%]{padding:16px;background:transparent}[_nghost-%COMP%]   ion-footer[_ngcontent-%COMP%]   .return-button[_ngcontent-%COMP%]{margin:0;--border-radius: 12px;height:48px}"]})}}return t})()}];let P=(()=>{class t{static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[r.iI.forChild(C),r.iI]})}}return t})(),M=(()=>{class t{static{this.\u0275fac=function(i){return new(i||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[g.MD,l.YN,o.bv,P]})}}return t})()}}]);