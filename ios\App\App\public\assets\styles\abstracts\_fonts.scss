@font-face {
  font-family: 'Mont Regular';
  font-style: normal;
  font-weight: 400;
  src: url('../../fonts/mont/Mont-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Mont Regular Italic';
  src: url('../../fonts/mont/Mont-RegularItalic.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Mont Thin Italic';
  src: url('../../fonts/mont/Mont-ThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: 'Mont Thin';
  src: url('../../fonts/mont/Mont-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Mont Light Italic';
  src: url('../../fonts/mont/Mont-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: 'Mont Light';
  src: url('../../fonts/mont/Mont-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Mont SemiBold';
  src: url('../../fonts/mont/Mont-SemiBold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'Mont SemiBold Italic';
  font-weight: 700;
  font-style: normal;
  src: url('../../fonts/mont/Mont-SemiBoldItalic.ttf') format('truetype');
}

@font-face {
  font-family: 'Mont Bold Italic';
  font-weight: 700;
  font-style: normal;
  src: url('../../fonts/mont/Mont-BoldItalic.ttf') format('truetype');
}
@font-face {
  font-family: 'Mont Bold';
  font-weight: 700;
  font-style: normal;
  src: url('../../fonts/mont/Mont-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'Mont Black';
  font-weight: 900;
  font-style: normal;
  src: url('../../fonts/mont/Mont-Black.ttf') format('truetype');
}
@font-face {
  font-family: 'Mont Black Italic';
  font-weight: 900;
  font-style: normal;
  src: url('../../fonts/mont/Mont-BlackItalic.ttf') format('truetype');
}

@font-face {
  font-family: 'Mont Extra Light Italic';
  font-weight: 900;
  font-style: normal;
  src: url('../../fonts/mont/Mont-ExtraLightItalic.ttf') format('truetype');
}

@font-face {
  font-family: 'Mont Extra Light';
  font-weight: 900;
  font-style: normal;
  src: url('../../fonts/mont/Mont-ExtraLight.ttf') format('truetype');
}

@font-face {
  font-family: 'Mont Heavy';
  font-weight: 900;
  font-style: normal;
  src: url('../../fonts/mont/Mont-Heavy.ttf') format('truetype');
}

@font-face {
  font-family: 'Mont Heavy Italic';
  font-weight: 900;
  font-style: normal;
  src: url('../../fonts/mont/Mont-HeavyItalic.ttf') format('truetype');
}

$fonts: (
  mont: (
    thin: 'Mont Thin',
    light: 'Mont Light',
    regular: 'Mont Regular',
    semibold: 'Mont SemiBold',
    bold: 'Mont Bold',
    black: 'Mont Black',
  )
)
