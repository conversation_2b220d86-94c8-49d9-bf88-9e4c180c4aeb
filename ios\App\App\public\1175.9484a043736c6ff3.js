"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1175],{61175:(_,m,a)=>{a.r(m),a.d(m,{ValidateClientOrderViaQrcodePageModule:()=>T});var l=a(56610),d=a(37222),i=a(77897),f=a(77575),c=a(73308),n=a(2978),C=a(74657),h=a(12330),O=a(23985),P=a(82571),M=a(94440);const b=["popover"];function v(o,u){1&o&&n.nrm(0,"ion-progress-bar",15)}function x(o,u){if(1&o){const t=n.RV6();n.j41(0,"div",16)(1,"ion-searchbar",17),n.bIt("ngModelChange",function(r){n.eBV(t);const s=n.XpG();return n.Njj(s.searchQuery=r)})("ngModelChange",function(){n.eBV(t);const r=n.XpG();return n.Njj(r.filterUsers())}),n.k0s()()}if(2&o){const t=n.XpG();n.R7$(1),n.Y8G("ngModel",t.searchQuery)}}function y(o,u){1&o&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",18),n.nrm(2,"ion-skeleton-text",19),n.k0s()()),2&o&&(n.R7$(2),n.Y8G("animated",!0))}const k=function(o){return["/order/product-scan/user-scan",o]},w=function(o){return{"empty-state":o}};function V(o,u){if(1&o&&(n.j41(0,"div",22)(1,"ion-label")(2,"h2"),n.EFF(3),n.nI1(4,"truncateString"),n.k0s(),n.j41(5,"span"),n.EFF(6),n.nI1(7,"translate"),n.k0s(),n.j41(8,"span"),n.EFF(9),n.nI1(10,"translate"),n.k0s()(),n.j41(11,"span",23),n.EFF(12),n.k0s()()),2&o){const t=u.$implicit,e=n.XpG(2);n.Y8G("routerLink",n.eq3(16,k,null==t?null:t._id))("ngClass",n.eq3(18,w,0===e.filteredUsers.length)),n.R7$(3),n.JRh(n.i5U(4,9,null==t?null:t.firstName,11)),n.R7$(3),n.Lme("",n.bMT(7,12,"indirect-user.phone")," : ",null==t?null:t.tel,""),n.R7$(3),n.E5c("",n.bMT(10,14,"indirect-user.address")," : ",null==t||null==t.address?null:t.address.region," ",null==t||null==t.address?null:t.address.city,""),n.R7$(3),n.JRh(null==t?null:t.firstName)}}function F(o,u){if(1&o&&(n.j41(0,"div",20),n.DNE(1,V,13,20,"div",21),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("ngForOf",t.filteredUsers)}}function U(o,u){1&o&&(n.j41(0,"div",24),n.nrm(1,"ion-img",25),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&o&&(n.R7$(3),n.JRh(n.bMT(4,1,"indirect-clients.empty")))}const I=[{path:"",component:(()=>{class o{constructor(t,e){this.userSrv=t,this.commonSrv=e,this.location=(0,n.WQX)(l.aZ),this.translateService=(0,n.WQX)(C.c$),this.modalCtrl=(0,n.WQX)(i.W3),this.router=(0,n.WQX)(f.Ix),this.isOpen=!1,this.users=[],this.skeletons=[1,2,3,4,5,6],this.filterData={category:0,limit:100,offset:0},this.filteredUsers=[],this.searchQuery="",this.isSearchVisible=!1,this.isMenuOpen={}}toggleSearch(){this.isSearchVisible=!this.isSearchVisible}ngOnInit(){var t=this;return(0,c.A)(function*(){t.isLoading=!0,yield t.getUsers(),t.router.events.subscribe(e=>{e instanceof f.wF&&"/navigation/indirect-user"===e.url&&t.getUsers()})})()}reset(){this.filterData.commercialRegion=null,this.ngOnInit()}presentPopover(t,e){this.popover?(this.popover.event=t,this.isOpen=!0,this.currentUser=e):console.error("Popover is not defined")}editProspect(){var t=this;return(0,c.A)(function*(){t.popover&&(yield t.popover.dismiss(),t.isOpen=!1),t.userSrv.currentUserParticular=t.currentUser,t.router.navigate(["/navigation/manage-user/create-indirect-user"])})()}onAddUser(){var t=this;return(0,c.A)(function*(){t.userSrv.currentUserParticular=null,t.router.navigate(["/navigation/manage-user/create-indirect-user"])})()}getUserCommercial(){var t=this;return(0,c.A)(function*(){t.filterData.commercialRegion=t.commonSrv.user?.address.commercialRegion,t.ngOnInit()})()}localisation(){var t=this;return(0,c.A)(function*(){t.popover&&(yield t.popover.dismiss(),t.isOpen=!1),t.userSrv.currentUserParticular=t.currentUser,t.router.navigate(["/navigation/indirect-user/location-view"])})()}localisations(){var t=this;return(0,c.A)(function*(){t.isLoading=!0;const e=t.commonSrv.user?.address.commercialRegion,r={...t.filterData,commercialRegion:e};try{const j=((yield t.userSrv.getUsers(r)).data||[]).filter(p=>p.localisation&&p?.localisation?.latitude&&p?.localisation?.longitude).map(p=>({latitude:p?.localisation?.latitude,longitude:p?.localisation?.longitude}));t.userSrv.currentUserLocalisation=j,t.router.navigate(["/navigation/indirect-user/location-view"])}catch(s){throw yield t.commonSrv.showToast({color:"danger",message:"Erreur lors de la r\xe9cup\xe9ration des utilisateurs"+s?.error?.message}),s}finally{t.isLoading=!1}})()}preventAlert(){var t=this;return(0,c.A)(function*(){t.isOpen=!1})()}back(){this.location.back()}showFilter(){var t=this;return(0,c.A)(function*(){const e=yield t.modalCtrl.create({component:h.f,initialBreakpoint:.7,cssClass:"modal",breakpoints:[0,.5,.7,1],mode:"ios",componentProps:{filterData:t.filterData,filteredUsersNames:t.filteredUsersNames}});e.present(),t.filterData=(yield e.onWillDismiss()).data,t.filterData&&(t.users=[],t.filterData.offset=0,yield t.getUsers())})()}getUsers(){var t=this;return(0,c.A)(function*(){try{t.skeletons=[1,2,3,4,5,6,7,8];const e=t.commonSrv.user?._id,r={...t.filterData,associatedCommercialId:e},s=yield t.userSrv.getUsers(r);t.users=s.data||[],t.filteredUsers=[...t.users],t.isLoading=!1,t.skeletons=[]}catch(e){console.error("Error fetching users:",e),t.users=[],t.filteredUsers=[],t.isLoading=!1,t.skeletons=[]}})()}filterUsers(){if(!this.searchQuery)return void(this.filteredUsers=[...this.users]);const t=this.searchQuery.toLowerCase().split("");this.filteredUsers=this.users.filter(e=>{const r=e.firstName.toLowerCase(),s=String(e.tel);return t.every(g=>r.includes(g)||s.includes(g))})}doRefresh(t){var e=this;return(0,c.A)(function*(){e.filterData={category:0,limit:50,offset:0},e.isLoading=!0,yield e.getUsers(),t.target.complete()})()}getFlowUsers(t){var e=this;return(0,c.A)(function*(){yield e.getUsers(),t.target.complete()})()}static{this.\u0275fac=function(e){return new(e||o)(n.rXU(O.D),n.rXU(P.h))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-validate-client-order-via-qrcode"]],viewQuery:function(e,r){if(1&e&&n.GBs(b,5),2&e){let s;n.mGM(s=n.lsd())&&(r.popover=s.first)}},decls:23,vars:15,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"click"],[1,"buttons"],[1,"transparent",3,"click"],["name","search"],["name","funnel-outline"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],["class","search",4,"ngIf"],[4,"ngFor","ngForOf"],["class","container",4,"ngIf"],["class","empty-list",4,"ngIf"],[3,"ionInfinite"],["type","indeterminate"],[1,"search"],[3,"ngModel","ngModelChange"],[1,"skeleton"],[3,"animated"],[1,"container"],["class","item",3,"routerLink","ngClass",4,"ngFor","ngForOf"],[1,"item",3,"routerLink","ngClass"],[1,"function"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(e,r){1&e&&(n.j41(0,"ion-header")(1,"div",0)(2,"ion-img",1),n.bIt("click",function(){return r.back()}),n.k0s(),n.j41(3,"ion-title"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",2)(7,"ion-button",3),n.bIt("click",function(){return r.toggleSearch()}),n.nrm(8,"ion-icon",4),n.k0s(),n.j41(9,"ion-button",3),n.bIt("click",function(){return r.showFilter()}),n.nrm(10,"ion-icon",5),n.k0s()()(),n.j41(11,"ion-content",6),n.DNE(12,v,1,0,"ion-progress-bar",7),n.j41(13,"ion-refresher",8),n.bIt("ionRefresh",function(g){return r.doRefresh(g)}),n.nrm(14,"ion-refresher-content",9),n.nI1(15,"translate"),n.nI1(16,"translate"),n.k0s(),n.DNE(17,x,2,1,"div",10),n.DNE(18,y,3,1,"ion-cart",11),n.DNE(19,F,2,1,"div",12),n.DNE(20,U,5,3,"div",13),n.j41(21,"ion-infinite-scroll",14),n.bIt("ionInfinite",function(g){return r.getFlowUsers(g)}),n.nrm(22,"ion-infinite-scroll-content"),n.k0s()()),2&e&&(n.R7$(4),n.SpI(" ",n.bMT(5,9,"qr-orders.select-client")," "),n.R7$(7),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",r.isLoading),n.R7$(2),n.FS9("pullingText",n.bMT(15,11,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(16,13,"refresher.refreshing"),"..."),n.R7$(3),n.Y8G("ngIf",r.isSearchVisible),n.R7$(1),n.Y8G("ngForOf",r.skeletons),n.R7$(1),n.Y8G("ngIf",!r.isLoading),n.R7$(1),n.Y8G("ngIf",!r.isLoading&&0===r.filteredUsers.length))},dependencies:[l.YU,l.Sq,l.bT,d.BC,d.vS,i.Jm,i.W9,i.eU,i.iq,i.KW,i.Ax,i.Hp,i.he,i.FH,i.To,i.Ki,i.S1,i.ds,i.Zx,i.BC,i.Gw,i.N7,f.Wk,M.c,C.D9],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{display:flex;align-items:center;background:var(--clr-white)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;color:#0b305c}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:auto}.header[_ngcontent-%COMP%]{--background: #D5DFEB;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-label[_ngcontent-%COMP%]{display:grid!important}ion-label[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-weight:800}ion-label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.75rem;color:#8597ad;margin-top:8px}ion-content[_ngcontent-%COMP%]{--background: #D5DFEB}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:white;border-radius:5px;box-shadow:0 4px 4px #00000017;width:calc(100% - 48px);margin:13px auto;padding:10px 20px;display:flex;justify-content:space-between}ion-content[_ngcontent-%COMP%]   .function[_ngcontent-%COMP%]{display:inline-block;width:100px;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:700;color:#0b305c;background:rgba(65,156,251,.168627451);padding:5px 10px;border-radius:30px;margin:auto;font-size:14px;font-weight:500}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:10vh;display:flex;flex-direction:column;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]{margin-bottom:3.8rem;gap:.8rem;align-items:flex-start;background:#ffffff;width:8.6em;border-radius:5px;padding-left:10px;padding-bottom:10px;padding-top:10px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]{display:flex;gap:5px;align-items:center}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;color:#0b305c;text-align:center;font-size:14px;font-weight:800!important}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[_ngcontent-%COMP%]{position:relative;width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=primary][_ngcontent-%COMP%]{--background: #4caf50;--color: white}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=secondary][_ngcontent-%COMP%]{--background: #2196f3;--color: white}ion-content[_ngcontent-%COMP%]   ion-fab[_ngcontent-%COMP%]   ion-fab-list[_ngcontent-%COMP%]   .fab-content[_ngcontent-%COMP%]   ion-fab-button[color=danger][_ngcontent-%COMP%]{--background: #f44336;--color: white}.buttons[_ngcontent-%COMP%]{display:flex;margin:6px 24px 6px 0;justify-content:space-between}.buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent !important;--box-shadow: none;color:#0b305c;border:none;--padding: auto}.buttons[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#419cfb}.dropdown-menu[_ngcontent-%COMP%]{position:absolute;background:#F1F2F4;--text-align: left;right:-6px;box-shadow:0 2px 10px #0003;z-index:10}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background: transparent;text-align:left}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{padding:auto;margin:auto}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border: 1px solid #D2DBE7;--background: #F1F2F4}.dropdown-menu[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover{background:#f0f0f0}.dropdown-menu[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{align-items:center;justify-content:flex-start;--background: #F1F2F4;--border: 1px solid #D2DBE7;text-align:left;--border-radius: none}.dropdown-menu[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}ion-list[_ngcontent-%COMP%]{display:grid;margin-right:24px}ion-list[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: #F1F2F4;--border: 1px solid #D2DBE7;text-align:left;--border-radius: none;width:100%}ion-list[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{text-align:left;padding:2px}.icon-container[_ngcontent-%COMP%]{position:relative;margin:auto 0}.custom-back-button[_ngcontent-%COMP%]::part(back-button-text){display:none}.success[_ngcontent-%COMP%]{color:green;text-transform:capitalize}.danger[_ngcontent-%COMP%]{color:red;text-transform:capitalize}.blue[_ngcontent-%COMP%]{color:#419cfb;text-transform:capitalize}.skeleton[_ngcontent-%COMP%]{height:3.5em;width:100%;margin-bottom:1rem;display:flex;align-items:center;justify-content:center}.skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:90%;height:100%}.custom-popover[_ngcontent-%COMP%]{--width: 150px}.small-icon[_ngcontent-%COMP%]{font-size:18px}.popover-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 8px}.popover[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.popover[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{margin-top:4px;font-size:14px;color:var(--ion-color-medium)}ion-searchbar[_ngcontent-%COMP%]{width:calc(100% - 48px);margin:auto}']})}}return o})()}];let R=(()=>{class o{static{this.\u0275fac=function(e){return new(e||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[f.iI.forChild(I),f.iI]})}}return o})();var D=a(24608),Q=a(93887);let T=(()=>{class o{static{this.\u0275fac=function(e){return new(e||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[l.MD,d.YN,i.bv,D.vj,Q.G,C.h,d.X1,R]})}}return o})()},94440:(_,m,a)=>{a.d(m,{c:()=>d});var l=a(2978);let d=(()=>{class i{transform(c,...n){return c?c.length>n[0]?`${c.substring(0,n[0]-3)}...`:c:""}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275pipe=l.EJ8({name:"truncateString",type:i,pure:!0})}}return i})()}}]);