"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5882],{25882:(R,C,c)=>{c.r(C),c.d(C,{AccountManagementPageModule:()=>v});var _=c(56610),t=c(37222),i=c(77897),M=c(77575),p=c(73308),n=c(2978),b=c(82571),f=c(74657),x=c(91285),E=c(14599),k=c(43556),P=c(94440);function u(l,y){1&l&&(n.j41(0,"div",13),n.nrm(1,"ion-spinner",14),n.k0s())}function d(l,y){if(1&l){const s=n.RV6();n.j41(0,"ion-item",16),n.bIt("click",function(){n.eBV(s);const g=n.XpG().$implicit,h=n.XpG();return n.Njj(h.switchProfile(g))}),n.j41(1,"ion-avatar",5),n.nrm(2,"img",6),n.k0s(),n.j41(3,"ion-label")(4,"h2"),n.EFF(5),n.nI1(6,"truncateString"),n.k0s(),n.j41(7,"h3"),n.EFF(8),n.nI1(9,"translate"),n.k0s()(),n.nrm(10,"ion-icon",17),n.k0s()}if(2&l){const s=n.XpG().$implicit,a=n.XpG();n.AVh("active-item",(null==s?null:s._id)===(null==a.currentProfile?null:a.currentProfile._id)),n.R7$(2),n.Y8G("src",(null==a.user?null:a.user.profilePicture)||"assets/icons/Profil2.png",n.B4B),n.R7$(3),n.JRh(n.i5U(6,5,null==s?null:s.name,20)),n.R7$(3),n.JRh(n.bMT(9,8,"account-management.manager"))}}function o(l,y){if(1&l&&(n.qex(0),n.DNE(1,d,11,10,"ion-item",15),n.bVm()),2&l){const s=y.$implicit,a=n.XpG();n.R7$(1),n.Y8G("ngIf",(null==s?null:s._id)!==(null==a.currentProfile?null:a.currentProfile._id))}}const r=[{path:"",component:(()=>{class l{constructor(s){this.router=s,this.associatedCompanies=[],this.isLoading=!1,this.location=(0,n.WQX)(_.aZ),this.modalCtrl=(0,n.WQX)(i.W3),this.translateService=(0,n.WQX)(f.c$),this.commonService=(0,n.WQX)(b.h),this.storageSrv=(0,n.WQX)(E.n),this.companiesService=(0,n.WQX)(k.B)}ngOnInit(){this.user=this.storageSrv.getUserConnected(),this.currentProfile=this.commonService.user?.company,this.associatedCompanies=this.commonService.user?.associatedCompanies||[]}handleSwitchProfile(s){var a=this;return(0,p.A)(function*(){if(s&&"object"==typeof s)try{a.isLoading=!0,a.currentProfile=s;const g=a.storageSrv.getUserConnected();g.company=s,a.commonService.user&&(a.commonService.user.company=s),a.storageSrv.store("USER_INFO",JSON.stringify(g)),yield a.modalCtrl.dismiss({confirm:!0}),yield a.commonService.showToast({message:"fr"===a.translateService.currentLang?"Compte chang\xe9 avec succ\xe8s":"Account switched successfully",color:"success"})}catch(g){yield a.commonService.showToast({message:"fr"===a.translateService.currentLang?"Une erreur est survenue lors du changement de compte":"An error occurred while switching accounts",color:"danger"}),console.error("Error switching profile:",g)}finally{a.isLoading=!1}else console.error("Invalid profile object")})()}switchProfile(s){var a=this;return(0,p.A)(function*(){const g=yield a.modalCtrl.create({component:x.y,cssClass:"modalClass",componentProps:{dataModal:{cancelButton:"fr"===a.translateService.currentLang?"Non":"No",confirmButton:"fr"===a.translateService.currentLang?"Oui":"Yes",text:"fr"===a.translateService.currentLang?"Vous \xeates sur le point de changer de compagnie. Voulez-vous continuer ?":"You are about to switch accounts. Do you want to continue?",handler:()=>a.handleSwitchProfile(s)}}});yield g.present();const{data:h}=yield g.onWillDismiss();h&&h.confirm&&(a.isLoading=!0,yield a.handleSwitchProfile(s))})()}back(){this.location.back()}redirectToAccount(){this.isLoading=!0,this.router.navigate(["/navigation/account"]).then(()=>{this.isLoading=!1},s=>{console.error("Navigation error:",s),this.isLoading=!1})}static{this.\u0275fac=function(a){return new(a||l)(n.rXU(M.Ix))}}static{this.\u0275cmp=n.VBU({type:l,selectors:[["app-account-management"]],decls:33,vars:22,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["class","ion-text-center ion-margin-top",4,"ngIf"],["lines","none",1,"profile-card","active-item",3,"click"],["slot","start"],["alt","profil image",1,"header-img",3,"src"],[1,"elt"],["name","checkmark-circle","color","success","slot","end"],[1,"other-accounts"],["color","medium",1,"section-title"],["lines","full"],[4,"ngFor","ngForOf"],[1,"ion-text-center","ion-margin-top"],["name","bubbles"],["class","profile-item custom-bottom-border",3,"active-item","click",4,"ngIf"],[1,"profile-item","custom-bottom-border",3,"click"],["name","sync","slot","end"]],template:function(a,g){1&a&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return g.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content"),n.DNE(7,u,2,0,"div",3),n.j41(8,"ion-item",4),n.bIt("click",function(){return g.redirectToAccount()}),n.j41(9,"ion-avatar",5),n.nrm(10,"img",6),n.k0s(),n.j41(11,"ion-label",7)(12,"h2"),n.EFF(13),n.nI1(14,"truncateString"),n.k0s(),n.j41(15,"h3"),n.EFF(16),n.nI1(17,"translate"),n.k0s(),n.j41(18,"p"),n.EFF(19),n.nI1(20,"translate"),n.k0s()(),n.nrm(21,"ion-icon",8),n.k0s(),n.j41(22,"div",9)(23,"ion-text",10)(24,"h2"),n.EFF(25),n.nI1(26,"translate"),n.k0s(),n.j41(27,"p"),n.EFF(28),n.nI1(29,"translate"),n.k0s()(),n.j41(30,"ion-card")(31,"ion-list",11),n.DNE(32,o,2,1,"ng-container",12),n.k0s()()()()),2&a&&(n.R7$(4),n.JRh(n.bMT(5,9,"account-management.title")),n.R7$(3),n.Y8G("ngIf",g.isLoading),n.R7$(3),n.Y8G("src",(null==g.user?null:g.user.profilePicture)||"assets/icons/Profil2.png",n.B4B),n.R7$(3),n.JRh(n.i5U(14,11,null==g.currentProfile?null:g.currentProfile.name,20)),n.R7$(3),n.JRh(n.bMT(17,14,"account-management.manager")),n.R7$(3),n.JRh(n.bMT(20,16,"account-management.status")),n.R7$(6),n.JRh(n.bMT(26,18,"account-management.other")),n.R7$(3),n.JRh(n.bMT(29,20,"account-management.text")),n.R7$(4),n.Y8G("ngForOf",g.associatedCompanies))},dependencies:[_.Sq,_.bT,i.mC,i.b_,i.W9,i.eU,i.iq,i.KW,i.uz,i.he,i.nf,i.w2,i.IO,i.BC,i.ai,f.D9,P.c],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background: #F1F2F4;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]{--background: #F1F2F4}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]{--background: rgba(65, 156, 251, .11);padding:1rem;border-radius:5rem}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   .active-item[_ngcontent-%COMP%]{border-radius:5rem}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border:.5px solid #546E8D;border-radius:50%}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{border-radius:50%}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{padding:1rem}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:600;font-size:18px;font-family:var(--mont-bold);color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:15px;color:#546e8d;text-transform:uppercase;font-family:var(--mont-regular)}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:13px;color:#546e8d}ion-content[_ngcontent-%COMP%]   .profile-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-left:auto;width:40px;height:40px}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]{padding:0 16px}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-family:var(--mont-bold);color:var(--clr-primary-900);font-size:20px;font-weight:400;margin-bottom:5px;margin-top:.5rem}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;font-family:var(--mont-regular);color:#546e8d;margin-bottom:.5rem}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{--background: var(--ion-color-light) !important}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]{background:transparent}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   .profile-item[_ngcontent-%COMP%]{--background: transparent;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   .custom-bottom-border[_ngcontent-%COMP%]{--border-width: 0 0 1.5px 0;--border-color: #E7EAEF;margin-top:1rem}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border:.5px solid #546E8D;border-radius:50%}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{padding:.8rem}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:600;font-size:16px;font-family:var(--mont-bold);color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:14px;color:#546e8d;text-transform:uppercase;font-family:var(--mont-regular)}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px;font-family:var(--mont-regular);color:#546e8d;margin:0}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#419cfb}ion-content[_ngcontent-%COMP%]   .other-accounts[_ngcontent-%COMP%]   .active-item[_ngcontent-%COMP%]{--background: var(--ion-color-light) !important}"]})}}return l})()}];let m=(()=>{class l{static{this.\u0275fac=function(a){return new(a||l)}}static{this.\u0275mod=n.$C({type:l})}static{this.\u0275inj=n.G2t({imports:[M.iI.forChild(r),M.iI]})}}return l})();var O=c(93887);let v=(()=>{class l{static{this.\u0275fac=function(a){return new(a||l)}}static{this.\u0275mod=n.$C({type:l})}static{this.\u0275inj=n.G2t({imports:[_.MD,t.YN,i.bv,m,f.h,O.G]})}}return l})()},43556:(R,C,c)=>{c.d(C,{B:()=>k});var _=c(73308),t=c(94934),i=c(45312),M=c(26409),n=(c(99987),c(2978)),b=c(33607),f=c(82571),x=c(14599),E=c(74657);let k=(()=>{class P{constructor(d,o,e,r,m){this.baseUrl=d,this.http=o,this.commonSrv=e,this.storageSrv=r,this.translateService=m,this.base_url=`${this.baseUrl.getOrigin()}${i.c.basePath}`,this.base_url+="companies"}create(d){var o=this;return(0,_.A)(function*(){try{return delete d._id,yield(0,t.s)(o.http.post(o.base_url,d))}catch(e){return o.commonSrv.getError("Echec de cr\xe9ation de la compagnie",e)}})()}getCompanies(d){var o=this;return(0,_.A)(function*(){try{let e=new M.Nl;const{category:r,city:m,limit:O,name:v,regionCom:l,solToId:y,tel:s,users:a,offset:g,enable:h=!0,projection:T,isLoyaltyProgDistributor:w}=d;return void 0!==r&&(e=e.append("category",r)),m&&(e=e.append("address.city",m)),v&&(e=e.append("name",v)),y&&(e=e.append("erpSoldToId",y)),s&&(e=e.append("tel",`${s}`)),T&&(e=e.append("projection",`${T}`)),a&&(e=e.append("users",`${a}`)),l&&(e=e.append("address.commercialRegion",l)),w&&(e=e.append("isLoyaltyProgDistributor",w)),void 0!==O&&(e=e.append("limit",O)),void 0!==g&&(e=e.append("offset",g)),e=e.set("enable",h),yield(0,t.s)(o.http.get(o.base_url,{params:e}))}catch(e){const m={message:o.commonSrv.getError("",e).message,color:"danger"};return yield o.commonSrv.showToast(m),e}})()}getParticularCompanies(d){var o=this;return(0,_.A)(function*(){let e=new M.Nl;const{limit:r,offset:m,enable:O=!0,commercialRegion:v}=d;return void 0!==r&&(e=e.append("limit",r)),void 0!==m&&(e=e.append("offset",m)),v&&(e=e.append("address.commercialRegion",v)),e=e.set("enable",O),yield(0,t.s)(o.http.get(o.base_url+"/particular-suppliers",{params:e}))})()}find(d){var o=this;return(0,_.A)(function*(){try{return yield(0,t.s)(o.http.get(o.base_url+"/"+d))}catch{return o.commonSrv.initCompany()}})()}getBalance(d){var o=this;return(0,_.A)(function*(){try{let e=new M.Nl;const{company:r}=o.storageSrv.getUserConnected();return e=e.set("_id",r?r?._id:d?.companyId),yield(0,t.s)(o.http.get(`${o.base_url}/balance`,{params:e}))}catch(e){return yield o.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),e}})()}getUsersCompany(d,o){var e=this;return(0,_.A)(function*(){try{let r=new M.Nl;const{email:m,enable:O=!0}=o;return m&&(r=r.append("email",m)),r=r.append("enable",O),yield(0,t.s)(e.http.get(`${e.base_url}/${d}/users`,{params:r}))}catch(r){return yield e.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),r}})()}static{this.\u0275fac=function(o){return new(o||P)(n.KVO(b.K),n.KVO(M.Qq),n.KVO(f.h),n.KVO(x.n),n.KVO(E.c$))}}static{this.\u0275prov=n.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})}}return P})()},91285:(R,C,c)=>{c.d(C,{y:()=>P});var _=c(99987),t=c(2978),i=c(77897),M=c(62049),p=c(82571),n=c(37222),b=c(56610);function f(u,d){if(1&u){const o=t.RV6();t.j41(0,"div",6)(1,"label",7),t.EFF(2),t.k0s(),t.j41(3,"ion-item",8)(4,"ion-textarea",9),t.bIt("ngModelChange",function(r){t.eBV(o);const m=t.XpG();return t.Njj(m.annulationMessage=r)}),t.k0s()()()}if(2&u){const o=t.XpG();t.R7$(2),t.JRh(null==o.dataModal?null:o.dataModal.message),t.R7$(2),t.Y8G("ngModel",o.annulationMessage)}}function x(u,d){if(1&u){const o=t.RV6();t.j41(0,"ion-button",10),t.bIt("click",function(){t.eBV(o);const r=t.XpG();return t.Njj(r.cancel())}),t.j41(1,"ion-label"),t.EFF(2),t.nI1(3,"titlecase"),t.k0s()()}if(2&u){const o=t.XpG();t.R7$(2),t.JRh(t.bMT(3,1,null==o.dataModal?null:o.dataModal.cancelButton))}}const E=function(u){return{"annulation-mode":u}},k=function(u){return{"single-button":u}};let P=(()=>{class u{constructor(o,e,r){this.modalCtrl=o,this.translateService=e,this.commonSrv=r,this.annulationMessage=""}ngOnInit(){}cancel(){return this.modalCtrl.dismiss(null,"annuler")}confirm(){let o=this.annulationMessage;this.dataModal.isAnnulation&&!this.annulationMessage.trim()&&(o=this.translateService.currentLang===_.T.French?"\xc0 la demande du client":"At the customers request"),this.dataModal.handler(o),this.modalCtrl.dismiss(this.dataModal.isAnnulation?{message:o}:null,"confirm")}static{this.\u0275fac=function(e){return new(e||u)(t.rXU(i.W3),t.rXU(M.E),t.rXU(p.h))}}static{this.\u0275cmp=t.VBU({type:u,selectors:[["app-base-modal"]],inputs:{dataModal:"dataModal"},decls:12,vars:12,consts:[["id","container",1,"scroller-container",3,"ngClass"],[1,"contain-text"],["class","message-container",4,"ngIf"],[1,"btn-validate",3,"ngClass"],["fill","solid","class","cancel",3,"click",4,"ngIf"],["fill","solid","color","primary",1,"yes",3,"click"],[1,"message-container"],[1,"message"],[1,"message-input"],["rows","4",1,"custom-textarea",3,"ngModel","ngModelChange"],["fill","solid",1,"cancel",3,"click"]],template:function(e,r){1&e&&(t.j41(0,"section",0)(1,"div",1)(2,"label"),t.EFF(3),t.nrm(4,"span"),t.k0s()(),t.DNE(5,f,5,2,"div",2),t.j41(6,"div",3),t.DNE(7,x,4,3,"ion-button",4),t.j41(8,"ion-button",5),t.bIt("click",function(){return r.confirm()}),t.j41(9,"ion-label"),t.EFF(10),t.nI1(11,"titlecase"),t.k0s()()()()),2&e&&(t.Y8G("ngClass",t.eq3(8,E,null==r.dataModal?null:r.dataModal.isAnnulation)),t.R7$(3),t.SpI(" ",null==r.dataModal?null:r.dataModal.text," "),t.R7$(2),t.Y8G("ngIf",null==r.dataModal?null:r.dataModal.isAnnulation),t.R7$(1),t.Y8G("ngClass",t.eq3(10,k,null==r.dataModal?null:r.dataModal.isAnnulation)),t.R7$(1),t.Y8G("ngIf",!(null!=r.dataModal&&r.dataModal.isAnnulation)),t.R7$(3),t.JRh(t.bMT(11,6,null==r.dataModal?null:r.dataModal.confirmButton)))},dependencies:[n.BC,n.vS,i.Jm,i.uz,i.he,i.nc,i.Gw,b.YU,b.bT,b.PV],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{height:100%;padding:var(--container-padding);background-color:#fff;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));display:flex;flex-direction:column}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]{text-align:center;font-family:var(--mont-regular);color:#143c5d;line-height:1;margin-bottom:var(--container-margin)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:"#0B305C";font-size:20px;text-align:center;display:block;margin-bottom:16px;font-family:var(--mont-bold)}#container[_ngcontent-%COMP%]   .contain-text[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#143c5d}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]{margin:14px 0}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-size:14px;font-weight:400;display:block;margin-bottom:8px;text-align:center;font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{--background: #f8f9fa;--border-radius: 8px;--padding-start: 12px;--padding-end: 12px;border:1px solid rgba(20,60,93,.5);border-radius:8px}#container[_ngcontent-%COMP%]   .message-container[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]   .custom-textarea[_ngcontent-%COMP%]{--padding-top: 8px;--padding-bottom: 8px;min-height:100px;font-size:14px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));display:flex;flex-direction:row;justify-content:center;margin-top:13px;gap:1em}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]{gap:0}#container[_ngcontent-%COMP%]   .btn-validate.single-button[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;max-width:none}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{--background: #fff;width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);color:#143c5d;font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:120px;border-radius:10px;border:1px solid #143c5d;font-family:var(--mont-semibold);font-size:10px}#container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[disabled][_ngcontent-%COMP%]{opacity:.5}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 16px}#container.annulation-mode[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   .yes[_ngcontent-%COMP%]{width:100%;font-size:16px}']})}}return u})()},94440:(R,C,c)=>{c.d(C,{c:()=>t});var _=c(2978);let t=(()=>{class i{transform(p,...n){return p?p.length>n[0]?`${p.substring(0,n[0]-3)}...`:p:""}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275pipe=_.EJ8({name:"truncateString",type:i,pure:!0})}}return i})()}}]);