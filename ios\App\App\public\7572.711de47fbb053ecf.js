"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7572],{17572:(I,O,o)=>{o.r(O),o.d(O,{ChoiceSuppliersPageModule:()=>K});var _=o(56610),d=o(37222),u=o(77897),P=o(77575),m=o(73308),h=o(58133),t=o(2978),S=o(14599),E=o(82571),v=o(43556),L=o(93387),g=o(95908),a=o(39316),e=o(62049),s=o(71333),n=o(94440),l=o(74657);function f(p,y){1&p&&t.nrm(0,"app-progress-spinner")}function b(p,y){if(1&p){const r=t.RV6();t.j41(0,"ion-card",14),t.bIt("click",function(){const C=t.eBV(r).$implicit,M=t.XpG();return t.Njj(M.nextStep(C))}),t.j41(1,"div",7)(2,"div",8),t.nrm(3,"ion-img",9),t.k0s(),t.j41(4,"div",15)(5,"label",16),t.EFF(6),t.nI1(7,"truncateString"),t.k0s(),t.j41(8,"p"),t.EFF(9),t.nI1(10,"truncateString"),t.k0s(),t.j41(11,"ion-button",17),t.EFF(12),t.nI1(13,"translate"),t.k0s()(),t.j41(14,"div",18),t.nrm(15,"ion-icon",19),t.k0s()()()}if(2&p){const r=y.$implicit,i=t.XpG();t.R7$(6),t.JRh(t.i5U(7,3,r.name,20)),t.R7$(3),t.JRh(t.i5U(10,6,null==i.commonService||null==i.commonService.user||null==i.commonService.user.address?null:i.commonService.user.address.region,28)),t.R7$(3),t.SpI(" ",t.bMT(13,9,"order-new-page.first-step.choose")," ")}}function D(p,y){if(1&p){const r=t.RV6();t.j41(0,"div",22),t.bIt("click",function(c){const M=t.eBV(r).$implicit;return t.XpG(2).selectSupplier(M),t.Njj(c.stopPropagation())}),t.EFF(1),t.k0s()}if(2&p){const r=y.$implicit;t.R7$(1),t.SpI(" ",r.name," ")}}function T(p,y){if(1&p&&(t.j41(0,"div",20),t.DNE(1,D,2,1,"div",21),t.k0s()),2&p){const r=t.XpG();t.R7$(1),t.Y8G("ngForOf",r.filteredSuppliers)}}const A=[{path:"",component:(()=>{class p{constructor(r,i,c,C,M,B,$,j,F){this.router=r,this.location=i,this.storageSrv=c,this.commonService=C,this.companySrv=M,this.priceSrv=B,this.packagingSrv=$,this.productSrv=j,this.translateService=F,this.isLoading=!0,this.isSelectedCompany=!1,this.filteredSuppliers=[],this.showSuggestions=!1,this.offset=0,this.limit=20,this.skeletons=[1,2,3,4,5,6],this.filterData={name:""},this.otherSupplier={id:"otherId",name:"Autre grossiste"},this.filterForm=new d.gE({name:new d.MJ("")}),this.user=this.storageSrv.getUserConnected()}ngOnInit(){var r=this;return(0,m.A)(function*(){r.isLoading=!0;try{const i=[r.getDistributorLoyalityProgram(),r.getSuppliers()];r.user.category!==h.s.Commercial&&i.push(r.getStore()),yield Promise.all(i)}catch(i){console.error("Error initializing component:",i)}finally{r.isLoading=!1}})()}getSuppliers(){var r=this;return(0,m.A)(function*(){r.skeletons=[1,2,3,4,5,6],r.isLoading=!0;const i={...r.filterData,offset:r.offset,limit:r.limit},c=r.commonService.user?.associatedSuppliers;if(c&&Array.isArray(c)&&c.length>0)r.suppliers=c;else{const C=yield r.companySrv.getParticularCompanies(i);r.suppliers=C.data,r.suppliers.push(r.otherSupplier),r.offset=r.offset+r.limit}r.isLoading=!1,r.skeletons=[]})()}getDistributorLoyalityProgram(){var r=this;return(0,m.A)(function*(){r.isLoading=!0;const i={...r.filterData,offset:r.offset,limit:r.limit,isLoyaltyProgDistributor:!0},c=yield r.companySrv.getCompanies(i);r.loyaltyProgramDistributor=c.data,r.filteredSuppliers=[],r.offset=r.offset+r.limit,r.isLoading=!1})()}selectCompany(r){this.selectedCompany=r,this.isSelectedCompany=!0}ionViewWillEnter(){return(0,m.A)(function*(){})()}getStore(){var r=this;return(0,m.A)(function*(){r.isLoading=!0;const i={companyId:r.companySrv.selectedCompanyForSalesOrderProcess?._id},c=yield r.priceSrv.getStores(i);c&&(r.initData=c,r.storageSrv.store("stores",JSON.stringify(c))),r.isLoading=!1})()}nextStep(r){var i=this;return(0,m.A)(function*(){const c=i.filterForm.value.name?.trim();c&&(!r||r.name!==c)&&(r={_id:null,name:c}),r||(r={...i.otherSupplier});const C={company:r._id,items:[],amount:null};yield i.storageSrv.store("cart",JSON.stringify(C)),yield i.storageSrv.store("supplier",JSON.stringify(r)),i.router.navigate(["order/recap-scan"]),i.productSrv.currentDataProductScan=[]})()}filterSuppliers(r){const i=r.target.value.toLowerCase();this.filterForm.patchValue({name:i}),""===i.trim()?this.showSuggestions=!1:(this.filteredSuppliers=this.loyaltyProgramDistributor.filter(c=>c.name.toLowerCase().includes(i)),this.showSuggestions=!0)}selectSupplier(r){this.selectedSupplier=r,this.filterForm.patchValue({name:r.name}),this.filteredSuppliers=[],this.showSuggestions=!1}onDocumentClick(r){this.showSuggestions=!1}back(){this.location.back()}static{this.\u0275fac=function(i){return new(i||p)(t.rXU(P.Ix),t.rXU(_.aZ),t.rXU(S.n),t.rXU(E.h),t.rXU(v.B),t.rXU(L.A),t.rXU(g.L),t.rXU(a.b),t.rXU(e.E))}}static{this.\u0275cmp=t.VBU({type:p,selectors:[["app-choice-suppliers"]],hostBindings:function(i,c){1&i&&t.bIt("click",function(M){return c.onDocumentClick(M)},!1,t.EBC)},decls:20,vars:14,consts:[[4,"ngIf"],[1,"header"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["id","container"],["class","custom-card",3,"click",4,"ngFor","ngForOf"],[1,"custom-card",3,"formGroup"],[1,"container"],[1,"image"],["src","../../../../assets/icons/man.png"],[1,"company","relative"],["formControlName","name","type","text",3,"clearInput","placeholder","input","focus"],["class","autocomplete-list",4,"ngIf"],["expand","block",3,"click"],[1,"custom-card",3,"click"],[1,"company"],["for",""],["expand","block"],[1,"chevron"],["slot","end","name","chevron-forward-outline","color","primary"],[1,"autocomplete-list"],["class","autocomplete-item",3,"click",4,"ngFor","ngForOf"],[1,"autocomplete-item",3,"click"]],template:function(i,c){1&i&&(t.DNE(0,f,1,0,"app-progress-spinner",0),t.j41(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-img",2),t.bIt("click",function(){return c.back()}),t.k0s(),t.j41(4,"ion-title",3),t.EFF(5),t.nI1(6,"translate"),t.k0s()()(),t.j41(7,"div",4),t.DNE(8,b,16,11,"ion-card",5),t.j41(9,"ion-card",6)(10,"div",7)(11,"div",8),t.nrm(12,"ion-img",9),t.k0s(),t.j41(13,"div",10)(14,"ion-input",11),t.bIt("input",function(M){return c.filterSuppliers(M)})("focus",function(){return c.showSuggestions=!0}),t.nI1(15,"translate"),t.DNE(16,T,2,1,"div",12),t.k0s(),t.j41(17,"ion-button",13),t.bIt("click",function(){return c.nextStep(c.selectedSupplier||c.otherSupplier)}),t.EFF(18),t.nI1(19,"translate"),t.k0s()()()()()),2&i&&(t.Y8G("ngIf",c.isLoading),t.R7$(5),t.JRh(t.bMT(6,8,"order-new-page.first-step.choose-store")),t.R7$(3),t.Y8G("ngForOf",c.suppliers),t.R7$(1),t.Y8G("formGroup",c.filterForm),t.R7$(5),t.FS9("placeholder",t.bMT(15,10,"order-new-page.first-step.supplier")),t.Y8G("clearInput",!0),t.R7$(2),t.Y8G("ngIf",c.filteredSuppliers.length>0&&c.showSuggestions),t.R7$(2),t.SpI(" ",t.bMT(19,12,"order-new-page.first-step.choose")," "))},dependencies:[_.Sq,_.bT,d.BC,d.cb,u.Jm,u.b_,u.eU,u.iq,u.KW,u.$w,u.BC,u.ai,u.Gw,s._,d.j4,d.JD,n.c,l.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:calc(41 * var(--res));margin:1rem;--border-color: transparent;--background: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;margin-bottom:0!important;color:var(--clr-primary-700);font-family:var(--mont-regular)}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));overflow:auto;height:100%}.title[_ngcontent-%COMP%]{text-align:center;color:#143c5d;font-size:22px}.custom-card[_ngcontent-%COMP%]{margin:12px 0;border:1px solid #dcdcdc;border-radius:8px}.custom-card[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:126px;padding:calc(41 * var(--res))}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;gap:1em;padding:1em}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border:2px solid #419CFB;border-radius:50%;padding:10px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:43px;width:43px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;align-items:baseline;color:#143c5d;width:75%}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:20px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{color:#fff;border-radius:4px;width:100%;background-color:#143c5d}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .chevron[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}ion-segment[_ngcontent-%COMP%]{--indicator-color: #0078d4;--background: transparent}ion-segment-button[checked][_ngcontent-%COMP%]{background-color:#0078d4;color:#fff}ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;position:relative;--padding-start: 0;font-size:calc(36 * var(--res));border-bottom:1px solid rgb(92,92,92)}.relative[_ngcontent-%COMP%]{position:relative}.autocomplete-list[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;border:1px solid #ccc;height:auto!important;overflow-y:auto;background:white;z-index:1;border-radius:4px;border:10px;box-shadow:0 4px 6px #0000001a}.autocomplete-item[_ngcontent-%COMP%]{padding:8px;cursor:pointer}.autocomplete-item[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}"]})}}return p})()}];let x=(()=>{class p{static{this.\u0275fac=function(i){return new(i||p)}}static{this.\u0275mod=t.$C({type:p})}static{this.\u0275inj=t.G2t({imports:[P.iI.forChild(A),P.iI]})}}return p})();var U=o(93887);let K=(()=>{class p{static{this.\u0275fac=function(i){return new(i||p)}}static{this.\u0275mod=t.$C({type:p})}static{this.\u0275inj=t.G2t({imports:[_.MD,d.YN,u.bv,U.G,l.h,d.X1,d.YN,x]})}}return p})()},43556:(I,O,o)=>{o.d(O,{B:()=>L});var _=o(73308),d=o(94934),u=o(45312),P=o(26409),h=(o(99987),o(2978)),t=o(33607),S=o(82571),E=o(14599),v=o(74657);let L=(()=>{class g{constructor(e,s,n,l,f){this.baseUrl=e,this.http=s,this.commonSrv=n,this.storageSrv=l,this.translateService=f,this.base_url=`${this.baseUrl.getOrigin()}${u.c.basePath}`,this.base_url+="companies"}create(e){var s=this;return(0,_.A)(function*(){try{return delete e._id,yield(0,d.s)(s.http.post(s.base_url,e))}catch(n){return s.commonSrv.getError("Echec de cr\xe9ation de la compagnie",n)}})()}getCompanies(e){var s=this;return(0,_.A)(function*(){try{let n=new P.Nl;const{category:l,city:f,limit:b,name:D,regionCom:T,solToId:R,tel:A,users:x,offset:U,enable:K=!0,projection:p,isLoyaltyProgDistributor:y}=e;return void 0!==l&&(n=n.append("category",l)),f&&(n=n.append("address.city",f)),D&&(n=n.append("name",D)),R&&(n=n.append("erpSoldToId",R)),A&&(n=n.append("tel",`${A}`)),p&&(n=n.append("projection",`${p}`)),x&&(n=n.append("users",`${x}`)),T&&(n=n.append("address.commercialRegion",T)),y&&(n=n.append("isLoyaltyProgDistributor",y)),void 0!==b&&(n=n.append("limit",b)),void 0!==U&&(n=n.append("offset",U)),n=n.set("enable",K),yield(0,d.s)(s.http.get(s.base_url,{params:n}))}catch(n){const f={message:s.commonSrv.getError("",n).message,color:"danger"};return yield s.commonSrv.showToast(f),n}})()}getParticularCompanies(e){var s=this;return(0,_.A)(function*(){let n=new P.Nl;const{limit:l,offset:f,enable:b=!0,commercialRegion:D}=e;return void 0!==l&&(n=n.append("limit",l)),void 0!==f&&(n=n.append("offset",f)),D&&(n=n.append("address.commercialRegion",D)),n=n.set("enable",b),yield(0,d.s)(s.http.get(s.base_url+"/particular-suppliers",{params:n}))})()}find(e){var s=this;return(0,_.A)(function*(){try{return yield(0,d.s)(s.http.get(s.base_url+"/"+e))}catch{return s.commonSrv.initCompany()}})()}getBalance(e){var s=this;return(0,_.A)(function*(){try{let n=new P.Nl;const{company:l}=s.storageSrv.getUserConnected();return n=n.set("_id",l?l?._id:e?.companyId),yield(0,d.s)(s.http.get(`${s.base_url}/balance`,{params:n}))}catch(n){return yield s.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),n}})()}getUsersCompany(e,s){var n=this;return(0,_.A)(function*(){try{let l=new P.Nl;const{email:f,enable:b=!0}=s;return f&&(l=l.append("email",f)),l=l.append("enable",b),yield(0,d.s)(n.http.get(`${n.base_url}/${e}/users`,{params:l}))}catch(l){return yield n.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),l}})()}static{this.\u0275fac=function(s){return new(s||g)(h.KVO(t.K),h.KVO(P.Qq),h.KVO(S.h),h.KVO(E.n),h.KVO(v.c$))}}static{this.\u0275prov=h.jDH({token:g,factory:g.\u0275fac,providedIn:"root"})}}return g})()},93387:(I,O,o)=>{o.d(O,{A:()=>E});var _=o(73308),d=o(26409),u=o(94934),P=o(58133),m=o(45312),h=o(2978),t=o(82571),S=o(33607);let E=(()=>{class v{constructor(g,a,e){this.http=g,this.commonSrv=a,this.baseUrlService=e,this.url=this.baseUrlService.getOrigin()+m.c.basePath+"prices"}getStores(g){var a=this;return(0,_.A)(function*(){try{let e=new d.Nl;const{companyId:s}=g;return s&&(e=e.append("company._id",s)),e=e.append("enable",!0),yield(0,u.s)(a.http.get(`${a.url}/${a.commonSrv.user.category===P.s.Commercial?"stores-sales":"stores"}`,{params:e}))}catch(e){const n={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(n),e}})()}getPricesForOrder(g){var a=this;return(0,_.A)(function*(){try{let e=new d.Nl;const{store:s,packaging:n,companyId:l,enable:f=!0}=g;return e=e.append("enable",f),s&&(e=e.append("store",s)),n&&(e=e.append("packaging",n)),l&&(e=e.append("company._id",l)),yield(0,u.s)(a.http.get(`${a.url}/${a.commonSrv.user.category===P.s.Commercial?"products-sales":"products"}`,{headers:{Authorization:`Bearer ${a.commonSrv?.user?.accessToken}`},params:e}))}catch(e){const n={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(n),e}})()}getPricesForCurrentOrder(g){var a=this;return(0,_.A)(function*(){try{let e=new d.Nl;const{storeId:s,packagingId:n,userId:l,enable:f=!0}=g;return e=e.append("enable",f),s&&(e=e.append("store",s)),n&&(e=e.append("packaging",n)),l&&(e=e.append("user",l)),yield(0,u.s)(a.http.get(`${a.url}/products-change`,{params:e}))}catch(e){return e}})()}getShippingAddress(g){var a=this;return(0,_.A)(function*(){try{let e=new d.Nl;const{offset:s,limit:n,enable:l=!0}=g;return s&&(e=e.append("offset",s)),n&&(e=e.append("limit",n)),e=e.append("enable",l),yield(0,u.s)(a.http.get(`${a.url}`,{params:e}))}catch(e){return e}})()}static{this.\u0275fac=function(a){return new(a||v)(h.KVO(d.Qq),h.KVO(t.h),h.KVO(S.K))}}static{this.\u0275prov=h.jDH({token:v,factory:v.\u0275fac,providedIn:"root"})}}return v})()},39316:(I,O,o)=>{o.d(O,{b:()=>E});var _=o(73308),d=o(26409),u=o(94934),P=o(45312),m=o(2978),h=o(82571),t=o(33607),S=o(77897);let E=(()=>{class v{constructor(g,a,e,s){this.http=g,this.commonSrv=a,this.baseUrlService=e,this.toastController=s,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+P.c.basePath+"products"}getProducts(g){var a=this;return(0,_.A)(function*(){try{let e=new d.Nl;return g?.limit&&(e=e.append("limit",g?.limit)),yield(0,u.s)(a.http.get(a.url,{params:e}))}catch(e){const n={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(n),e}})()}getProduct(g){var a=this;return(0,_.A)(function*(){try{return yield(0,u.s)(a.http.get(`${a.url}/${g}`))}catch(e){const n={message:a.commonSrv.getError("",e).message,color:"danger"};return yield a.commonSrv.showToast(n),e}})()}static{this.\u0275fac=function(a){return new(a||v)(m.KVO(d.Qq),m.KVO(h.h),m.KVO(t.K),m.KVO(S.K_))}}static{this.\u0275prov=m.jDH({token:v,factory:v.\u0275fac,providedIn:"root"})}}return v})()},94440:(I,O,o)=>{o.d(O,{c:()=>d});var _=o(2978);let d=(()=>{class u{transform(m,...h){return m?m.length>h[0]?`${m.substring(0,h[0]-3)}...`:m:""}static{this.\u0275fac=function(h){return new(h||u)}}static{this.\u0275pipe=_.EJ8({name:"truncateString",type:u,pure:!0})}}return u})()}}]);