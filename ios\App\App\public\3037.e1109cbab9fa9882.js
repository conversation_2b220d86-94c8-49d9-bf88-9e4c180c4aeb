"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3037],{63037:(P,w,x)=>{x.d(w,{_k:()=>p,ep:()=>I});var y=x(73308),C=x(2978),p=function(e){return e[e.Up=1]="Up",e[e.Down=3]="Down",e[e.Right=6]="Right",e[e.Left=8]="Left",e[e.UpMirrored=2]="UpMirrored",e[e.DownMirrored=4]="DownMirrored",e[e.LeftMirrored=5]="LeftMirrored",e[e.RightMirrored=7]="RightMirrored",e[e.Default=0]="Default",e[e.NotJpeg=-1]="NotJpeg",e[e.NotDefined=-2]="NotDefined",e}(p||{});class l{}l.getOrientation=e=>new Promise((i,r)=>{try{const n=new FileReader;n.onload=()=>{const a=new DataView(n.result);if(!a.byteLength||65496!==a.getUint16(0,!1))return i(p.NotDefined);const c=a.byteLength;let t=2;for(;t<c;){const u=a.getUint16(t,!1);if(t+=2,65505===u){if(1165519206!==a.getUint32(t+=2,!1))return i(p.NotJpeg);const g=18761===a.getUint16(t+=6,!1);t+=a.getUint32(t+4,g);const m=a.getUint16(t,g);t+=2;for(let o=0;o<m;o++)if(274===a.getUint16(t+12*o,g))return i(a.getUint16(t+12*o+8,g))}else{if(65280!=(65280&u))break;t+=a.getUint16(t,!1)}}return i(p.NotJpeg)},n.readAsArrayBuffer(e)}catch{return r(p.Default)}}),l.uploadFile=(e,i=!0,r=!1)=>new Promise(function(n,a){const c=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),t=/iPad|iPhone|iPod/i.test(navigator.userAgent);Promise.resolve(c||t).then(u=>u?l.generateUploadInputNative(window.document,i,r):l.generateUploadInputRenderer(e,i,r)).then(u=>{const g=u?Array.from(u):[],m=g.map(d=>l.getOrientation(d)),o=g.map(d=>l.fileToDataURL(d));let s=[];Promise.all(m).then(d=>(s=d,Promise.all(o))).then(d=>{const f=d.map((h,_)=>({image:h.dataUrl,orientation:s[_],fileName:h.fileName}));n(i?f:f[0])})}).catch(u=>a(u))}),l.fileToDataURL=e=>new Promise((i,r)=>{const n=new FileReader;n.onload=a=>{i({dataUrl:a.target.result,fileName:e.name})};try{n.readAsDataURL(e)}catch(a){r(`ngx-image-compress - probably no file have been selected: ${a}`)}}),l.generateUploadInputRenderer=(e,i=!0,r=!1)=>{let n=!1;return new Promise((a,c)=>{const t=e.createElement("input");e.setStyle(t,"display","none"),e.setProperty(t,"type","file"),e.setProperty(t,"accept","image/*"),i&&e.setProperty(t,"multiple","true"),e.listen(t,"click",u=>{u.target.value=""}),e.listen(t,"change",u=>{n=!0,a(u.target.files)}),r&&window.addEventListener("focus",()=>{setTimeout(()=>{n||c(new Error("file upload on blur - no file selected"))},300)},{once:!0}),t.click()})},l.generateUploadInputNative=(e,i=!0,r=!1)=>{let n=!1;return new Promise((a,c)=>{const t=e.createElement("input");t.id="upload-input"+new Date,t.style.display="none",t.setAttribute("type","file"),t.setAttribute("accept","image/*"),i&&t.setAttribute("multiple","true"),e.body.appendChild(t),t.addEventListener("change",()=>{n=!0,a(t.files),e.body.removeChild(e.getElementById(t.id))},{once:!0}),r&&window.addEventListener("focus",()=>{setTimeout(()=>{!n&&e.getElementById(t.id)&&(c(new Error("file upload on blur - no file selected")),e.body.removeChild(e.getElementById(t.id)))},300)},{once:!0}),t.click()})},l.compress=(e,i,r,n=50,a=50,c=0,t=0)=>new Promise(function(u,g){a/=100,n/=100;const m=new Image;m.onload=()=>{const o=r.createElement("canvas"),s=o.getContext("2d");if(!s)return g("No canvas context available");let d=m.naturalWidth,f=m.naturalHeight;if(!CSS.supports("image-orientation","from-image")&&(i===p.Right||i===p.Left)){const U=d;d=f,f=U}n=Math.min(n,c?c/d:1,t?t/f:1),o.width=d*n,o.height=f*n;const v=Math.PI/180;CSS.supports("image-orientation","from-image")||i===p.Up?s.drawImage(m,0,0,o.width,o.height):i===p.Right?(s.save(),s.rotate(90*v),s.translate(0,-o.width),s.drawImage(m,0,0,o.height,o.width),s.restore()):i===p.Left?(s.save(),s.rotate(-90*v),s.translate(-o.width,0),s.drawImage(m,0,0,o.height,o.width),s.restore()):i===p.Down?(s.save(),s.rotate(180*v),s.translate(-o.width,-o.height),s.drawImage(m,0,0,o.width,o.height),s.restore()):s.drawImage(m,0,0,o.width,o.height);const b=e.substr(5,e.split(";")[0].length-5),S=o.toDataURL(b,a);u(S)},m.onerror=o=>g(o),m.src=e}),l.byteCount=e=>encodeURI(e).split(/%..|./).length-1,l.getImageMaxSize=function(){var e=(0,y.A)(function*(i,r,n,a=!1){const t=m=>(m/1024/1024).toFixed(2);r&&console.debug("NgxImageCompress - Opening upload window");const u=yield l.uploadFile(n,!1,a);let g;for(let m=0;m<10;m++){const o=l.byteCount(u.image);g=yield l.compress(u.image,u.orientation,n,50,100);const s=l.byteCount(g);if(console.debug("NgxImageCompress -","Compression from",t(o),"MB to",t(s),"MB"),s>=o)throw 0===m?(r&&console.debug("NgxImageCompress -","File can't be reduced at all - returning the original",t(o),"MB large"),{...u,image:g}):(r&&console.debug("NgxImageCompress -","File can't be reduced more - returning the best we can, which is ",t(o),"MB large"),{...u,image:g});if(s<1024*i*1024)return r&&console.debug("NgxImageCompress -","Here your file",t(s),"MB large"),{...u,image:g};if(9===m)throw r&&console.debug("NgxImageCompress -","File can't reach the desired size after",10,"tries. Returning file ",t(o),"MB large"),{...u,image:g};r&&console.debug("NgxImageCompress -","Reached",t(s),"MB large. Trying another time after",m+1,"times"),u.image=g}throw r&&console.debug("NgxImageCompress - Unexpected error"),{}});return function(i,r,n){return e.apply(this,arguments)}}();let I=(()=>{class e{constructor(r){this.DOC_ORIENTATION=p,this.render=r.createRenderer(null,null)}byteCount(r){return l.byteCount(r)}getOrientation(r){return l.getOrientation(r)}uploadFile(){return l.uploadFile(this.render,!1)}uploadMultipleFiles(){return l.uploadFile(this.render,!0)}uploadFileOrReject(){return l.uploadFile(this.render,!1,!0)}uploadMultipleFilesOrReject(){return l.uploadFile(this.render,!0,!0)}compressFile(r,n,a=50,c=50,t=0,u=0){return l.compress(r,n,this.render,a,c,t,u)}uploadAndGetImageWithMaxSize(r=1,n=!1,a=!1){return l.getImageMaxSize(r,n,this.render,a).then(c=>c.image).catch(c=>{throw c.image})}uploadAndGetImageWithMaxSizeAndMetas(r=1,n=!1,a=!1){return l.getImageMaxSize(r,n,this.render,a)}}return e.\u0275fac=function(r){return new(r||e)(C.KVO(C._9s))},e.\u0275prov=C.jDH({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);