"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1095],{61095:(y,d,s)=>{s.d(d,{l:()=>O});var l=s(73308),u=s(94934),m=s(56610),v=s(45312),r=s(99987),g=s(26409),i=s(37222),_=s(73014),c=s(2978),p=s(82571),T=s(77897),f=s(33607),D=s(62049);let O=(()=>{class h{constructor(e,n,t,a,o){this.http=e,this.commonSrv=n,this.modalCtrl=t,this.baseUrlService=a,this.translateService=o,this.schedules=[],this.removals=[],this.remainingQtyTonne=0,this.remainingQtyTonneInStock=0,this.dailyCeiling=300,this.maxRemovalLine=10,this.minCapacityToRemove=.1,this.datePipe=new m.vh("fr"),this.scheduleForm=new i.gE({quantity:new i.MJ(0,[i.k0.required,i.k0.min(.1)]),tonneQuantity:new i.MJ(0,[i.k0.required,i.k0.min(.025)]),collection_date:new i.MJ("",[i.k0.required])}),this.url=this.baseUrlService.getOrigin()+v.c.basePath}editSchedule(){var e=this;return(0,l.A)(function*(){if(!e.remainingQtyTonne)return e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===r.T.French?"Renseignez au pr\xe9alable les quantit\xe9s":"Fill in the quantities as required"});const n=e.scheduleForm.get("quantity").value;return Number.isInteger(n)?void 0:(e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===r.T.French?"Veuillez renseigner une valeur de sac enti\xe8re":"Please enter a integer value of sac"}),void e.scheduleForm.patchValue({quantity:null,tonneQuantity:null}))})()}getScheduleDates(){const e=new Date,n=new Date(this.removalObject?.end)>e?new Date(new Date(this.removalObject?.end).setDate(new Date(this.removalObject?.end).getDate()+1)):this.removalObject?.start?new Date(this.removalObject?.start):new Date,t=this.remainingQtyTonneInStock?new Date(this.removalObject?.start)>e?new Date(this.removalObject?.start):new Date:n,a=new Date;return{minDate:t,maxDate:this.remainingQtyTonneInStock?new Date(this.removalObject.end):new Date(a.setMonth(a?.getMonth()+3))}}closeForm(){this.modalCtrl.dismiss()}getTotalTonnage(){let n=this.schedules.map(t=>t.nbrTruck?t?.nbrTonnage*t?.nbrTruck:+t.nbrTonnage).reduce((t,a)=>t+a,0);return Math.round(1e3*n)/1e3}getRemovals(){this?.cartItem?.quantity>0?this.removals?.filter((e,n)=>e.itemId===this.cartItem?.product?._id?(this.schedules=e.schedules,this.schedulesTypeSelected=e.removalType,this.currentRemoval=e,this.removals.splice(n,1)):this.removals):this.schedules=[]}verifyDailyCeiling(e,n){if(e=new m.vh("fr").transform(e,"dd/MM/yy"),n>this.dailyCeiling)return this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===r.T.French?"Vous avez exc\xe9d\xe9 votre capacit\xe9 de tonnage journali\xe8re qui est de 300 tonnes":"You have exceeded your daily tonnage capacity which is 300 tonnes"}),!0;let t=0;return this.schedules.forEach(a=>{new m.vh("fr").transform(a.removalDate,"dd/MM/yy")===e&&(t+=parseInt(`${this.schedulesTypeSelected===_.JN.perDate?a.nbrTonnage:a.nbrTonnage*a.nbrTruck}`))}),this.dailyCeiling<t+parseInt(`${n}`)&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===r.T.French?"Vous avez exc\xe9d\xe9 votre capacit\xe9 de tonnage pour ce jour qui est de 300 tonnes, s\xe9lectionnez un autre jour.":"You have exceeded your tonnage capacity for that day which is 300 tonnes, select another day."}),!0)}saveSchedule(e){if(this?.schedules?.length>=this?.maxRemovalLine)return this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===r.T.French?`Vous ne pouvez pas pas planifier plus de ${this?.maxRemovalLine} lignes d'enl\xe8vements`:`You have exceeded your daily tonnage capacity ${this?.maxRemovalLine} removal lines`}),new Error("maxRemovalLine");this.schedules.push(e),this.remainingQtyTonne-=this.schedulesTypeSelected===_.JN.perDate?e?.nbrTonnage:parseFloat((e?.nbrTonnage*e?.nbrTruck).toFixed(3)),this.scheduleForm.get("collection_date").reset(),this.commonSrv.showToast({icon:"checkmark-circle-outline",color:"success",message:this.translateService.currentLang===r.T.French?"Vous avez ajout\xe9 une ligne \xe0 votre planification":"You have added a line for your planning"}),0===this.remainingQtyTonne&&(this.isGoodPlannify=!0),this.remainingQtyTonne=Math.round(1e3*this.remainingQtyTonne)/1e3,this.closeForm()}getPlanificationByStores(e){var n=this;return(0,l.A)(function*(){try{let t=new g.Nl;const{storeId:a,today:o}=e;return a&&(t=t.append("storeId",a)),o&&(t=t.append("today",o)),yield(0,u.s)(n.http.get(`${n.url}planifications/custom`,{params:t}))}catch(t){const o={message:n.commonSrv.getError("",t).message,color:"danger"};return yield n.commonSrv.showToast(o),t}})()}getRemovalsObject(){var e=this;return(0,l.A)(function*(){try{const n={storeId:e.cart.store._id,today:(new Date).valueOf()};if(e.availableRemovals=yield e.getPlanificationByStores(n),!(e.availableRemovals instanceof Array)){const a=e.commonSrv.getError("Une erreur s'est produite",e.availableRemovals);throw new Error(JSON.stringify({title:a?.data,message:a?.message}))}const t=e.availableRemovals?.find(a=>a?.data?.product?._id===e.cartItem?.product?._id);if(!t)throw new Error(JSON.stringify({title:e.translateService.currentLang===r.T.French?"Indisponible":"Unavailable",message:e.translateService.currentLang===r.T.French?"Aucune planification en production n'est disponible pour ce produit. ":"No production planning is available for this product."}));return e.removalObject=t.data}catch(n){return n}})()}verifyCanRemoval(e){return!!(this.remainingQtyTonneInStock&&e>this.remainingQtyTonneInStock)&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===r.T.French?`Vous avez exc\xe9d\xe9 la quantit\xe9 en stock (${this.remainingQtyTonneInStock} tonnes)`:`You have exceeded the stock quantityLeft (${this.remainingQtyTonneInStock} tonnes)`}),!0)}getRemainingQuantityStock(){const e=this.removalObject?.clientShare?.find(t=>t?.company?._id===this.commonSrv?.user?.company?._id);if(e?.quantity)return this.remainingQtyTonneInStock=e?.quantity??null;const n=this.removalObject?.categoryShare?.find(t=>t?.category===this.commonSrv?.user?.company?.category);return this.remainingQtyTonneInStock=n?.quantity?n?.quantity:this.removalObject?.quantityLeft??null}static{this.\u0275fac=function(n){return new(n||h)(c.KVO(g.Qq),c.KVO(p.h),c.KVO(T.W3),c.KVO(f.K),c.KVO(D.E))}}static{this.\u0275prov=c.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()},73014:(y,d,s)=>{s.d(d,{J7:()=>l,JN:()=>u,iB:()=>v});class l{}var u=function(r){return r[r.PerQuantity=1]="PerQuantity",r[r.perDate=2]="perDate",r}(u||{});class v{}}}]);