"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1864],{32928:(x,h,e)=>{e.d(h,{Y:()=>c});var d=e(44912),P=e(53229);function c(M=0,l=d.E){return M<0&&(M=0),(0,P.O)(M,M,l)}},71864:(x,h,e)=>{e.r(h),e.d(h,{MarketPlacePageModule:()=>on});var d=e(56610),P=e(37222),c=e(77897),M=e(77575),l=e(73308),b=e(35025),y=e.n(b),k=e(32928),f=e(99987),v=e(33074),g=e(45312),n=e(2978),s=e(82571),u=e(97130),_=e(94934),O=e(26409),w=e(33607);let m=(()=>{class r{constructor(t,o,i,a){this.http=t,this.commonSrv=o,this.baseUrlService=i,this.toastController=a,this.url=this.baseUrlService.getOrigin()+g.c.basePath+"category"}getCategories(t){var o=this;return(0,l.A)(function*(){try{return yield(0,_.s)(o.http.get(o.url))}catch(i){const p={message:o.commonSrv.getError("",i).message,color:"danger"};return yield o.commonSrv.showToast(p),i}})()}getCategory(t){var o=this;return(0,l.A)(function*(){try{return yield(0,_.s)(o.http.get(`${o.url}/${t}`))}catch(i){const p={message:o.commonSrv.getError("",i).message,color:"danger"};return yield o.commonSrv.showToast(p),i}})()}static{this.\u0275fac=function(o){return new(o||r)(n.KVO(O.Qq),n.KVO(s.h),n.KVO(w.K),n.KVO(c.K_))}}static{this.\u0275prov=n.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();var A=e(55988),S=e(62049),V=e(63829);function U(r,C){if(1&r){const t=n.RV6();n.j41(0,"ion-slide",3),n.bIt("click",function(){const a=n.eBV(t).$implicit,p=n.XpG();return n.Njj(p.onProductClick(a))}),n.j41(1,"div",4)(2,"div",5),n.nrm(3,"ion-img",6),n.k0s(),n.j41(4,"div",7),n.EFF(5),n.k0s(),n.j41(6,"div",8),n.EFF(7),n.k0s(),n.j41(8,"div",9),n.EFF(9),n.k0s()()()}if(2&r){const t=C.$implicit;n.R7$(3),n.Y8G("src",null==t?null:t.image)("alt",null==t?null:t.name),n.R7$(2),n.JRh(null==t?null:t.name),n.R7$(2),n.JRh(null==t?null:t.sku),n.R7$(2),n.SpI("",null==t?null:t.price," Point(s)")}}let L=(()=>{class r{getImgStyle(t){return{"background-image":`url('${t}')`,"background-repeat":"no-repeat","background-position":"center","background-size":"contain"}}onProductClick(t){this.productSelected.emit(t)}constructor(){this.autoplay=!0,this.productSelected=new n.bkB,this.slideProductsOpts={initialSlide:0,speed:2e3,spaceBetween:10,slidesPerView:3,autoplay:!0,grabCursor:!0,cubeEffect:{shadow:!0,slideShadows:!0,shadowOffset:20,shadowScale:.94}}}ngOnInit(){this.slideProductsOpts.autoplay=this.autoplay}static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-products-slider"]],inputs:{items:"items",autoplay:"autoplay"},outputs:{productSelected:"productSelected"},decls:3,vars:2,consts:[[1,"slides",3,"options"],["slideOffer",""],[3,"click",4,"ngFor","ngForOf"],[3,"click"],[1,"product"],[1,"img-contain"],[1,"logo-icon",3,"src","alt"],[1,"product-label"],[1,"product-qte"],[1,"product-price"]],template:function(o,i){1&o&&(n.j41(0,"ion-slides",0,1),n.DNE(2,U,10,5,"ion-slide",2),n.k0s()),2&o&&(n.Y8G("options",i.slideProductsOpts),n.R7$(2),n.Y8G("ngForOf",i.items))},dependencies:[c.KW,c.q3,c.tR,d.Sq],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.slides[_ngcontent-%COMP%]{margin-bottom:1em}.product[_ngcontent-%COMP%]{text-align:left;width:100%;font-size:14px;padding:1em}.product[_ngcontent-%COMP%]   .img-contain[_ngcontent-%COMP%]{border-radius:9.19px;margin-bottom:.5em;overflow:hidden}.product[_ngcontent-%COMP%]   .img-contain[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:15vh}.product[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]{color:#0b305c;font-family:Mont SemiBold;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;font-size:13px}.product[_ngcontent-%COMP%]   .product-qte[_ngcontent-%COMP%]{color:#6d839d;font-size:12px}.product[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{color:#419cfb;font-size:12px}"]})}}return r})();var $=e(74657),N=e(11244);const j=function(r){return{active:r}};function K(r,C){if(1&r){const t=n.RV6();n.j41(0,"div",12)(1,"ion-chip",26),n.bIt("click",function(){const a=n.eBV(t).$implicit,p=n.XpG();return n.Njj(p.handleClick(a))}),n.j41(2,"ion-label",14),n.EFF(3),n.nI1(4,"capitalize"),n.k0s()()()}if(2&r){const t=C.$implicit,o=n.XpG();n.R7$(1),n.Y8G("ngClass",n.eq3(5,j,(null==t?null:t.label)===o.selectedItem))("outline",!0),n.R7$(2),n.SpI(" ",n.bMT(4,3,null==t?null:t.label)," ")}}function G(r,C){if(1&r&&(n.j41(0,"div",27)(1,"div",28)(2,"div",29),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.j41(5,"div",30)(6,"div",31)(7,"div",32),n.EFF(8),n.k0s(),n.j41(9,"div",33),n.EFF(10),n.nI1(11,"translate"),n.k0s()(),n.j41(12,"div",31)(13,"div",32),n.EFF(14),n.k0s(),n.j41(15,"div",33),n.EFF(16),n.nI1(17,"translate"),n.k0s()(),n.j41(18,"div",31)(19,"div",32),n.EFF(20),n.k0s(),n.j41(21,"div",33),n.EFF(22),n.nI1(23,"translate"),n.k0s()(),n.j41(24,"div",31)(25,"div",32),n.EFF(26),n.k0s(),n.j41(27,"div",33),n.EFF(28),n.nI1(29,"translate"),n.k0s()()(),n.j41(30,"div",34)(31,"div",35),n.EFF(32),n.nI1(33,"translate"),n.nI1(34,"date"),n.k0s()()()()),2&r){const t=n.XpG();n.R7$(3),n.JRh(n.bMT(4,11,"tab-bar.reward-products.available")),n.R7$(5),n.JRh(null==t.remainingTime?null:t.remainingTime.remainingDays),n.R7$(2),n.JRh(n.bMT(11,13,"tab-bar.reward-products.countdown.days")),n.R7$(4),n.JRh(null==t.remainingTime?null:t.remainingTime.remainingHours),n.R7$(2),n.JRh(n.bMT(17,15,"tab-bar.reward-products.countdown.hours")),n.R7$(4),n.JRh(null==t.remainingTime?null:t.remainingTime.remainingMinutes),n.R7$(2),n.JRh(n.bMT(23,17,"tab-bar.reward-products.countdown.minutes")),n.R7$(4),n.JRh(null==t.remainingTime?null:t.remainingTime.remainingSeconds),n.R7$(2),n.JRh(n.bMT(29,19,"tab-bar.reward-products.countdown.seconds")),n.R7$(4),n.Lme(" ",n.bMT(33,21,"tab-bar.reward-products.deadline")," : ",n.i5U(34,23,null==t.marketplaceStatus?null:t.marketplaceStatus.closingTime,"dd/MM/yyyy"),"")}}const z=function(r){return{"background-image":r}};function Y(r,C){if(1&r&&n.nrm(0,"ion-slide",36),2&r){const t=C.$implicit;n.Y8G("ngStyle",n.eq3(1,z,"url("+(null==t?null:t.img)+")"))}}function W(r,C){if(1&r){const t=n.RV6();n.j41(0,"div")(1,"div",38)(2,"b")(3,"div",39),n.EFF(4),n.k0s()(),n.j41(5,"a",40),n.bIt("click",function(){n.eBV(t);const i=n.XpG().$implicit,a=n.XpG();return n.Njj(a.showCategoryProducts(i.code))}),n.EFF(6),n.nI1(7,"translate"),n.k0s()(),n.j41(8,"app-products-slider",41),n.bIt("productSelected",function(i){n.eBV(t);const a=n.XpG(2);return n.Njj(a.onProductClick(i))}),n.k0s()()}if(2&r){const t=n.XpG().$implicit,o=n.XpG();n.R7$(4),n.JRh(t.label),n.R7$(2),n.JRh(n.bMT(7,4,"market-place.see-more-button")),n.R7$(2),n.Y8G("autoplay",!0)("items",o.categoryProducts[t.code])}}function H(r,C){if(1&r&&(n.j41(0,"div"),n.DNE(1,W,9,6,"div",37),n.k0s()),2&r){const t=C.$implicit,o=n.XpG();n.R7$(1),n.Y8G("ngIf",o.categoryProducts[t.code]&&o.categoryProducts[t.code].length>0)}}function X(r,C){1&r&&n.nrm(0,"ion-slide",42),2&r&&n.Y8G("ngStyle",n.eq3(1,z,"url("+C.$implicit.img+")"))}const I=6e4,T=60*I,B=24*T,nn=[{path:"",component:(()=>{class r{constructor(t,o,i,a,p,D,R,F){this.route=t,this.router=o,this.platform=i,this.commonSrv=a,this.versionSrv=p,this.categoryService=D,this.marketPlaceService=R,this.translateService=F,this.slideOpts={initialSlide:0,speed:0,spaceBetween:16,autoplay:!1,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}},this.slideImagesOpts={initialSlide:0,speed:2e3,spaceBetween:10,slidesPerView:1.5,autoplay:!0,cubeEffect:{shadow:!0,slideShadows:!0,shadowOffset:20,shadowScale:.94}},this.slidebtns={initialSlide:0,speed:2e3,spaceBetween:3,slidesPerView:2.5,autoplay:!0,grabCursor:!0,updateAutoHeight:2e3,cubeEffect:{shadow:!0,slideShadows:!0,shadowOffset:20,shadowScale:.94}},this.slidedailyImages={initialSlide:0,speed:2e3,spaceBetween:5,slidesPerView:1.5,autoplay:!1,grabCursor:!0,cubeEffect:{shadow:!0,slideShadows:!0,shadowOffset:20,shadowScale:.94}},this.images=[],this.actualData=[],this.btnsSlide=[{label:"Toutes cat\xe9gories",value:1},{label:"Offres sp\xe9ciales",value:2},{label:"Alimentations & boissons",value:3},{label:"Sant\xe9 et bien \xeatre",value:4},{label:"Maison et Jardin",value:5},{label:"Mode et accessoires",value:4},{label:"Services",value:5}],this.tendencySlides=[],this.dailyImages=[],this.selectedItem="Toutes cat\xe9gories",this.filterForm={level:"",enable:!0,created_at:"",date:{start:y()().startOf("year"),end:y()().endOf("year")}},this.offset=0,this.limit=50,this.total=0,this.filterData={limit:20,offset:0,sku:"",categoryCode:""},this.isCountdownActive=!1,this.remainingTime={remainingDays:"00",remainingHours:"00",remainingMinutes:"00",remainingSeconds:"00"},this.categories=[],this.currentCategories=[],this.categoryProducts={},this.storeProductsCategories=[]}ngOnInit(){var t=this;return(0,l.A)(function*(){t.commonSrv.showNav=!0,t.currVersion=t.versionSrv.getAppVersion()||t.platform.is("android")?g.c.appVersionAndroid:g.c.appVersionIos,yield t.initializeData()})()}ionViewWillEnter(){var t=this;return(0,l.A)(function*(){t.commonSrv.showNav=!0})()}initializeData(){var t=this;return(0,l.A)(function*(){yield Promise.all([t.loadCategories(),t.getImage()])})()}showCategoryProducts(t){this.commonSrv.productCategory=t,this.router.navigateByUrl("navigation/market-place/category-products")}loadCategories(){var t=this;return(0,l.A)(function*(){const o=yield t.categoryService.getCategories();!o||!o.data||(t.categories=o.data,t.currentCategories=[...t.categories],t.storeProductsCategories=[...t.categories],yield t.loadProductsByCategories())})()}loadProductsByCategories(){var t=this;return(0,l.A)(function*(){if(!t.categories||0===t.categories.length)return;const o=t.categories.map(function(){var i=(0,l.A)(function*(a){const p=yield t.getItems({...t.filterData,categoryCode:a.code});!p||0===p.length||(t.categoryProducts[a.code]=p)});return function(a){return i.apply(this,arguments)}}());yield Promise.all(o)})()}getItems(t){var o=this;return(0,l.A)(function*(){return o.marketPlaceService.getItems(t).then(i=>{if(!i||!i.data)return[];if(i.data.length>0){const a=i.data[0];o.isCountdownActive=a?.marketplaceStatus?.isActive,o.marketplaceStatus=a?.marketplaceStatus,o.isCountdownActive&&a?.marketplaceStatus?.closingTime&&o.startCountdown(a.marketplaceStatus.closingTime)}return i.data}).catch(i=>(console.error("Error fetching items:",i),[]))})()}startCountdown(t){this.countdownSubscription&&this.countdownSubscription.unsubscribe();const o=()=>{const i=(new Date).getTime(),a=t-i;if(a<=0)return this.isCountdownActive=!1,this.remainingTime={remainingDays:"00",remainingHours:"00",remainingMinutes:"00",remainingSeconds:"00"},this.countdownSubscription&&this.countdownSubscription.unsubscribe(),void this.commonSrv.showToast({message:this.translateService.currentLang===f.T.French?"Le temps est \xe9coul\xe9 ! Vous pouvez maintenant \xe9changer vos produits.":"Time is up! You can now exchange your products.",color:"success",duration:5e3,position:"top"});const p=Math.floor(a/B),D=Math.floor(a%B/T),R=Math.floor(a%T/I),F=Math.floor(a%I/1e3);this.remainingTime={remainingDays:p.toString().padStart(2,"0"),remainingHours:D.toString().padStart(2,"0"),remainingMinutes:R.toString().padStart(2,"0"),remainingSeconds:F.toString().padStart(2,"0")}};o(),this.countdownSubscription=(0,k.Y)(1e3).subscribe(()=>{o()})}ngOnDestroy(){this.countdownSubscription&&this.countdownSubscription.unsubscribe()}getRandomProducts(){var t=this;return(0,l.A)(function*(){for(const o of t.categories)t[`${o?.label}`]=yield t.getItems({...t.filterData,categoryCode:o?.code}),t.actualData.push(t[`${o?.label}`]);return t.actualData})()}onProductClick(t){this.marketPlaceService.currentItems=t,this.marketPlaceService.isOpen=this.isCountdownActive,this.router.navigate(["navigation/market-place/product-detail"])}allproductsDisplay(){this.categories=this.storeProductsCategories,this.selectedItem="Toutes cat\xe9gories"}handleClick(t){this.selectedItem=t?.label,this.categories=this.storeProductsCategories,this.categories=this.categories.filter(o=>o?.code==t?.code)}getImage(){var t=this;return(0,l.A)(function*(){const o={offset:t.offset,limit:t.limit,...t.filterForm},i=(yield t.commonSrv.getAllImageBanner(o))?.data;t.images=i.filter(a=>a.level===v.H.STORE_1).map(a=>({img:a?.image})),t.images?.length||(t.images=[{img:"/assets/images/marketPlace.png"},{img:"/assets/images/marketPlace1.png"},{img:"/assets/images/marketPlace2.png"},{img:"/assets/images/marketPlace3.png"}]),t.tendencySlides=i.filter(a=>a.level===v.H.STORE_2).map(a=>({img:a?.image})),t.tendencySlides?.length||(t.tendencySlides=[{img:"/assets/images/tendancy-slide1.png"},{img:"/assets/images/tendancy-slide2.png"},{img:"/assets/images/tendancy-slide3.png"}]),t.dailyImages=i.filter(a=>a.level===v.H.STORE_3).map(a=>({img:a?.image})),t.dailyImages?.length||(t.dailyImages=[{img:"/assets/images/daily.png"},{img:"/assets/images/bread.png"},{img:"/assets/images/faming.png"}])})()}getCategories(){var t=this;return(0,l.A)(function*(){t.categories=(yield t.categoryService.getCategories()).data})()}back(){this.route.navigate(["navigation/home"])}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(M.Ix),n.rXU(M.Ix),n.rXU(c.OD),n.rXU(s.h),n.rXU(u.I),n.rXU(m),n.rXU(A.L),n.rXU(S.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-market-place"]],decls:32,vars:21,consts:[[1,"market-place"],[1,"header-page"],[1,"div-start"],[1,"left-block"],[1,"logo-tag",3,"click"],["src","../../../assets/icons/logo_1.svg","alt","Logo",1,"logo-icon"],[1,"market"],[3,"avatar","account","message","search"],[1,"main-container",3,"fullscreen"],["id","container"],[1,"btn-products-slides"],[1,"part-search-chip"],[1,"space-h-v"],["color","success",3,"ngClass","click"],[1,"categories"],["class","space-h-v",4,"ngFor","ngForOf","ngForTrackBy"],["class","countdown-container",4,"ngIf"],[1,"slides",3,"options","pager"],["slideOffer",""],["class","slide",3,"ngStyle",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[1,"slides","slidesDaily",3,"options"],["slideDaily",""],[3,"ngStyle",4,"ngFor","ngForOf"],[1,"end-slide"],[1,"version"],["color","success",3,"ngClass","outline","click"],[1,"countdown-container"],[1,"countdown-card"],[1,"countdown-message"],[1,"countdown-timer"],[1,"time-block"],[1,"time-value"],[1,"time-label"],[1,"deadline-bar"],[1,"deadline-text"],[1,"slide",3,"ngStyle"],[4,"ngIf"],[1,"special-offer"],[1,"title"],[1,"seemore",3,"click"],[3,"autoplay","items","productSelected"],[3,"ngStyle"]],template:function(o,i){1&o&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar")(3,"div",1)(4,"div",2)(5,"div",3)(6,"div",4),n.bIt("click",function(){return i.back()}),n.nrm(7,"ion-img",5),n.k0s(),n.j41(8,"ion-label",6),n.EFF(9),n.nI1(10,"translate"),n.k0s()(),n.nrm(11,"app-header-actions",7),n.k0s()()()(),n.j41(12,"ion-content",8)(13,"div",9)(14,"div",10)(15,"div",11)(16,"div",12)(17,"ion-chip",13),n.bIt("click",function(){return i.allproductsDisplay()}),n.j41(18,"ion-label",14),n.EFF(19," Toutes cat\xe9gories "),n.k0s()()(),n.DNE(20,K,5,7,"div",15),n.k0s()(),n.DNE(21,G,35,26,"div",16),n.j41(22,"ion-slides",17,18),n.DNE(24,Y,1,3,"ion-slide",19),n.k0s(),n.DNE(25,H,2,1,"div",20),n.j41(26,"ion-slides",21,22),n.DNE(28,X,1,3,"ion-slide",23),n.k0s(),n.j41(29,"div",24)(30,"div",25),n.EFF(31),n.k0s()()()()()),2&o&&(n.R7$(9),n.JRh(n.bMT(10,17,"tab-bar.marketPlace")),n.R7$(2),n.Y8G("avatar",!0)("account",!1)("message",!0)("search",!1),n.R7$(1),n.Y8G("fullscreen",!0),n.R7$(5),n.Y8G("ngClass",n.eq3(19,j,"Toutes cat\xe9gories"===i.selectedItem)),n.R7$(3),n.Y8G("ngForOf",i.currentCategories)("ngForTrackBy",i.trackByFn),n.R7$(1),n.Y8G("ngIf",i.isCountdownActive),n.R7$(1),n.Y8G("options",i.slideImagesOpts)("pager",!0),n.R7$(2),n.Y8G("ngForOf",i.images),n.R7$(1),n.Y8G("ngForOf",i.categories),n.R7$(1),n.Y8G("options",i.slidedailyImages),n.R7$(2),n.Y8G("ngForOf",i.dailyImages),n.R7$(3),n.SpI(" version: ",i.currVersion," "))},dependencies:[d.YU,d.Sq,d.bT,d.B3,c.ZB,c.W9,c.eU,c.KW,c.he,c.q3,c.tR,c.ai,V.e,L,d.vh,$.D9,N.F],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{border-radius:0 0 15px 15px!important}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: 2.5rem;--border-color: transparent;--background-color: transparent;border-radius:0 0 15px 15px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .toolbar-background[_ngcontent-%COMP%]{border-radius:0 0 15px 15px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .toolbar-container[_ngcontent-%COMP%]{padding-top:2.5rem}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   div.header-page[_ngcontent-%COMP%]{margin-bottom:3.75vmin;border-radius:0 0 15px 15px!important}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:1rem}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .market[_ngcontent-%COMP%]{font-size:calc(51 * var(--res));font-family:var(--mont-semibold);color:var(--ion-color-primary)}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .logo-tag[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;background-color:var(--ion-color-primary);border-radius:8px;width:1.2em;height:1.2em}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .logo-tag[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:55%}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:100}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(37.5 * var(--res));background-color:#eef2f9;margin-top:-1em;padding-top:1.5em;padding-bottom:calc(22 * var(--resH));height:max-content}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .btn-products-slides[_ngcontent-%COMP%]{position:fixed;z-index:1000;width:calc(100% - 1.3em)}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]{display:flex;white-space:nowrap;overflow-x:scroll;scrollbar-width:none;-webkit-scrollbar-width:none}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]   .space-all[_ngcontent-%COMP%]{padding:0 0 0 calc(41 * var(--res))}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{padding-top:0;margin-bottom:20px}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip.active[_ngcontent-%COMP%]{--color: $color-nine;border:none}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{margin-right:8px;border-radius:25px;height:2.5rem;padding:0 12px;background-color:#fff;font-size:"Mont SemiBold"}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]{font-size:11px!important}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[color=success][_ngcontent-%COMP%]{color:#143c5d;border-color:#d8eafe}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#fff!important;background:var(--clr-primary-900)}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-container[_ngcontent-%COMP%]{margin:2.5em 0 1.5em;box-shadow:1px 9px 13px -1px #0d0ba782;border-radius:1rem}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]{background-image:linear-gradient(135deg,var(--ion-color-primary) 0%,var(--ion-color-primary-shade) 100%);border-radius:1rem;overflow:hidden;box-shadow:0 4px 8px #0000001a;position:relative}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]   .countdown-message[_ngcontent-%COMP%]{color:#fff;font-size:.9rem;font-weight:600;text-align:center;padding:20px 16px 10px;position:relative;z-index:1;font-family:Mont Bold}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]{display:flex;justify-content:space-around;padding:10px 16px 20px;position:relative;z-index:1}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .time-block[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .time-block[_ngcontent-%COMP%]   .time-value[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;color:#fff;line-height:1;font-family:Mont Bold}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%]   .time-block[_ngcontent-%COMP%]   .time-label[_ngcontent-%COMP%]{font-size:.8rem;color:#fff;margin-top:5px;font-weight:900;font-family:Mont Bold}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]   .deadline-bar[_ngcontent-%COMP%]{background-color:var(--ion-color-primary-shade);padding:12px 16px;text-align:center;position:relative;z-index:1}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .countdown-card[_ngcontent-%COMP%]   .deadline-bar[_ngcontent-%COMP%]   .deadline-text[_ngcontent-%COMP%]{color:#fff;font-size:.8rem;font-weight:600;text-align:end;font-family:Mont Bold}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slides[_ngcontent-%COMP%]{margin-top:2.2em;height:50vw}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{padding-bottom:calc(25 * var(--res));background-repeat:no-repeat;background-size:contain}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slides[_ngcontent-%COMP%]   .swiper-pagination-bullets[_ngcontent-%COMP%]   .swiper-pagination-bullet[_ngcontent-%COMP%]{width:5px!important;height:5px!important}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .slidesDaily[_ngcontent-%COMP%]{margin-block:1em}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .special-offer[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;color:#0b305c;font-weight:700;font-size:var(--fs-13-px);margin-bottom:1em;font-family:Mont SemiBold}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .special-offer[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .special-offer[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .special-offer[_ngcontent-%COMP%]   .seemore[_ngcontent-%COMP%]{font-size:.875rem;color:#419cfb;font-family:Mont SemiBold}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .end-slide[_ngcontent-%COMP%]{margin-bottom:10%}.main-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .end-slide[_ngcontent-%COMP%]   .version[_ngcontent-%COMP%]{font-size:10px}.slide-container[_ngcontent-%COMP%]{width:100%;border-radius:9px;height:40vw;margin-bottom:1em}.slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]{border-radius:9px;height:100%;--bullet-background: white;--bullet-background-active: red}.slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%], .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%]{width:19px;height:19px;background-size:19px 19px}.slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%], .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-prev[_ngcontent-%COMP%]{background-image:url(arrow-back-white.e97e691d01c56343.svg)}.slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%], .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   .swiper-button-next[_ngcontent-%COMP%]{background-image:url(arrow-forward-white.a589d59d565b95fe.svg)}.slide-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%], .slide-container[_ngcontent-%COMP%]   .slide-content[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{background-repeat:no-repeat;background-size:cover}.btn-slide[_ngcontent-%COMP%]   ion-chip.active[_ngcontent-%COMP%]{--background: $color-secondary;--color: $color-nine}.btn-slide[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{margin-right:8px;border-radius:20px;padding:0 12px}.btn-slide[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]{font-weight:100;font-size:10px!important}.btn-slide[_ngcontent-%COMP%]   ion-chip[color=success][_ngcontent-%COMP%]{color:#143c5d;border-color:#d8eafe;background-color:#fff}.btn-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#fff!important;background:#419CFB!important}.swiper-container-horizontal[_ngcontent-%COMP%] > .swiper-pagination-bullets[_ngcontent-%COMP%]   .swiper-pagination-bullet[_ngcontent-%COMP%]{height:5px!important;width:5px!important}@media (max-width: 360px){.countdown-timer[_ngcontent-%COMP%]   .time-block[_ngcontent-%COMP%]   .time-value[_ngcontent-%COMP%]{font-size:2rem!important}.countdown-timer[_ngcontent-%COMP%]   .time-block[_ngcontent-%COMP%]   .time-label[_ngcontent-%COMP%]{font-size:.7rem!important}}']})}}return r})()},{path:"product-detail",loadChildren:()=>Promise.all([e.e(2076),e.e(5571)]).then(e.bind(e,65571)).then(r=>r.ProductDetailPageModule)},{path:"historic-market-place",loadChildren:()=>Promise.all([e.e(2076),e.e(1382)]).then(e.bind(e,71382)).then(r=>r.HistoricMarketPlacePageModule)},{path:"historic-menu",loadChildren:()=>e.e(4870).then(e.bind(e,54870)).then(r=>r.HistoricMenuPageModule)},{path:"order-detail/:idOrder",loadChildren:()=>Promise.all([e.e(7784),e.e(2076),e.e(4519)]).then(e.bind(e,26801)).then(r=>r.OrderDetailMarketPlacePageModule)},{path:"category-products",loadChildren:()=>e.e(1192).then(e.bind(e,71192)).then(r=>r.CategoryProductsPageModule)}];let tn=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[M.iI.forChild(nn),M.iI]})}}return r})();var en=e(93887);let on=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[d.MD,P.YN,c.bv,$.h,en.G,tn]})}}return r})()},11244:(x,h,e)=>{e.d(h,{F:()=>P});var d=e(2978);let P=(()=>{class c{transform(l){return console.log(),`${l?.slice(0,1)?.toLocaleUpperCase()+l?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(b){return new(b||c)}}static{this.\u0275pipe=d.EJ8({name:"capitalize",type:c,pure:!0})}}return c})()},55988:(x,h,e)=>{e.d(h,{L:()=>f});var d=e(73308),P=e(26409),c=e(94934),M=e(45312),l=e(2978),b=e(82571),y=e(33607),k=e(77897);let f=(()=>{class v{constructor(n,s,u,_){this.http=n,this.commonSrv=s,this.baseUrlService=u,this.toastController=_,this.url=this.baseUrlService.getOrigin()+M.c.basePath+"items"}getItems(n){var s=this;return(0,d.A)(function*(){let{limit:u,categoryCode:_,offset:O,sku:w}=n;try{let m=new P.Nl;return u&&(m=m.append("limit",u)),_&&(m=m.append("category.code",_)),O&&(m=m.append("offset",O)),w&&(m=m.append("sku",w)),yield(0,c.s)(s.http.get(s.url,{params:m}))}catch(m){const S={message:s.commonSrv.getError("",m).message,color:"danger"};return yield s.commonSrv.showToast(S),m}})()}getMarketPlaceItem(n){var s=this;return(0,d.A)(function*(){try{return yield(0,c.s)(s.http.get(`${s.url}/${n}`))}catch(u){const O={message:s.commonSrv.getError("",u).message,color:"danger"};return yield s.commonSrv.showToast(O),u}})()}static{this.\u0275fac=function(s){return new(s||v)(l.KVO(P.Qq),l.KVO(b.h),l.KVO(y.K),l.KVO(k.K_))}}static{this.\u0275prov=l.jDH({token:v,factory:v.\u0275fac,providedIn:"root"})}}return v})()},97130:(x,h,e)=>{e.d(h,{I:()=>k});var d=e(73308),P=e(45312),c=e(2978),M=e(77897),l=e(49957),b=e(82571),y=e(14599);let k=(()=>{class f{constructor(g,n,s,u){this.platform=g,this.appVersion=n,this.commonService=s,this.storageService=u}isUpToDate(){var g=this;return(0,d.A)(function*(){let n;try{n=yield g.getAppVersion()}catch(s){"cordova_not_available"===s&&(n=g.platform.is("android")?P.c?.appVersionAndroid:P.c?.appVersionIos)}finally{return g.isLatestVersion(n)}})()}isLatestVersion(g){var n=this;return(0,d.A)(function*(){let s;console.log("appVersion:",g);try{s=yield n.getMinimalVersion()}catch{s=n.platform.is("android")?P.c?.appVersionAndroid:P.c?.appVersionIos}finally{return console.log(s,g),n.isNewerVersion(s,g)}})()}getMinimalVersion(){var g=this;return(0,d.A)(function*(){return g.currentPlatform=g.platform.is("android")?"android":"ios",yield g.commonService.getMinimalAppVersion(g.currentPlatform)})()}getAppVersion(){var g=this;return(0,d.A)(function*(){return yield g.appVersion.getVersionNumber()})()}isNewerVersion(g,n){const s=g?.split("."),u=n?.split("."),_=Math?.max(s?.length,u?.length);for(let O=0;O<_;O++){let w=parseInt(s[O]||"0",10),m=parseInt(u[O]||"0",10);if(m>w)return!0;if(m<w)return!1}return!0}static{this.\u0275fac=function(n){return new(n||f)(c.KVO(M.OD),c.KVO(l.U),c.KVO(b.h),c.KVO(y.n))}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})()}}]);