@use "../abstracts/common" as reset;

html {
    --res: calc(0.01 * 10vmin);
    --resH: calc(0.08 * 10vh);
    --resW: calc(0.08 * 10vw);
    --space-1: 3rem; //48px
    --space-2: calc(112.5 * var(--res));
    --space-3: calc(75 * var(--res));
    --space-4: calc(50 * var(--res));
    --space-5: calc(37.5 * var(--res));
    --main-title: calc(60 * var(--res));
}

body {
    font-size: clamp(0.625rem, 2vw + 1rem, 3rem);
    font-family: reset.$font-regular !important;
}

* {
    font-family: reset.$font-regular;
}

ion-title,
ion-button {
    letter-spacing: -0.01rem; //-0.165px;
    font-family: reset.$font-bold;
}

ion-title {
    position: relative;
    color:reset.$color-eight;
}

ion-text,
ion-label,
ion-input,
ion-select,
ion-accordion,
ion-accordion-group,
ion-toggle,
ion-item {
    font-family: reset.$font-regular;
    letter-spacing: -0.01rem;
    font-size: calc(40 * var(--res));
}

ion-text {
    line-height: 1.5em !important;
}

ion-content {
    #container {
        padding: reset.$padding-5;
    }
}

ion-button {
    --border-radius: calc(20 * var(--res));
    font-weight: 700;
    --padding-top: calc(31.25 * var(--res));
    --padding-bottom: calc(31.25 * var(--res));
    font-size: calc(36 * var(--res));
    text-transform: uppercase;
}

@supports (mask-image: paint(smooth-corners)) {
    ion-button.is-loaded {
        border-radius: 0;
        mask-image: paint(smooth-corners);
        --smooth-corners: 4.1;
    }
}

ion-chip {
    padding: reset.$padding-3;
    --background: transparent;

    ion-label {
        font-family:reset.$font-bold;
        font-weight: 600;
    }
}

.toolbar-background,
ion-buttons {
    transform: none;
}

.toolbar-background {
    background: transparent;
}

ion-toolbar {
    --min-height: none;
    --padding-start: 0px;
    --padding-end: 0px;
    --padding-bottom: 0px;
}

.item-label-floating,
.item-label-stacked {
    --min-height: 3rem !important;
}

ion-back-button {
    --icon-padding-end: 0.625rem;
}

.action-sheet-button {
    .action-sheet-button-inner {
        font-family: reset.$font-regular;
        text-transform: uppercase;
    }
}

ion-tab-bar {
    --background: transparent;
}

.fab-button-close-active {
    --background: var(--ion-color-primary) !important;
    ion-fab-button {
        --background: var(--ion-color-primary);
    }
}

.action-sheet-container {
    .action-sheet-group {
        .action-sheet-cancel {
            color: reset.$color-six;
        }
    }
}

.alert-button-role-annuler {
    color: reset.$color-six !important;
}

// .ion-color-success {
//     --ion-color-base: #E79F2A !important;
// }

ion-select::part(icon) {
    width: calc(50 * var(--res));
    opacity: 0.5;
}
.details {
    --width:14em;
  }
.modalDialog{
    --backdrop-opacity: 0.6;
    --box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.6);
    --color: white;
    --width: 300px;
    // --offset-x: 2em;
    --offset-y: -3em;
}

