"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8759],{88759:($,T,i)=>{i.r(T),i.d(T,{OrderDetailPageModule:()=>fe});var g=i(56610),_=i(37222),u=i(77897),O=i(77575),d=i(73308),f=i(88233),h=i(99987),I=i(58133),F=i(92533),R=i(91285),w=i(39893),a=i(92882),c=i(26409),e=i(2978),s=i(81559),p=i(57870),v=i(28863),P=i(82571),M=i(62049),C=i(54648),b=i(11244),S=i(94440),y=i(36384),E=i(74657);function j(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-buttons",6)(1,"ion-button",7),e.bIt("click",function(o){e.eBV(n);const m=e.XpG();return e.Njj(m.presentPopover(o))}),e.nrm(2,"ion-icon",8),e.k0s()()}}function A(t,l){1&t&&(e.j41(0,"ion-label",2),e.EFF(1),e.nI1(2,"capitalize"),e.nI1(3,"translate"),e.k0s()),2&t&&(e.R7$(1),e.SpI(" ",e.bMT(2,1,e.bMT(3,3,"order.detail.validated-at"))," :"))}function N(t,l){1&t&&(e.j41(0,"ion-label",2),e.EFF(1),e.nI1(2,"capitalize"),e.nI1(3,"translate"),e.k0s()),2&t&&(e.R7$(1),e.SpI(" ",e.bMT(2,1,e.bMT(3,3,"history-page.ref"))," :"))}function Y(t,l){if(1&t&&(e.j41(0,"ion-label",17),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&t){const n=e.XpG(2);e.R7$(1),e.SpI(" ",e.i5U(2,1,null==n.orderService||null==n.orderService.order||null==n.orderService.order.dates?null:n.orderService.order.dates.validated,"dd/MM/YY")," ")}}function G(t,l){if(1&t&&(e.j41(0,"ion-label",17),e.EFF(1),e.nI1(2,"capitalize"),e.k0s()),2&t){const n=e.XpG(2);e.R7$(1),e.SpI(" ",e.bMT(2,1,null==n.orderService||null==n.orderService.order?null:n.orderService.order.reference)," ")}}function z(t,l){if(1&t&&(e.j41(0,"div",36)(1,"div",30)(2,"div",37),e.nrm(3,"ion-img",38),e.k0s()(),e.j41(4,"div",39),e.EFF(5),e.k0s(),e.j41(6,"div",40),e.EFF(7),e.nI1(8,"number"),e.k0s(),e.j41(9,"div",41),e.EFF(10),e.nI1(11,"number"),e.k0s()()),2&t){const n=l.$implicit;e.R7$(5),e.Lme(" ",null==n?null:n.quantity," ",null==n||null==n.packaging?null:n.packaging.label," "),e.R7$(2),e.SpI(" ",e.i5U(8,4,n.unitPrice,"")," XAF "),e.R7$(3),e.SpI(" ",e.i5U(11,7,(null==n?null:n.unitPrice)*(null==n?null:n.quantity),"")," XAF")}}function V(t,l){if(1&t&&(e.j41(0,"div")(1,"ion-title",12),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",29),e.nrm(5,"div",30),e.j41(6,"div",31),e.EFF(7,"Quantit\xe9"),e.k0s(),e.j41(8,"div",32),e.EFF(9,"PU"),e.k0s(),e.j41(10,"div",33),e.EFF(11,"Montant"),e.k0s()(),e.j41(12,"div",34),e.DNE(13,z,12,10,"div",35),e.k0s()()),2&t){const n=e.XpG(2);e.R7$(2),e.JRh(e.bMT(3,2,"order.detail.product-list")),e.R7$(11),e.Y8G("ngForOf",null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart?null:n.orderService.order.cart.items)}}function U(t,l){if(1&t&&(e.j41(0,"div",42)(1,"ion-title",12),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",13)(5,"ion-label",43),e.EFF(6),e.k0s()()()),2&t){const n=e.XpG(2);e.R7$(2),e.SpI("",e.bMT(3,2,"order.detail.motif-rejet")," "),e.R7$(4),e.JRh(null==n.orderService||null==n.orderService.order?null:n.orderService.order.rejectReason)}}function L(t,l){if(1&t){const n=e.RV6();e.j41(0,"form",44)(1,"ion-title",12),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"ion-item")(5,"ion-label",45),e.EFF(6),e.nI1(7,"capitalize"),e.nI1(8,"translate"),e.k0s(),e.nrm(9,"ion-input",46),e.k0s(),e.j41(10,"ion-item")(11,"ion-label",45),e.EFF(12),e.nI1(13,"capitalize"),e.nI1(14,"translate"),e.k0s(),e.nrm(15,"ion-input",47),e.k0s(),e.j41(16,"ion-item")(17,"ion-label",45),e.EFF(18),e.nI1(19,"capitalize"),e.nI1(20,"translate"),e.k0s(),e.nrm(21,"ion-input",48),e.k0s(),e.j41(22,"ion-item")(23,"ion-label",45),e.EFF(24),e.nI1(25,"capitalize"),e.nI1(26,"translate"),e.k0s(),e.nrm(27,"ion-input",49),e.k0s(),e.j41(28,"ion-item")(29,"ion-label",45),e.EFF(30),e.nI1(31,"capitalize"),e.nI1(32,"translate"),e.k0s(),e.nrm(33,"ion-input",50),e.k0s(),e.j41(34,"div",51)(35,"ion-button",52),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.toggleEditMode())}),e.EFF(36),e.nI1(37,"capitalize"),e.nI1(38,"translate"),e.k0s(),e.j41(39,"ion-button",53),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.saveCarrier())}),e.EFF(40),e.nI1(41,"capitalize"),e.nI1(42,"translate"),e.k0s()()()}if(2&t){const n=e.XpG(2);e.Y8G("formGroup",n.carrierForm),e.R7$(2),e.JRh(e.bMT(3,9,"order-new-page.third-step.carrier-title")),e.R7$(4),e.JRh(e.bMT(7,11,e.bMT(8,13,"order-new-page.first-step.driver-name"))),e.R7$(6),e.JRh(e.bMT(13,15,e.bMT(14,17,"bottom-sheet-validation.tel"))),e.R7$(6),e.JRh(e.bMT(19,19,e.bMT(20,21,"order-new-page.first-step.driver-id"))),e.R7$(6),e.JRh(e.bMT(25,23,e.bMT(26,25,"order-new-page.first-step.driver-category"))),e.R7$(6),e.JRh(e.bMT(31,27,e.bMT(32,29,"order-new-page.first-step.driver-vehicle"))),e.R7$(6),e.JRh(e.bMT(37,31,e.bMT(38,33,"button.cancel"))),e.R7$(4),e.JRh(e.bMT(41,35,e.bMT(42,37,"profile.retailer.save-button-label")))}}function X(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",54),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.toggleEditMode())}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}2&t&&(e.R7$(1),e.SpI(" ",e.bMT(2,1,"button.save-edit-carrier")," "))}function B(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",55),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalConfirmValidation())}),e.j41(1,"ion-label"),e.EFF(2," VALIDER"),e.k0s()()}}function J(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",55),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalConfirmValidation())}),e.j41(1,"ion-label"),e.EFF(2," VALIDER"),e.k0s()()}}function H(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",56),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalRejectOrder())}),e.j41(1,"ion-label"),e.EFF(2," REJETER"),e.k0s()()}}function W(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",56),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalRejectOrder())}),e.j41(1,"ion-label"),e.EFF(2," REJETER"),e.k0s()()}}function K(t,l){1&t&&(e.j41(0,"ion-label",60),e.EFF(1),e.k0s()),2&t&&(e.R7$(1),e.SpI(" ","VALIDER LA COMMANDE"," "))}function Q(t,l){if(1&t){const n=e.RV6();e.j41(0,"div",57),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalConfirmValidation())}),e.j41(1,"ion-button",58),e.DNE(2,K,2,1,"ion-label",59),e.k0s()()}if(2&t){const n=e.XpG(2);e.R7$(2),e.Y8G("ngIf",!n.isLoading)}}const k=function(t){return[t]};function Z(t,l){if(1&t&&(e.j41(0,"div",9)(1,"div",10)(2,"div",11)(3,"ion-title",12),e.EFF(4),e.nI1(5,"capitalize"),e.nI1(6,"translate"),e.k0s(),e.j41(7,"div",13)(8,"div",14)(9,"ion-label",2),e.EFF(10),e.nI1(11,"capitalize"),e.nI1(12,"translate"),e.k0s(),e.j41(13,"ion-label",2),e.EFF(14),e.nI1(15,"capitalize"),e.nI1(16,"translate"),e.k0s(),e.j41(17,"ion-label",2),e.EFF(18),e.nI1(19,"capitalize"),e.nI1(20,"translate"),e.k0s(),e.j41(21,"ion-label",2),e.EFF(22),e.nI1(23,"capitalize"),e.nI1(24,"translate"),e.k0s(),e.DNE(25,A,4,5,"ion-label",15),e.DNE(26,N,4,5,"ion-label",15),e.k0s(),e.j41(27,"div",16)(28,"ion-label",17),e.EFF(29),e.k0s(),e.j41(30,"ion-label",17),e.EFF(31),e.nI1(32,"date"),e.j41(33,"span",17),e.EFF(34),e.nI1(35,"capitalize"),e.nI1(36,"translate"),e.nI1(37,"date"),e.k0s()(),e.j41(38,"ion-label",17),e.EFF(39),e.nI1(40,"capitalize"),e.nI1(41,"capitalize"),e.k0s(),e.j41(42,"ion-label",17),e.EFF(43),e.nI1(44,"capitalize"),e.nI1(45,"truncateString"),e.k0s(),e.DNE(46,Y,3,4,"ion-label",18),e.DNE(47,G,3,3,"ion-label",18),e.k0s()()(),e.DNE(48,V,14,4,"div",19),e.nrm(49,"app-purchase-summary",20),e.DNE(50,U,7,4,"div",21),e.j41(51,"div",22),e.DNE(52,L,43,39,"form",23),e.DNE(53,X,3,3,"ion-button",24),e.k0s(),e.j41(54,"div",22)(55,"ion-title",12),e.EFF(56),e.nI1(57,"translate"),e.k0s(),e.j41(58,"div",13)(59,"div",14)(60,"ion-label",2),e.EFF(61),e.nI1(62,"capitalize"),e.nI1(63,"translate"),e.k0s(),e.j41(64,"ion-label",2),e.EFF(65),e.nI1(66,"capitalize"),e.nI1(67,"translate"),e.k0s(),e.j41(68,"ion-label",2),e.EFF(69),e.nI1(70,"capitalize"),e.nI1(71,"translate"),e.k0s(),e.j41(72,"ion-label",2),e.EFF(73),e.nI1(74,"capitalize"),e.nI1(75,"translate"),e.k0s()(),e.j41(76,"div",16)(77,"ion-label",17),e.EFF(78),e.nI1(79,"transformEnumToString"),e.k0s(),e.j41(80,"ion-label",17),e.EFF(81),e.k0s(),e.j41(82,"ion-label",17),e.EFF(83),e.nI1(84,"date"),e.k0s(),e.j41(85,"ion-label",17),e.EFF(86),e.nI1(87,"number"),e.k0s()()()(),e.j41(88,"div",25),e.DNE(89,B,3,0,"ion-button",26),e.DNE(90,J,3,0,"ion-button",26),e.DNE(91,H,3,0,"ion-button",27),e.DNE(92,W,3,0,"ion-button",27),e.k0s(),e.DNE(93,Q,3,1,"div",28),e.k0s()()),2&t){const n=e.XpG();e.R7$(4),e.JRh(e.bMT(5,39,e.bMT(6,41,"order.detail.title"))),e.R7$(6),e.SpI("",e.bMT(11,43,e.bMT(12,45,"order.detail.reference"))," : "),e.R7$(4),e.SpI("",e.bMT(15,47,e.bMT(16,49,"order.detail.created-at"))," :"),e.R7$(4),e.SpI("",e.bMT(19,51,e.bMT(20,53,"order.detail.name"))," :"),e.R7$(4),e.SpI("",e.bMT(23,55,e.bMT(24,57,"order.detail.email"))," :"),e.R7$(3),e.Y8G("ngIf",null==n.orderService||null==n.orderService.order||null==n.orderService.order.dates?null:n.orderService.order.dates.validated),e.R7$(1),e.Y8G("ngIf",null==n.orderService||null==n.orderService.order?null:n.orderService.order.reference),e.R7$(3),e.JRh(null==n.orderService||null==n.orderService.order?null:n.orderService.order.appReference),e.R7$(2),e.SpI("",e.i5U(32,59,null==n.orderService||null==n.orderService.order?null:n.orderService.order.created_at,"dd/MM/YY")," \xa0 "),e.R7$(3),e.JRh(e.bMT(35,62,e.bMT(36,64,"preposition.to"))+e.i5U(37,66,null==n.orderService||null==n.orderService.order?null:n.orderService.order.created_at,"HH:mm:ss")),e.R7$(5),e.Lme("",e.bMT(40,69,(null==n.orderService||null==n.orderService.order||null==n.orderService.order.user?null:n.orderService.order.user.firstName)||"")," ",e.bMT(41,71,(null==n.orderService||null==n.orderService.order||null==n.orderService.order.user?null:n.orderService.order.user.lastName)||"N/A"),""),e.R7$(4),e.JRh(e.bMT(44,73,e.i5U(45,75,(null==n.orderService||null==n.orderService.order||null==n.orderService.order.user?null:n.orderService.order.user.email)||"N/A",18))),e.R7$(3),e.Y8G("ngIf",null==n.orderService||null==n.orderService.order||null==n.orderService.order.dates?null:n.orderService.order.dates.validated),e.R7$(1),e.Y8G("ngIf",null==n.orderService||null==n.orderService.order?null:n.orderService.order.reference),e.R7$(1),e.Y8G("ngIf",!1),e.R7$(1),e.Y8G("cart",null==n.orderService||null==n.orderService.order?null:n.orderService.order.cart)("orderPrice",null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart?null:n.orderService.order.cart.amount)("shipping",null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart?null:n.orderService.order.cart.shipping)("itemsLimited",null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart||null==n.orderService.order.cart.items?null:n.orderService.order.cart.items.slice(0,3))("shippingInfo",null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart||null==n.orderService.order.cart.amount?null:n.orderService.order.cart.amount.shippingInfo)("carrierOrderInformation",null==n.orderService||null==n.orderService.order?null:n.orderService.order.carrier),e.R7$(1),e.Y8G("ngIf",null==n.orderService||null==n.orderService.order?null:n.orderService.order.rejectReason),e.R7$(2),e.Y8G("ngIf",n.editMode),e.R7$(1),e.Y8G("ngIf",!n.editMode),e.R7$(3),e.JRh(e.bMT(57,78,"order.detail.delivery.title")),e.R7$(5),e.SpI("",e.bMT(62,80,e.bMT(63,82,"order.detail.delivery.mode"))," : "),e.R7$(4),e.SpI("",e.bMT(66,84,e.bMT(67,86,"order.detail.delivery.location"))," : "),e.R7$(4),e.SpI("",e.bMT(70,88,e.bMT(71,90,"order.detail.delivery.date"))," : "),e.R7$(4),e.SpI("",e.bMT(74,92,e.bMT(75,94,"order.detail.delivery.amount"))," : "),e.R7$(5),e.SpI("",e.i5U(79,96,null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart?null:n.orderService.order.cart.renderType,"renderType")," "),e.R7$(3),e.JRh(1==(null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart?null:n.orderService.order.cart.renderType)?null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart||null==n.orderService.order.cart.store?null:n.orderService.order.cart.store.label:(null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart||null==n.orderService.order.cart.shipping?null:n.orderService.order.cart.shipping.label)||"N/A"),e.R7$(2),e.SpI(" ",e.brH(84,99,null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart?null:n.orderService.order.cart.shipping.deliveryDate,"dd/MM/yyyy","fr")||"N/A"," "),e.R7$(3),e.SpI("",e.i5U(87,103,null==n.orderService||null==n.orderService.order||null==n.orderService.order.cart||null==n.orderService.order.cart.amount?null:n.orderService.order.cart.amount.shipping,"")," XAF"),e.R7$(3),e.Y8G("ngIf",!n.isValidate&&e.eq3(106,k,null==n.orderStatus?null:n.orderStatus.CREDIT_IN_VALIDATION).includes(null==n.orderService||null==n.orderService.order?null:n.orderService.order.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.DRH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE))),e.R7$(1),e.Y8G("ngIf",!n.isValidate&&e.eq3(108,k,n.orderStatus.CREATED).includes(null==n.orderService||null==n.orderService.order?null:n.orderService.order.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.CORDO_RH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE))),e.R7$(1),e.Y8G("ngIf",!n.isReject&&e.eq3(110,k,null==n.orderStatus?null:n.orderStatus.CREDIT_IN_VALIDATION).includes(null==n.orderService||null==n.orderService.order?null:n.orderService.order.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.DRH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE))),e.R7$(1),e.Y8G("ngIf",!n.isReject&&e.eq3(112,k,null==n.orderStatus?null:n.orderStatus.CREATED).includes(null==n.orderService||null==n.orderService.order?null:n.orderService.order.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.CORDO_RH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE))),e.R7$(1),e.Y8G("ngIf",(null==n.orderService||null==n.orderService.order?null:n.orderService.order.status)===n.orderStatus.CREDIT_IN_AWAIT_VALIDATION&&n.commonSrv.user.category===n.userCategory.Commercial)}}function q(t,l){1&t&&(e.j41(0,"ion-label",2),e.EFF(1),e.nI1(2,"capitalize"),e.nI1(3,"translate"),e.k0s()),2&t&&(e.R7$(1),e.SpI(" ",e.bMT(2,1,e.bMT(3,3,"order.detail.validated-at"))," :"))}function ee(t,l){1&t&&(e.j41(0,"ion-label",2),e.EFF(1),e.nI1(2,"capitalize"),e.nI1(3,"translate"),e.k0s()),2&t&&(e.R7$(1),e.SpI(" ",e.bMT(2,1,e.bMT(3,3,"history-page.ref"))," :"))}function ne(t,l){if(1&t&&(e.j41(0,"ion-label",17),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&t){const n=e.XpG(2);e.R7$(1),e.SpI(" ",e.i5U(2,1,null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.dates?null:n.orderRetailService.orderRetail.dates.validated,"dd/MM/YY")," ")}}function te(t,l){if(1&t&&(e.j41(0,"ion-label",17),e.EFF(1),e.nI1(2,"capitalize"),e.k0s()),2&t){const n=e.XpG(2);e.R7$(1),e.SpI(" ",e.bMT(2,1,null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.reference)," ")}}function re(t,l){if(1&t&&(e.j41(0,"div",36)(1,"div",30)(2,"div",37),e.nrm(3,"ion-img",61),e.k0s()(),e.j41(4,"div",39),e.EFF(5),e.k0s(),e.j41(6,"div",40),e.EFF(7),e.nI1(8,"number"),e.k0s(),e.j41(9,"div",41),e.EFF(10),e.nI1(11,"number"),e.k0s()()),2&t){const n=l.$implicit;e.R7$(3),e.FS9("src",null==n||null==n.product?null:n.product.image),e.R7$(2),e.Lme(" ",null==n?null:n.quantity," ",null==n||null==n.packaging?null:n.packaging.label," "),e.R7$(2),e.SpI(" ",e.i5U(8,5,n.unitPrice,"00")," XAF "),e.R7$(3),e.SpI(" ",e.i5U(11,8,(null==n?null:n.unitPrice)*(null==n?null:n.quantity),"00")," XAF")}}function oe(t,l){if(1&t&&(e.j41(0,"div")(1,"ion-title",12),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",29),e.nrm(5,"div",30),e.j41(6,"div",31),e.EFF(7,"Quantit\xe9"),e.k0s(),e.j41(8,"div",32),e.EFF(9,"PU"),e.k0s(),e.j41(10,"div",33),e.EFF(11,"Montant"),e.k0s()(),e.j41(12,"div",34),e.DNE(13,re,12,11,"div",35),e.k0s()()),2&t){const n=e.XpG(2);e.R7$(2),e.JRh(e.bMT(3,2,"order.detail.product-list")),e.R7$(11),e.Y8G("ngForOf",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.cart?null:n.orderRetailService.orderRetail.cart.items)}}function ie(t,l){if(1&t&&(e.j41(0,"div",42)(1,"ion-title",12),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",13)(5,"ion-label",43),e.EFF(6),e.k0s()()()),2&t){const n=e.XpG(2);e.R7$(2),e.SpI("",e.bMT(3,2,"order.detail.motif-rejet")," "),e.R7$(4),e.JRh(null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.validation?null:n.orderRetailService.orderRetail.validation.raison)}}function le(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",55),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalConfirmValidation())}),e.j41(1,"ion-label"),e.EFF(2," VALIDER"),e.k0s()()}}function ce(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",55),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalConfirmValidation())}),e.j41(1,"ion-label"),e.EFF(2," VALIDER"),e.k0s()()}}function ae(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",56),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalRejectOrder())}),e.j41(1,"ion-label"),e.EFF(2," REJETER"),e.k0s()()}}function de(t,l){if(1&t){const n=e.RV6();e.j41(0,"ion-button",56),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.showModalRejectOrder())}),e.j41(1,"ion-label"),e.EFF(2," REJETER"),e.k0s()()}}function se(t,l){if(1&t&&(e.j41(0,"div",9)(1,"div",10)(2,"div",11)(3,"ion-title",12),e.EFF(4),e.nI1(5,"capitalize"),e.nI1(6,"translate"),e.k0s(),e.j41(7,"div",13)(8,"div",14)(9,"ion-label",2),e.EFF(10),e.nI1(11,"capitalize"),e.nI1(12,"translate"),e.k0s(),e.j41(13,"ion-label",2),e.EFF(14),e.nI1(15,"capitalize"),e.nI1(16,"translate"),e.k0s(),e.j41(17,"ion-label",2),e.EFF(18),e.nI1(19,"capitalize"),e.nI1(20,"translate"),e.k0s(),e.j41(21,"ion-label",2),e.EFF(22),e.nI1(23,"capitalize"),e.nI1(24,"translate"),e.k0s(),e.DNE(25,q,4,5,"ion-label",15),e.DNE(26,ee,4,5,"ion-label",15),e.k0s(),e.j41(27,"div",16)(28,"ion-label",17),e.EFF(29),e.k0s(),e.j41(30,"ion-label",17),e.EFF(31),e.nI1(32,"date"),e.j41(33,"span",17),e.EFF(34),e.nI1(35,"capitalize"),e.nI1(36,"translate"),e.nI1(37,"date"),e.k0s()(),e.j41(38,"ion-label",17),e.EFF(39),e.nI1(40,"capitalize"),e.k0s(),e.j41(41,"ion-label",17),e.EFF(42),e.nI1(43,"capitalize"),e.nI1(44,"truncateString"),e.k0s(),e.DNE(45,ne,3,4,"ion-label",18),e.DNE(46,te,3,3,"ion-label",18),e.k0s()()(),e.DNE(47,oe,14,4,"div",19),e.nrm(48,"app-purchase-summary",20),e.DNE(49,ie,7,4,"div",21),e.j41(50,"div",25),e.DNE(51,le,3,0,"ion-button",26),e.DNE(52,ce,3,0,"ion-button",26),e.DNE(53,ae,3,0,"ion-button",27),e.DNE(54,de,3,0,"ion-button",27),e.k0s()()()),2&t){const n=e.XpG();e.R7$(4),e.JRh(e.bMT(5,26,e.bMT(6,28,"order.detail.title"))),e.R7$(6),e.SpI("",e.bMT(11,30,e.bMT(12,32,"order.detail.reference"))," : "),e.R7$(4),e.SpI("",e.bMT(15,34,e.bMT(16,36,"order.detail.created-at"))," :"),e.R7$(4),e.SpI("",e.bMT(19,38,e.bMT(20,40,"order.detail.name"))," :"),e.R7$(4),e.SpI("",e.bMT(23,42,e.bMT(24,44,"order.detail.email"))," :"),e.R7$(3),e.Y8G("ngIf",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.dates?null:n.orderRetailService.orderRetail.dates.validated),e.R7$(1),e.Y8G("ngIf",null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.reference),e.R7$(3),e.JRh(null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.appReference),e.R7$(2),e.SpI("",e.i5U(32,46,null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.created_at,"dd/MM/YY")," \xa0 "),e.R7$(3),e.JRh(e.bMT(35,49,e.bMT(36,51,"preposition.to"))+e.i5U(37,53,null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.created_at,"HH:mm:ss")),e.R7$(5),e.SpI("",e.bMT(40,56,(null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.user?null:n.orderRetailService.orderRetail.user.firstName)||"N/A")," "),e.R7$(3),e.JRh(e.bMT(43,58,e.i5U(44,60,(null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.user?null:n.orderRetailService.orderRetail.user.email)||"N/A",18))),e.R7$(3),e.Y8G("ngIf",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.dates?null:n.orderRetailService.orderRetail.dates.validated),e.R7$(1),e.Y8G("ngIf",null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.reference),e.R7$(1),e.Y8G("ngIf",!1),e.R7$(1),e.Y8G("cart",null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.cart)("orderPrice",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.cart||null==n.orderRetailService.orderRetail.cart.items?null:n.orderRetailService.orderRetail.cart.items.unitPrice)("shipping",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.cart?null:n.orderRetailService.orderRetail.cart.items)("itemsLimited",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.cart||null==n.orderRetailService.orderRetail.cart.items?null:n.orderRetailService.orderRetail.cart.items.slice(0,3))("shippingInfo",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.cart||null==n.orderRetailService.orderRetail.cart.items?null:n.orderRetailService.orderRetail.cart.items.unitPrice)("carrierOrderInformation",null==n.orderService||null==n.orderService.order?null:n.orderService.order.carrier),e.R7$(1),e.Y8G("ngIf",null==n.orderRetailService||null==n.orderRetailService.orderRetail||null==n.orderRetailService.orderRetail.validation?null:n.orderRetailService.orderRetail.validation.raison),e.R7$(2),e.Y8G("ngIf",!n.isValidate&&e.eq3(63,k,null==n.orderStatus?null:n.orderStatus.CREDIT_IN_VALIDATION).includes(null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.DRH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE))),e.R7$(1),e.Y8G("ngIf",!n.isValidate&&e.eq3(65,k,n.orderStatus.CREATED).includes(null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.CORDO_RH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE))),e.R7$(1),e.Y8G("ngIf",!n.isReject&&e.eq3(67,k,null==n.orderStatus?null:n.orderStatus.CREDIT_IN_VALIDATION).includes(null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.DRH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE))),e.R7$(1),e.Y8G("ngIf",!n.isReject&&e.eq3(69,k,null==n.orderStatus?null:n.orderStatus.CREATED).includes(null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.status)&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.employeeType)===n.employeeType.CORDO_RH&&(null==n.commonSrv||null==n.commonSrv.user?null:n.commonSrv.user.authorizations.includes(n.orderAction.VALIDATE)))}}function ue(t,l){1&t&&(e.j41(0,"ion-label",2),e.EFF(1),e.nI1(2,"capitalize"),e.nI1(3,"translate"),e.k0s()),2&t&&(e.R7$(1),e.SpI(" ",e.bMT(2,1,e.bMT(3,3,"order.detail.validated-at"))," :"))}function ge(t,l){if(1&t&&(e.j41(0,"ion-label",17),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&t){const n=e.XpG(2);e.R7$(1),e.SpI(" ",e.i5U(2,1,null==n.marketService||null==n.marketService.order||null==n.marketService.order.validation?null:n.marketService.order.validation.date,"dd/MM/YY")," ")}}function _e(t,l){if(1&t&&(e.j41(0,"ion-label",17),e.EFF(1),e.nI1(2,"capitalize"),e.k0s()),2&t){const n=e.XpG(2);e.R7$(1),e.SpI(" ",e.bMT(2,1,null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.reference)," ")}}function me(t,l){if(1&t&&(e.j41(0,"div")(1,"ion-title",12),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",29),e.nrm(5,"div",30),e.j41(6,"div",31),e.EFF(7,"Quantit\xe9"),e.k0s(),e.j41(8,"div",32),e.EFF(9,"PU"),e.k0s(),e.j41(10,"div",33),e.EFF(11,"Montant"),e.k0s()(),e.j41(12,"div",34)(13,"div",36)(14,"div",30)(15,"div",37),e.nrm(16,"ion-img",61),e.k0s()(),e.j41(17,"div",39),e.EFF(18),e.k0s(),e.j41(19,"div",40),e.EFF(20),e.nI1(21,"number"),e.k0s(),e.j41(22,"div",41),e.EFF(23),e.nI1(24,"number"),e.k0s()()()()),2&t){const n=e.XpG(2);e.R7$(2),e.JRh(e.bMT(3,6,"order.detail.product-list")),e.R7$(14),e.FS9("src",null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart||null==n.marketService.order.cart.items?null:n.marketService.order.cart.items.image),e.R7$(2),e.Lme(" ",1," ",null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart||null==n.marketService.order.cart.items?null:n.marketService.order.cart.items.name," "),e.R7$(2),e.SpI(" ",e.i5U(21,8,null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart?null:n.marketService.order.cart.items.price,"00")," POINTS "),e.R7$(3),e.SpI(" ",e.i5U(24,11,1*(null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart||null==n.marketService.order.cart.items?null:n.marketService.order.cart.items.price),"00")," POINTS")}}function pe(t,l){if(1&t&&(e.j41(0,"div")(1,"li",36)(2,"div",65)(3,"ion-label"),e.EFF(4),e.nI1(5,"capitalize"),e.k0s(),e.j41(6,"ion-label"),e.EFF(7),e.k0s()(),e.j41(8,"ion-label",64),e.EFF(9),e.k0s(),e.j41(10,"ion-label",64),e.EFF(11),e.nI1(12,"number"),e.k0s()(),e.nrm(13,"li",66),e.k0s()),2&t){const n=e.XpG(2);e.R7$(4),e.JRh(e.bMT(5,4,null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart||null==n.marketService.order.cart.items?null:n.marketService.order.cart.items.name)),e.R7$(3),e.JRh(null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart||null==n.marketService.order.cart.items?null:n.marketService.order.cart.items.name),e.R7$(2),e.JRh(null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart?null:n.marketService.order.cart.quantity),e.R7$(2),e.SpI("",e.brH(12,6,null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart||null==n.marketService.order.cart.items?null:n.marketService.order.cart.items.price,"","fr")," POINTS")}}function ve(t,l){if(1&t&&(e.j41(0,"div",42)(1,"ion-title",12),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",13)(5,"ion-label",43),e.EFF(6),e.k0s()()()),2&t){const n=e.XpG(2);e.R7$(2),e.SpI("",e.bMT(3,2,"order.detail.motif-rejet")," "),e.R7$(4),e.JRh(null==n.marketService||null==n.marketService.order||null==n.marketService.order.validation?null:n.marketService.order.validation.raison)}}function Me(t,l){if(1&t&&(e.j41(0,"div",9)(1,"div",10)(2,"div",11)(3,"ion-title",12),e.EFF(4),e.nI1(5,"capitalize"),e.nI1(6,"translate"),e.k0s(),e.j41(7,"div",13)(8,"div",14)(9,"ion-label",2),e.EFF(10),e.nI1(11,"capitalize"),e.nI1(12,"translate"),e.k0s(),e.j41(13,"ion-label",2),e.EFF(14),e.nI1(15,"capitalize"),e.nI1(16,"translate"),e.k0s(),e.j41(17,"ion-label",2),e.EFF(18),e.nI1(19,"capitalize"),e.nI1(20,"translate"),e.k0s(),e.j41(21,"ion-label",2),e.EFF(22),e.nI1(23,"capitalize"),e.nI1(24,"translate"),e.k0s(),e.DNE(25,ue,4,5,"ion-label",15),e.k0s(),e.j41(26,"div",16)(27,"ion-label",17),e.EFF(28),e.k0s(),e.j41(29,"ion-label",17),e.EFF(30),e.nI1(31,"date"),e.j41(32,"span",17),e.EFF(33),e.nI1(34,"capitalize"),e.nI1(35,"translate"),e.nI1(36,"date"),e.k0s()(),e.j41(37,"ion-label",17),e.EFF(38),e.nI1(39,"capitalize"),e.k0s(),e.j41(40,"ion-label",17),e.EFF(41),e.nI1(42,"capitalize"),e.nI1(43,"truncateString"),e.k0s(),e.DNE(44,ge,3,4,"ion-label",18),e.DNE(45,_e,3,3,"ion-label",18),e.k0s()()(),e.DNE(46,me,25,14,"div",19),e.j41(47,"ul",62)(48,"li",63)(49,"ion-label",64)(50,"strong"),e.EFF(51),e.nI1(52,"translate"),e.k0s()(),e.j41(53,"ion-label",64)(54,"strong"),e.EFF(55),e.nI1(56,"translate"),e.k0s()(),e.j41(57,"ion-label",64)(58,"strong"),e.EFF(59),e.nI1(60,"translate"),e.k0s()()(),e.DNE(61,pe,14,10,"div",19),e.k0s(),e.DNE(62,ve,7,4,"div",21),e.k0s()()),2&t){const n=e.XpG();e.R7$(4),e.JRh(e.bMT(5,19,e.bMT(6,21,"order.detail.title"))),e.R7$(6),e.SpI("",e.bMT(11,23,e.bMT(12,25,"order.detail.reference"))," : "),e.R7$(4),e.SpI("",e.bMT(15,27,e.bMT(16,29,"order.detail.created-at"))," :"),e.R7$(4),e.SpI("",e.bMT(19,31,e.bMT(20,33,"order.detail.name"))," :"),e.R7$(4),e.SpI("",e.bMT(23,35,e.bMT(24,37,"order.detail.email"))," :"),e.R7$(3),e.Y8G("ngIf",null==n.marketService||null==n.marketService.order||null==n.marketService.order.validation?null:n.marketService.order.validation.date),e.R7$(3),e.JRh(null==n.marketService||null==n.marketService.order?null:n.marketService.order.appReference),e.R7$(2),e.SpI("",e.i5U(31,39,null==n.marketService||null==n.marketService.order?null:n.marketService.order.created_at,"dd/MM/YY")," \xa0 "),e.R7$(3),e.JRh(e.bMT(34,42,e.bMT(35,44,"preposition.to"))+e.i5U(36,46,null==n.marketService||null==n.marketService.order?null:n.marketService.order.created_at,"HH:mm:ss")),e.R7$(5),e.SpI("",e.bMT(39,49,(null==n.marketService||null==n.marketService.order||null==n.marketService.order.user?null:n.marketService.order.user.firstName)||(null==n.marketService||null==n.marketService.order||null==n.marketService.order.user?null:n.marketService.order.user.lastName)||"N/A")," "),e.R7$(3),e.JRh(e.bMT(42,51,e.i5U(43,53,(null==n.marketService||null==n.marketService.order||null==n.marketService.order.user?null:n.marketService.order.user.email)||"N/A",18))),e.R7$(3),e.Y8G("ngIf",null==n.marketService||null==n.marketService.order||null==n.marketService.order.validation?null:n.marketService.order.validation.date),e.R7$(1),e.Y8G("ngIf",null==n.orderRetailService||null==n.orderRetailService.orderRetail?null:n.orderRetailService.orderRetail.reference),e.R7$(1),e.Y8G("ngIf",!1),e.R7$(5),e.JRh(e.bMT(52,56,"order-new-page.third-step.product")),e.R7$(4),e.JRh(e.bMT(56,58,"order-new-page.third-step.qte")),e.R7$(4),e.JRh(e.bMT(60,60,"order-new-page.third-step.unitPrice")),e.R7$(2),e.Y8G("ngIf",null==n.marketService||null==n.marketService.order||null==n.marketService.order.cart?null:n.marketService.order.cart.items),e.R7$(1),e.Y8G("ngIf",null==n.marketService||null==n.marketService.order||null==n.marketService.order.validation?null:n.marketService.order.validation.raison)}}const x=function(t,l,n){return[t,l,n]},Oe=[{path:"",component:(()=>{class t{constructor(n,r,o,m,D,be,Se,Re,ye,ke,Ie,Te){this.location=n,this.orderService=r,this.orderRetailService=o,this.marketService=m,this.alertController=D,this.popoverCtrl=be,this.commonSrv=Se,this.modalCtrl=Re,this.translateService=ye,this.router=ke,this.route=Ie,this.fb=Te,this.slideProductOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:10},this.isValidate=!1,this.isReject=!1,this.isLoading=!1,this.isPopoverOpen=!1,this.editMode=!1,this.carrierForm=new _.gE({}),this.orderStatus=f.Re,this.userCategory=I.s,this.orderAction=f.T3,this.employeeType=I.P,this.actionHandlers={modifier:()=>this.modifyOrder(),telecharger:()=>this.generatePurchaseOrder(this.orderService.order._id),annuler:()=>this.handleCancellation()},this.trackByFn=(Fe,je)=>Fe}ionViewWillEnter(){var n=this;return(0,d.A)(function*(){if(!n.orderService.order&&!n.route.snapshot.params.idOrder)return n.back();if(!n.orderService.order){const r=n.route.snapshot.params.idOrder;n.orderService.order=yield n.orderService.find(r),n.orderRetailService.orderRetail=yield n.orderRetailService.findOrderRetail(r),n.marketService.order=yield n.marketService.find(r)}n.initForm(),n.loadCarrierData()})()}back(){this.orderService.order=null,this.orderRetailService.orderRetail=null,this.marketService.order=null,this.location.back()}showModalRemovalDetail(n){this.orders=n,this.orderService.modalDetailRemoval=!0}presentPopover(n){var r=this;return(0,d.A)(function*(){const o=yield r.popoverCtrl.create({component:F.c,event:n,cssClass:"custom-popover",mode:"md",componentProps:{actions:[{label:r.getTranslatedText("Modifier la commande","Edit order"),action:"modifier"},{label:r.getTranslatedText("T\xe9l\xe9charger Bon de co..","Download PO"),action:"telecharger"},{label:r.getTranslatedText("Demande d'annulation","Cancellation request"),action:"annuler"}]}});yield o.present();const{data:m}=yield o.onWillDismiss();m&&r.handlePopoverAction(m)})()}handlePopoverAction(n){const r=this.actionHandlers[n];r&&r()}modifyOrder(){this.canModifyOrder()?this.navigateToOrderUpdate():this.commonSrv.showToast({message:this.translateService.currentLang===h.T.French?"Une erreur est survenue.":"An error occurred.",color:"warning"})}canModifyOrder(){return this.orderService.order.status!==f.Re.VALIDATED&&this.commonSrv.user.category!==I.s.Commercial}navigateToOrderUpdate(){this.router.navigate([`order/detail/${this.orderService.order._id}/order-update`]).then(r=>console.log(r?"Navigation r\xe9ussie":"\xc9chec de la navigation")).catch(r=>console.error("Erreur lors de la navigation",r))}cancelOrder(){this.commonSrv.showToast({message:this.translateService.currentLang===h.T.French?"Une erreur est survenue.":"An error occurred.",color:"warning"})}showModalConfirmValidation(){var n=this;return(0,d.A)(function*(){const r=yield n.modalCtrl.create({component:w.d,cssClass:"modal",initialBreakpoint:.4,breakpoints:[0,.75,.5],mode:"ios",componentProps:{statusToUpdate:f.Re.PAID,order:n.orderService?.order}});yield r.present();const{data:o}=yield r.onWillDismiss();o&&n.updateOrderStatus(o)})()}updateOrderStatus(n){this.orderService.order.status=n.status,this.orderService.order.reference=n.reference}showModalRejectOrder(){var n=this;return(0,d.A)(function*(){yield(yield n.alertController.create({header:n.getTranslatedText("Rejet de commande","Reject Order"),message:n.getTranslatedText("Vous \xeates sur le point de rejeter cette commande.\n Confirmez vous cette action ?","You are about to reject this order.\n Do you confirm this action ?"),cssClass:"custom-loading",buttons:[{text:n.getTranslatedText("Annuler","Cancel"),cssClass:"alert-button-cancel"},{text:n.getTranslatedText("Rejeter","Reject"),cssClass:"alert-button-confirm",handler:()=>n.rejectOrder()}]})).present()})()}rejectOrder(){var n=this;return(0,d.A)(function*(){yield n.orderService.RhRejectOrder(n.orderService.order),n.isReject=!0,n.isValidate=!0})()}showDetail(n){var r=this;return(0,d.A)(function*(){yield(yield r.modalCtrl.create({component:a.F,cssClass:"modal",initialBreakpoint:.35,breakpoints:[0,.75,.8,.35,.9,.95,1],mode:"ios",componentProps:{item:n,packaging:n?.packaging}})).present()})()}generatePurchaseOrder(n){var r=this;return(0,d.A)(function*(){if(n){r.isLoading=!0;try{const o=yield r.orderService.generatePurchaseOrder(n);o?.pdfPath?window.open(o.pdfPath,"_blank"):console.log("Chemin du PDF non disponible")}catch(o){console.error("Erreur lors de la g\xe9n\xe9ration du bon de commande:",o)}finally{setTimeout(()=>{r.isLoading=!1},1e3)}}else console.log("ID de commande non disponible")})()}getTranslatedText(n,r){return this.translateService.currentLang===h.T.French?n:r}handleCancellation(){var n=this;return(0,d.A)(function*(){if(!n.order?._id)return void(yield n.commonSrv.showToast({message:n.translateService.currentLang===h.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"}));let r=n.translateService.currentLang===h.T.French?"\xc0 la demande du client":"At the customers request";const o=yield n.modalCtrl.create({component:R.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:"Envoyer",cancelButton:"Annuler",text:"Demmande d annulation de la commande",message:"Renseigner le motif de la demande",isAnnulation:!0,handler:D=>{r=D||r,n.processCancellationRequest(r)}}}});yield o.present();const{role:m}=yield o.onWillDismiss();"confirm"===m&&n.showFinalConfirmation(r)})()}processCancellationRequest(n){var r=this;return(0,d.A)(function*(){if(r.order?._id)try{r.isLoading=!0,r.order.messageCancellation=n}catch{yield r.commonSrv.showToast({message:r.translateService.currentLang===h.T.French?"Une erreur est survenue lors du traitement de votre demande.":"An error occurred while processing your request.",color:"danger"})}finally{r.isLoading=!1}else yield r.commonSrv.showToast({message:r.translateService.currentLang===h.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"})})()}sendCancellationRequest(n){var r=this;return(0,d.A)(function*(){if(r.order?._id)try{r.isLoading=!0;const o={messageCancellation:n,cancellationStatus:f.q.ISSUE},m=yield r.orderService.cancellationOrder(r.order._id,o);if(m instanceof c.yz)return void(yield r.commonSrv.showToast({message:r.translateService.currentLang===h.T.French?`Une erreur est survenue. ${m?.message}`:`An error occurred. ${m?.message}`,color:"warning"}));r.order&&(r.order.messageCancellation=n,r.order.cancellationStatus=f.q.ISSUE),yield r.commonSrv.showToast({message:r.translateService.currentLang===h.T.French?`Demande d'annulation pour la commande ${r.order.appReference} envoy\xe9e avec succ\xe8s`:`Cancellation request for order ${r.order.appReference} sent successfully`,color:"success"}),r.router.navigateByUrl("order/history")}catch{yield r.commonSrv.showToast({message:r.translateService.currentLang===h.T.French?"Une erreur est survenue lors de l'envoi de la demande.":"An error occurred while sending the request.",color:"danger"})}finally{r.isLoading=!1}else yield r.commonSrv.showToast({message:r.translateService.currentLang===h.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"})})()}showFinalConfirmation(n){var r=this;return(0,d.A)(function*(){yield(yield r.modalCtrl.create({component:R.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:"Confirmer",cancelButton:"Annuler",text:"\xcates-vous s\xfbr de vouloir envoyer cette demande d'annulation ?",handler:()=>{r.sendCancellationRequest(n)}}}})).present()})()}initForm(){this.carrierForm=this.fb.group({name:[""],phone:[""],idCard:[""],vehicleCategory:[""],vehiclePlate:[""]})}toggleEditMode(){this.editMode=!this.editMode,this.editMode&&this.loadCarrierData()}loadCarrierData(){const n=this.orderService?.order?.carrier||{_id:"",name:"",phone:"",idCard:"",vehicleCategory:"",vehiclePlate:"",driverLicense:""};this.carrierForm.patchValue({name:n?.name,phone:n?.phone,idCard:n?.idCard,vehicleCategory:n?.vehicleCategory,vehiclePlate:n?.vehiclePlate})}saveCarrier(){var n=this;return(0,d.A)(function*(){const r=n.carrierForm.value,o=n.orderService?.order?._id;if(o)try{const m=yield n.orderService.updateCarrier(o,r);console.log("Transporteur mis \xe0 jour :",m),n.orderService.order.carrier=r,n.editMode=!1}catch(m){console.error("Erreur lors de la mise \xe0 jour du transporteur :",m)}else console.error("ID de commande introuvable")})()}static{this.\u0275fac=function(r){return new(r||t)(e.rXU(g.aZ),e.rXU(s.Q),e.rXU(p.l),e.rXU(v.j),e.rXU(u.hG),e.rXU(u.IE),e.rXU(P.h),e.rXU(u.W3),e.rXU(M.E),e.rXU(O.Ix),e.rXU(O.nX),e.rXU(_.ok))}}static{this.\u0275cmp=e.VBU({type:t,selectors:[["app-order-detail"]],decls:14,vars:18,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"color-primary"],["slot","end",4,"ngIf"],["id","container","class","order-history-page",4,"ngIf"],["slot","end"],[3,"click"],["name","ellipsis-vertical"],["id","container",1,"order-history-page"],[1,"scroller-container","historic-bill-detail-container","containers"],[1,"order-detail"],[1,"h3-title"],[1,"bill-info"],[1,"right-block"],["class","title",4,"ngIf"],[1,"left-block"],[1,"value"],["class","value",4,"ngIf"],[4,"ngIf"],[3,"cart","orderPrice","shipping","itemsLimited","shippingInfo","carrierOrderInformation"],["class","order-summary",4,"ngIf"],[1,"info-deliver"],[3,"formGroup",4,"ngIf"],["expand","full","class","btn-class",3,"click",4,"ngIf"],[1,"flex-dir"],["class","btn--meduim btn--upper","color","primary","expand","block",3,"click",4,"ngIf"],["class","btn--meduim btn--upper","color","danger","expand","block",3,"click",4,"ngIf"],["class","btn-validate",3,"click",4,"ngIf"],[1,"list-elt-header"],[1,"col","col-desc"],[1,"col","col-qdt","title"],[1,"col","col-price","title"],[1,"col","col-amount","title"],[1,"list-elt-contain"],["class"," list-elt ",4,"ngFor","ngForOf"],[1,"list-elt"],[1,"col-desc-elt"],["src","../../../assets/images/cimencam.png"],[1,"col","col-qdt"],[1,"col","col-price"],[1,"col","col-amount"],[1,"order-summary"],[1,"value","reason"],[3,"formGroup"],["position","floating",1,"title"],["formControlName","name",1,"value"],["formControlName","phone",1,"value"],["formControlName","idCard",1,"value"],["formControlName","vehicleCategory",1,"value"],["formControlName","vehiclePlate",1,"value"],[1,"button-container"],["expand","full","color","danger",3,"click"],["expand","full","color","primary",3,"click"],["expand","full",1,"btn-class",3,"click"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"],["color","danger","expand","block",1,"btn--meduim","btn--upper",3,"click"],[1,"btn-validate",3,"click"],["color","primary","expand","block",1,"btn--meduim","btn--upper"],["class","green-btn",4,"ngIf"],[1,"green-btn"],[3,"src"],[1,"product-list"],[1,"list-elt","head"],[1,"col"],[1,"col","product"],[1,"list-elt","line"]],template:function(r,o){1&r&&(e.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),e.bIt("click",function(){return o.back()}),e.k0s(),e.j41(3,"ion-title",2),e.EFF(4),e.nI1(5,"capitalize"),e.nI1(6,"translate"),e.j41(7,"span",3),e.EFF(8),e.k0s()(),e.DNE(9,j,3,0,"ion-buttons",4),e.k0s()(),e.j41(10,"ion-content"),e.DNE(11,Z,94,114,"div",5),e.DNE(12,se,55,71,"div",5),e.DNE(13,Me,63,62,"div",5),e.k0s()),2&r&&(e.R7$(4),e.SpI(" ",e.bMT(5,6,e.bMT(6,8,"order.detail.reference"))," "),e.R7$(4),e.SpI(" ",(null==o.orderService||null==o.orderService.order?null:o.orderService.order.customerReference)||(null==o.orderService||null==o.orderService.order?null:o.orderService.order.appReference)||(null==o.marketService||null==o.marketService.order?null:o.marketService.order.appReference)," "),e.R7$(1),e.Y8G("ngIf",!e.sMw(10,x,o.userCategory.Commercial,null==o.userCategory?null:o.userCategory.DonutAnimator,o.userCategory.Particular).includes(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)),e.R7$(2),e.Y8G("ngIf",e.sMw(14,x,o.userCategory.Commercial,o.userCategory.CompanyUser,null==o.userCategory?null:o.userCategory.DonutAnimator).includes(null==o.commonSrv||null==o.commonSrv.user?null:o.commonSrv.user.category)),e.R7$(1),e.Y8G("ngIf",o.commonSrv.user.category===o.userCategory.Particular&&!o.commonSrv.orderDetailNotification),e.R7$(1),e.Y8G("ngIf",o.commonSrv.user.category===o.userCategory.Particular&&o.commonSrv.orderDetailNotification))},dependencies:[g.Sq,g.bT,_.qT,_.BC,_.cb,u.Jm,u.QW,u.W9,u.eU,u.iq,u.KW,u.$w,u.uz,u.he,u.BC,u.ai,u.Gw,C.N,_.j4,_.JD,g.QX,g.vh,b.F,S.c,y.E,E.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background:#cfcfcf;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   app-purchase-summary[_ngcontent-%COMP%]{margin:1rem 0}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]{background-color:#e5e5e5;text-align:-webkit-center;width:100%;display:flex;align-items:center;align-items:baseline}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block;margin-bottom:.4em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]{background:var(--clr-default-400);background:#ffffff;border-radius:1rem;width:100%;padding-bottom:.2rem;margin-top:10px;box-sizing:content-box}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:1rem calc(41 * var(--res));border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{width:95%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .green-btn[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .flex-dir[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:95%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .flex-dir[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:48%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]{width:95%;padding:0 .5em;background:#ffffff;margin-bottom:1rem!important;border-radius:5px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .form--header[_ngcontent-%COMP%]{text-transform:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{box-shadow:0 4px 25px #00000017;border-radius:1px;overflow-x:hidden;overflow-y:auto;max-height:calc(25 * var(--resH))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{padding:5px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]{font-size:14px;display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{height:100%;padding:5px 0;display:flex;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:calc(100% - 300px)}@media only screen and (max-width: 530px){ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:80px}}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%]{width:80px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%]{width:105px;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%]{width:105px;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:ce}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%]{width:25px;height:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{margin-left:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]{height:30px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#000000a6;height:100%;text-align:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{text-align:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,0,0,.24)}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-bottom:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:0 calc(41 * var(--res)) 1rem;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .reason[_ngcontent-%COMP%]{margin-bottom:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]{width:91%;background:#ffffff;margin:1rem calc(41 * var(--res));border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px;padding:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-bottom:15px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de;font-size:59%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:10px;width:100%;margin-top:15px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]{margin-top:calc(50 * var(--res));margin-bottom:calc(50 * var(--res));flex:1}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100%;width:100%;padding:calc(25 * var(--res));display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .mWidth[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(8.3 * var(--resH));width:100px!important}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{padding:0;height:100%;box-shadow:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:space-between;gap:2px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-around}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(29 * var(--res))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   .sac[_ngcontent-%COMP%]{height:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:55%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .form--footer[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(40.7 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{overflow-y:auto;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]{gap:.2em;display:flex;align-items:center;align-items:flex-start;margin:0 calc(41 * var(--res));padding:calc(20 * var(--res)) calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#3c597d;text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap;font-size:calc(35 * var(--res));font-family:Mont SemiBold}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:first-child{width:41%}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:first-child   ion-label[_ngcontent-%COMP%]:first-child{text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap;font-family:Mont SemiBold;font-size:calc(35 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:nth-child(2){width:23%;min-width:27px}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]:last-child{flex-grow:1;text-align:end}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]{gap:5px;flex-wrap:wrap;display:flex;align-items:center;justify-content:flex-start}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:last-child{display:flex;align-items:center;font-size:calc(29.1 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:first-child{width:100%;text-align:start}@media only screen and (max-width: 530px){ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.product[_ngcontent-%COMP%]{gap:0}}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .equal[_ngcontent-%COMP%]{width:32%!important}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.line[_ngcontent-%COMP%]{padding:0;margin:0 calc(2 * 41 * var(--res));border-bottom:#ccdef1 solid 1px}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.head[_ngcontent-%COMP%]{background-color:#f1f8ff;border-radius:calc(12 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.head[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#000}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.footer[_ngcontent-%COMP%]{background-color:#f5f6f6;border-radius:calc(12 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .list-elt.footer[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#0b305c;font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]{margin-top:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:first-child   ion-text[_ngcontent-%COMP%]:first-child{color:#303950}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:first-child   ion-text[_ngcontent-%COMP%]:last-child{color:#103a5a}ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child   ion-text[_ngcontent-%COMP%]{color:#419cfb}"]})}}return t})()},{path:"order-removals-details",loadChildren:()=>i.e(148).then(i.bind(i,80148)).then(t=>t.OrderRemovalsDetailsPageModule)},{path:"order-update",loadChildren:()=>i.e(8607).then(i.bind(i,58607)).then(t=>t.OrderUpdatePageModule)}];let Pe=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=e.$C({type:t})}static{this.\u0275inj=e.G2t({imports:[O.iI.forChild(Oe),O.iI]})}}return t})();var Ce=i(93887),he=i(5083);let fe=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=e.$C({type:t})}static{this.\u0275inj=e.G2t({imports:[g.MD,_.YN,u.bv,Pe,Ce.G,E.h,he.NewOrderPageModule,_.X1]})}}return t})()},57870:($,T,i)=>{i.d(T,{l:()=>F});var g=i(73308),_=i(26409),u=i(45312),O=i(56610),d=i(94934),f=i(2978),h=i(82571),I=i(33607);let F=(()=>{class R{constructor(a,c,e){this.http=a,this.commonSrv=c,this.baseUrlService=e,this.url=this.baseUrlService.getOrigin()+u.c.basePath}getOrdersRetaill(a){var c=this;return(0,g.A)(function*(){try{let e=new _.Nl;const{num:s,status:p,offset:v,limit:P,startDate:M,endDate:C,customerReference:b,particularId:S}=a;return M&&C&&(e=e.append("startDate",new O.vh("fr").transform(M,"YYYY-MM-dd")),e=e.append("endDate",new O.vh("fr").transform(C,"YYYY-MM-dd"))),b&&(e=e.append("customerReference",b)),void 0!==v&&(e=e.append("offset",v)),P&&(e=e.append("limit",P)),p&&(e=e.append("status",p)),s&&(e=e.append("appReference",s)),S&&(e=e.append("particular",S)),yield(0,d.s)(c.http.get(`${c.url}order-supplier/particulars`,{params:e}))}catch(e){return e}})()}getAllOrderRetaillForCommercial(a){var c=this;return(0,g.A)(function*(){try{let e=new _.Nl;const{num:s,status:p,offset:v,limit:P,startDate:M,endDate:C,customerReference:b,region:S,userId:y}=a;return M&&C&&(e=e.append("startDate",new O.vh("fr").transform(M,"YYYY-MM-dd")),e=e.append("endDate",new O.vh("fr").transform(C,"YYYY-MM-dd"))),b&&(e=e.append("customerReference",b)),void 0!==v&&(e=e.append("offset",v)),P&&(e=e.append("limit",P)),p&&(e=e.append("status",p)),s&&(e=e.append("appReference",s)),S&&(e=e.append("distributors.address.region",S)),y&&(e=e.append("supplier.associatedCommercial._id",y)),yield(0,d.s)(c.http.get(`${c.url}order-supplier`,{params:e}))}catch(e){return e}})()}getOrderRetaillParticular(a){var c=this;return(0,g.A)(function*(){try{let e=new _.Nl;const{status:s,offset:p,limit:v,startDate:P,endDate:M,particularId:C}=a;return P&&M&&(e=e.append("startDate",new O.vh("fr").transform(P,"YYYY-MM-dd")).append("endDate",new O.vh("fr").transform(M,"YYYY-MM-dd"))),void 0!==p&&(e=e.append("offset",p)),v&&(e=e.append("limit",v)),s&&(e=e.append("status",s)),C&&(e=e.append("particularId",C)),yield(0,d.s)(c.http.get(`${c.url}order-supplier/particulars`,{params:e}))}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",e),e}})()}getScannerOrderParticular(a){var c=this;return(0,g.A)(function*(){try{let e=new _.Nl;const{status:s,offset:p,limit:v,startDate:P,endDate:M,particularId:C}=a;return P&&M&&(e=e.append("startDate",new O.vh("fr").transform(P,"YYYY-MM-dd")).append("endDate",new O.vh("fr").transform(M,"YYYY-MM-dd"))),void 0!==p&&(e=e.append("offset",p)),v&&(e=e.append("limit",v)),s&&(e=e.append("status",s)),yield(0,d.s)(c.http.get(`${c.url}scanner-data/particular/${C}`,{params:e}))}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",e),e}})()}validateRetailOrder(a){var c=this;return(0,g.A)(function*(){try{return yield(0,d.s)(c.http.patch(`${c.url}order-supplier/${a._id}`,{orders:a},{headers:{Authorization:`Bearer ${c.commonSrv?.user?.accessToken}`}}))}catch(e){return c.commonSrv.getError(e?.message,e)}})()}rejectRetailOrder(a,c){var e=this;return(0,g.A)(function*(){try{return yield(0,d.s)(e.http.patch(`${e.url}order-supplier/${a._id}/rejectOder`,{rejectMessage:c,user:e.commonSrv.user},{headers:{Authorization:`Bearer ${e.commonSrv?.user?.accessToken}`}}))}catch(s){return e.commonSrv.getError(s?.message,s)}})()}imageOrderValidated(a){var c=this;return(0,g.A)(function*(){try{return yield(0,d.s)(c.http.post(`${c.url}images`,a))}catch(e){return e}})()}findOrderRetail(a){var c=this;return(0,g.A)(function*(){try{return yield(0,d.s)(c.http.get(c.url+"scanner-data/"+a))}catch{return null}})()}getImageRetail(a){var c=this;return(0,g.A)(function*(){let e=new _.Nl;const{appRef:s}=a;return s&&(e=e.append("appRef",s)),yield(0,d.s)(c.http.get(`${c.url}images`,{params:e}))})()}getVolumeOrderByParticularClient(a){var c=this;return(0,g.A)(function*(){let e=new _.Nl;const{status:s=300,offset:p,limit:v,enable:P=!0,associatedCommercialId:M,startDate:C,endDate:b,customerName:S}=a;void 0!==p&&(e=e.append("offset",p)),v&&(e=e.append("limit",v)),s&&(e=e.append("status",s)),M&&(e=e.append("user.associatedCommercial._id",M)),e=e.append("enable",P),C&&b&&(e=e.append("startDate",new O.vh("fr").transform(C,"YYYY-MM-dd")),e=e.append("endDate",new O.vh("fr").transform(b,"YYYY-MM-dd"))),S&&(e=e.append("user.firstName",S));try{return yield(0,d.s)(c.http.get(`${c.url}scanner-data/volume-order-by-particular-client`,{params:e}))}catch(y){const j={message:c.commonSrv.getError("",y).message,color:"danger"};return yield c.commonSrv.showToast(j),y}})()}static{this.\u0275fac=function(c){return new(c||R)(f.KVO(_.Qq),f.KVO(h.h),f.KVO(I.K))}}static{this.\u0275prov=f.jDH({token:R,factory:R.\u0275fac,providedIn:"root"})}}return R})()}}]);