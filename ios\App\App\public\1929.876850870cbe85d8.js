"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1929],{14561:(P,w,l)=>{l.d(w,{a:()=>c,b:()=>i,p:()=>g});const g=(f,...y)=>console.warn(`[Ionic Warning]: ${f}`,...y),i=(f,...y)=>console.error(`[Ionic Error]: ${f}`,...y),c=(f,...y)=>console.error(`<${f.tagName.toLowerCase()}> must be used inside ${y.join(" or ")}.`)},33049:(P,w,l)=>{l.r(w),l.d(w,{ion_app:()=>O,ion_buttons:()=>H,ion_content:()=>L,ion_footer:()=>j,ion_header:()=>N,ion_router_outlet:()=>W,ion_title:()=>_,ion_toolbar:()=>F});var g=l(73308),i=l(29814),c=l(42673),f=l(46184),y=l(9404),k=l(35367),v=l(98717),b=l(36664),h=l(73888),m=l(9626),T=l(89345);l(94706);const O=class{constructor(t){(0,i.r)(this,t)}componentDidLoad(){var t=this;V((0,g.A)(function*(){const e=(0,c.a)(window,"hybrid");if(c.c.getBoolean("_testing")||l.e(3045).then(l.bind(l,83045)).then(n=>n.startTapClick(c.c)),c.c.getBoolean("statusTap",e)&&l.e(9622).then(l.bind(l,19622)).then(n=>n.startStatusTap()),c.c.getBoolean("inputShims",X())){const n=(0,c.a)(window,"ios")?"ios":"android";l.e(6545).then(l.bind(l,96545)).then(r=>r.startInputShims(c.c,n))}const o=yield Promise.resolve().then(l.bind(l,28226));c.c.getBoolean("hardwareBackButton",e)?o.startHardwareBackButton():o.blockHardwareBackButton(),typeof window<"u"&&l.e(2076).then(l.bind(l,81843)).then(n=>n.startKeyboardAssist(window)),l.e(2076).then(l.bind(l,7572)).then(n=>t.focusVisible=n.startFocusVisible())}))}setFocus(t){var e=this;return(0,g.A)(function*(){e.focusVisible&&e.focusVisible.setFocus(t)})()}render(){const t=(0,c.b)(this);return(0,i.h)(i.H,{class:{[t]:!0,"ion-page":!0,"force-statusbar-padding":c.c.getBoolean("_forceStatusbarPadding")}})}get el(){return(0,i.i)(this)}},X=()=>!!((0,c.a)(window,"ios")&&(0,c.a)(window,"mobile")||(0,c.a)(window,"android")&&(0,c.a)(window,"mobileweb")),V=t=>{"requestIdleCallback"in window?window.requestIdleCallback(t):setTimeout(t,32)};O.style="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}";const H=class{constructor(t){(0,i.r)(this,t),this.collapse=!1}render(){const t=(0,c.b)(this);return(0,i.h)(i.H,{class:{[t]:!0,"buttons-collapse":this.collapse}})}};H.style={ios:".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-start:5px;--padding-end:5px;margin-left:2px;margin-right:2px;height:32px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.sc-ion-buttons-ios-s ion-button{margin-left:unset;margin-right:unset;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px}}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;margin-right:0.3em;font-size:24px;line-height:0.67}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-right:unset;-webkit-margin-end:0.3em;margin-inline-end:0.3em}}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;margin-left:0.4em;font-size:24px;line-height:0.67}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:unset;-webkit-margin-start:0.4em;margin-inline-start:0.4em}}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:28px;line-height:0.67}",md:".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;--padding-start:8px;--padding-end:8px;--box-shadow:none;margin-left:2px;margin-right:2px;height:32px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.sc-ion-buttons-md-s ion-button{margin-left:unset;margin-right:unset;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px}}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:48px;height:48px}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;margin-right:0.3em;font-size:1.4em}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.sc-ion-buttons-md-s ion-icon[slot=start]{margin-right:unset;-webkit-margin-end:0.3em;margin-inline-end:0.3em}}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;margin-left:0.4em;font-size:1.4em}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:unset;-webkit-margin-start:0.4em;margin-inline-start:0.4em}}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}"};const L=class{constructor(t){(0,i.r)(this,t),this.ionScrollStart=(0,i.e)(this,"ionScrollStart",7),this.ionScroll=(0,i.e)(this,"ionScroll",7),this.ionScrollEnd=(0,i.e)(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.fullscreen=!1,this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}connectedCallback(){this.isMainContent=null===this.el.closest("ion-menu, ion-popover, ion-modal")}disconnectedCallback(){this.onScrollEnd()}onAppLoad(){this.resize()}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{null!==this.el.offsetParent&&this.resize()},100)}shouldForceOverscroll(){const{forceOverscroll:t}=this,e=(0,c.b)(this);return void 0===t?"ios"===e&&(0,c.a)("ios"):t}resize(){this.fullscreen?(0,i.f)(()=>this.readDimensions()):(0!==this.cTop||0!==this.cBottom)&&(this.cTop=this.cBottom=0,(0,i.j)(this))}readDimensions(){const t=q(this.el),e=Math.max(this.el.offsetTop,0),o=Math.max(t.offsetHeight-e-this.el.offsetHeight,0);(e!==this.cTop||o!==this.cBottom)&&(this.cTop=e,this.cBottom=o,(0,i.j)(this))}onScroll(t){const e=Date.now(),o=!this.isScrolling;this.lastScroll=e,o&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,(0,i.f)(n=>{this.queued=!1,this.detail.event=t,tt(this.detail,this.scrollEl,n,o),this.ionScroll.emit(this.detail)}))}getScrollElement(){var t=this;return(0,g.A)(function*(){return t.scrollEl||(yield new Promise(e=>(0,f.c)(t.el,e))),Promise.resolve(t.scrollEl)})()}getBackgroundElement(){var t=this;return(0,g.A)(function*(){return t.backgroundContentEl||(yield new Promise(e=>(0,f.c)(t.el,e))),Promise.resolve(t.backgroundContentEl)})()}scrollToTop(t=0){return this.scrollToPoint(void 0,0,t)}scrollToBottom(){var t=this;return(0,g.A)(function*(e=0){const o=yield t.getScrollElement();return t.scrollToPoint(void 0,o.scrollHeight-o.clientHeight,e)}).apply(this,arguments)}scrollByPoint(t,e,o){var n=this;return(0,g.A)(function*(){const r=yield n.getScrollElement();return n.scrollToPoint(t+r.scrollLeft,e+r.scrollTop,o)})()}scrollToPoint(t,e){var o=this;return(0,g.A)(function*(n,r,s=0){const a=yield o.getScrollElement();if(s<32)return null!=r&&(a.scrollTop=r),void(null!=n&&(a.scrollLeft=n));let d,p=0;const u=new Promise(E=>d=E),x=a.scrollTop,A=a.scrollLeft,z=null!=r?r-x:0,B=null!=n?n-A:0,G=E=>{const ut=Math.min(1,(E-p)/s)-1,I=Math.pow(ut,3)+1;0!==z&&(a.scrollTop=Math.floor(I*z+x)),0!==B&&(a.scrollLeft=Math.floor(I*B+A)),I<1?requestAnimationFrame(G):d()};return requestAnimationFrame(E=>{p=E,G(E)}),u}).apply(this,arguments)}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){const{isMainContent:t,scrollX:e,scrollY:o,el:n}=this,r=(0,y.i)(n)?"rtl":"ltr",s=(0,c.b)(this),a=this.shouldForceOverscroll(),d="ios"===s,p=t?"main":"div";return this.resize(),(0,i.h)(i.H,{class:(0,k.c)(this.color,{[s]:!0,"content-sizing":(0,k.h)("ion-popover",this.el),overscroll:a,[`content-${r}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},(0,i.h)("div",{ref:u=>this.backgroundContentEl=u,id:"background-content",part:"background"}),(0,i.h)(p,{class:{"inner-scroll":!0,"scroll-x":e,"scroll-y":o,overscroll:(e||o)&&a},ref:u=>this.scrollEl=u,onScroll:this.scrollEvents?u=>this.onScroll(u):void 0,part:"scroll"},(0,i.h)("slot",null)),d?(0,i.h)("div",{class:"transition-effect"},(0,i.h)("div",{class:"transition-cover"}),(0,i.h)("div",{class:"transition-shadow"})):null,(0,i.h)("slot",{name:"fixed"}))}get el(){return(0,i.i)(this)}},q=t=>{const e=t.closest("ion-tabs");return e||(t.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content")||(t=>{var e;return t.parentElement?t.parentElement:null!==(e=t.parentNode)&&void 0!==e&&e.host?t.parentNode.host:null})(t))},tt=(t,e,o,n)=>{const r=t.currentX,s=t.currentY,d=e.scrollLeft,p=e.scrollTop,u=o-t.currentTime;if(n&&(t.startTime=o,t.startX=d,t.startY=p,t.velocityX=t.velocityY=0),t.currentTime=o,t.currentX=t.scrollLeft=d,t.currentY=t.scrollTop=p,t.deltaX=d-t.startX,t.deltaY=p-t.startY,u>0&&u<100){const A=(p-s)/u;t.velocityX=(d-r)/u*.7+.3*t.velocityX,t.velocityY=.7*A+.3*t.velocityY}};L.style=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.outer-content){--background:var(--ion-color-step-50, #f2f2f2)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);padding-left:var(--padding-start);padding-right:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.inner-scroll{padding-left:unset;padding-right:unset;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end)}}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:10px;height:100%;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAgCAYAAAAIXrg4AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQ1IDc5LjE2MzQ5OSwgMjAxOC8wOC8xMy0xNjo0MDoyMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTkgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MTE3MDgzRkQ5QTkyMTFFOUEwNzQ5MkJFREE1NUY2MjQiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MTE3MDgzRkU5QTkyMTFFOUEwNzQ5MkJFREE1NUY2MjQiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoxMTcwODNGQjlBOTIxMUU5QTA3NDkyQkVEQTU1RjYyNCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxMTcwODNGQzlBOTIxMUU5QTA3NDkyQkVEQTU1RjYyNCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PmePEuQAAABNSURBVHjaYvz//z8DIxAwMDAwATGMhmFmPDQuOSZks0AMmoJBaQHjkPfB0Lfg/2gQjVow+HPy/yHvg9GiYjQfjMbBqAWjFgy/4hogwADYqwdzxy5BuwAAAABJRU5ErkJggg==);background-repeat:repeat-y;background-size:10px 16px}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}';const R=(t,e)=>{(0,i.f)(()=>{const d=(0,f.l)(0,1-(t.scrollTop-(t.scrollHeight-t.clientHeight-10))/10,1);(0,i.c)(()=>{e.style.setProperty("--opacity-scale",d.toString())})})},j=class{constructor(t){var e=this;(0,i.r)(this,t),this.keyboardCtrl=null,this.keyboardVisible=!1,this.translucent=!1,this.checkCollapsibleFooter=()=>{if("ios"!==(0,c.b)(this))return;const{collapse:n}=this,r="fade"===n;if(this.destroyCollapsibleFooter(),r){const s=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),a=s?(0,v.a)(s):null;if(!a)return void(0,v.p)(this.el);this.setupFadeFooter(a)}},this.setupFadeFooter=function(){var o=(0,g.A)(function*(n){const r=e.scrollEl=yield(0,v.g)(n);e.contentScrollCallback=()=>{R(r,e.el)},r.addEventListener("scroll",e.contentScrollCallback),R(r,e.el)});return function(n){return o.apply(this,arguments)}}()}componentDidLoad(){this.checkCollapsibleFooter()}componentDidUpdate(){this.checkCollapsibleFooter()}connectedCallback(){this.keyboardCtrl=(0,b.c)(t=>{this.keyboardVisible=t})}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}destroyCollapsibleFooter(){this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0)}render(){const{translucent:t,collapse:e}=this,o=(0,c.b)(this),n=this.el.closest("ion-tabs"),r=n?.querySelector(":scope > ion-tab-bar");return(0,i.h)(i.H,{role:"contentinfo",class:{[o]:!0,[`footer-${o}`]:!0,"footer-translucent":t,[`footer-translucent-${o}`]:t,"footer-toolbar-padding":!(this.keyboardVisible||r&&"bottom"===r.slot),[`footer-collapse-${e}`]:void 0!==e}},"ios"===o&&t&&(0,i.h)("div",{class:"footer-background"}),(0,i.h)("slot",null))}get el(){return(0,i.i)(this)}};j.style={ios:"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}",md:'ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md::before{left:0;top:-2px;bottom:auto;background-position:left 0 top 0;position:absolute;width:100%;height:2px;background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAHBAMAAADzDtBxAAAAD1BMVEUAAAAAAAAAAAAAAAAAAABPDueNAAAABXRSTlMUCS0gBIh/TXEAAAAaSURBVAjXYxCEAgY4UIICBmMogMsgFLtAAQCNSwXZKOdPxgAAAABJRU5ErkJggg==");background-repeat:repeat-x;content:""}[dir=rtl] .footer-md::before,:host-context([dir=rtl]) .footer-md::before{left:unset;right:unset;right:0}[dir=rtl] .footer-md::before,:host-context([dir=rtl]) .footer-md::before{background-position:right 0 top 0}.footer-md.ion-no-border::before{display:none}'};const U=t=>{const e=document.querySelector(`${t}.ion-cloned-element`);if(null!==e)return e;const o=document.createElement(t);return o.classList.add("ion-cloned-element"),o.style.setProperty("display","none"),document.body.appendChild(o),o},Y=t=>{if(!t)return;const e=t.querySelectorAll("ion-toolbar");return{el:t,toolbars:Array.from(e).map(o=>{const n=o.querySelector("ion-title");return{el:o,background:o.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:n,innerTitleEl:n?n.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(o.querySelectorAll("ion-buttons"))}})}},D=(t,e)=>{"fade"!==t.collapse&&(void 0===e?t.style.removeProperty("--opacity-scale"):t.style.setProperty("--opacity-scale",e.toString()))},C=(t,e=!0)=>{const o=t.el;e?(o.classList.remove("header-collapse-condense-inactive"),o.removeAttribute("aria-hidden")):(o.classList.add("header-collapse-condense-inactive"),o.setAttribute("aria-hidden","true"))},Z=(t,e,o)=>{(0,i.f)(()=>{const n=t.scrollTop,r=e.clientHeight,s=o?o.clientHeight:0;if(null!==o&&n<s)return e.style.setProperty("--opacity-scale","0"),void t.style.setProperty("clip-path",`inset(${r}px 0px 0px 0px)`);const p=(0,f.l)(0,(n-s)/10,1);(0,i.c)(()=>{t.style.removeProperty("clip-path"),e.style.setProperty("--opacity-scale",p.toString())})})},N=class{constructor(t){var e=this;(0,i.r)(this,t),this.inheritedAttributes={},this.translucent=!1,this.setupFadeHeader=function(){var o=(0,g.A)(function*(n,r){const s=e.scrollEl=yield(0,v.g)(n);e.contentScrollCallback=()=>{Z(e.scrollEl,e.el,r)},s.addEventListener("scroll",e.contentScrollCallback),Z(e.scrollEl,e.el,r)});return function(n,r){return o.apply(this,arguments)}}()}componentWillLoad(){this.inheritedAttributes=(0,f.i)(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){var t=this;return(0,g.A)(function*(){if("ios"!==(0,c.b)(t))return;const{collapse:o}=t,n="condense"===o,r="fade"===o;if(t.destroyCollapsibleHeader(),n){const s=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),a=s?(0,v.a)(s):null;(0,i.c)(()=>{U("ion-title").size="large",U("ion-back-button")}),yield t.setupCondenseHeader(a,s)}else if(r){const s=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),a=s?(0,v.a)(s):null;if(!a)return void(0,v.p)(t.el);const d=a.querySelector('ion-header[collapse="condense"]');yield t.setupFadeHeader(a,d)}})()}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(t,e){var o=this;return(0,g.A)(function*(){if(!t||!e)return void(0,v.p)(o.el);if(typeof IntersectionObserver>"u")return;o.scrollEl=yield(0,v.g)(t);const n=e.querySelectorAll("ion-header");if(o.collapsibleMainHeader=Array.from(n).find(d=>"condense"!==d.collapse),!o.collapsibleMainHeader)return;const r=Y(o.collapsibleMainHeader),s=Y(o.el);r&&s&&(C(r,!1),D(r.el,0),o.intersectionObserver=new IntersectionObserver(d=>{((t,e,o,n)=>{(0,i.c)(()=>{const r=n.scrollTop;((t,e,o)=>{if(!t[0].isIntersecting)return;const n=t[0].intersectionRatio>.9||o<=0?0:100*(1-t[0].intersectionRatio)/75;D(e.el,1===n?void 0:n)})(t,e,r);const s=t[0],a=s.intersectionRect,d=a.width*a.height,u=0===d&&0==s.rootBounds.width*s.rootBounds.height,x=Math.abs(a.left-s.boundingClientRect.left),A=Math.abs(a.right-s.boundingClientRect.right);u||d>0&&(x>=5||A>=5)||(s.isIntersecting?(C(e,!1),C(o)):(0===a.x&&0===a.y||0!==a.width&&0!==a.height)&&r>0&&(C(e),C(o,!1),D(e.el)))})})(d,r,s,o.scrollEl)},{root:t,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),o.intersectionObserver.observe(s.toolbars[s.toolbars.length-1].el),o.contentScrollCallback=()=>{((t,e,o)=>{(0,i.f)(()=>{const r=(0,f.l)(1,1+-t.scrollTop/500,1.1);null===o.querySelector("ion-refresher.refresher-native")&&(0,i.c)(()=>{((t=[],e=1,o=!1)=>{t.forEach(n=>{const r=n.ionTitleEl,s=n.innerTitleEl;!r||"large"!==r.size||(s.style.transition=o?"all 0.2s ease-in-out":"",s.style.transform=`scale3d(${e}, ${e}, 1)`)})})(e.toolbars,r)})})})(o.scrollEl,s,t)},o.scrollEl.addEventListener("scroll",o.contentScrollCallback),(0,i.c)(()=>{void 0!==o.collapsibleMainHeader&&o.collapsibleMainHeader.classList.add("header-collapse-main")}))})()}render(){const{translucent:t,inheritedAttributes:e}=this,o=(0,c.b)(this),n=this.collapse||"none",r=(0,k.h)("ion-menu",this.el)?"none":"banner";return(0,i.h)(i.H,Object.assign({role:r,class:{[o]:!0,[`header-${o}`]:!0,"header-translucent":this.translucent,[`header-collapse-${n}`]:!0,[`header-translucent-${o}`]:this.translucent}},e),"ios"===o&&t&&(0,i.h)("div",{class:"header-background"}),(0,i.h)("slot",null))}get el(){return(0,i.i)(this)}};N.style={ios:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:7px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{height:48px;padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}",md:'ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md::after{left:0;bottom:-5px;background-position:left 0 top -2px;position:absolute;width:100%;height:5px;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAHBAMAAADzDtBxAAAAD1BMVEUAAAAAAAAAAAAAAAAAAABPDueNAAAABXRSTlMUCS0gBIh/TXEAAAAaSURBVAjXYxCEAgY4UIICBmMogMsgFLtAAQCNSwXZKOdPxgAAAABJRU5ErkJggg==);background-repeat:repeat-x;content:""}[dir=rtl] .header-md::after,:host-context([dir=rtl]) .header-md::after{left:unset;right:unset;right:0}[dir=rtl] .header-md::after,:host-context([dir=rtl]) .header-md::after{background-position:right 0 top -2px}.header-collapse-condense{display:none}.header-md.ion-no-border::after{display:none}'};const W=class{constructor(t){(0,i.r)(this,t),this.ionNavWillLoad=(0,i.e)(this,"ionNavWillLoad",7),this.ionNavWillChange=(0,i.e)(this,"ionNavWillChange",3),this.ionNavDidChange=(0,i.e)(this,"ionNavDidChange",3),this.gestureOrAnimationInProgress=!1,this.mode=(0,c.b)(this),this.animated=!0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(void 0!==this.swipeHandler)}connectedCallback(){var t=this;return(0,g.A)(function*(){t.gesture=(yield l.e(2076).then(l.bind(l,16481))).createSwipeBackGesture(t.el,()=>!t.gestureOrAnimationInProgress&&!!t.swipeHandler&&t.swipeHandler.canStart(),()=>(t.gestureOrAnimationInProgress=!0,void(t.swipeHandler&&t.swipeHandler.onStart())),o=>{var n;return null===(n=t.ani)||void 0===n?void 0:n.progressStep(o)},(o,n,r)=>{if(t.ani){t.ani.onFinish(()=>{t.gestureOrAnimationInProgress=!1,t.swipeHandler&&t.swipeHandler.onEnd(o)},{oneTimeCallback:!0});let s=o?-.001:.001;o?s+=(0,h.g)([0,0],[.32,.72],[0,1],[1,1],n)[0]:(t.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),s+=(0,h.g)([0,0],[1,0],[.68,.28],[1,1],n)[0]),t.ani.progressEnd(o?1:0,s,r)}else t.gestureOrAnimationInProgress=!1}),t.swipeHandlerChanged()})()}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(t,e,o){var n=this;return(0,g.A)(function*(){const r=yield n.lock();let s=!1;try{s=yield n.transition(t,e,o)}catch(a){console.error(a)}return r(),s})()}setRouteId(t,e,o,n){var r=this;return(0,g.A)(function*(){return{changed:yield r.setRoot(t,e,{duration:"root"===o?0:void 0,direction:"back"===o?"back":"forward",animationBuilder:n}),element:r.activeEl}})()}getRouteId(){var t=this;return(0,g.A)(function*(){const e=t.activeEl;return e?{id:e.tagName,element:e,params:t.activeParams}:void 0})()}setRoot(t,e,o){var n=this;return(0,g.A)(function*(){if(n.activeComponent===t&&(0,f.s)(e,n.activeParams))return!1;const r=n.activeEl,s=yield(0,m.a)(n.delegate,n.el,t,["ion-page","ion-page-invisible"],e);return n.activeComponent=t,n.activeEl=s,n.activeParams=e,yield n.commit(s,r,o),yield(0,m.d)(n.delegate,r),!0})()}transition(t,e){var o=this;return(0,g.A)(function*(n,r,s={}){if(r===n)return!1;o.ionNavWillChange.emit();const{el:a,mode:d}=o,p=o.animated&&c.c.getBoolean("animated",!0),u=s.animationBuilder||o.animation||c.c.get("navAnimation");return yield(0,T.t)(Object.assign(Object.assign({mode:d,animated:p,enteringEl:n,leavingEl:r,baseEl:a,progressCallback:s.progressAnimation?x=>{void 0===x||o.gestureOrAnimationInProgress?o.ani=x:(o.gestureOrAnimationInProgress=!0,x.onFinish(()=>{o.gestureOrAnimationInProgress=!1,o.swipeHandler&&o.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),x.progressEnd(0,0,0))}:void 0},s),{animationBuilder:u})),o.ionNavDidChange.emit(),!0}).apply(this,arguments)}lock(){var t=this;return(0,g.A)(function*(){const e=t.waitPromise;let o;return t.waitPromise=new Promise(n=>o=n),void 0!==e&&(yield e),o})()}render(){return(0,i.h)("slot",null)}get el(){return(0,i.i)(this)}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}};W.style=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;overflow:hidden;z-index:0}";const _=class{constructor(t){(0,i.r)(this,t),this.ionStyle=(0,i.e)(this,"ionStyle",7)}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){const t=this.getSize();this.ionStyle.emit({[`title-${t}`]:!0})}getSize(){return void 0!==this.size?this.size:"default"}render(){const t=(0,c.b)(this),e=this.getSize();return(0,i.h)(i.H,{class:(0,k.c)(this.color,{[t]:!0,[`title-${e}`]:!0,"title-rtl":"rtl"===document.dir})},(0,i.h)("div",{class:"toolbar-title"},(0,i.h)("slot",null)))}get el(){return(0,i.i)(this)}static get watchers(){return{size:["sizeChanged"]}}};_.style={ios:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{left:0;top:0;padding-left:90px;padding-right:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:17px;font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host-context([dir=rtl]){left:unset;right:unset;right:0}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host{padding-left:unset;padding-right:unset;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px}}:host(.title-small){padding-left:9px;padding-right:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:13px;font-weight:normal}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host(.title-small){padding-left:unset;padding-right:unset;-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px}}:host(.title-large){padding-left:16px;padding-right:16px;padding-top:0;padding-bottom:0;-webkit-transform-origin:left center;transform-origin:left center;bottom:0;-ms-flex-align:end;align-items:flex-end;min-width:100%;padding-bottom:6px;font-size:34px;font-weight:700;text-align:start}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host(.title-large){padding-left:unset;padding-right:unset;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}",md:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{padding-left:20px;padding-right:20px;padding-top:0;padding-bottom:0;font-size:20px;font-weight:500;letter-spacing:0.0125em}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host{padding-left:unset;padding-right:unset;-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px}}:host(.title-small){width:100%;height:100%;font-size:15px;font-weight:normal}"};const F=class{constructor(t){(0,i.r)(this,t),this.childrenStyles=new Map}componentWillLoad(){const t=Array.from(this.el.querySelectorAll("ion-buttons")),e=t.find(r=>"start"===r.slot);e&&e.classList.add("buttons-first-slot");const o=t.reverse(),n=o.find(r=>"end"===r.slot)||o.find(r=>"primary"===r.slot)||o.find(r=>"secondary"===r.slot);n&&n.classList.add("buttons-last-slot")}childrenStyle(t){t.stopPropagation();const e=t.target.tagName,o=t.detail,n={},r=this.childrenStyles.get(e)||{};let s=!1;Object.keys(o).forEach(a=>{const d=`toolbar-${a}`,p=o[a];p!==r[d]&&(s=!0),p&&(n[d]=!0)}),s&&(this.childrenStyles.set(e,n),(0,i.j)(this))}render(){const t=(0,c.b)(this),e={};return this.childrenStyles.forEach(o=>{Object.assign(e,o)}),(0,i.h)(i.H,{class:Object.assign(Object.assign({},e),(0,k.c)(this.color,{[t]:!0,"in-toolbar":(0,k.h)("ion-toolbar",this.el)}))},(0,i.h)("div",{class:"toolbar-background"}),(0,i.h)("div",{class:"toolbar-container"},(0,i.h)("slot",{name:"start"}),(0,i.h)("slot",{name:"secondary"}),(0,i.h)("div",{class:"toolbar-content"},(0,i.h)("slot",null)),(0,i.h)("slot",{name:"primary"}),(0,i.h)("slot",{name:"end"})))}get el(){return(0,i.i)(this)}};F.style={ios:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;padding-left:var(--ion-safe-area-left);padding-right:var(--ion-safe-area-right);display:block;position:relative;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host{padding-left:unset;padding-right:unset;-webkit-padding-start:var(--ion-safe-area-left);padding-inline-start:var(--ion-safe-area-left);-webkit-padding-end:var(--ion-safe-area-right);padding-inline-end:var(--ion-safe-area-right)}}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{padding-left:var(--padding-start);padding-right:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.toolbar-container{padding-left:unset;padding-right:unset;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end)}}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, #f7f7f7));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.2))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}:host(.toolbar-searchbar) ::slotted(ion-back-button){height:38px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",md:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;padding-left:var(--ion-safe-area-left);padding-right:var(--ion-safe-area-right);display:block;position:relative;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host{padding-left:unset;padding-right:unset;-webkit-padding-start:var(--ion-safe-area-left);padding-inline-start:var(--ion-safe-area-left);-webkit-padding-end:var(--ion-safe-area-right);padding-inline-end:var(--ion-safe-area-right)}}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{padding-left:var(--padding-start);padding-right:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.toolbar-container{padding-left:unset;padding-right:unset;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end)}}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, #c1c4cd)));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){margin-left:4px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){::slotted(.buttons-first-slot){margin-left:unset;-webkit-margin-start:4px;margin-inline-start:4px}}::slotted(.buttons-last-slot){margin-right:4px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){::slotted(.buttons-last-slot){margin-right:unset;-webkit-margin-end:4px;margin-inline-end:4px}}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}"}},35367:(P,w,l)=>{l.d(w,{c:()=>c,g:()=>y,h:()=>i,o:()=>v});var g=l(73308);const i=(b,h)=>null!==h.closest(b),c=(b,h)=>"string"==typeof b&&b.length>0?Object.assign({"ion-color":!0,[`ion-color-${b}`]:!0},h):h,y=b=>{const h={};return(b=>void 0!==b?(Array.isArray(b)?b:b.split(" ")).filter(m=>null!=m).map(m=>m.trim()).filter(m=>""!==m):[])(b).forEach(m=>h[m]=!0),h},k=/^[a-z][a-z0-9+\-.]*:/,v=function(){var b=(0,g.A)(function*(h,m,T,M){if(null!=h&&"#"!==h[0]&&!k.test(h)){const S=document.querySelector("ion-router");if(S)return m?.preventDefault(),S.push(h,T,M)}return!1});return function(m,T,M,S){return b.apply(this,arguments)}}()}}]);