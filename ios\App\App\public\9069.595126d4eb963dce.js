"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9069],{92391:(N,v,o)=>{o.d(v,{Nn:()=>P,gH:()=>d});var d=function(e){return e.CalendarEvent="CALENDAR_EVENT",e.ContactInfo="CONTACT_INFO",e.DriversLicense="DRIVERS_LICENSE",e.Email="EMAIL",e.Geo="GEO",e.Isbn="ISBN",e.Phone="PHONE",e.Product="PRODUCT",e.Sms="SMS",e.Text="TEXT",e.Url="URL",e.Wifi="WIFI",e.Unknown="UNKNOWN",e}(d||{}),P=function(e){return e.Front="FRONT",e.Back="BACK",e}(P||{})},94761:(N,v,o)=>{o.d(v,{vi:()=>c});var g=o(22126);o(92391);const c=(0,g.F3)("BarcodeScanner",{web:()=>o.e(5499).then(o.bind(o,85499)).then(P=>new P.BarcodeScannerWeb)})},99069:(N,v,o)=>{o.r(v),o.d(v,{ReportingOrderParticularAssociatedPageModule:()=>w});var g=o(56610),d=o(37222),c=o(77897),P=o(77575),M=o(73308),h=o(99987),t=o(2978),y=o(82571),A=o(62049),e=o(74657);function T(O,I){if(1&O){const s=t.RV6();t.j41(0,"ion-datetime",19,20),t.bIt("ionChange",function(){t.eBV(s);const f=t.sdS(1);return t.Njj(f.confirm(!0))}),t.k0s()}}function l(O,I){if(1&O){const s=t.RV6();t.j41(0,"ion-datetime",21,20),t.bIt("ionChange",function(){t.eBV(s);const f=t.sdS(1);return t.Njj(f.confirm(!0))}),t.k0s()}}let r=(()=>{class O{constructor(s,m,f){this.commonSrv=s,this.modalCtrl=m,this.translateService=f,this.filterForm=new d.gE({startDate:new d.MJ(""),endDate:new d.MJ(""),customerName:new d.MJ("")})}ngOnInit(){this.filterForm.reset(),this.filterForm.patchValue({startDate:this.filterData?.startDate,endDate:this.filterData?.endDate,customerName:this.filterData?.customerName}),this.filterForm?.updateValueAndValidity()}closeModal(){var s=this;return(0,M.A)(function*(){const m=s.filterForm.value;if(m.startDate>m.endDate)return yield s.commonSrv.showToast({message:s.translateService.currentLang===h.T.French?"Veuillez renseigner une date de d\xe9but inf\xe9rieure \xe0 celle de la date de fin":"Please enter a start date less than the end date",color:"warning"});s.modalCtrl.dismiss({...s.filterForm.value})})()}static{this.\u0275fac=function(m){return new(m||O)(t.rXU(y.h),t.rXU(c.W3),t.rXU(A.E))}}static{this.\u0275cmp=t.VBU({type:O,selectors:[["app-filter-particular-associated"]],inputs:{filterData:"filterData"},decls:43,vars:25,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end"],["src","assets/icons/close.svg",3,"click"],["id","content",3,"formGroup"],[1,"form-group","padding-horizontal"],[1,"title"],[1,"mbottom"],[1,"date-time"],["slot","start","src","assets/icons/calendar.svg",1,"ion-text-center"],["id","date","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","date","size","cover","side","top","alignment","center"],["id","enddate","placeholder","JJ/MM/AAAA",1,"ion-text-start",3,"value"],["trigger","enddate","size","cover","side","top","alignment","center"],["position","floating",1,"title"],["formControlName","customerName","clearInput",""],[1,"add-qty-btn","mbottom","padding-horizontal",3,"click"],["color","primary",1,"btn","add-line",3,"disabled"],["name","search-sharp"],["formControlName","startDate","presentation","date","locale","fr-FR",3,"ionChange"],["popoverDatetime",""],["formControlName","endDate","presentation","date","locale","fr-FR",3,"ionChange"]],template:function(m,f){1&m&&(t.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2)(4,"ion-img",3),t.bIt("click",function(){return f.closeModal()}),t.k0s()(),t.j41(5,"ion-label"),t.EFF(6),t.nI1(7,"translate"),t.k0s()()(),t.j41(8,"ion-content")(9,"form",4)(10,"div",5)(11,"ion-label",6),t.EFF(12),t.nI1(13,"translate"),t.k0s(),t.j41(14,"div",7)(15,"ion-item",8),t.nrm(16,"ion-icon",9)(17,"ion-input",10),t.nI1(18,"date"),t.j41(19,"ion-popover",11),t.DNE(20,T,2,0,"ng-template"),t.k0s()()()(),t.j41(21,"div",5)(22,"ion-label",6),t.EFF(23),t.nI1(24,"translate"),t.k0s(),t.j41(25,"div",7)(26,"ion-item",8),t.nrm(27,"ion-icon",9)(28,"ion-input",12),t.nI1(29,"date"),t.j41(30,"ion-popover",13),t.DNE(31,l,2,0,"ng-template"),t.k0s()()()(),t.j41(32,"div",5)(33,"ion-item",7)(34,"ion-label",14),t.EFF(35),t.nI1(36,"translate"),t.k0s(),t.nrm(37,"ion-input",15),t.k0s()(),t.j41(38,"div",16),t.bIt("click",function(){return f.closeModal()}),t.j41(39,"ion-button",17),t.nrm(40,"ion-icon",18),t.EFF(41),t.nI1(42,"translate"),t.k0s()()()()()),2&m&&(t.R7$(6),t.JRh(t.bMT(7,9,"history-page.title-filter")),t.R7$(3),t.Y8G("formGroup",f.filterForm),t.R7$(3),t.SpI("",t.bMT(13,11,"history-page.startDate")," "),t.R7$(5),t.FS9("value",t.i5U(18,13,f.filterForm.get("startDate").value,"dd/MM/yyyy")),t.R7$(6),t.SpI("",t.bMT(24,16,"history-page.endDate")," "),t.R7$(5),t.FS9("value",t.i5U(29,18,f.filterForm.get("endDate").value,"dd/MM/yyyy")),t.R7$(7),t.JRh(t.bMT(36,21,"reporting.clientName")),t.R7$(4),t.Y8G("disabled",f.filterForm.invalid),t.R7$(2),t.SpI(" ",t.bMT(42,23,"history-page.btn-filter")," "))},dependencies:[d.qT,d.BC,d.cb,c.Jm,c.W9,c.A9,c.eU,c.iq,c.KW,c.$w,c.uz,c.he,c.Zx,c.ai,c.CF,c.Je,c.Gw,d.j4,d.JD,g.vh,e.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;color:#000;display:flex;flex-direction:column;height:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{font-family:Mont Regular;--padding-start: 0;font-size:calc(36 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: var(--space-4) !important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .unit[_ngcontent-%COMP%]{margin-right:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .tonne[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{margin-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .full-input[_ngcontent-%COMP%]{margin:.5em 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .schedule-truck[_ngcontent-%COMP%]   .btn-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-schedule[_ngcontent-%COMP%]{margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res));width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]   .ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#1e1e1e!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fMedium[_ngcontent-%COMP%]{font-family:Mont Light}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{padding:calc(25 * var(--res)) 0 calc(75 * var(--res)) 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: white}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(30 * var(--res));line-height:initial}ion-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));width:100%}"]})}}return O})();var n=o(68896),a=o(32327),i=o(57870),_=o(71604),p=o(11244);function u(O,I){if(1&O&&(t.qex(0),t.j41(1,"ion-row")(2,"ion-col",16),t.EFF(3),t.k0s(),t.j41(4,"ion-col",16),t.EFF(5),t.k0s()(),t.bVm()),2&O){const s=I.$implicit;t.R7$(3),t.JRh((null==s?null:s.firstName)||"N/A"),t.R7$(2),t.JRh((null==s?null:s.totalQuantity)||"N/A")}}const b=[{path:"",component:(()=>{class O{constructor(s,m,f,R,Y){this.scannerService=s,this.commonSrv=m,this.modalCtrl=f,this.reportingSrv=R,this.retailService=Y,this.evolutionOrderParticularClients=[],this.totalProducts=0,this.filterData={startDate:null,endDate:null,customerName:""}}ngOnInit(){var s=this;return(0,M.A)(function*(){yield s.getVolumeOrderByParticularClient(),s.geTotalProduct()})()}loadData(){var s=this;return(0,M.A)(function*(){s.getVolumeOrderByParticularClient()})()}doRefresh(s){var m=this;return(0,M.A)(function*(){m.filterData={customerName:"",startDate:null,endDate:null},yield m.loadData(),s.target.complete()})()}showFilter(){var s=this;return(0,M.A)(function*(){const m=yield s.modalCtrl.create({component:r,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.5],mode:"ios",componentProps:{filterData:{...s.filterData},isShow:!0}});m.present();const f=(yield m.onWillDismiss()).data;s.filterData={...s.filterData,...f},s.filterData&&(yield s.getVolumeOrderByParticularClient())})()}getVolumeOrderByParticularClient(){var s=this;return(0,M.A)(function*(){const m={associatedCommercialId:s.commonSrv?.user?._id,...s.filterData},f=yield s.scannerService.getVolumeOrderByParticularClient(m),R=yield s.retailService.getVolumeOrderByParticularClient(m);s.evolutionOrderParticularClients.push(...f,...R)})()}geTotalProduct(){this.totalProducts=this.evolutionOrderParticularClients?.reduce((s,m)=>s+(m.totalQuantity||0),0)}static{this.\u0275fac=function(m){return new(m||O)(t.rXU(n.I),t.rXU(y.h),t.rXU(c.W3),t.rXU(a.k),t.rXU(i.l))}}static{this.\u0275cmp=t.VBU({type:O,selectors:[["app-reporting-order-particular-associated"]],decls:38,vars:31,consts:[[1,"header"],["mode","md","slot","start","text",""],["defaultHref","./"],[1,"title-and-filter-container"],[1,"title"],["slot","fixed"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText","ionRefresh"],[1,"ion-content"],[1,"p-card"],[1,"p-card-header"],[1,"filter"],["src","/assets/icons/refresh-green.png",1,"img-refresh",3,"click"],["expand","block","fill","outline","color","primary","size","small",3,"click"],["slot","start","src","/assets/icons/filtre-icon.png",1,"img-filter"],["size","5",1,"row-title"],[4,"ngFor","ngForOf"],["size","5"]],template:function(m,f){1&m&&(t.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-buttons",1),t.nrm(3,"ion-back-button",2),t.k0s(),t.j41(4,"div",3)(5,"h1",4),t.EFF(6),t.nI1(7,"capitalize"),t.nI1(8,"translate"),t.k0s()()()(),t.j41(9,"ion-content")(10,"ion-refresher",5)(11,"ion-refresher-content",6),t.bIt("ionRefresh",function(Y){return f.doRefresh(Y)}),t.nI1(12,"translate"),t.nI1(13,"translate"),t.k0s()(),t.j41(14,"div",7)(15,"p-card",8)(16,"div",9)(17,"div"),t.EFF(18),t.k0s(),t.j41(19,"div",10)(20,"ion-img",11),t.bIt("click",function(Y){return f.doRefresh(Y)}),t.k0s(),t.j41(21,"ion-button",12),t.bIt("click",function(){return f.showFilter()}),t.nrm(22,"ion-img",13),t.EFF(23),t.nI1(24,"capitalize"),t.nI1(25,"translate"),t.k0s()()()(),t.j41(26,"ion-grid")(27,"ion-row")(28,"ion-col",14),t.EFF(29),t.nI1(30,"titlecase"),t.nI1(31,"translate"),t.k0s(),t.j41(32,"ion-col",14),t.EFF(33),t.nI1(34,"titlecase"),t.nI1(35,"translate"),t.nI1(36,"translate"),t.k0s()(),t.DNE(37,u,6,2,"ng-container",15),t.k0s()()()),2&m&&(t.R7$(6),t.JRh(t.bMT(7,9,t.bMT(8,11,"reporting.top-products"))),t.R7$(5),t.FS9("pullingText",t.bMT(12,13,"refresher.pull")),t.Mz_("refreshingText","",t.bMT(13,15,"refresher.refreshing"),"..."),t.R7$(7),t.SpI("Total : ",f.totalProducts," "),t.R7$(5),t.SpI(" ",t.bMT(24,17,t.bMT(25,19,"reporting.btn-filter"))," "),t.R7$(6),t.JRh(t.bMT(30,21,t.bMT(31,23,"reporting.clientName"))),t.R7$(4),t.Lme("",t.bMT(34,25,t.bMT(35,27,"reporting.quantity")),"(",t.bMT(36,29,"reporting.bags"),")50KG"),t.R7$(4),t.Y8G("ngForOf",f.evolutionOrderParticularClients))},dependencies:[g.Sq,c.el,c.Jm,c.QW,c.hU,c.W9,c.lO,c.eU,c.KW,c.To,c.Ki,c.ln,c.ai,c.tY,_.Z,g.PV,p.F,e.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--border-color: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{width:4rem}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-size:calc(55 * var(--res));text-align:start;flex-grow:1}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title-and-filter-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]{display:flex;align-items:flex-start;flex-direction:column;height:4em;padding:1em var(--container-padding);background-position:center;background-size:cover;border-radius:16px;width:100%;background-image:url(/assets/images/Total-card.png)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]{display:flex;flex-direction:column;font-size:1em;font-family:Mont Regular}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%]{font-size:var(--fs-32-px);color:var(--clr-tertiary-600);font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:var(--fs-15-px);color:var(--clr-tertiary-600)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-size:var(--fs-10-px)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .icon-home[_ngcontent-%COMP%]{height:48px;width:48px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]{padding:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{width:100%;margin:20px .5em;background-color:#d9d9d9}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:0 0 calc(25 * var(--res)) 0;font-family:var(--mont-semibold);font-size:calc(42 * var(--res));color:#143c5d}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{display:flex;align-items:center;gap:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   .img-refresh[_ngcontent-%COMP%]{width:calc(50 * var(--res));cursor:pointer}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{text-transform:capitalize;--padding-top: calc(35 * var(--res));--padding-bottom: calc(35 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .img-filter[_ngcontent-%COMP%]{margin-right:calc(15 * var(--res));width:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-chart[_ngcontent-%COMP%]{height:12rem}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{background:#CEE5FE;padding:1em;border-radius:10px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{padding-bottom:10px;margin-bottom:10px;border-bottom:1px dashed var(--ion-color-dark);display:flex;flex-direction:row;justify-content:space-between;font-size:calc(30 * var(--res));text-align:center}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-title[_ngcontent-%COMP%]{font-weight:500;background:hwb(211 53% 4%);border-radius:10px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .semi[_ngcontent-%COMP%]{font-family:Mont SemiBold!important}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-data[_ngcontent-%COMP%]{color:#143c5d;font-size:12px;font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .row-header[_ngcontent-%COMP%]{border-style:none!important}ion-content[_ngcontent-%COMP%]   .product-contain[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between}"]})}}return O})()}];let D=(()=>{class O{static{this.\u0275fac=function(m){return new(m||O)}}static{this.\u0275mod=t.$C({type:O})}static{this.\u0275inj=t.G2t({imports:[P.iI.forChild(b),P.iI]})}}return O})();var E=o(93887),x=o(60787);let w=(()=>{class O{static{this.\u0275fac=function(m){return new(m||O)}}static{this.\u0275mod=t.$C({type:O})}static{this.\u0275inj=t.G2t({imports:[g.MD,d.YN,c.bv,x.F,_.D,D,E.G,e.h,d.X1]})}}return O})()},32327:(N,v,o)=>{o.d(v,{k:()=>A});var g=o(73308),d=o(56610),c=o(26409),P=o(94934),M=o(45312),h=o(2978),t=o(33607),y=o(82571);let A=(()=>{class e{constructor(l,r,n){this.http=l,this.baseUrl=r,this.commonSrv=n,this.base_url=`${this.baseUrl.getOrigin()}${M.c.basePath}`}geTotalQuantityOrder(l){var r=this;return(0,g.A)(function*(){try{let n=new c.Nl;const{status:a,startDate:i,endDate:_,enable:p=!0,userId:u}=l;return a&&(n=n.append("status",a)),i&&_&&(n=n.append("startDate",new d.vh("fr").transform(i,"YYYY-MM-dd")),n=n.append("endDate",new d.vh("fr").transform(_,"YYYY-MM-dd"))),n=n.append("enable",p),u&&(n=n.append("user",u)),yield(0,P.s)(r.http.get(`${r.base_url}reporting/total-quantity-evolution`,{params:n}))}catch(n){return r.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}geTotalProduct(l){var r=this;return(0,g.A)(function*(){try{let n=new c.Nl;const{status:a,startDate:i,endDate:_,product:p,enable:u=!0,userId:C}=l;return i&&_&&(n=n.append("startDate",new d.vh("fr").transform(i,"YYYY-MM-dd")),n=n.append("endDate",new d.vh("fr").transform(_,"YYYY-MM-dd"))),a&&(n=n.append("status",a)),p&&(n=n.append("items.product._id",p)),C&&(n=n.append("user",C)),n=n.append("enable",u),yield(0,P.s)(r.http.get(`${r.base_url}reporting/product-quantity`,{params:n}))}catch(n){return yield r.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}getTotalEarnPoint(l){var r=this;return(0,g.A)(function*(){try{let n=new c.Nl;const{startDate:a,endDate:i,region:_,enable:p=!0}=l;return _&&(n=n.append("region",_)),a&&i&&(n=n.append("startDate",new d.vh("fr").transform(a,"YYYY-MM-dd")),n=n.append("endDate",new d.vh("fr").transform(i,"YYYY-MM-dd"))),n=n.append("enable",p),yield(0,P.s)(r.http.get(`${r.base_url}reporting/points-evolution`,{params:n}))}catch(n){return yield r.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}getDistributorVolume(l){var r=this;return(0,g.A)(function*(){try{let n=new c.Nl;const{status:a,startDate:i,endDate:_,product:p,enable:u=!0,userId:C}=l;return a&&(n=n.append("status",a)),p&&(n=n.append("items.product._id",p)),C&&(n=n.append("user",C)),i&&_&&(n=n.append("startDate",new d.vh("fr").transform(i,"YYYY-MM-dd")),n=n.append("endDate",new d.vh("fr").transform(_,"YYYY-MM-dd"))),n=n.append("enable",u),yield(0,P.s)(r.http.get(`${r.base_url}reporting/distributor-volume`,{params:n}))}catch(n){return yield r.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}getTotalPoint(l){var r=this;return(0,g.A)(function*(){try{let n=new c.Nl;const{status:a,product:p,enable:u=!0,region:C}=l;return a&&(n=n.append("status",a)),p&&(n=n.append("items.product._id",p)),C&&(n=n.append("payment.mode.id",C)),n=n.append("enable",u),yield(0,P.s)(r.http.get(`${r.base_url}reporting/retail-points`,{params:n}))}catch(n){return yield r.commonSrv.showToast({message:n.error.message,color:"danger"}),n}})()}static{this.\u0275fac=function(r){return new(r||e)(h.KVO(c.Qq),h.KVO(t.K),h.KVO(y.h))}}static{this.\u0275prov=h.jDH({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})()},11244:(N,v,o)=>{o.d(v,{F:()=>d});var g=o(2978);let d=(()=>{class c{transform(M){return console.log(),`${M?.slice(0,1)?.toLocaleUpperCase()+M?.slice(1)?.toLocaleLowerCase()}`}static{this.\u0275fac=function(h){return new(h||c)}}static{this.\u0275pipe=g.EJ8({name:"capitalize",type:c,pure:!0})}}return c})()},57870:(N,v,o)=>{o.d(v,{l:()=>A});var g=o(73308),d=o(26409),c=o(45312),P=o(56610),M=o(94934),h=o(2978),t=o(82571),y=o(33607);let A=(()=>{class e{constructor(l,r,n){this.http=l,this.commonSrv=r,this.baseUrlService=n,this.url=this.baseUrlService.getOrigin()+c.c.basePath}getOrdersRetaill(l){var r=this;return(0,g.A)(function*(){try{let n=new d.Nl;const{num:a,status:i,offset:_,limit:p,startDate:u,endDate:C,customerReference:b,particularId:D}=l;return u&&C&&(n=n.append("startDate",new P.vh("fr").transform(u,"YYYY-MM-dd")),n=n.append("endDate",new P.vh("fr").transform(C,"YYYY-MM-dd"))),b&&(n=n.append("customerReference",b)),void 0!==_&&(n=n.append("offset",_)),p&&(n=n.append("limit",p)),i&&(n=n.append("status",i)),a&&(n=n.append("appReference",a)),D&&(n=n.append("particular",D)),yield(0,M.s)(r.http.get(`${r.url}order-supplier/particulars`,{params:n}))}catch(n){return n}})()}getAllOrderRetaillForCommercial(l){var r=this;return(0,g.A)(function*(){try{let n=new d.Nl;const{num:a,status:i,offset:_,limit:p,startDate:u,endDate:C,customerReference:b,region:D,userId:E}=l;return u&&C&&(n=n.append("startDate",new P.vh("fr").transform(u,"YYYY-MM-dd")),n=n.append("endDate",new P.vh("fr").transform(C,"YYYY-MM-dd"))),b&&(n=n.append("customerReference",b)),void 0!==_&&(n=n.append("offset",_)),p&&(n=n.append("limit",p)),i&&(n=n.append("status",i)),a&&(n=n.append("appReference",a)),D&&(n=n.append("distributors.address.region",D)),E&&(n=n.append("supplier.associatedCommercial._id",E)),yield(0,M.s)(r.http.get(`${r.url}order-supplier`,{params:n}))}catch(n){return n}})()}getOrderRetaillParticular(l){var r=this;return(0,g.A)(function*(){try{let n=new d.Nl;const{status:a,offset:i,limit:_,startDate:p,endDate:u,particularId:C}=l;return p&&u&&(n=n.append("startDate",new P.vh("fr").transform(p,"YYYY-MM-dd")).append("endDate",new P.vh("fr").transform(u,"YYYY-MM-dd"))),void 0!==i&&(n=n.append("offset",i)),_&&(n=n.append("limit",_)),a&&(n=n.append("status",a)),C&&(n=n.append("particularId",C)),yield(0,M.s)(r.http.get(`${r.url}order-supplier/particulars`,{params:n}))}catch(n){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",n),n}})()}getScannerOrderParticular(l){var r=this;return(0,g.A)(function*(){try{let n=new d.Nl;const{status:a,offset:i,limit:_,startDate:p,endDate:u,particularId:C}=l;return p&&u&&(n=n.append("startDate",new P.vh("fr").transform(p,"YYYY-MM-dd")).append("endDate",new P.vh("fr").transform(u,"YYYY-MM-dd"))),void 0!==i&&(n=n.append("offset",i)),_&&(n=n.append("limit",_)),a&&(n=n.append("status",a)),yield(0,M.s)(r.http.get(`${r.url}scanner-data/particular/${C}`,{params:n}))}catch(n){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",n),n}})()}validateRetailOrder(l){var r=this;return(0,g.A)(function*(){try{return yield(0,M.s)(r.http.patch(`${r.url}order-supplier/${l._id}`,{orders:l},{headers:{Authorization:`Bearer ${r.commonSrv?.user?.accessToken}`}}))}catch(n){return r.commonSrv.getError(n?.message,n)}})()}rejectRetailOrder(l,r){var n=this;return(0,g.A)(function*(){try{return yield(0,M.s)(n.http.patch(`${n.url}order-supplier/${l._id}/rejectOder`,{rejectMessage:r,user:n.commonSrv.user},{headers:{Authorization:`Bearer ${n.commonSrv?.user?.accessToken}`}}))}catch(a){return n.commonSrv.getError(a?.message,a)}})()}imageOrderValidated(l){var r=this;return(0,g.A)(function*(){try{return yield(0,M.s)(r.http.post(`${r.url}images`,l))}catch(n){return n}})()}findOrderRetail(l){var r=this;return(0,g.A)(function*(){try{return yield(0,M.s)(r.http.get(r.url+"scanner-data/"+l))}catch{return null}})()}getImageRetail(l){var r=this;return(0,g.A)(function*(){let n=new d.Nl;const{appRef:a}=l;return a&&(n=n.append("appRef",a)),yield(0,M.s)(r.http.get(`${r.url}images`,{params:n}))})()}getVolumeOrderByParticularClient(l){var r=this;return(0,g.A)(function*(){let n=new d.Nl;const{status:a=300,offset:i,limit:_,enable:p=!0,associatedCommercialId:u,startDate:C,endDate:b,customerName:D}=l;void 0!==i&&(n=n.append("offset",i)),_&&(n=n.append("limit",_)),a&&(n=n.append("status",a)),u&&(n=n.append("user.associatedCommercial._id",u)),n=n.append("enable",p),C&&b&&(n=n.append("startDate",new P.vh("fr").transform(C,"YYYY-MM-dd")),n=n.append("endDate",new P.vh("fr").transform(b,"YYYY-MM-dd"))),D&&(n=n.append("user.firstName",D));try{return yield(0,M.s)(r.http.get(`${r.url}scanner-data/volume-order-by-particular-client`,{params:n}))}catch(E){const w={message:r.commonSrv.getError("",E).message,color:"danger"};return yield r.commonSrv.showToast(w),E}})()}static{this.\u0275fac=function(r){return new(r||e)(h.KVO(d.Qq),h.KVO(t.h),h.KVO(y.K))}}static{this.\u0275prov=h.jDH({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})()},68896:(N,v,o)=>{o.d(v,{I:()=>T});var g=o(73308),d=o(2978),c=o(94761),P=o(82571),M=o(45312),h=o(26409),t=o(33607),y=o(14599),A=o(94934),e=o(56610);let T=(()=>{class l{constructor(){this.commonSrv=(0,d.WQX)(P.h),this.http=(0,d.WQX)(h.Qq),this.baseUrl=(0,d.WQX)(t.K),this.storageSrv=(0,d.WQX)(y.n),this.base_url=`${this.baseUrl.getOrigin()}${M.c.basePath}`}validateScanData(n){var a=this;return(0,g.A)(function*(){try{return yield(0,A.s)(a.http.post(`${a.base_url}scanner-data`,n))}catch(i){const p={message:a.commonSrv.getError("",i).message,color:"danger"};return yield a.commonSrv.showToast(p),i}})()}checkPermission(){return(0,g.A)(function*(){try{const{camera:n}=yield c.vi.requestPermissions();return"granted"===n}catch(n){return console.log(n),!1}})()}stopScan(){var n=this;return(0,g.A)(function*(){n.currDisplay=!1,document.querySelector("body").classList.remove("scanner-active")})()}showContent(){document.querySelectorAll(".hide-on-scan").forEach(a=>{a.style.display=""}),document.querySelector("body").classList.remove("scanner-active")}prepareScanner(){return(0,g.A)(function*(){document.body.classList.add("scanner-active")})()}startScan(){var n=this;return(0,g.A)(function*(){try{if(!(yield n.checkPermission()))return void n.commonSrv.showToast({color:"danger",message:"Permission refus\xe9e pour utiliser la cam\xe9ra"});yield n.prepareScanner();const{barcodes:i}=yield c.vi.scan();if(n.restoreUI(),i&&i.length>0)return i[0].displayValue;n.commonSrv.showToast({color:"warning",message:"Aucun code-barres d\xe9tect\xe9"})}catch(a){console.error("Erreur lors du scan",a),n.commonSrv.showToast({color:"danger",message:"Erreur lors du scan"})}finally{n.stopScan()}})()}restoreUI(){document.body.classList.remove("scanner-active")}getVolumeOrderByParticularClient(n){var a=this;return(0,g.A)(function*(){let i=new h.Nl;const{status:_=300,offset:p,limit:u,enable:C=!0,associatedCommercialId:b,startDate:D,endDate:E,customerName:x}=n;void 0!==p&&(i=i.append("offset",p)),u&&(i=i.append("limit",u)),_&&(i=i.append("status",_)),b&&(i=i.append("user.associatedCommercial._id",b)),i=i.append("enable",C),D&&E&&(i=i.append("startDate",new e.vh("fr").transform(D,"YYYY-MM-dd")),i=i.append("endDate",new e.vh("fr").transform(E,"YYYY-MM-dd"))),x&&(i=i.append("user.firstName",x));try{return yield(0,A.s)(a.http.get(`${a.base_url}scanner-data/volume-order-by-particular-client`,{params:i}))}catch(w){const I={message:a.commonSrv.getError("",w).message,color:"danger"};return yield a.commonSrv.showToast(I),w}})()}static{this.\u0275fac=function(a){return new(a||l)}}static{this.\u0275prov=d.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}}return l})()}}]);