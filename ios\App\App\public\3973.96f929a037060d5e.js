"use strict";(self.webpack<PERSON>hunkapp=self.webpackChunkapp||[]).push([[3973],{43973:(M,a,t)=>{t.r(a),t.d(a,{ManageAccountModule:()=>e});var d=t(56610),l=t(77575),s=t(2978);const h=[{path:"create-indirect-user",loadChildren:()=>Promise.all([t.e(3037),t.e(2126),t.e(2076),t.e(9507)]).then(t.bind(t,29507)).then(n=>n.CreateIndirectUserPageModule)},{path:"user-list",loadChildren:()=>t.e(6634).then(t.bind(t,6634)).then(n=>n.UserListPageModule)}];let i=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=s.$C({type:n})}static{this.\u0275inj=s.G2t({imports:[l.iI.forChild(h),l.iI]})}}return n})();var c=t(93887),u=t(74657);let e=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=s.$C({type:n})}static{this.\u0275inj=s.G2t({imports:[d.MD,c.G,u.h,i]})}}return n})()}}]);