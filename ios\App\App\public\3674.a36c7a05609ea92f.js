"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3674],{63674:(A,v,s)=>{s.r(v),s.d(v,{CompanieAccountPageModule:()=>$});var d=s(56610),m=s(37222),i=s(77897),f=s(77575),_=s(73308),n=s(2978),b=s(74657);function x(o,C){if(1&o){const t=n.RV6();n.j41(0,"ion-item",16)(1,"div",17),n.bIt("click",function(){const u=n.eBV(t).$implicit,M=n.XpG(2);return n.Njj(M.selectName(u.label))}),n.EFF(2),n.k0s()()}if(2&o){const t=C.$implicit;n.R7$(2),n.JRh(t.label)}}function w(o,C){if(1&o&&(n.j41(0,"div")(1,"ion-list",5),n.DNE(2,x,3,1,"ion-item",15),n.k0s()()),2&o){const t=n.XpG();n.R7$(2),n.Y8G("ngForOf",t.dataName)}}function E(o,C){if(1&o&&(n.j41(0,"ion-select-option",18),n.EFF(1),n.k0s()),2&o){const t=C.$implicit;n.Y8G("value",t.code),n.R7$(1),n.SpI(" ",t.value," ")}}let T=(()=>{class o{constructor(t){this.modalCtrl=t,this.filterForm=new m.gE({name:new m.MJ(""),solToId:new m.MJ(""),phone:new m.MJ(""),regionCom:new m.MJ("")}),this.regionsComm=[{code:"R1",value:"R1"},{code:"R2",value:"R2"},{code:"R3",value:"R3"}]}ngOnInit(){this.filterForm?.updateValueAndValidity(),this.isEdit=!1,this.dataName=[...this.filteredCompaniesNames]}handleChangeName(t){this.isEdit=!0;const r=t.target.value.toLowerCase();this.dataName=this.filteredCompaniesNames.filter(a=>a.label.toLowerCase().includes(r.toLowerCase())).slice(0,5)}selectName(t){this.isEdit=!1,this.dataName=[],this.filterForm.value.name=t}resetFilter(){this.modalCtrl.dismiss({})}closeModal(){var t=this;return(0,_.A)(function*(){t.modalCtrl.dismiss({...t.filterForm.value})})()}static{this.\u0275fac=function(r){return new(r||o)(n.rXU(i.W3))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-filter-companies"]],inputs:{filterData:"filterData",filteredCompaniesNames:"filteredCompaniesNames"},decls:36,vars:23,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end"],["src","assets/icons/close.svg",3,"click"],["id","content",3,"formGroup"],[1,"filter-item"],["position","floating",1,"title"],["formControlName","name","type","text",3,"clearInput","value","input"],[4,"ngIf"],["formControlName","solToId","type","number",3,"clearInput"],["formControlName","phone","type","tel",3,"clearInput"],["interface","action-sheet","formControlName","regionCom"],[3,"value",4,"ngFor","ngForOf"],["expand","block","color","primary",1,"btn-submit",3,"disabled","click"],["name","search-sharp"],["lines","none",4,"ngFor","ngForOf"],["lines","none"],[3,"click"],[3,"value"]],template:function(r,a){1&r&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2)(4,"ion-img",3),n.bIt("click",function(){return a.closeModal()}),n.k0s()(),n.j41(5,"ion-label"),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"form",4)(10,"ion-list")(11,"ion-item",5)(12,"ion-label",6),n.EFF(13),n.nI1(14,"translate"),n.k0s(),n.j41(15,"ion-input",7),n.bIt("input",function(M){return a.handleChangeName(M)}),n.k0s()(),n.DNE(16,w,3,1,"div",8),n.j41(17,"ion-item",5)(18,"ion-label",6),n.EFF(19,"SolTO ID"),n.k0s(),n.nrm(20,"ion-input",9),n.k0s(),n.j41(21,"ion-item",5)(22,"ion-label",6),n.EFF(23),n.nI1(24,"translate"),n.k0s(),n.nrm(25,"ion-input",10),n.k0s(),n.j41(26,"ion-item",5)(27,"ion-label",6),n.EFF(28),n.nI1(29,"translate"),n.k0s(),n.j41(30,"ion-select",11),n.DNE(31,E,2,2,"ion-select-option",12),n.k0s()()(),n.j41(32,"ion-button",13),n.bIt("click",function(){return a.closeModal()}),n.nrm(33,"ion-icon",14),n.EFF(34),n.nI1(35,"translate"),n.k0s()()()()),2&r&&(n.R7$(6),n.JRh(n.bMT(7,13,"companie-account-page.filter.title")),n.R7$(3),n.Y8G("formGroup",a.filterForm),n.R7$(4),n.SpI(" ",n.bMT(14,15,"companie-account-page.filter.name-label")," "),n.R7$(2),n.Y8G("clearInput",!0)("value",a.filterForm.value.name),n.R7$(1),n.Y8G("ngIf",a.isEdit&&a.dataName.length>0),n.R7$(4),n.Y8G("clearInput",!0),n.R7$(3),n.SpI(" ",n.bMT(24,17,"companie-account-page.filter.phone-label")," "),n.R7$(2),n.Y8G("clearInput",!0),n.R7$(3),n.SpI(" ",n.bMT(29,19,"companie-account-page.filter.region-label")," "),n.R7$(3),n.Y8G("ngForOf",a.regionsComm),n.R7$(1),n.Y8G("disabled",a.filterForm.invalid),n.R7$(2),n.SpI(" ",n.bMT(35,21,"companie-account-page.filter.btn-submit")," "))},dependencies:[d.Sq,d.bT,m.qT,m.BC,m.cb,i.Jm,i.W9,i.eU,i.iq,i.KW,i.$w,i.uz,i.he,i.nf,i.Nm,i.Ip,i.Zx,i.ai,i.su,i.Je,i.Gw,m.j4,m.JD,b.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;color:#1e1e1e}#content[_ngcontent-%COMP%]{color:#000;height:100%;padding:calc(41 * var(--res)) 0}#content[_ngcontent-%COMP%]   .filter-item[_ngcontent-%COMP%]{max-width:95%;margin-bottom:calc(31.25 * var(--res))}#content[_ngcontent-%COMP%]   .filter-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(40 * var(--res));margin-bottom:calc(15 * var(--res))}#content[_ngcontent-%COMP%]   .filter-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]::part(icon){margin-bottom:calc(37.5 * var(--res))}#content[_ngcontent-%COMP%]   .btn-submit[_ngcontent-%COMP%]{margin:calc(50 * var(--res));--padding-top: calc(60 * var(--res));--padding-bottom: calc(60 * var(--res))}"]})}}return o})();var h=s(82571),D=s(43556),l=s(68953),c=s(58133);let e=(()=>{class o{transform(t,...r){return t===l.kJ.Baker?"bg-tertiary-100":t===l.kJ.GMS?"bg-primary-100":t===l.kJ.WholeSaler?"bg-info-100":t===l.kJ.Group?"#hsl(37, 80%, 64%)":t===l.kJ.Industry?"#1421da":t===l.kJ.EXPORT?"#1421de":t===c.s.Particular?"bg-secondary-100":t===c.s.EmployeeLapasta?"bg-warning-100":""}static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275pipe=n.EJ8({name:"colorCompanies",type:o,pure:!0})}}return o})(),g=(()=>{class o{transform(t){return"number"==typeof t?l.kJ[t]||c.s[t]:t}static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275pipe=n.EJ8({name:"labelCompanies",type:o,pure:!0})}}return o})();const p=function(o){return["/navigation/companies-account/detail/",o]};function P(o,C){if(1&o){const t=n.RV6();n.j41(0,"ion-card")(1,"ion-card-content",14)(2,"div",15),n.bIt("click",function(){const u=n.eBV(t).$implicit,M=n.XpG();return n.Njj(M.companySrv.company=u)}),n.j41(3,"ion-card-title",16),n.EFF(4),n.k0s(),n.j41(5,"div",17)(6,"div"),n.EFF(7),n.k0s(),n.j41(8,"div"),n.EFF(9),n.k0s()()(),n.j41(10,"ion-button",18),n.nI1(11,"colorCompanies"),n.EFF(12),n.nI1(13,"labelCompanies"),n.k0s()()()}if(2&o){const t=C.$implicit;n.R7$(2),n.Y8G("routerLink",n.eq3(10,p,t._id)),n.R7$(2),n.JRh((null==t?null:t.name)||"N/A"),n.R7$(3),n.SpI("SolTO ID: ",(null==t?null:t.erpSoldToId)||"N/A",""),n.R7$(2),n.SpI("N\xb0: ",(null==t?null:t.tel)||"N/A",""),n.R7$(1),n.Y8G("ngClass",n.bMT(11,6,t.category)),n.R7$(2),n.SpI(" ",n.bMT(13,8,t.category)," ")}}function O(o,C){1&o&&(n.j41(0,"ion-cart")(1,"ion-thumbnail",19),n.nrm(2,"ion-skeleton-text",20),n.k0s()()),2&o&&(n.R7$(2),n.Y8G("animated",!0))}function y(o,C){1&o&&(n.j41(0,"div",21),n.nrm(1,"ion-img",22),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&o&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"companie-account-page.empty-company")," "))}const k=[{path:"",component:(()=>{class o{constructor(t,r,a,u){this.location=t,this.commonSrv=r,this.companySrv=a,this.modalCtrl=u,this.offset=0,this.limit=20,this.skeletons=[1,2,3,4,5,6],this.filterData={name:"",solToId:0,phone:"",regionCom:""}}ngOnInit(){var t=this;return(0,_.A)(function*(){t.companies=[],yield t.getCompanies(),yield t.getElementsForFilters()})()}back(){this.location.back()}getCompanies(){var t=this;return(0,_.A)(function*(){t.skeletons=[1,2,3,4,5,6],t.isLoading=!0;const r={...t.filterData,offset:t.offset,limit:t.limit},a=yield t.companySrv.getCompanies(r);t.companies=t.companies.concat(a?.data),t.offset=t.offset+t.limit,t.isLoading=!1,t.skeletons=[]})()}resetFilter(){var t=this;return(0,_.A)(function*(){t.filterData=null,t.offset=0,t.companies=[],yield t.getCompanies()})()}doRefresh(t){var r=this;return(0,_.A)(function*(){try{r.filterData={name:"",solToId:0,phone:"",regionCom:""},r.companies=[],r.offset=0,yield r.getCompanies()}catch(a){console.error("Erreur lors du rafra\xeechissement :",a)}finally{t.target.complete()}})()}getFlowCompanies(t){var r=this;return(0,_.A)(function*(){yield r.getCompanies(),t.target.complete()})()}getElementsForFilters(){var t=this;return(0,_.A)(function*(){const a=yield t.commonSrv.getElementForFilterByKeys("companies",{keyForFilters:["name"]});t.filteredCompaniesNames=a?.dataname})()}formatIndex(t){return(t+1).toString()}showFilter(){var t=this;return(0,_.A)(function*(){const r=yield t.modalCtrl.create({component:T,initialBreakpoint:.7,cssClass:"modal",breakpoints:[0,.5,.7,1],mode:"ios",componentProps:{company:t.filterData,filteredCompaniesNames:t.filteredCompaniesNames}});r.present(),t.filterData=(yield r.onWillDismiss()).data,t.filterData&&(t.companies=[],t.offset=0,yield t.getCompanies())})()}static{this.\u0275fac=function(r){return new(r||o)(n.rXU(d.aZ),n.rXU(h.h),n.rXU(D.B),n.rXU(i.W3))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-companies-account"]],decls:24,vars:16,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",1,"img",3,"click"],[1,"title"],[1,"ion-padding","ion-content-padding",3,"fullscreen"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],[1,"filter"],["slot","start","src","/assets/icons/refresh-green.png",1,"img-refresh",3,"click"],["expand","block","fill","outline","color","primary","size","small",3,"click"],["slot","start","src","/assets/icons/filtre-icon.png",1,"img-filter"],[1,"list-companies"],[4,"ngFor","ngForOf"],["class","empty-list",4,"ngIf"],[3,"ionInfinite"],[1,"card-company"],[1,"card-company-content",3,"routerLink","click"],["color","primary"],[1,"card-company-info"],["size","small","slot","end",3,"ngClass"],[1,"skeleton"],[3,"animated"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(r,a){1&r&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return a.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content",3)(7,"div")(8,"ion-refresher",4),n.bIt("ionRefresh",function(M){return a.doRefresh(M)}),n.nrm(9,"ion-refresher-content",5),n.nI1(10,"translate"),n.nI1(11,"translate"),n.k0s(),n.j41(12,"div",6)(13,"ion-img",7),n.bIt("click",function(){return a.resetFilter()}),n.k0s(),n.j41(14,"ion-button",8),n.bIt("click",function(){return a.showFilter()}),n.nrm(15,"ion-img",9),n.EFF(16),n.nI1(17,"translate"),n.k0s()(),n.j41(18,"div",10),n.DNE(19,P,14,12,"ion-card",11),n.DNE(20,O,3,1,"ion-cart",11),n.k0s(),n.DNE(21,y,5,3,"div",12),n.k0s(),n.j41(22,"ion-infinite-scroll",13),n.bIt("ionInfinite",function(M){return a.getFlowCompanies(M)}),n.nrm(23,"ion-infinite-scroll-content"),n.k0s()()),2&r&&(n.R7$(4),n.JRh(n.bMT(5,8,"companie-account-page.title")),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(3),n.FS9("pullingText",n.bMT(10,10,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(11,12,"refresher.refreshing"),"..."),n.R7$(7),n.SpI(" ",n.bMT(17,14,"companie-account-page.btn-filter")," "),n.R7$(3),n.Y8G("ngForOf",a.companies),n.R7$(1),n.Y8G("ngForOf",a.skeletons),n.R7$(1),n.Y8G("ngIf",(null==a.companies?null:a.companies.length)<=0&&!a.isLoading))},dependencies:[d.YU,d.Sq,d.bT,i.Jm,i.b_,i.I9,i.tN,i.W9,i.eU,i.KW,i.Ax,i.Hp,i.he,i.To,i.Ki,i.ds,i.Zx,i.BC,i.ai,i.N7,f.Wk,b.D9,e,g],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start;color:#0b305c;font-family:Mont Regular}ion-content[_ngcontent-%COMP%]{--padding-top: 4px}ion-content[_ngcontent-%COMP%]   ion-progress-bar[_ngcontent-%COMP%]{margin-bottom:2rem}ion-content[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   .img-refresh[_ngcontent-%COMP%]{width:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{text-transform:capitalize;--padding-top: calc(45 * var(--res));--padding-bottom: calc(45 * var(--res));transform:translateY(-3px)}ion-content[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .img-filter[_ngcontent-%COMP%]{margin-right:calc(15 * var(--res));width:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-around;margin-top:calc(50 * var(--res));gap:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]{padding:calc(25 * var(--res));padding-left:calc(50 * var(--res));padding-right:calc(41 * var(--res));display:flex;align-items:center;justify-content:space-between;gap:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .card-company-content[_ngcontent-%COMP%]{max-width:50%}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .card-company-content[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{text-transform:uppercase;font-family:Mont Bold!important;font-weight:900!important;font-size:calc(42 * var(--res));text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .card-company-content[_ngcontent-%COMP%]   .card-company-info[_ngcontent-%COMP%]{font-weight:"Mont Light";margin-top:3px}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .card-company-content[_ngcontent-%COMP%]   .card-company-info[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{font-family:Mont Light;font-size:calc(31 * var(--res));color:#000;padding-top:2px;text-overflow:ellipsis;overflow:hidden!important;text-wrap:nowrap}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:40%;max-width:20rem;text-transform:none;font-weight:500;font-family:Mont Light;--padding-top: calc(22 * var(--res));--padding-bottom: calc(22 * var(--res));--box-shadow: none;--color: #000}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .bg-tertiary-100[_ngcontent-%COMP%]{--background: hsl(210, 12%, 97%)}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .bg-primary-100[_ngcontent-%COMP%]{--background: hsl(146, 81%, 57%)}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .bg-info-100[_ngcontent-%COMP%]{--background: hsl(200, 100%, 90%)}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .bg-tertiary-400[_ngcontent-%COMP%]{--background: hsl(0, 2%, 74%)}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .card-company[_ngcontent-%COMP%]   .bg-warning-100[_ngcontent-%COMP%]{--background: hsl(40, 79%, 84%)}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]{height:4em;width:100%;margin-bottom:1rem}ion-content[_ngcontent-%COMP%]   .list-companies[_ngcontent-%COMP%]   .skeleton[_ngcontent-%COMP%]   ion-skeleton-text[_ngcontent-%COMP%]{border-radius:10px;width:100%;height:100%}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{margin-top:10vh;display:flex;flex-direction:column;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}ion-infinite-scroll-content[_ngcontent-%COMP%]{min-height:15px}']})}}return o})()},{path:"detail/:id",loadChildren:()=>s.e(8827).then(s.bind(s,48827)).then(o=>o.CompanyAccountDetailPageModule)}];let F=(()=>{class o{static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[f.iI.forChild(k),f.iI]})}}return o})();var I=s(93887);let $=(()=>{class o{static{this.\u0275fac=function(r){return new(r||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[d.MD,m.YN,i.bv,b.h,m.X1,F,I.G]})}}return o})()},43556:(A,v,s)=>{s.d(v,{B:()=>T});var d=s(73308),m=s(94934),i=s(45312),f=s(26409),n=(s(99987),s(2978)),b=s(33607),x=s(82571),w=s(14599),E=s(74657);let T=(()=>{class h{constructor(l,c,e,g,p){this.baseUrl=l,this.http=c,this.commonSrv=e,this.storageSrv=g,this.translateService=p,this.base_url=`${this.baseUrl.getOrigin()}${i.c.basePath}`,this.base_url+="companies"}create(l){var c=this;return(0,d.A)(function*(){try{return delete l._id,yield(0,m.s)(c.http.post(c.base_url,l))}catch(e){return c.commonSrv.getError("Echec de cr\xe9ation de la compagnie",e)}})()}getCompanies(l){var c=this;return(0,d.A)(function*(){try{let e=new f.Nl;const{category:g,city:p,limit:P,name:O,regionCom:y,solToId:R,tel:k,users:F,offset:I,enable:$=!0,projection:o,isLoyaltyProgDistributor:C}=l;return void 0!==g&&(e=e.append("category",g)),p&&(e=e.append("address.city",p)),O&&(e=e.append("name",O)),R&&(e=e.append("erpSoldToId",R)),k&&(e=e.append("tel",`${k}`)),o&&(e=e.append("projection",`${o}`)),F&&(e=e.append("users",`${F}`)),y&&(e=e.append("address.commercialRegion",y)),C&&(e=e.append("isLoyaltyProgDistributor",C)),void 0!==P&&(e=e.append("limit",P)),void 0!==I&&(e=e.append("offset",I)),e=e.set("enable",$),yield(0,m.s)(c.http.get(c.base_url,{params:e}))}catch(e){const p={message:c.commonSrv.getError("",e).message,color:"danger"};return yield c.commonSrv.showToast(p),e}})()}getParticularCompanies(l){var c=this;return(0,d.A)(function*(){let e=new f.Nl;const{limit:g,offset:p,enable:P=!0,commercialRegion:O}=l;return void 0!==g&&(e=e.append("limit",g)),void 0!==p&&(e=e.append("offset",p)),O&&(e=e.append("address.commercialRegion",O)),e=e.set("enable",P),yield(0,m.s)(c.http.get(c.base_url+"/particular-suppliers",{params:e}))})()}find(l){var c=this;return(0,d.A)(function*(){try{return yield(0,m.s)(c.http.get(c.base_url+"/"+l))}catch{return c.commonSrv.initCompany()}})()}getBalance(l){var c=this;return(0,d.A)(function*(){try{let e=new f.Nl;const{company:g}=c.storageSrv.getUserConnected();return e=e.set("_id",g?g?._id:l?.companyId),yield(0,m.s)(c.http.get(`${c.base_url}/balance`,{params:e}))}catch(e){return yield c.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de votre solde",color:"danger"}),e}})()}getUsersCompany(l,c){var e=this;return(0,d.A)(function*(){try{let g=new f.Nl;const{email:p,enable:P=!0}=c;return p&&(g=g.append("email",p)),g=g.append("enable",P),yield(0,m.s)(e.http.get(`${e.base_url}/${l}/users`,{params:g}))}catch(g){return yield e.commonSrv.showToast({message:"Une erreur est survenue lors de la r\xe9cup\xe9ration de vos informations",color:"danger"}),g}})()}static{this.\u0275fac=function(c){return new(c||h)(n.KVO(b.K),n.KVO(f.Qq),n.KVO(x.h),n.KVO(w.n),n.KVO(E.c$))}}static{this.\u0275prov=n.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})()}}]);