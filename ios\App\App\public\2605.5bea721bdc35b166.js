"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2605],{76060:(N,C,r)=>{r.d(C,{I:()=>a,a:()=>p});var a=function(i){return i[i.CREATE=100]="CREATE",i[i.READ=200]="READ",i[i.DELETE=300]="DELETE",i}(a||{}),p=function(i){return i[i.FEEDBACK=100]="FEEDBACK",i[i.ORDER=200]="ORDER",i[i.GENERAL=300]="GENERAL",i}(p||{})},72605:(N,C,r)=>{r.r(C),r.d(C,{NotificationsPageModule:()=>G});var a=r(56610),p=r(37222),i=r(77897),b=r(77575),g=r(73308),f=r(76060),n=r(2978),O=r(96514),x=r(14599),M=r(23985),w=r(82571),u=r(74657),d=r(94440);function l(c,m){if(1&c){const t=n.RV6();n.j41(0,"ion-button",11),n.bIt("click",function(){n.eBV(t);const e=n.XpG(2);return n.Njj(e.toggleSelectionMode())}),n.nrm(1,"ion-icon",12),n.k0s()}}function h(c,m){if(1&c){const t=n.RV6();n.qex(0),n.j41(1,"ion-img",8),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.back())}),n.k0s(),n.j41(2,"ion-title",9),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.DNE(5,l,2,0,"ion-button",10),n.bVm()}if(2&c){const t=n.XpG();n.R7$(3),n.JRh(n.bMT(4,2,"notification.your-notification")),n.R7$(2),n.Y8G("ngIf",(null==t.dataNotification?null:t.dataNotification.length)>0)}}function P(c,m){if(1&c){const t=n.RV6();n.qex(0),n.j41(1,"ion-checkbox",13),n.bIt("ionChange",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.selectAllNotifications())}),n.k0s(),n.j41(2,"ion-title",14),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.j41(5,"ion-button",15),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.deleteSelectedNotifications())}),n.nrm(6,"ion-icon",16),n.k0s(),n.j41(7,"ion-button",11),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSelectionMode())}),n.nrm(8,"ion-icon",17),n.k0s(),n.bVm()}if(2&c){const t=n.XpG();n.R7$(1),n.Y8G("checked",t.selectedCount===t.dataNotification.length),n.R7$(2),n.Lme("",t.selectedCount," ",n.bMT(4,4,"notification.selected"),""),n.R7$(2),n.Y8G("disabled",t.isDeletingLoading||0===t.selectedCount)}}function v(c,m){1&c&&n.nrm(0,"ion-progress-bar",18)}function y(c,m){1&c&&(n.j41(0,"div",19),n.nrm(1,"ion-spinner",20),n.k0s())}function S(c,m){if(1&c){const t=n.RV6();n.qex(0),n.j41(1,"ion-checkbox",26),n.bIt("ionChange",function(){n.eBV(t);const e=n.XpG().$implicit,s=n.XpG();return n.Njj(s.toggleNotificationSelection(null==e||null==e._id?null:e._id.toString()))})("click",function(e){return e.stopPropagation()}),n.k0s(),n.bVm()}if(2&c){const t=n.XpG().$implicit,o=n.XpG();n.R7$(1),n.Y8G("checked",o.selectedNotifications.has(null==t||null==t._id?null:t._id.toString()))}}function D(c,m){if(1&c){const t=n.RV6();n.j41(0,"div",21),n.bIt("click",function(){const s=n.eBV(t).$implicit,_=n.XpG();return n.Njj(_.isSelectionMode?null:_.showDetailNotification(s))}),n.DNE(1,S,2,1,"ng-container",1),n.nrm(2,"img",22),n.j41(3,"div",23)(4,"div",24)(5,"span"),n.EFF(6),n.nI1(7,"date"),n.k0s(),n.j41(8,"div"),n.EFF(9),n.nI1(10,"truncateString"),n.k0s()()(),n.nrm(11,"div",25),n.k0s()}if(2&c){const t=m.$implicit,o=n.XpG();n.AVh("selected",o.selectedNotifications.has(null==t||null==t._id?null:t._id.toString())),n.Y8G("ngStyle",o.notifications(t.status)),n.R7$(1),n.Y8G("ngIf",o.isSelectionMode),n.R7$(5),n.SpI("La Cadyst Grain le ",n.i5U(7,7,null==t||null==t.dates?null:t.dates.created,"dd/MM/yy HH:mm:ss "),""),n.R7$(3),n.JRh(n.i5U(10,10,null==t?null:t.message,15)),n.R7$(2),n.Y8G("ngStyle",o.manageStatusNotification(t.status))}}function R(c,m){1&c&&(n.j41(0,"div",27)(1,"div",28),n.nrm(2,"img",29),n.k0s(),n.j41(3,"p",30),n.EFF(4),n.nI1(5,"translate"),n.k0s()()),2&c&&(n.R7$(4),n.JRh(n.bMT(5,1,"notification.notification-not-found")))}function F(c,m){if(1&c){const t=n.RV6();n.j41(0,"ion-button",12),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.commonSrv.navigateTo(null==e.notificationService||null==e.notificationService.currentNotification?null:e.notificationService.currentNotification.redirect))}),n.j41(1,"ion-label",2),n.EFF(2," Consultez d\xe9tails"),n.k0s()()}}const T=[{path:"",component:(()=>{class c{constructor(t,o,e,s,_,k,E,U){this.notificationService=t,this.storageService=o,this.userSrv=e,this.location=s,this.router=_,this.commonSrv=k,this.translateService=E,this.cdr=U,this.notificationCategory=f.a,this.isLoading=!1,this.isDeletingLoading=!1,this.isSelectionMode=!1,this.selectedNotifications=new Set,this.selectedCount=0}ngOnInit(){}ionViewWillEnter(){var t=this;return(0,g.A)(function*(){t.isLoading=!0,t.user=t.storageService.getUserConnected();const o=yield t.userSrv?.find(t.user?._id),e={email:t.user?.email,userId:o?._id},s=o?.notifications?.filter(_=>_?.status!==f.I.DELETE);e.notifications=JSON.stringify({$in:s?.map(_=>_?.id)}),t.dataNotification=(yield t.notificationService.getMessages(e))?.data,t.dataNotification?.forEach(_=>{const k=o?.notifications?.find(E=>E?.id===_?._id?.toString());k&&(_.status=k?.status)}),t.isLoading=!1})()}manageStatusNotification(t){if(100==t)return{background:"rgb(203 5 58)"}}notifications(t){if(100!=t)return{background:"white"}}showDetailNotification(t){var o=this;return(0,g.A)(function*(){o.notificationService.currentNotification=t,o.router.navigate(o.notificationService.currentNotification?.isGeneralNotif||o.notificationService.currentNotification?.category===o.notificationCategory.ORDER||o.notificationService.currentNotification?.category===o.notificationCategory.FEEDBACK?["/navigation/notifications/general-detail"]:["/navigation/notifications/detail"]),t.status==f.I.CREATE&&(yield o.notificationService.makeRead(t?._id))})()}back(){this.location.back()}toggleSelectionMode(){this.isSelectionMode=!this.isSelectionMode,this.isSelectionMode||(this.selectedNotifications.clear(),this.updateSelectedCount()),this.cdr.detectChanges()}toggleNotificationSelection(t){this.selectedNotifications.has(t)?this.selectedNotifications.delete(t):this.selectedNotifications.add(t),this.updateSelectedCount(),this.cdr.detectChanges()}selectAllNotifications(){this.selectedNotifications.size===this.dataNotification?.length?this.selectedNotifications.clear():this.dataNotification.forEach(t=>{if(t?._id){const o=t?._id?.toString();this.selectedNotifications.add(o)}}),this.updateSelectedCount(),this.cdr.detectChanges()}updateSelectedCount(){this.selectedCount=this.selectedNotifications.size,this.cdr.detectChanges()}deleteSelectedNotifications(){var t=this;return(0,g.A)(function*(){if(0===t.selectedNotifications.size)return;t.isDeletingLoading=!0;const o=Array.from(t.selectedNotifications);try{yield new Promise(s=>setTimeout(s,2e3)),yield t.notificationService.deleteNotifications(o).catch(s=>{throw 404===s.status?new Error("Route not found"):s});const e=o.length;t.dataNotification=t.dataNotification.filter(s=>!o.includes(s?._id?.toString()??"")),t.selectedNotifications.clear(),t.updateSelectedCount(),t.isSelectionMode=!1,yield t.commonSrv.showToast({message:"fr"===t.translateService.currentLang?`${e} notification(s) supprim\xe9e(s) avec succ\xe8s`:`${e} notification(s) successfully deleted`,color:"success"})}catch(e){console.error("Error deleting notifications:",e),yield t.ionViewWillEnter(),yield t.commonSrv.showToast({message:"fr"===t.translateService.currentLang?"Une erreur est survenue lors de la suppression des notifications":"An error occurred while deleting notifications",color:"danger"})}finally{t.isDeletingLoading=!1,t.cdr.detectChanges()}})()}deleteNotification(t){var o=this;return(0,g.A)(function*(){if(t){o.isDeletingLoading=!0;try{yield new Promise(e=>setTimeout(e,2e3)),yield o.notificationService.deleteNotifications([...t]).catch(e=>{throw 404===e.status?new Error("Delete operation failed"):e}),o.dataNotification=o.dataNotification.filter(e=>e?._id?.toString()!==t),o.selectedNotifications.has(t)&&(o.selectedNotifications.delete(t),o.updateSelectedCount()),yield o.commonSrv.showToast({message:"fr"===o.translateService.currentLang?"Notification supprim\xe9e avec succ\xe8s":"Notification successfully deleted",color:"success"})}catch(e){console.error("Error deleting notification:",e),yield o.ionViewWillEnter(),yield o.commonSrv.showToast({message:"fr"===o.translateService.currentLang?"Une erreur est survenue lors de la suppression de la notification":"An error occurred while deleting the notification",color:"danger"})}finally{o.isDeletingLoading=!1,o.cdr.detectChanges()}}})()}static{this.\u0275fac=function(o){return new(o||c)(n.rXU(O.I),n.rXU(x.n),n.rXU(M.D),n.rXU(a.aZ),n.rXU(b.Ix),n.rXU(w.h),n.rXU(u.c$),n.rXU(n.gRc))}}static{this.\u0275cmp=n.VBU({type:c,selectors:[["app-notifications"]],decls:10,vars:9,consts:[[1,"header"],[4,"ngIf"],[3,"fullscreen"],["type","indeterminate",4,"ngIf"],["class","ion-text-center ion-margin-top",4,"ngIf"],[1,"card"],["class","notification-detail",3,"ngStyle","selected","click",4,"ngFor","ngForOf"],["class","empty",4,"ngIf"],["slot","start","src","/assets/icons/arrow-blue.svg",3,"click"],[1,"title"],["slot","end","fill","clear",3,"click",4,"ngIf"],["slot","end","fill","clear",3,"click"],["name","ellipsis-vertical"],["slot","start",3,"checked","ionChange"],[1,"selection-title"],["slot","end","fill","clear","color","danger",3,"disabled","click"],["name","trash"],["name","close"],["type","indeterminate"],[1,"ion-text-center","ion-margin-top"],["name","bubbles"],[1,"notification-detail",3,"ngStyle","click"],["src","assets/logos/cadyst.png","alt","Image absente",1,"empty-list-img"],[1,"detail"],[1,"message-contain"],[1,"status",3,"ngStyle"],["slot","start",3,"checked","ionChange","click"],[1,"empty"],[1,"img-container"],["src","/assets/icons/Research paper-amico.svg","alt","Image absente",1,"empty-list-img"],[1,"illustration-label"]],template:function(o,e){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0),n.DNE(2,h,6,4,"ng-container",1),n.DNE(3,P,9,6,"ng-container",1),n.k0s()(),n.j41(4,"ion-content",2),n.DNE(5,v,1,0,"ion-progress-bar",3),n.DNE(6,y,2,0,"div",4),n.j41(7,"div",5),n.DNE(8,D,12,13,"div",6),n.DNE(9,R,6,3,"div",7),n.k0s()()),2&o&&(n.R7$(2),n.Y8G("ngIf",!e.isSelectionMode),n.R7$(1),n.Y8G("ngIf",e.isSelectionMode),n.R7$(1),n.Y8G("fullscreen",!0),n.R7$(1),n.Y8G("ngIf",e.isLoading),n.R7$(1),n.Y8G("ngIf",e.isDeletingLoading),n.R7$(1),n.AVh("content-disabled",e.isDeletingLoading),n.R7$(1),n.Y8G("ngForOf",e.dataNotification),n.R7$(1),n.Y8G("ngIf",!(null!=e.dataNotification&&e.dataNotification.length||e.isLoading)))},dependencies:[a.Sq,a.bT,a.B3,i.Jm,i.eY,i.W9,i.eU,i.iq,i.KW,i.FH,i.w2,i.BC,i.ai,i.hB,a.vh,d.c,u.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:calc(41 * var(--res));padding-block:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#f4f4f4;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res));cursor:pointer}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;color:var(--clr-primary-700);font-family:var(--mont-regular)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .selection-title[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-primary)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--padding-start: 8px;--padding-end: 8px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin:0 10px;--size: 20px;--checkbox-background-checked: var(--ion-color-primary)}.content-disabled[_ngcontent-%COMP%]{opacity:.5;pointer-events:none;transition:opacity .3s ease}ion-spinner[_ngcontent-%COMP%]{--color: var(--ion-color-primary);width:35px;height:35px}.card[_ngcontent-%COMP%]{margin:1em;position:relative;transition:all .3s ease}.notification-detail[_ngcontent-%COMP%]{align-items:flex-start;justify-content:space-between;display:flex;gap:.5em;overflow-y:auto;padding:.5em .5em 0;height:100px;background:#f4f4f4;border-radius:10px;margin-bottom:.5em;transition:all .3s ease;position:relative}.notification-detail.selected[_ngcontent-%COMP%]{background-color:rgba(var(--ion-color-primary-rgb),.1)}.notification-detail[_ngcontent-%COMP%]   .empty-list-img[_ngcontent-%COMP%]{border-radius:5px}.notification-detail[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{width:70%;cursor:pointer}.notification-detail[_ngcontent-%COMP%]:hover{transform:scale(.98);box-shadow:#959da533 0 8px 24px}.notification-detail[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px}.notification-detail[_ngcontent-%COMP%]   .message-contain[_ngcontent-%COMP%]{display:flex;flex-direction:column;font-size:12px}.notification-detail[_ngcontent-%COMP%]   .message-contain[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:700;font-size:12px;line-height:30px}.notification-detail[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{height:11px;width:11px;border-radius:50%;flex-shrink:0}.notification-detail[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{--size: 17px;--checkbox-background-checked: var(--ion-color-primary)}.empty[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;height:100%}.empty[_ngcontent-%COMP%]   .img-container[_ngcontent-%COMP%]{width:60%}.empty[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{text-align:center}"]})}}return c})()},{path:"detail",component:(()=>{class c{constructor(t,o){this.notificationService=t,this.location=o}ngOnInit(){}back(){this.location.back()}static{this.\u0275fac=function(o){return new(o||c)(n.rXU(O.I),n.rXU(a.aZ))}}static{this.\u0275cmp=n.VBU({type:c,selectors:[["app-notification-detail"]],decls:34,vars:13,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"card"],[1,"details"],[1,"form-group"],["for","categoryInput",1,"label"],["nbInput","","fullWidth","","size","large","rows","8","cols","40","required","",1,"response"],["nbInput","","fullWidth","","size","large","type","text","for","categoryInput","disabled","",1,"input",3,"placeholder"],["for","subCategoryInput",1,"label"],["nbInput","","fullWidth","","size","large","type","text","for","subCategoryInput","disabled","",1,"input",3,"placeholder"],["for","messageInput",1,"label"],["nbInput","","fullWidth","","size","large","rows","8","cols","40","required","","id","messageInput",1,"input",3,"disabled","value"]],template:function(o,e){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return e.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4,"D\xe9tail de la notification."),n.k0s()()(),n.j41(5,"ion-content")(6,"div",3)(7,"div",4)(8,"div",5)(9,"h2"),n.EFF(10),n.nI1(11,"date"),n.k0s()(),n.j41(12,"div",5)(13,"label",6),n.EFF(14,"R\xe9ponse de La Cadyst Grain"),n.k0s(),n.j41(15,"div",7),n.EFF(16),n.k0s()(),n.j41(17,"div",5)(18,"label",6),n.EFF(19,"Categorie de la r\xe9clamation"),n.k0s(),n.nrm(20,"input",8),n.k0s(),n.j41(21,"div",5)(22,"label",9),n.EFF(23,"Motif de la r\xe9clamation"),n.k0s(),n.nrm(24,"input",10),n.k0s(),n.j41(25,"div",5)(26,"label",11),n.EFF(27,"Contenu de votre r\xe9clamation "),n.k0s(),n.j41(28,"textarea",12),n.EFF(29,"              "),n.k0s()(),n.j41(30,"div",5)(31,"label",11),n.EFF(32),n.nI1(33,"date"),n.k0s()()()()()),2&o&&(n.R7$(10),n.SpI("La Cadyst Grain le ",n.i5U(11,7,null==e.notificationService||null==e.notificationService.currentNotification?null:e.notificationService.currentNotification.dates.created,"dd/MM/yyyy HH:mm:ss")," "),n.R7$(6),n.SpI(" ",(null==e.notificationService.currentNotification?null:e.notificationService.currentNotification.message)||"non renseign\xe9"," "),n.R7$(4),n.FS9("placeholder",null==e.notificationService.currentNotification||null==e.notificationService.currentNotification.feedback?null:e.notificationService.currentNotification.feedback.category.label),n.R7$(4),n.FS9("placeholder",null==e.notificationService.currentNotification||null==e.notificationService.currentNotification.feedback?null:e.notificationService.currentNotification.feedback.subCategory.label),n.R7$(4),n.FS9("value",(null==e.notificationService.currentNotification||null==e.notificationService.currentNotification.feedback?null:e.notificationService.currentNotification.feedback.message)||"non renseign\xe9"),n.Y8G("disabled",!0),n.R7$(4),n.SpI("La r\xe9clamation a \xe9t\xe9 faite le ",n.i5U(33,10,null==e.notificationService.currentNotification||null==e.notificationService.currentNotification.feedback?null:e.notificationService.currentNotification.feedback.created_at,"dd/MM/yyyy HH:mm:ss"),""))},dependencies:[i.W9,i.eU,i.KW,i.BC,i.ai,a.vh],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));display:flex;align-items:center;background:#fff}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}.card[_ngcontent-%COMP%]{margin:1em;overflow-y:auto}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1em}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:700;font-size:16px}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:14px}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%]{font-size:16px;height:78px;width:100%;font-family:courier;border:none;padding:1em;margin-top:14px}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .response[_ngcontent-%COMP%]{font-size:14px;background:rgba(119,116,116,.6784313725);border:none;padding:1em;margin-top:1px}"]})}}return c})()},{path:"general-detail",component:(()=>{class c{constructor(t,o,e){this.notificationService=t,this.location=o,this.commonSrv=e,this.notificationCategory=f.a}back(){this.location.back()}static{this.\u0275fac=function(o){return new(o||c)(n.rXU(O.I),n.rXU(a.aZ),n.rXU(w.h))}}static{this.\u0275cmp=n.VBU({type:c,selectors:[["app-general-notif-detail"]],decls:22,vars:11,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"card"],[1,"notication-detail"],["src","assets/logos/cadyst.png","alt","Image absente",1,"empty-list-img"],[1,"detail"],[1,"message-contain"],[1,"date"],[1,"notication-msg"],["alt","",3,"src"],["class","btn mbottom250 btn--meduim btn--upper",3,"click",4,"ngIf"],[1,"btn","mbottom250","btn--meduim","btn--upper",3,"click"]],template:function(o,e){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return e.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4,"D\xe9tail de la notification"),n.k0s()()(),n.j41(5,"ion-content")(6,"div",3)(7,"div",4),n.nrm(8,"img",5),n.j41(9,"div",6)(10,"div",7)(11,"div",2),n.EFF(12),n.nI1(13,"truncateString"),n.k0s(),n.j41(14,"span",8),n.EFF(15),n.nI1(16,"date"),n.k0s()()()(),n.j41(17,"div",9)(18,"div",2),n.EFF(19),n.k0s(),n.nrm(20,"img",10),n.DNE(21,F,3,0,"ion-button",11),n.k0s()()()),2&o&&(n.R7$(12),n.JRh(n.i5U(13,5,null==e.notificationService||null==e.notificationService.currentNotification?null:e.notificationService.currentNotification.title,50)),n.R7$(3),n.JRh(n.i5U(16,8,null==e.notificationService||null==e.notificationService.currentNotification||null==e.notificationService.currentNotification.dates?null:e.notificationService.currentNotification.dates.created,"dd/MM/yy HH:mm:ss ")),n.R7$(4),n.JRh(null==e.notificationService||null==e.notificationService.currentNotification?null:e.notificationService.currentNotification.message),n.R7$(1),n.Y8G("src",null==e.notificationService||null==e.notificationService.currentNotification?null:e.notificationService.currentNotification.img,n.B4B),n.R7$(1),n.Y8G("ngIf",(null==e.notificationService||null==e.notificationService.currentNotification?null:e.notificationService.currentNotification.category)===e.notificationCategory.ORDER))},dependencies:[a.bT,i.Jm,i.W9,i.eU,i.KW,i.he,i.BC,i.ai,a.vh,d.c],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));display:flex;align-items:center;background:#fff}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}.card[_ngcontent-%COMP%]{margin:0 1em 1em;overflow-y:auto}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1em}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:700;font-size:16px}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:14px}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%]{font-size:16px;height:78px;width:100%;font-family:courier;border:none;padding:1em;margin-top:14px}.card[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .response[_ngcontent-%COMP%]{font-size:14px;background:rgba(119,116,116,.6784313725);border:none;padding:1em;margin-top:1px}.card[_ngcontent-%COMP%]   .notication-detail[_ngcontent-%COMP%]{align-items:flex-start;display:flex;flex-direction:row;gap:.5em;overflow-y:auto;margin:1em 0}.card[_ngcontent-%COMP%]   .notication-detail[_ngcontent-%COMP%]   .empty-list-img[_ngcontent-%COMP%]{border-radius:10px}.card[_ngcontent-%COMP%]   .notication-detail[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{width:50%}.card[_ngcontent-%COMP%]   .notication-detail[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px}.card[_ngcontent-%COMP%]   .notication-detail[_ngcontent-%COMP%]   .message-contain[_ngcontent-%COMP%]{display:flex;flex-direction:column}.card[_ngcontent-%COMP%]   .notication-detail[_ngcontent-%COMP%]   .message-contain[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:14px;color:#143c5d}.card[_ngcontent-%COMP%]   .notication-detail[_ngcontent-%COMP%]   .message-contain[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:10px;color:#727272}.card[_ngcontent-%COMP%]   .notication-msg[_ngcontent-%COMP%]{display:flex;justify-content:center;flex-direction:column;padding:1em;gap:.5em;font-size:12px;background:#f4f4f4;border-radius:15px}"]})}}return c})()}];let z=(()=>{class c{static{this.\u0275fac=function(o){return new(o||c)}}static{this.\u0275mod=n.$C({type:c})}static{this.\u0275inj=n.G2t({imports:[b.iI.forChild(T),b.iI]})}}return c})();var $=r(93887);let G=(()=>{class c{static{this.\u0275fac=function(o){return new(o||c)}}static{this.\u0275mod=n.$C({type:c})}static{this.\u0275inj=n.G2t({imports:[a.MD,p.YN,i.bv,z,$.G,u.h]})}}return c})()},94440:(N,C,r)=>{r.d(C,{c:()=>p});var a=r(2978);let p=(()=>{class i{transform(g,...f){return g?g.length>f[0]?`${g.substring(0,f[0]-3)}...`:g:""}static{this.\u0275fac=function(f){return new(f||i)}}static{this.\u0275pipe=a.EJ8({name:"truncateString",type:i,pure:!0})}}return i})()},96514:(N,C,r)=>{r.d(C,{I:()=>x});var a=r(73308),p=r(26409),i=r(94934),b=r(35025),g=r.n(b),f=r(45312),n=r(2978),O=r(33607);let x=(()=>{class M{constructor(u,d){this.http=u,this.baseUrlService=d,this.url=this.baseUrlService.getOrigin()+f.c.basePath}getMessages(u){var d=this;return(0,a.A)(function*(){try{let l=new p.Nl;const{email:h,date:P,notifications:v,userId:y,enable:S=!0}=u;return P?.startDate&&P?.endDate&&(l=l.append("startDate",g()(P?.startDate).format("YYYY-MM-DD")),l=l.append("endDate",g()(P?.endDate).format("YYYY-MM-DD"))),h&&(l=l.append("feedback.user.email",h)),y&&(l=l.append("userId",y)),v&&(l=l.append("_id",v)),l=l.append("enable",S),yield(0,i.s)(d.http.get(`${d.url}notifications`,{params:l}))}catch(l){return l}})()}makeRead(u){var d=this;return(0,a.A)(function*(){try{return yield(0,i.s)(d.http.patch(`${d.url}notifications/${u}`,{}))}catch(l){return l}})()}deleteNotifications(u){var d=this;return(0,a.A)(function*(){return(0,i.s)(d.http.patch(`${d.url}notifications/${u[0]}/delete`,{ids:u}))})()}static{this.\u0275fac=function(d){return new(d||M)(n.KVO(p.Qq),n.KVO(O.K))}}static{this.\u0275prov=n.jDH({token:M,factory:M.\u0275fac,providedIn:"root"})}}return M})()}}]);