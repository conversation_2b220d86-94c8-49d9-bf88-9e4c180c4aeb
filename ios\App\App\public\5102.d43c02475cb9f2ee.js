"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[5102],{15102:(q,u,i)=>{i.r(u),i.d(u,{OrderDetailPageModule:()=>H});var g=i(56610),m=i(37222),b=i(93887),p=i(74657),c=i(77897),_=i(77575),a=i(73308),O=i(88233),s=i(99987),M=i(58133),v=i(92533),h=i(91285),f=i(39893),R=i(92882),n=i(2978),y=i(57870),S=i(82571),I=i(62049),T=i(54648),k=i(11244),j=i(94440);function F(r,l){if(1&r){const e=n.RV6();n.j41(0,"ion-buttons",22)(1,"ion-button",23),n.bIt("click",function(t){n.eBV(e);const d=n.XpG();return n.Njj(d.presentPopover(t))}),n.nrm(2,"ion-icon",24),n.k0s()()}}function w(r,l){1&r&&(n.j41(0,"ion-label",2),n.EFF(1),n.nI1(2,"capitalize"),n.nI1(3,"translate"),n.k0s()),2&r&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,n.bMT(3,3,"order.detail.validated-at"))," :"))}function D(r,l){1&r&&(n.j41(0,"ion-label",2),n.EFF(1),n.nI1(2,"capitalize"),n.nI1(3,"translate"),n.k0s()),2&r&&(n.R7$(1),n.SpI(" ",n.bMT(2,1,n.bMT(3,3,"history-page.ref"))," :"))}function E(r,l){if(1&r&&(n.j41(0,"ion-label",13),n.EFF(1),n.nI1(2,"date"),n.k0s()),2&r){const e=n.XpG();n.R7$(1),n.SpI(" ",n.i5U(2,1,null==e.orderRetailService||null==e.orderRetailService.orderRetail||null==e.orderRetailService.orderRetail.dates?null:e.orderRetailService.orderRetail.dates.validated,"dd/MM/YY")," ")}}function A(r,l){if(1&r&&(n.j41(0,"ion-label",13),n.EFF(1),n.nI1(2,"capitalize"),n.k0s()),2&r){const e=n.XpG();n.R7$(1),n.SpI(" ",n.bMT(2,1,null==e.orderRetailService||null==e.orderRetailService.orderRetail?null:e.orderRetailService.orderRetail.reference)," ")}}function $(r,l){if(1&r&&(n.j41(0,"div",32)(1,"div",26)(2,"div",33),n.nrm(3,"ion-img",34),n.k0s()(),n.j41(4,"div",35),n.EFF(5),n.k0s(),n.j41(6,"div",36),n.EFF(7),n.nI1(8,"number"),n.k0s(),n.j41(9,"div",37),n.EFF(10),n.nI1(11,"number"),n.k0s()()),2&r){const e=l.$implicit;n.R7$(3),n.FS9("src",null==e||null==e.product?null:e.product.image),n.R7$(2),n.Lme(" ",null==e?null:e.quantity," ",null==e||null==e.packaging?null:e.packaging.label," "),n.R7$(2),n.SpI(" ",n.i5U(8,5,e.unitPrice,"00")," XAF "),n.R7$(3),n.SpI(" ",n.i5U(11,8,(null==e?null:e.unitPrice)*(null==e?null:e.quantity),"00")," XAF")}}function V(r,l){if(1&r&&(n.j41(0,"div")(1,"ion-title",8),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",25),n.nrm(5,"div",26),n.j41(6,"div",27),n.EFF(7,"Quantit\xe9"),n.k0s(),n.j41(8,"div",28),n.EFF(9,"PU"),n.k0s(),n.j41(10,"div",29),n.EFF(11,"Montant"),n.k0s()(),n.j41(12,"div",30),n.DNE(13,$,12,11,"div",31),n.k0s()()),2&r){const e=n.XpG();n.R7$(2),n.JRh(n.bMT(3,2,"order.detail.product-list")),n.R7$(11),n.Y8G("ngForOf",null==e.orderRetailService||null==e.orderRetailService.orderRetail||null==e.orderRetailService.orderRetail.cart?null:e.orderRetailService.orderRetail.cart.items)}}function N(r,l){if(1&r&&(n.j41(0,"div",38)(1,"ion-title",8),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"div",9)(5,"ion-label",39),n.EFF(6),n.k0s()()()),2&r){const e=n.XpG();n.R7$(2),n.SpI("",n.bMT(3,2,"order.detail.motif-rejet")," "),n.R7$(4),n.JRh(null==e.orderRetailService||null==e.orderRetailService.orderRetail||null==e.orderRetailService.orderRetail.validation?null:e.orderRetailService.orderRetail.validation.raison)}}function G(r,l){if(1&r){const e=n.RV6();n.j41(0,"ion-button",40),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalConfirmValidation())}),n.j41(1,"ion-label"),n.EFF(2," VALIDER"),n.k0s()()}}function L(r,l){if(1&r){const e=n.RV6();n.j41(0,"ion-button",40),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalConfirmValidation())}),n.j41(1,"ion-label"),n.EFF(2," VALIDER"),n.k0s()()}}function z(r,l){if(1&r){const e=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalRejectOrder())}),n.j41(1,"ion-label"),n.EFF(2," REJETER"),n.k0s()()}}function x(r,l){if(1&r){const e=n.RV6();n.j41(0,"ion-button",41),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalRejectOrder())}),n.j41(1,"ion-label"),n.EFF(2," REJETER"),n.k0s()()}}function U(r,l){1&r&&(n.j41(0,"ion-label",45),n.EFF(1),n.k0s()),2&r&&(n.R7$(1),n.SpI(" ","VALIDER LA COMMANDE"," "))}function X(r,l){if(1&r){const e=n.RV6();n.j41(0,"div",42),n.bIt("click",function(){n.eBV(e);const t=n.XpG();return n.Njj(t.showModalConfirmValidation())}),n.j41(1,"ion-button",43),n.DNE(2,U,2,1,"ion-label",44),n.k0s()()}if(2&r){const e=n.XpG();n.R7$(2),n.Y8G("ngIf",!e.isLoading)}}const C=function(r){return[r]},Y=[{path:"",component:(()=>{class r{constructor(e,o,t,d,P,W,J,Q,K){this.location=e,this.orderRetailService=o,this.alertController=t,this.popoverCtrl=d,this.commonSrv=P,this.modalCtrl=W,this.translateService=J,this.router=Q,this.route=K,this.slideProductOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:10},this.isValidate=!1,this.isReject=!1,this.isLoading=!1,this.isPopoverOpen=!1,this.orderStatus=O.Re,this.userCategory=M.s,this.orderAction=O.T3,this.employeeType=M.P,this.orderId="",this.actionHandlers={modifier:()=>this.modifyOrder(),telecharger:()=>this.generatePurchaseOrder(this.orderRetailService?.orderRetail?._id),annuler:()=>this.handleCancellation()},this.trackByFn=(Z,tn)=>Z}ionViewWillEnter(){var e=this;return(0,a.A)(function*(){if(!e.orderRetailService.orderRetail&&!e.route.snapshot.params.orderId)return e.back();if(!e.orderRetailService.orderRetail){const o=e.route.snapshot.params.orderId;e.orderId=o,e.orderRetailService.orderRetail=null,e.orderRetailService.orderRetail=yield e.orderRetailService.findOrderRetail(o)}e.order=yield e.orderRetailService.orderRetail})()}back(){this.orderRetailService.orderRetail=null,this.location.back()}showModalRemovalDetail(e){}presentPopover(e){var o=this;return(0,a.A)(function*(){const t=yield o.popoverCtrl.create({component:v.c,event:e,cssClass:"custom-popover",mode:"md",componentProps:{actions:[{label:o.getTranslatedText("Modifier la commande","Edit order"),action:"modifier"},{label:o.getTranslatedText("T\xe9l\xe9charger Bon de co..","Download PO"),action:"telecharger"},{label:o.getTranslatedText("Demande d'annulation","Cancellation request"),action:"annuler"}]}});yield t.present();const{data:d}=yield t.onWillDismiss();d&&o.handlePopoverAction(d)})()}handlePopoverAction(e){const o=this.actionHandlers[e];o&&o()}modifyOrder(){this.canModifyOrder()?this.navigateToOrderUpdate():this.commonSrv.showToast({message:this.translateService.currentLang===s.T.French?"Une erreur est survenue.":"An error occurred.",color:"warning"})}canModifyOrder(){return this.orderRetailService?.orderRetail?.status!==O.Re.VALIDATED&&this.commonSrv.user.category!==M.s.Commercial}navigateToOrderUpdate(){const e=this.orderRetailService?.orderRetail?._id;this.router.navigate([`order/detail/${e}/order-update`]).then(o=>console.log(o?"Navigation r\xe9ussie":"\xc9chec de la navigation")).catch(o=>console.error("Erreur lors de la navigation",o))}cancelOrder(){this.commonSrv.showToast({message:this.translateService.currentLang===s.T.French?"Une erreur est survenue.":"An error occurred.",color:"warning"})}showModalConfirmValidation(){var e=this;return(0,a.A)(function*(){const o=yield e.modalCtrl.create({component:f.d,cssClass:"modal",initialBreakpoint:.4,breakpoints:[0,.75,.5],mode:"ios",componentProps:{statusToUpdate:O.Re.PAID,order:e.orderRetailService?.orderRetail}});yield o.present();const{data:t}=yield o.onWillDismiss();t&&e.updateOrderStatus(t)})()}updateOrderStatus(e){this.orderRetailService.orderRetail.status=e.status,this.orderRetailService.orderRetail.reference=e.reference}showModalRejectOrder(){var e=this;return(0,a.A)(function*(){yield(yield e.alertController.create({header:e.getTranslatedText("Rejet de commande","Reject Order"),message:e.getTranslatedText("Vous \xeates sur le point de rejeter cette commande.\n Confirmez vous cette action ?","You are about to reject this order.\n Do you confirm this action ?"),cssClass:"custom-loading",buttons:[{text:e.getTranslatedText("Annuler","Cancel"),cssClass:"alert-button-cancel"},{text:e.getTranslatedText("Rejeter","Reject"),cssClass:"alert-button-confirm",handler:()=>e.rejectOrder()}]})).present()})()}rejectOrder(){return(0,a.A)(function*(){})()}showDetail(e){var o=this;return(0,a.A)(function*(){yield(yield o.modalCtrl.create({component:R.F,cssClass:"modal",initialBreakpoint:.35,breakpoints:[0,.75,.8,.35,.9,.95,1],mode:"ios",componentProps:{item:e,packaging:e?.packaging}})).present()})()}generatePurchaseOrder(e){var o=this;return(0,a.A)(function*(){e?o.isLoading=!0:console.log("ID de commande non disponible")})()}getTranslatedText(e,o){return this.translateService.currentLang===s.T.French?e:o}handleCancellation(){var e=this;return(0,a.A)(function*(){if(!e.order?._id)return void(yield e.commonSrv.showToast({message:e.translateService.currentLang===s.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"}));let o=e.translateService.currentLang===s.T.French?"\xc0 la demande du client":"At the customers request";const t=yield e.modalCtrl.create({component:h.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:"Envoyer",cancelButton:"Annuler",text:"Demmande d annulation de la commande",message:"Renseigner le motif de la demande",isAnnulation:!0,handler:P=>{o=P||o,e.processCancellationRequest(o)}}}});yield t.present();const{role:d}=yield t.onWillDismiss();"confirm"===d&&e.showFinalConfirmation(o)})()}processCancellationRequest(e){var o=this;return(0,a.A)(function*(){if(o.order?._id)try{o.isLoading=!0,o.order.messageCancellation=e}catch{yield o.commonSrv.showToast({message:o.translateService.currentLang===s.T.French?"Une erreur est survenue lors du traitement de votre demande.":"An error occurred while processing your request.",color:"danger"})}finally{o.isLoading=!1}else yield o.commonSrv.showToast({message:o.translateService.currentLang===s.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"})})()}sendCancellationRequest(e){var o=this;return(0,a.A)(function*(){if(!o.order?._id)return yield o.commonSrv.showToast({message:o.translateService.currentLang===s.T.French?"La commande n'est pas correctement charg\xe9e":"Order is not properly loaded",color:"warning"}),e})()}showFinalConfirmation(e){var o=this;return(0,a.A)(function*(){yield(yield o.modalCtrl.create({component:h.y,cssClass:"modalClass",componentProps:{dataModal:{confirmButton:"Confirmer",cancelButton:"Annuler",text:"\xcates-vous s\xfbr de vouloir envoyer cette demande d'annulation ?",handler:()=>{o.sendCancellationRequest(e)}}}})).present()})()}static{this.\u0275fac=function(o){return new(o||r)(n.rXU(g.aZ),n.rXU(y.l),n.rXU(c.hG),n.rXU(c.IE),n.rXU(S.h),n.rXU(c.W3),n.rXU(I.E),n.rXU(_.Ix),n.rXU(_.nX))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-order-detail"]],decls:67,vars:78,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"color-primary"],["slot","end",4,"ngIf"],["id","container",1,"order-history-page"],[1,"scroller-container","historic-bill-detail-container","containers"],[1,"order-detail"],[1,"h3-title"],[1,"bill-info"],[1,"right-block"],["class","title",4,"ngIf"],[1,"left-block"],[1,"value"],["class","value",4,"ngIf"],[4,"ngIf"],[3,"cart","orderPrice","shipping","itemsLimited","shippingInfo"],["class","order-summary",4,"ngIf"],[1,"flex-dir"],["class","btn--meduim btn--upper","color","primary","expand","block",3,"click",4,"ngIf"],["class","btn--meduim btn--upper","color","danger","expand","block",3,"click",4,"ngIf"],["class","btn-validate",3,"click",4,"ngIf"],["slot","end"],[3,"click"],["name","ellipsis-vertical"],[1,"list-elt-header"],[1,"col","col-desc"],[1,"col","col-qdt","title"],[1,"col","col-price","title"],[1,"col","col-amount","title"],[1,"list-elt-contain"],["class"," list-elt ",4,"ngFor","ngForOf"],[1,"list-elt"],[1,"col-desc-elt"],[3,"src"],[1,"col","col-qdt"],[1,"col","col-price"],[1,"col","col-amount"],[1,"order-summary"],[1,"value","reason"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"],["color","danger","expand","block",1,"btn--meduim","btn--upper",3,"click"],[1,"btn-validate",3,"click"],["color","primary","expand","block",1,"btn--meduim","btn--upper"],["class","green-btn",4,"ngIf"],[1,"green-btn"]],template:function(o,t){1&o&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return t.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"capitalize"),n.nI1(6,"translate"),n.j41(7,"span",3),n.EFF(8),n.k0s()(),n.DNE(9,F,3,0,"ion-buttons",4),n.k0s()(),n.j41(10,"ion-content")(11,"div",5)(12,"div",6)(13,"div",7)(14,"ion-title",8),n.EFF(15),n.nI1(16,"capitalize"),n.nI1(17,"translate"),n.k0s(),n.j41(18,"div",9)(19,"div",10)(20,"ion-label",2),n.EFF(21),n.nI1(22,"capitalize"),n.nI1(23,"translate"),n.k0s(),n.j41(24,"ion-label",2),n.EFF(25),n.nI1(26,"capitalize"),n.nI1(27,"translate"),n.k0s(),n.j41(28,"ion-label",2),n.EFF(29),n.nI1(30,"capitalize"),n.nI1(31,"translate"),n.k0s(),n.j41(32,"ion-label",2),n.EFF(33),n.nI1(34,"capitalize"),n.nI1(35,"translate"),n.k0s(),n.DNE(36,w,4,5,"ion-label",11),n.DNE(37,D,4,5,"ion-label",11),n.k0s(),n.j41(38,"div",12)(39,"ion-label",13),n.EFF(40),n.k0s(),n.j41(41,"ion-label",13),n.EFF(42),n.nI1(43,"date"),n.j41(44,"span",13),n.EFF(45),n.nI1(46,"capitalize"),n.nI1(47,"translate"),n.nI1(48,"date"),n.k0s()(),n.j41(49,"ion-label",13),n.EFF(50),n.nI1(51,"capitalize"),n.k0s(),n.j41(52,"ion-label",13),n.EFF(53),n.nI1(54,"capitalize"),n.nI1(55,"truncateString"),n.k0s(),n.DNE(56,E,3,4,"ion-label",14),n.DNE(57,A,3,3,"ion-label",14),n.k0s()()(),n.DNE(58,V,14,4,"div",15),n.nrm(59,"app-purchase-summary",16),n.DNE(60,N,7,4,"div",17),n.j41(61,"div",18),n.DNE(62,G,3,0,"ion-button",19),n.DNE(63,L,3,0,"ion-button",19),n.DNE(64,z,3,0,"ion-button",20),n.DNE(65,x,3,0,"ion-button",20),n.k0s(),n.DNE(66,X,3,1,"div",21),n.k0s()()()),2&o&&(n.R7$(4),n.SpI(" ",n.bMT(5,29,n.bMT(6,31,"order.detail.reference"))," "),n.R7$(4),n.SpI(" ",null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail._id," "),n.R7$(1),n.Y8G("ngIf",t.commonSrv.user.category!==t.userCategory.Commercial),n.R7$(6),n.JRh(n.bMT(16,33,n.bMT(17,35,"order.detail.title"))),n.R7$(6),n.SpI("",n.bMT(22,37,n.bMT(23,39,"order.detail.reference"))," : "),n.R7$(4),n.SpI("",n.bMT(26,41,n.bMT(27,43,"order.detail.created-at"))," :"),n.R7$(4),n.SpI("",n.bMT(30,45,n.bMT(31,47,"order.detail.name"))," :"),n.R7$(4),n.SpI("",n.bMT(34,49,n.bMT(35,51,"order.detail.email"))," :"),n.R7$(3),n.Y8G("ngIf",null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.dates?null:t.orderRetailService.orderRetail.dates.validated),n.R7$(1),n.Y8G("ngIf",null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.reference),n.R7$(3),n.JRh(null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail._id),n.R7$(2),n.SpI("",n.i5U(43,53,null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.created_at,"dd/MM/YY")," \xa0 "),n.R7$(3),n.JRh(n.bMT(46,56,n.bMT(47,58,"preposition.to"))+n.i5U(48,60,null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.created_at,"HH:mm:ss")),n.R7$(5),n.SpI("",n.bMT(51,63,(null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.user?null:t.orderRetailService.orderRetail.user.firstName)||"N/A")," "),n.R7$(3),n.JRh(n.bMT(54,65,n.i5U(55,67,(null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.user?null:t.orderRetailService.orderRetail.user.email)||"N/A",18))),n.R7$(3),n.Y8G("ngIf",null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.dates?null:t.orderRetailService.orderRetail.dates.validated),n.R7$(1),n.Y8G("ngIf",null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.reference),n.R7$(1),n.Y8G("ngIf",!1),n.R7$(1),n.Y8G("cart",null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.cart)("orderPrice",null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.cart||null==t.orderRetailService.orderRetail.cart.items?null:t.orderRetailService.orderRetail.cart.items.unitPrice)("shipping",null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.cart?null:t.orderRetailService.orderRetail.cart.items)("itemsLimited",null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.cart||null==t.orderRetailService.orderRetail.cart.items?null:t.orderRetailService.orderRetail.cart.items.slice(0,3))("shippingInfo",null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.cart||null==t.orderRetailService.orderRetail.cart.items?null:t.orderRetailService.orderRetail.cart.items.unitPrice),n.R7$(1),n.Y8G("ngIf",null==t.orderRetailService||null==t.orderRetailService.orderRetail||null==t.orderRetailService.orderRetail.validation?null:t.orderRetailService.orderRetail.validation.raison),n.R7$(2),n.Y8G("ngIf",!t.isValidate&&n.eq3(70,C,null==t.orderStatus?null:t.orderStatus.CREDIT_IN_VALIDATION).includes(null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.DRH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",!t.isValidate&&n.eq3(72,C,t.orderStatus.CREATED).includes(null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.CORDO_RH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",!t.isReject&&n.eq3(74,C,null==t.orderStatus?null:t.orderStatus.CREDIT_IN_VALIDATION).includes(null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.DRH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",!t.isReject&&n.eq3(76,C,null==t.orderStatus?null:t.orderStatus.CREATED).includes(null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.status)&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.employeeType)===t.employeeType.CORDO_RH&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.authorizations.includes(t.orderAction.VALIDATE))),n.R7$(1),n.Y8G("ngIf",(null==t.orderRetailService||null==t.orderRetailService.orderRetail?null:t.orderRetailService.orderRetail.status)===t.orderStatus.CREDIT_IN_AWAIT_VALIDATION&&t.commonSrv.user.category===t.userCategory.Commercial))},dependencies:[g.Sq,g.bT,c.Jm,c.QW,c.W9,c.eU,c.iq,c.KW,c.he,c.BC,c.ai,T.N,g.QX,g.vh,k.F,j.c,p.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent;background:#cfcfcf;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Regular;text-align:start;color:var(--clr-primary-900)}ion-content[_ngcontent-%COMP%]   app-purchase-summary[_ngcontent-%COMP%]{margin:1rem 0}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]{background-color:#e5e5e5;text-align:-webkit-center;width:100%;display:flex;align-items:center;align-items:baseline}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]{background:var(--clr-default-400);background:#ffffff;border-radius:1rem;width:100%;padding-bottom:.2rem;margin-top:10px;box-sizing:content-box}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:1rem calc(41 * var(--res));border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-detail[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{width:95%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .green-btn[_ngcontent-%COMP%]{color:#fff}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .flex-dir[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:95%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .flex-dir[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:48%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]{width:95%;padding:0 .5em;background:#ffffff;margin-bottom:1rem!important;border-radius:5px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .form--header[_ngcontent-%COMP%]{text-transform:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{box-shadow:0 4px 25px #00000017;border-radius:1px;overflow-x:hidden;overflow-y:auto;max-height:calc(25 * var(--resH))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]{padding:5px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]{font-size:14px;display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{height:100%;padding:5px 0;display:flex;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:calc(100% - 300px)}@media only screen and (max-width: 530px){ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{width:80px}}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-qdt[_ngcontent-%COMP%]{width:80px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-price[_ngcontent-%COMP%]{width:105px;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-amount[_ngcontent-%COMP%]{width:105px;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:ce}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .col-desc-elt-contain[_ngcontent-%COMP%]{width:25px;height:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%]   .list-elt[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]   .col-desc-elt[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{margin-left:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]{height:30px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{color:#000000a6;height:100%;text-align:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-header[_ngcontent-%COMP%]   .col.col-desc[_ngcontent-%COMP%]{text-align:unset}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,0,0,.24)}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .list-products[_ngcontent-%COMP%]   .list-elt-contain[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:last-child{border-bottom:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:0 calc(41 * var(--res)) 1rem;border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .order-summary[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .reason[_ngcontent-%COMP%]{margin-bottom:10px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]{width:91%;padding:0 .5em;background:#ffffff;margin:1rem calc(41 * var(--res));border-radius:5px;border:1px solid #ebebeb;box-shadow:#d3d3d3 0 4px 16px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .h3-title[_ngcontent-%COMP%]{margin:2vh 0;color:#143c5d}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{width:50%;letter-spacing:1px;text-align:initial}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]{display:flex;align-items:flex-end;flex-direction:column}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .right-block[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:.5em;color:#000000de;font-size:59%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .info-deliver[_ngcontent-%COMP%]   .bill-info[_ngcontent-%COMP%]   .left-block[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;display:flex;overflow:hidden;color:#000000de;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-weight:600}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]{margin-top:calc(50 * var(--res));margin-bottom:calc(50 * var(--res));flex:1}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100%;width:100%;padding:calc(25 * var(--res));display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .slide-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .mWidth[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(8.3 * var(--resH));width:100px!important}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{padding:0;height:100%;box-shadow:none}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{height:100%;display:flex;justify-content:space-between;gap:2px}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column;justify-content:space-around}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(29 * var(--res))}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]   .sac[_ngcontent-%COMP%]{height:100%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:55%}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#000}ion-content[_ngcontent-%COMP%]   .order-history-page[_ngcontent-%COMP%]   .historic-bill-detail-container[_ngcontent-%COMP%]   .form--footer[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}"]})}}return r})()}];let B=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[_.iI.forChild(Y),_.iI]})}}return r})(),H=(()=>{class r{static{this.\u0275fac=function(o){return new(o||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[g.MD,m.YN,c.bv,b.G,p.h,B]})}}return r})()}}]);