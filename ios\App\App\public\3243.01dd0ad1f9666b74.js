"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3243],{33243:(l,a,i)=>{i.r(a),i.d(a,{Geolocation:()=>u,GeolocationWeb:()=>s});var o=i(73308),c=i(22126);class s extends c.E_{getCurrentPosition(e){return(0,o.A)(function*(){return new Promise((t,r)=>{navigator.geolocation.getCurrentPosition(n=>{t(n)},n=>{r(n)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0},e))})})()}watchPosition(e,t){return(0,o.A)(function*(){return`${navigator.geolocation.watchPosition(n=>{t(n)},n=>{t(null,n)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0,minimumUpdateInterval:5e3},e))}`})()}clearWatch(e){return(0,o.A)(function*(){navigator.geolocation.clearWatch(parseInt(e.id,10))})()}checkPermissions(){var e=this;return(0,o.A)(function*(){if(typeof navigator>"u"||!navigator.permissions)throw e.unavailable("Permissions API not available in this browser");const t=yield navigator.permissions.query({name:"geolocation"});return{location:t.state,coarseLocation:t.state}})()}requestPermissions(){var e=this;return(0,o.A)(function*(){throw e.unimplemented("Not implemented on web.")})()}}const u=new s}}]);