"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9622],{19622:(h,n,e)=>{e.r(n),e.d(n,{startStatusTap:()=>l});var r=e(73308),_=e(29814),o=e(98717),d=e(46184);const l=()=>{const s=window;s.addEventListener("statusTap",()=>{(0,_.f)(()=>{const a=document.elementFromPoint(s.innerWidth/2,s.innerHeight/2);if(!a)return;const t=(0,o.f)(a);t&&new Promise(P=>(0,d.c)(t,P)).then(()=>{(0,_.c)((0,r.A)(function*(){t.style.setProperty("--overflow","hidden"),yield(0,o.s)(t,300),t.style.removeProperty("--overflow")}))})})})}}}]);