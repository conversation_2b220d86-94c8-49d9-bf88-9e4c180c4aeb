"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7646],{77646:(ut,S,x)=>{x.r(S),x.d(S,{ion_datetime:()=>B,ion_picker:()=>H,ion_picker_column:()=>K});var C=x(73308),a=x(29814),O=x(89979),_=x(42673),G=x(7572),D=x(46184),v=x(14561),T=x(9404),W=x(35367),o=x(44522),P=x(97255),A=x(28775),z=x(95480);x(94706);const F=(t,e,i,n)=>!!(null===t.day||void 0!==n&&!n.includes(t.day)||e&&(0,o.i)(t,e)||i&&(0,o.b)(t,i)),I=(t,{minParts:e,maxParts:i})=>!!(((t,e,i)=>!!(e&&e.year>t||i&&i.year<t))(t.year,e,i)||e&&(0,o.i)(t,e)||i&&(0,o.b)(t,i)),B=class{constructor(t){(0,a.r)(this,t),this.ionCancel=(0,a.e)(this,"ionCancel",7),this.ionChange=(0,a.e)(this,"ionChange",7),this.ionFocus=(0,a.e)(this,"ionFocus",7),this.ionBlur=(0,a.e)(this,"ionBlur",7),this.ionStyle=(0,a.e)(this,"ionStyle",7),this.ionRender=(0,a.e)(this,"ionRender",7),this.inputId="ion-dt-"+nt++,this.prevPresentation=null,this.activePartsClone=[],this.showMonthAndYear=!1,this.activeParts=[],this.workingParts={month:5,day:28,year:2021,hour:13,minute:52,ampm:"pm"},this.isPresented=!1,this.isTimePopoverOpen=!1,this.color="primary",this.name=this.inputId,this.disabled=!1,this.readonly=!1,this.presentation="date-time",this.cancelText="Cancel",this.doneText="Done",this.clearText="Clear",this.locale="default",this.firstDayOfWeek=0,this.multiple=!1,this.showDefaultTitle=!1,this.showDefaultButtons=!1,this.showClearButton=!1,this.showDefaultTimeLabel=!0,this.size="fixed",this.preferWheel=!1,this.getActivePartsWithFallback=()=>{var e;const{defaultParts:i}=this;return null!==(e=this.getActivePart())&&void 0!==e?e:i},this.getActivePart=()=>{const{activePartsClone:e}=this;return Array.isArray(e)?e[0]:e},this.closeParentOverlay=()=>{const e=this.el.closest("ion-modal, ion-popover");e&&e.dismiss()},this.setWorkingParts=e=>{this.workingParts=Object.assign({},e)},this.setActiveParts=(e,i=!1)=>{const{multiple:n,minParts:r,maxParts:s,activePartsClone:d}=this,h=(0,o.v)(e,r,s);if(this.setWorkingParts(h),n){const l=Array.isArray(d)?d:[d];this.activeParts=i?l.filter(p=>!(0,o.c)(p,h)):[...l,h]}else this.activeParts=Object.assign({},h);null!==this.el.querySelector('[slot="buttons"]')||this.showDefaultButtons||this.confirm()},this.initializeKeyboardListeners=()=>{const e=this.calendarBodyRef;if(!e)return;const i=this.el.shadowRoot,n=e.querySelector(".calendar-month:nth-of-type(2)"),s=new MutationObserver(d=>{var h;null!==(h=d[0].oldValue)&&void 0!==h&&h.includes("ion-focused")||!e.classList.contains("ion-focused")||this.focusWorkingDay(n)});s.observe(e,{attributeFilter:["class"],attributeOldValue:!0}),this.destroyKeyboardMO=()=>{s?.disconnect()},e.addEventListener("keydown",d=>{const h=i.activeElement;if(!h||!h.classList.contains("calendar-day"))return;const c=(0,o.f)(h);let l;switch(d.key){case"ArrowDown":d.preventDefault(),l=(0,o.n)(c);break;case"ArrowUp":d.preventDefault(),l=(0,o.m)(c);break;case"ArrowRight":d.preventDefault(),l=(0,o.l)(c);break;case"ArrowLeft":d.preventDefault(),l=(0,o.k)(c);break;case"Home":d.preventDefault(),l=(0,o.j)(c);break;case"End":d.preventDefault(),l=(0,o.h)(c);break;case"PageUp":d.preventDefault(),l=d.shiftKey?(0,o.O)(c):(0,o.d)(c);break;case"PageDown":d.preventDefault(),l=d.shiftKey?(0,o.N)(c):(0,o.e)(c);break;default:return}F(l,this.minParts,this.maxParts)||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),l)),requestAnimationFrame(()=>this.focusWorkingDay(n)))})},this.focusWorkingDay=e=>{const i=e.querySelectorAll(".calendar-day-padding"),{day:n}=this.workingParts;if(null===n)return;const r=e.querySelector(`.calendar-day:nth-of-type(${i.length+n})`);r&&r.focus()},this.processMinParts=()=>{const{min:e,defaultParts:i}=this;this.minParts=void 0!==e?(0,o.p)(e,i):void 0},this.processMaxParts=()=>{const{max:e,defaultParts:i}=this;this.maxParts=void 0!==e?(0,o.o)(e,i):void 0},this.initializeCalendarListener=()=>{const e=this.calendarBodyRef;if(!e)return;const i=e.querySelectorAll(".calendar-month"),n=i[0],r=i[1],s=i[2],h="ios"===(0,_.b)(this)&&typeof navigator<"u"&&navigator.maxTouchPoints>1;(0,a.c)(()=>{e.scrollLeft=n.clientWidth*((0,T.i)(this.el)?-1:1);const c=u=>{const f=e.getBoundingClientRect(),b=this.el.shadowRoot.elementFromPoint(f.x+f.width/2,f.y+f.height/2);if(!b)return;const k=b.closest(".calendar-month");if(!k)return;const w=k.getBoundingClientRect();return Math.abs(w.x-f.x)>2?void 0:k===n?(0,o.d)(u):k===s?(0,o.e)(u):void 0},l=()=>{h&&(e.style.removeProperty("pointer-events"),m=!1);const u=c(this.workingParts);if(!u)return;const{month:f,day:y,year:b}=u;I({month:f,year:b,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})})||(e.style.setProperty("overflow","hidden"),(0,a.c)(()=>{this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:f,day:y,year:b})),e.scrollLeft=r.clientWidth*((0,T.i)(this.el)?-1:1),e.style.removeProperty("overflow")}))};let p,m=!1;const g=()=>{p&&clearTimeout(p),!m&&h&&(e.style.setProperty("pointer-events","none"),m=!0),p=setTimeout(l,50)};e.addEventListener("scroll",g),this.destroyCalendarListener=()=>{e.removeEventListener("scroll",g)}})},this.destroyInteractionListeners=()=>{const{destroyCalendarListener:e,destroyKeyboardMO:i}=this;void 0!==e&&e(),void 0!==i&&i()},this.processValue=e=>{const i=""!==e&&null!=e;let n=i?(0,o.q)(e):this.defaultParts;const{minParts:r,maxParts:s,multiple:d}=this;!d&&Array.isArray(e)&&(this.value=e[0],n=n[0]),i&&(0,o.w)(n,r,s);const h=Array.isArray(n)?n[0]:n,{month:c,day:l,year:p,hour:m,minute:g,tzOffset:u}=(0,o.P)(h,r,s),f=(0,o.Q)(m);this.setWorkingParts({month:c,day:l,year:p,hour:m,minute:g,tzOffset:u,ampm:f}),this.activeParts=i?Array.isArray(n)?[...n]:{month:c,day:l,year:p,hour:m,minute:g,tzOffset:u,ampm:f}:[]},this.onFocus=()=>{this.ionFocus.emit()},this.onBlur=()=>{this.ionBlur.emit()},this.hasValue=()=>null!=this.value&&""!==this.value,this.nextMonth=()=>{const e=this.calendarBodyRef;if(!e)return;const i=e.querySelector(".calendar-month:last-of-type");i&&e.scrollTo({top:0,left:2*i.offsetWidth*((0,T.i)(this.el)?-1:1),behavior:"smooth"})},this.prevMonth=()=>{const e=this.calendarBodyRef;!e||!e.querySelector(".calendar-month:first-of-type")||e.scrollTo({top:0,left:0,behavior:"smooth"})},this.toggleMonthAndYearView=()=>{this.showMonthAndYear=!this.showMonthAndYear}}disabledChanged(){this.emitStyle()}minChanged(){this.processMinParts()}maxChanged(){this.processMaxParts()}yearValuesChanged(){this.parsedYearValues=(0,o.r)(this.yearValues)}monthValuesChanged(){this.parsedMonthValues=(0,o.r)(this.monthValues)}dayValuesChanged(){this.parsedDayValues=(0,o.r)(this.dayValues)}hourValuesChanged(){this.parsedHourValues=(0,o.r)(this.hourValues)}minuteValuesChanged(){this.parsedMinuteValues=(0,o.r)(this.minuteValues)}activePartsChanged(){this.activePartsClone=this.activeParts}valueChanged(){const{value:t,minParts:e,maxParts:i,workingParts:n,multiple:r}=this;if(this.hasValue()){if(!r&&Array.isArray(t))return void(this.value=t[0]);const s=(0,o.q)(t);if(s)if((0,o.w)(s,e,i),Array.isArray(s))this.activePartsClone=[...s];else{const{month:d,day:h,year:c,hour:l,minute:p}=s,m=null!=l?l>=12?"pm":"am":void 0;this.activePartsClone=Object.assign(Object.assign({},this.activeParts),{month:d,day:h,year:c,hour:l,minute:p,ampm:m}),this.setWorkingParts(Object.assign(Object.assign({},n),{ampm:m}))}else(0,v.p)(`Unable to parse date string: ${t}. Please provide a valid ISO 8601 datetime string.`)}this.emitStyle(),this.ionChange.emit({value:t})}confirm(){var t=this;return(0,C.A)(function*(e=!1){const{isCalendarPicker:i,activeParts:n}=t;if(void 0!==n||!i){const r=Array.isArray(n);if(r&&0===n.length)t.value=void 0;else{if(r){const s=(0,o.s)(n).map(d=>new Date(d));for(let d=0;d<s.length;d++)n[d].tzOffset=-1*s[d].getTimezoneOffset()}else{const s=new Date((0,o.s)(n));n.tzOffset=-1*s.getTimezoneOffset()}t.value=(0,o.s)(n)}}e&&t.closeParentOverlay()}).apply(this,arguments)}reset(t){var e=this;return(0,C.A)(function*(){e.processValue(t)})()}cancel(){var t=this;return(0,C.A)(function*(e=!1){t.ionCancel.emit(),e&&t.closeParentOverlay()}).apply(this,arguments)}get isCalendarPicker(){const{presentation:t}=this;return"date"===t||"date-time"===t||"time-date"===t}connectedCallback(){this.clearFocusVisible=(0,G.startFocusVisible)(this.el).destroy}disconnectedCallback(){this.clearFocusVisible&&(this.clearFocusVisible(),this.clearFocusVisible=void 0)}initializeListeners(){this.initializeCalendarListener(),this.initializeKeyboardListeners()}componentDidLoad(){const e=new IntersectionObserver(s=>{s[0].isIntersecting&&(this.initializeListeners(),(0,a.c)(()=>{this.el.classList.add("datetime-ready")}))},{threshold:.01});(0,D.r)(()=>e?.observe(this.el));const n=new IntersectionObserver(s=>{s[0].isIntersecting||(this.destroyInteractionListeners(),this.showMonthAndYear=!1,(0,a.c)(()=>{this.el.classList.remove("datetime-ready")}))},{threshold:0});(0,D.r)(()=>n?.observe(this.el));const r=(0,D.g)(this.el);r.addEventListener("ionFocus",s=>s.stopPropagation()),r.addEventListener("ionBlur",s=>s.stopPropagation())}componentDidRender(){const{presentation:t,prevPresentation:e,calendarBodyRef:i,minParts:n,preferWheel:r}=this,s=!r&&["date-time","time-date","date"].includes(t);if(void 0!==n&&s&&i){const d=i.querySelector(".calendar-month:nth-of-type(1)");d&&(i.scrollLeft=d.clientWidth*((0,T.i)(this.el)?-1:1))}null!==e?t!==e&&(this.prevPresentation=t,this.destroyInteractionListeners(),this.initializeListeners(),this.showMonthAndYear=!1,(0,D.r)(()=>{this.ionRender.emit()})):this.prevPresentation=t}componentWillLoad(){const{el:t,highlightedDates:e,multiple:i,presentation:n,preferWheel:r}=this;i&&("date"!==n&&(0,v.p)('Multiple date selection is only supported for presentation="date".',t),r&&(0,v.p)('Multiple date selection is not supported with preferWheel="true".',t)),void 0!==e&&("date"!==n&&"date-time"!==n&&"time-date"!==n&&(0,v.p)("The highlightedDates property is only supported with the date, date-time, and time-date presentations.",t),r&&(0,v.p)('The highlightedDates property is not supported with preferWheel="true".',t)),this.processMinParts(),this.processMaxParts();const s=this.parsedHourValues=(0,o.r)(this.hourValues),d=this.parsedMinuteValues=(0,o.r)(this.minuteValues),h=this.parsedMonthValues=(0,o.r)(this.monthValues),c=this.parsedYearValues=(0,o.r)(this.yearValues),l=this.parsedDayValues=(0,o.r)(this.dayValues),p=this.todayParts=(0,o.q)((0,o.t)());this.defaultParts=(0,o.u)(p,h,l,c,s,d),this.processValue(this.value),this.emitStyle()}emitStyle(){this.ionStyle.emit({interactive:!0,datetime:!0,"interactive-disabled":this.disabled})}renderFooter(){const{showDefaultButtons:t,showClearButton:e}=this;if(null===this.el.querySelector('[slot="buttons"]')&&!t&&!e)return;const n=()=>{this.reset(),this.value=void 0};return(0,a.h)("div",{class:"datetime-footer"},(0,a.h)("div",{class:"datetime-buttons"},(0,a.h)("div",{class:{"datetime-action-buttons":!0,"has-clear-button":this.showClearButton}},(0,a.h)("slot",{name:"buttons"},(0,a.h)("ion-buttons",null,t&&(0,a.h)("ion-button",{id:"cancel-button",color:this.color,onClick:()=>this.cancel(!0)},this.cancelText),(0,a.h)("div",null,e&&(0,a.h)("ion-button",{id:"clear-button",color:this.color,onClick:()=>n()},this.clearText),t&&(0,a.h)("ion-button",{id:"confirm-button",color:this.color,onClick:()=>this.confirm(!0)},this.doneText)))))))}renderWheelPicker(t=this.presentation){const e="time-date"===t?[this.renderTimePickerColumns(t),this.renderDatePickerColumns(t)]:[this.renderDatePickerColumns(t),this.renderTimePickerColumns(t)];return(0,a.h)("ion-picker-internal",null,e)}renderDatePickerColumns(t){return"date-time"===t||"time-date"===t?this.renderCombinedDatePickerColumn():this.renderIndividualDatePickerColumns(t)}renderCombinedDatePickerColumn(){const{defaultParts:t,workingParts:e,locale:i,minParts:n,maxParts:r,todayParts:s,isDateEnabled:d}=this,h=this.getActivePartsWithFallback(),c=(0,o.I)(e),l=c[c.length-1];c[0].day=1,l.day=(0,o.x)(l.month,l.year);const p=void 0!==n&&(0,o.b)(n,c[0])?n:c[0],m=void 0!==r&&(0,o.i)(r,l)?r:l,g=(0,o.y)(i,s,p,m,this.parsedDayValues,this.parsedMonthValues);let u=g.items;const f=g.parts;return d&&(u=u.map((b,k)=>{const w=f[k];let M;try{M=!d((0,o.s)(w))}catch(E){(0,v.b)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",E)}return Object.assign(Object.assign({},b),{disabled:M})})),(0,a.h)("ion-picker-column-internal",{class:"date-column",color:this.color,items:u,value:null!==e.day?`${e.year}-${e.month}-${e.day}`:`${t.year}-${t.month}-${t.day}`,onIonChange:b=>{this.destroyCalendarListener&&this.destroyCalendarListener();const{value:k}=b.detail,w=f.find(({month:M,day:E,year:V})=>k===`${V}-${M}-${E}`);this.setWorkingParts(Object.assign(Object.assign({},e),w)),this.setActiveParts(Object.assign(Object.assign({},h),w)),this.initializeCalendarListener(),b.stopPropagation()}})}renderIndividualDatePickerColumns(t){const{workingParts:e,isDateEnabled:i}=this,r="year"!==t&&"time"!==t?(0,o.z)(this.locale,e,this.minParts,this.maxParts,this.parsedMonthValues):[];let d="date"===t?(0,o.A)(this.locale,e,this.minParts,this.maxParts,this.parsedDayValues):[];i&&(d=d.map(m=>{const{value:g}=m,u="string"==typeof g?parseInt(g):g,f={month:e.month,day:u,year:e.year};let y;try{y=!i((0,o.s)(f))}catch(b){(0,v.b)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",b)}return Object.assign(Object.assign({},m),{disabled:y})}));const c="month"!==t&&"time"!==t?(0,o.B)(this.locale,this.defaultParts,this.minParts,this.maxParts,this.parsedYearValues):[];let p=[];return p=(0,o.C)(this.locale,{month:"numeric",day:"numeric"})?[this.renderMonthPickerColumn(r),this.renderDayPickerColumn(d),this.renderYearPickerColumn(c)]:[this.renderDayPickerColumn(d),this.renderMonthPickerColumn(r),this.renderYearPickerColumn(c)],p}renderDayPickerColumn(t){var e;if(0===t.length)return[];const{workingParts:i}=this,n=this.getActivePartsWithFallback();return(0,a.h)("ion-picker-column-internal",{class:"day-column",color:this.color,items:t,value:null!==(e=null!==i.day?i.day:this.defaultParts.day)&&void 0!==e?e:void 0,onIonChange:r=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},i),{day:r.detail.value})),this.setActiveParts(Object.assign(Object.assign({},n),{day:r.detail.value})),this.initializeCalendarListener(),r.stopPropagation()}})}renderMonthPickerColumn(t){if(0===t.length)return[];const{workingParts:e}=this,i=this.getActivePartsWithFallback();return(0,a.h)("ion-picker-column-internal",{class:"month-column",color:this.color,items:t,value:e.month,onIonChange:n=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},e),{month:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},i),{month:n.detail.value})),this.initializeCalendarListener(),n.stopPropagation()}})}renderYearPickerColumn(t){if(0===t.length)return[];const{workingParts:e}=this,i=this.getActivePartsWithFallback();return(0,a.h)("ion-picker-column-internal",{class:"year-column",color:this.color,items:t,value:e.year,onIonChange:n=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},e),{year:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},i),{year:n.detail.value})),this.initializeCalendarListener(),n.stopPropagation()}})}renderTimePickerColumns(t){if(["date","month","month-year","year"].includes(t))return[];const i=void 0!==this.getActivePart(),{hoursData:n,minutesData:r,dayPeriodData:s}=(0,o.D)(this.locale,this.workingParts,this.hourCycle,i?this.minParts:void 0,i?this.maxParts:void 0,this.parsedHourValues,this.parsedMinuteValues);return[this.renderHourPickerColumn(n),this.renderMinutePickerColumn(r),this.renderDayPeriodPickerColumn(s)]}renderHourPickerColumn(t){const{workingParts:e}=this;if(0===t.length)return[];const i=this.getActivePartsWithFallback();return(0,a.h)("ion-picker-column-internal",{color:this.color,value:i.hour,items:t,numericInput:!0,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},e),{hour:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},i),{hour:n.detail.value})),n.stopPropagation()}})}renderMinutePickerColumn(t){const{workingParts:e}=this;if(0===t.length)return[];const i=this.getActivePartsWithFallback();return(0,a.h)("ion-picker-column-internal",{color:this.color,value:i.minute,items:t,numericInput:!0,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},e),{minute:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},i),{minute:n.detail.value})),n.stopPropagation()}})}renderDayPeriodPickerColumn(t){const{workingParts:e}=this;if(0===t.length)return[];const i=this.getActivePartsWithFallback(),n=(0,o.E)(this.locale);return(0,a.h)("ion-picker-column-internal",{style:n?{order:"-1"}:{},color:this.color,value:i.ampm,items:t,onIonChange:r=>{const s=(0,o.R)(e,r.detail.value);this.setWorkingParts(Object.assign(Object.assign({},e),{ampm:r.detail.value,hour:s})),this.setActiveParts(Object.assign(Object.assign({},i),{ampm:r.detail.value,hour:s})),r.stopPropagation()}})}renderWheelView(t){const{locale:e}=this,n=(0,o.C)(e)?"month-first":"year-first";return(0,a.h)("div",{class:{[`wheel-order-${n}`]:!0}},this.renderWheelPicker(t))}renderCalendarHeader(t){const e="ios"===t?O.l:O.p,i="ios"===t?O.o:O.q,n=((t,e,i)=>{const n=Object.assign(Object.assign({},(0,o.d)(this.workingParts)),{day:null});return I(n,{minParts:e,maxParts:i})})(0,this.minParts,this.maxParts),r=((t,e)=>{const i=Object.assign(Object.assign({},(0,o.e)(this.workingParts)),{day:null});return I(i,{maxParts:e})})(0,this.maxParts),s=this.el.getAttribute("dir")||void 0;return(0,a.h)("div",{class:"calendar-header"},(0,a.h)("div",{class:"calendar-action-buttons"},(0,a.h)("div",{class:"calendar-month-year"},(0,a.h)("ion-item",{button:!0,detail:!1,lines:"none",onClick:()=>this.toggleMonthAndYearView()},(0,a.h)("ion-label",null,(0,o.G)(this.locale,this.workingParts),(0,a.h)("ion-icon",{"aria-hidden":"true",icon:this.showMonthAndYear?e:i,lazy:!1,flipRtl:!0})))),(0,a.h)("div",{class:"calendar-next-prev"},(0,a.h)("ion-buttons",null,(0,a.h)("ion-button",{"aria-label":"previous month",disabled:n,onClick:()=>this.prevMonth()},(0,a.h)("ion-icon",{dir:s,"aria-hidden":"true",slot:"icon-only",icon:O.c,lazy:!1,flipRtl:!0})),(0,a.h)("ion-button",{"aria-label":"next month",disabled:r,onClick:()=>this.nextMonth()},(0,a.h)("ion-icon",{dir:s,"aria-hidden":"true",slot:"icon-only",icon:O.o,lazy:!1,flipRtl:!0}))))),(0,a.h)("div",{class:"calendar-days-of-week","aria-hidden":"true"},(0,o.F)(this.locale,t,this.firstDayOfWeek%7).map(d=>(0,a.h)("div",{class:"day-of-week"},d))))}renderMonth(t,e){const i=void 0===this.parsedYearValues||this.parsedYearValues.includes(e),n=void 0===this.parsedMonthValues||this.parsedMonthValues.includes(t),r=!i||!n,s=I({month:t,year:e,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})}),d=this.workingParts.month===t&&this.workingParts.year===e,h=this.getActivePartsWithFallback();return(0,a.h)("div",{"aria-hidden":d?null:"true",class:{"calendar-month":!0,"calendar-month-disabled":!d&&s}},(0,a.h)("div",{class:"calendar-month-grid"},(0,o.H)(t,e,this.firstDayOfWeek%7).map((c,l)=>{const{day:p,dayOfWeek:m}=c,{el:g,highlightedDates:u,isDateEnabled:f,multiple:y}=this,b={month:t,day:p,year:e},k=null===p,{isActive:w,isToday:M,ariaLabel:E,ariaSelected:V,disabled:ht,text:pt}=((t,e,i,n,r,s,d)=>{const c=void 0!==(Array.isArray(i)?i:[i]).find(m=>(0,o.c)(e,m)),l=(0,o.c)(e,n);return{disabled:F(e,r,s,d),isActive:c,isToday:l,ariaSelected:c?"true":null,ariaLabel:(0,o.g)(t,l,e),text:null!=e.day?(0,o.a)(t,e):null}})(this.locale,b,this.activePartsClone,this.todayParts,this.minParts,this.maxParts,this.parsedDayValues),q=(0,o.s)(b);let j,L=r||ht;if(!L&&void 0!==f)try{L=!f(q)}catch(mt){(0,v.b)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",g,mt)}return void 0!==u&&!w&&null!==p&&(j=((t,e,i)=>{if(Array.isArray(t)){const n=e.split("T")[0],r=t.find(s=>s.date===n);if(r)return{textColor:r.textColor,backgroundColor:r.backgroundColor}}else try{return t(e)}catch(n){(0,v.b)("Exception thrown from provided `highlightedDates` callback. Please check your function and try again.",i,n)}})(u,q,g)),(0,a.h)("button",{tabindex:"-1","data-day":p,"data-month":t,"data-year":e,"data-index":l,"data-day-of-week":m,disabled:L,class:{"calendar-day-padding":k,"calendar-day":!0,"calendar-day-active":w,"calendar-day-today":M},style:j&&{color:j.textColor},"aria-hidden":k?"true":null,"aria-selected":V,"aria-label":E,onClick:()=>{k||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:t,day:p,year:e})),y?this.setActiveParts({month:t,day:p,year:e},w):this.setActiveParts(Object.assign(Object.assign({},h),{month:t,day:p,year:e})))}},(0,a.h)("div",{class:"calendar-day-highlight",style:{backgroundColor:j?.backgroundColor}}),pt)})))}renderCalendarBody(){return(0,a.h)("div",{class:"calendar-body ion-focusable",ref:t=>this.calendarBodyRef=t,tabindex:"0"},(0,o.I)(this.workingParts).map(({month:t,year:e})=>this.renderMonth(t,e)))}renderCalendar(t){return(0,a.h)("div",{class:"datetime-calendar",key:"datetime-calendar"},this.renderCalendarHeader(t),this.renderCalendarBody())}renderTimeLabel(){if(null!==this.el.querySelector('[slot="time-label"]')||this.showDefaultTimeLabel)return(0,a.h)("slot",{name:"time-label"},"Time")}renderTimeOverlay(){var t=this;const e=(0,o.J)(this.locale,this.hourCycle),i=this.getActivePartsWithFallback();return[(0,a.h)("div",{class:"time-header"},this.renderTimeLabel()),(0,a.h)("button",{class:{"time-body":!0,"time-body-active":this.isTimePopoverOpen},"aria-expanded":"false","aria-haspopup":"true",onClick:(n=(0,C.A)(function*(r){const{popoverRef:s}=t;s&&(t.isTimePopoverOpen=!0,s.present(new CustomEvent("ionShadowTarget",{detail:{ionShadowTarget:r.target}})),yield s.onWillDismiss(),t.isTimePopoverOpen=!1)}),function(s){return n.apply(this,arguments)})},(0,o.K)(this.locale,i,e)),(0,a.h)("ion-popover",{alignment:"center",translucent:!0,overlayIndex:1,arrow:!1,onWillPresent:n=>{n.target.querySelectorAll("ion-picker-column-internal").forEach(s=>s.scrollActiveItemIntoView())},style:{"--offset-y":"-10px","--min-width":"fit-content"},keyboardEvents:!0,ref:n=>this.popoverRef=n},this.renderWheelPicker("time"))];var n}getHeaderSelectedDateText(){const{activeParts:t,multiple:e,titleSelectedDatesFormatter:i}=this,n=Array.isArray(t);let r;if(e&&n&&1!==t.length){if(r=`${t.length} days`,void 0!==i)try{r=i((0,o.s)(t))}catch(s){(0,v.b)("Exception in provided `titleSelectedDatesFormatter`: ",s)}}else r=(0,o.L)(this.locale,this.getActivePartsWithFallback());return r}renderHeader(t=!0){if(null!==this.el.querySelector('[slot="title"]')||this.showDefaultTitle)return(0,a.h)("div",{class:"datetime-header"},(0,a.h)("div",{class:"datetime-title"},(0,a.h)("slot",{name:"title"},"Select Date")),t&&(0,a.h)("div",{class:"datetime-selected-date"},this.getHeaderSelectedDateText()))}renderTime(){const{presentation:t}=this;return(0,a.h)("div",{class:"datetime-time"},"time"===t?this.renderWheelPicker():this.renderTimeOverlay())}renderCalendarViewMonthYearPicker(){return(0,a.h)("div",{class:"datetime-year"},this.renderWheelView("month-year"))}renderDatetime(t){const{presentation:e,preferWheel:i}=this;if(i&&("date"===e||"date-time"===e||"time-date"===e))return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];switch(e){case"date-time":return[this.renderHeader(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderTime(),this.renderFooter()];case"time-date":return[this.renderHeader(),this.renderTime(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderFooter()];case"time":return[this.renderHeader(!1),this.renderTime(),this.renderFooter()];case"month":case"month-year":case"year":return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];default:return[this.renderHeader(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderFooter()]}}render(){const{name:t,value:e,disabled:i,el:n,color:r,isPresented:s,readonly:d,showMonthAndYear:h,preferWheel:c,presentation:l,size:p}=this,m=(0,_.b)(this),g="year"===l||"month"===l||"month-year"===l,u=h||g,f=h&&!g,y="date"===l||"date-time"===l||"time-date"===l,b=y&&c,k=y&&!c;return(0,D.e)(!0,n,t,(0,o.M)(e),i),(0,a.h)(a.H,{"aria-disabled":i?"true":null,onFocus:this.onFocus,onBlur:this.onBlur,class:Object.assign({},(0,W.c)(r,{[m]:!0,"datetime-presented":s,"datetime-readonly":d,"datetime-disabled":i,"show-month-and-year":u,"month-year-picker-open":f,[`datetime-presentation-${l}`]:!0,[`datetime-size-${p}`]:!0,"datetime-prefer-wheel":b,"datetime-grid":k}))},this.renderDatetime(m))}get el(){return(0,a.i)(this)}static get watchers(){return{disabled:["disabledChanged"],min:["minChanged"],max:["maxChanged"],yearValues:["yearValuesChanged"],monthValues:["monthValuesChanged"],dayValues:["dayValuesChanged"],hourValues:["hourValuesChanged"],minuteValues:["minuteValuesChanged"],activeParts:["activePartsChanged"],value:["valueChanged"]}}};let nt=0;B.style={ios:":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px;position:absolute;visibility:hidden;pointer-events:none}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-readonly),:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled){opacity:0.4}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .calendar-action-buttons ion-item ion-icon{padding-left:4px;padding-right:0;padding-top:0;padding-bottom:0}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-action-buttons ion-item ion-icon{padding-left:unset;padding-right:unset;-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0}}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day{padding-left:0px;padding-right:0px;padding-top:0px;padding-bottom:0px;margin-left:0px;margin-right:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-day{padding-left:unset;padding-right:unset;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px}}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-day{margin-left:unset;margin-right:unset;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px}}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day-highlight{border-radius:32px;padding-left:4px;padding-right:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:32px;height:32px;z-index:-1}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.calendar-day-highlight{padding-left:unset;padding-right:unset;-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px}}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;padding-left:12px;padding-right:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .time-body{padding-left:unset;padding-right:unset;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px}}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-light, #ffffff);--background-rgb:var(--ion-color-light-rgb);--title-color:var(--ion-color-step-600, #666666)}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{padding-left:16px;padding-right:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, #cccccc);font-size:14px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .datetime-header{padding-left:unset;padding-right:unset;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}:host .calendar-action-buttons ion-item{--padding-start:16px;--background-hover:transparent;--background-activated:transparent;font-size:16px;font-weight:600}:host .calendar-action-buttons ion-item ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{padding-left:8px;padding-right:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, #b3b3b3);font-size:12px;font-weight:600;line-height:24px;text-transform:uppercase}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-days-of-week{padding-left:unset;padding-right:unset;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px}}:host .calendar-body .calendar-month .calendar-month-grid{padding-left:8px;padding-right:8px;padding-top:8px;padding-bottom:8px;height:calc(100% - 16px)}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-body .calendar-month .calendar-month-grid{padding-left:unset;padding-right:unset;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px}}:host .calendar-day{font-size:20px}.calendar-day:focus .calendar-day-highlight,.calendar-day.calendar-day-active .calendar-day-highlight{opacity:0.2}.calendar-day.calendar-day-active .calendar-day-highlight{background:var(--ion-color-base)}.calendar-day:focus .calendar-day-highlight{background:var(--ion-color-base) !important}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-today.calendar-day-active .calendar-day-highlight{background:var(--ion-color-base);opacity:1}:host .datetime-time{padding-left:16px;padding-right:16px;padding-top:8px;padding-bottom:16px;font-size:16px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .datetime-time{padding-left:unset;padding-right:unset;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{padding-left:8px;padding-right:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, #cccccc)}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .datetime-buttons{padding-left:unset;padding-right:unset;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px}}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}",md:":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px;position:absolute;visibility:hidden;pointer-events:none}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-readonly),:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled){opacity:0.4}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .calendar-action-buttons ion-item ion-icon{padding-left:4px;padding-right:0;padding-top:0;padding-bottom:0}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-action-buttons ion-item ion-icon{padding-left:unset;padding-right:unset;-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0}}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day{padding-left:0px;padding-right:0px;padding-top:0px;padding-bottom:0px;margin-left:0px;margin-right:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-day{padding-left:unset;padding-right:unset;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px}}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-day{margin-left:unset;margin-right:unset;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px}}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day-highlight{border-radius:32px;padding-left:4px;padding-right:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:32px;height:32px;z-index:-1}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.calendar-day-highlight{padding-left:unset;padding-right:unset;-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px}}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;padding-left:12px;padding-right:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .time-body{padding-left:unset;padding-right:unset;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px}}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-step-100, #ffffff);--title-color:var(--ion-color-contrast)}:host .datetime-header{padding-left:20px;padding-right:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .datetime-header{padding-left:unset;padding-right:unset;-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px}}:host .datetime-header .datetime-title{font-size:12px;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:34px}:host .datetime-calendar .calendar-action-buttons ion-item{--padding-start:20px}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{color:var(--ion-color-step-650, #595959)}:host .calendar-days-of-week{padding-left:10px;padding-right:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, gray);font-size:14px;line-height:36px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-days-of-week{padding-left:unset;padding-right:unset;-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px}}:host .calendar-body .calendar-month .calendar-month-grid{padding-left:10px;padding-right:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-body .calendar-month .calendar-month-grid{padding-left:unset;padding-right:unset;-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px}}:host .calendar-day{padding-left:0px;padding-right:0;padding-top:13px;padding-bottom:13px;font-size:14px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .calendar-day{padding-left:unset;padding-right:unset;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0;padding-inline-end:0}}.calendar-day:focus .calendar-day-highlight{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}.calendar-day.calendar-day-today .calendar-day-highlight{border:1px solid var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active .calendar-day-highlight{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .datetime-time{padding-left:16px;padding-right:16px;padding-top:8px;padding-bottom:8px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .datetime-time{padding-left:unset;padding-right:unset;-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}}:host .time-header{color:var(--ion-color-step-650, #595959)}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{padding-left:10px;padding-right:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){:host .datetime-buttons{padding-left:unset;padding-right:unset;-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px}}:host .datetime-view-buttons ion-button{color:var(--ion-color-step-800, #333333)}"};const R=t=>{const e=(0,A.c)(),i=(0,A.c)(),n=(0,A.c)();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),n.addElement(t.querySelector(".picker-wrapper")).fromTo("transform","translateY(100%)","translateY(0%)"),e.addElement(t).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,n])},Y=t=>{const e=(0,A.c)(),i=(0,A.c)(),n=(0,A.c)();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",.01),n.addElement(t.querySelector(".picker-wrapper")).fromTo("transform","translateY(0%)","translateY(100%)"),e.addElement(t).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,n])},H=class{constructor(t){(0,a.r)(this,t),this.didPresent=(0,a.e)(this,"ionPickerDidPresent",7),this.willPresent=(0,a.e)(this,"ionPickerWillPresent",7),this.willDismiss=(0,a.e)(this,"ionPickerWillDismiss",7),this.didDismiss=(0,a.e)(this,"ionPickerDidDismiss",7),this.presented=!1,this.keyboardClose=!0,this.buttons=[],this.columns=[],this.duration=0,this.showBackdrop=!0,this.backdropDismiss=!0,this.animated=!0,this.onBackdropTap=()=>{this.dismiss(void 0,P.B)},this.dispatchCancelHandler=e=>{if((0,P.i)(e.detail.role)){const n=this.buttons.find(r=>"cancel"===r.role);this.callButtonHandler(n)}}}connectedCallback(){(0,P.e)(this.el)}present(){var t=this;return(0,C.A)(function*(){yield(0,P.d)(t,"pickerEnter",R,R,void 0),t.duration>0&&(t.durationTimeout=setTimeout(()=>t.dismiss(),t.duration))})()}dismiss(t,e){return this.durationTimeout&&clearTimeout(this.durationTimeout),(0,P.f)(this,t,e,"pickerLeave",Y,Y)}onDidDismiss(){return(0,P.g)(this.el,"ionPickerDidDismiss")}onWillDismiss(){return(0,P.g)(this.el,"ionPickerWillDismiss")}getColumn(t){return Promise.resolve(this.columns.find(e=>e.name===t))}buttonClick(t){var e=this;return(0,C.A)(function*(){const i=t.role;return(0,P.i)(i)?e.dismiss(void 0,i):(yield e.callButtonHandler(t))?e.dismiss(e.getSelected(),t.role):Promise.resolve()})()}callButtonHandler(t){var e=this;return(0,C.A)(function*(){return!(t&&!1===(yield(0,P.s)(t.handler,e.getSelected())))})()}getSelected(){const t={};return this.columns.forEach((e,i)=>{const n=void 0!==e.selectedIndex?e.options[e.selectedIndex]:void 0;t[e.name]={text:n?n.text:void 0,value:n?n.value:void 0,columnIndex:i}}),t}render(){const{htmlAttributes:t}=this,e=(0,_.b)(this);return(0,a.h)(a.H,Object.assign({"aria-modal":"true",tabindex:"-1"},t,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[e]:!0,[`picker-${e}`]:!0,"overlay-hidden":!0},(0,W.g)(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonPickerWillDismiss:this.dispatchCancelHandler}),(0,a.h)("ion-backdrop",{visible:this.showBackdrop,tappable:this.backdropDismiss}),(0,a.h)("div",{tabindex:"0"}),(0,a.h)("div",{class:"picker-wrapper ion-overlay-wrapper",role:"dialog"},(0,a.h)("div",{class:"picker-toolbar"},this.buttons.map(i=>(0,a.h)("div",{class:ot(i)},(0,a.h)("button",{type:"button",onClick:()=>this.buttonClick(i),class:st(i)},i.text)))),(0,a.h)("div",{class:"picker-columns"},(0,a.h)("div",{class:"picker-above-highlight"}),this.presented&&this.columns.map(i=>(0,a.h)("ion-picker-column",{col:i})),(0,a.h)("div",{class:"picker-below-highlight"}))),(0,a.h)("div",{tabindex:"0"}))}get el(){return(0,a.i)(this)}},ot=t=>({[`picker-toolbar-${t.role}`]:void 0!==t.role,"picker-toolbar-button":!0}),st=t=>Object.assign({"picker-button":!0,"ion-activatable":!0},(0,W.g)(t.cssClass));H.style={ios:".sc-ion-picker-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}[dir=rtl].sc-ion-picker-ios-h,[dir=rtl] .sc-ion-picker-ios-h{left:unset;right:unset;right:0}.overlay-hidden.sc-ion-picker-ios-h{display:none}.picker-wrapper.sc-ion-picker-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;margin-left:auto;margin-right:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.picker-wrapper.sc-ion-picker-ios{margin-left:unset;margin-right:unset;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}}.picker-toolbar.sc-ion-picker-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-ios:active,.picker-button.sc-ion-picker-ios:focus{outline:none}.picker-columns.sc-ion-picker-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;direction:ltr;overflow:hidden}.picker-above-highlight.sc-ion-picker-ios,.picker-below-highlight.sc-ion-picker-ios{display:none;pointer-events:none}.sc-ion-picker-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-ios:last-child .picker-button.sc-ion-picker-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-ios,.picker-button.ion-activated.sc-ion-picker-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:1em;padding-right:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:16px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.picker-button.sc-ion-picker-ios,.picker-button.ion-activated.sc-ion-picker-ios{padding-left:unset;padding-right:unset;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em}}.picker-columns.sc-ion-picker-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-ios{left:0;top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}[dir=rtl].sc-ion-picker-ios .picker-above-highlight.sc-ion-picker-ios,[dir=rtl].sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}.picker-below-highlight.sc-ion-picker-ios{left:0;top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}[dir=rtl].sc-ion-picker-ios .picker-below-highlight.sc-ion-picker-ios,[dir=rtl].sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}",md:".sc-ion-picker-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}[dir=rtl].sc-ion-picker-md-h,[dir=rtl] .sc-ion-picker-md-h{left:unset;right:unset;right:0}.overlay-hidden.sc-ion-picker-md-h{display:none}.picker-wrapper.sc-ion-picker-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;margin-left:auto;margin-right:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.picker-wrapper.sc-ion-picker-md{margin-left:unset;margin-right:unset;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}}.picker-toolbar.sc-ion-picker-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-md:active,.picker-button.sc-ion-picker-md:focus{outline:none}.picker-columns.sc-ion-picker-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;direction:ltr;overflow:hidden}.picker-above-highlight.sc-ion-picker-md,.picker-below-highlight.sc-ion-picker-md{display:none;pointer-events:none}.sc-ion-picker-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-md,.picker-button.ion-activated.sc-ion-picker-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:1.1em;padding-right:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.picker-button.sc-ion-picker-md,.picker-button.ion-activated.sc-ion-picker-md{padding-left:unset;padding-right:unset;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em}}.picker-columns.sc-ion-picker-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-md{left:0;top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}[dir=rtl].sc-ion-picker-md .picker-above-highlight.sc-ion-picker-md,[dir=rtl].sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}.picker-below-highlight.sc-ion-picker-md{left:0;top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}[dir=rtl].sc-ion-picker-md .picker-below-highlight.sc-ion-picker-md,[dir=rtl].sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}"};const K=class{constructor(t){(0,a.r)(this,t),this.ionPickerColChange=(0,a.e)(this,"ionPickerColChange",7),this.optHeight=0,this.rotateFactor=0,this.scaleFactor=1,this.velocity=0,this.y=0,this.noAnimate=!0}colChanged(){this.refresh()}connectedCallback(){var t=this;return(0,C.A)(function*(){let e=0,i=.81;"ios"===(0,_.b)(t)&&(e=-.46,i=1),t.rotateFactor=e,t.scaleFactor=i,t.gesture=(yield Promise.resolve().then(x.bind(x,53847))).createGesture({el:t.el,gestureName:"picker-swipe",gesturePriority:100,threshold:0,passive:!1,onStart:r=>t.onStart(r),onMove:r=>t.onMove(r),onEnd:r=>t.onEnd(r)}),t.gesture.enable(),t.tmrId=setTimeout(()=>{t.noAnimate=!1,t.refresh(!0)},250)})()}componentDidLoad(){const t=this.optsEl;t&&(this.optHeight=t.firstElementChild?t.firstElementChild.clientHeight:0),this.refresh()}disconnectedCallback(){void 0!==this.rafId&&cancelAnimationFrame(this.rafId),this.tmrId&&clearTimeout(this.tmrId),this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}emitColChange(){this.ionPickerColChange.emit(this.col)}setSelected(t,e){const i=t>-1?-t*this.optHeight:0;this.velocity=0,void 0!==this.rafId&&cancelAnimationFrame(this.rafId),this.update(i,e,!0),this.emitColChange()}update(t,e,i){if(!this.optsEl)return;let n=0,r=0;const{col:s,rotateFactor:d}=this,h=s.selectedIndex=this.indexForY(-t),c=0===e?"":e+"ms",l=`scale(${this.scaleFactor})`,p=this.optsEl.children;for(let m=0;m<p.length;m++){const g=p[m],u=s.options[m],f=m*this.optHeight+t;let y="";if(0!==d){const k=f*d;Math.abs(k)<=90?(n=0,r=90,y=`rotateX(${k}deg) `):n=-9999}else r=0,n=f;const b=h===m;y+=`translate3d(0px,${n}px,${r}px) `,1!==this.scaleFactor&&!b&&(y+=l),this.noAnimate?(u.duration=0,g.style.transitionDuration=""):e!==u.duration&&(u.duration=e,g.style.transitionDuration=c),y!==u.transform&&(u.transform=y),g.style.transform=y,b!==u.selected&&(u.selected=b,b?g.classList.add($):g.classList.remove($))}this.col.prevSelected=h,i&&(this.y=t),this.lastIndex!==h&&((0,z.b)(),this.lastIndex=h)}decelerate(){if(0!==this.velocity){this.velocity*=ct,this.velocity=this.velocity>0?Math.max(this.velocity,1):Math.min(this.velocity,-1);let t=this.y+this.velocity;t>this.minY?(t=this.minY,this.velocity=0):t<this.maxY&&(t=this.maxY,this.velocity=0),this.update(t,0,!0),Math.round(t)%this.optHeight!=0||Math.abs(this.velocity)>1?this.rafId=requestAnimationFrame(()=>this.decelerate()):(this.velocity=0,this.emitColChange(),(0,z.h)())}else if(this.y%this.optHeight!=0){const t=Math.abs(this.y%this.optHeight);this.velocity=t>this.optHeight/2?1:-1,this.decelerate()}}indexForY(t){return Math.min(Math.max(Math.abs(Math.round(t/this.optHeight)),0),this.col.options.length-1)}onStart(t){t.event.cancelable&&t.event.preventDefault(),t.event.stopPropagation(),(0,z.a)(),void 0!==this.rafId&&cancelAnimationFrame(this.rafId);const e=this.col.options;let i=e.length-1,n=0;for(let r=0;r<e.length;r++)e[r].disabled||(i=Math.min(i,r),n=Math.max(n,r));this.minY=-i*this.optHeight,this.maxY=-n*this.optHeight}onMove(t){t.event.cancelable&&t.event.preventDefault(),t.event.stopPropagation();let e=this.y+t.deltaY;e>this.minY?(e=Math.pow(e,.8),this.bounceFrom=e):e<this.maxY?(e+=Math.pow(this.maxY-e,.9),this.bounceFrom=e):this.bounceFrom=0,this.update(e,0,!1)}onEnd(t){if(this.bounceFrom>0)return this.update(this.minY,100,!0),void this.emitColChange();if(this.bounceFrom<0)return this.update(this.maxY,100,!0),void this.emitColChange();if(this.velocity=(0,D.l)(-U,23*t.velocityY,U),0===this.velocity&&0===t.deltaY){const e=t.event.target.closest(".picker-opt");e?.hasAttribute("opt-index")&&this.setSelected(parseInt(e.getAttribute("opt-index"),10),N)}else{if(this.y+=t.deltaY,Math.abs(t.velocityY)<.05){const e=t.deltaY>0,i=Math.abs(this.y)%this.optHeight/this.optHeight;e&&i>.5?this.velocity=-1*Math.abs(this.velocity):!e&&i<=.5&&(this.velocity=Math.abs(this.velocity))}this.decelerate()}}refresh(t){var e;let i=this.col.options.length-1,n=0;const r=this.col.options;for(let d=0;d<r.length;d++)r[d].disabled||(i=Math.min(i,d),n=Math.max(n,d));if(0!==this.velocity)return;const s=(0,D.l)(i,null!==(e=this.col.selectedIndex)&&void 0!==e?e:0,n);if(this.col.prevSelected!==s||t){const d=s*this.optHeight*-1;this.velocity=0,this.update(d,N,!0)}}render(){const t=this.col,e=(0,_.b)(this);return(0,a.h)(a.H,{class:Object.assign({[e]:!0,"picker-col":!0,"picker-opts-left":"left"===this.col.align,"picker-opts-right":"right"===this.col.align},(0,W.g)(t.cssClass)),style:{"max-width":this.col.columnWidth}},t.prefix&&(0,a.h)("div",{class:"picker-prefix",style:{width:t.prefixWidth}},t.prefix),(0,a.h)("div",{class:"picker-opts",style:{maxWidth:t.optionsWidth},ref:i=>this.optsEl=i},t.options.map((i,n)=>(0,a.h)("button",{"aria-label":i.ariaLabel,class:{"picker-opt":!0,"picker-opt-disabled":!!i.disabled},"opt-index":n},i.text))),t.suffix&&(0,a.h)("div",{class:"picker-suffix",style:{width:t.suffixWidth}},t.suffix))}get el(){return(0,a.i)(this)}static get watchers(){return{col:["colChanged"]}}},$="picker-opt-selected",ct=.97,U=90,N=150;K.style={ios:".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{left:0;top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}[dir=rtl] .picker-opt,:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{padding-left:4px;padding-right:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.picker-col{padding-left:unset;padding-right:unset;-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px}}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}[dir=rtl] .picker-opt,:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}",md:".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{left:0;top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}[dir=rtl] .picker-opt,:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{padding-left:8px;padding-right:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){.picker-col{padding-left:unset;padding-right:unset;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px}}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #3880ff)}"}}}]);