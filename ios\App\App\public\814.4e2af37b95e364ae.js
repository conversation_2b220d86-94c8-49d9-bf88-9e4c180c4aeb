"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[814],{40814:(E,_,o)=>{o.r(_),o.d(_,{ion_text:()=>u});var r=o(29814),l=o(42673),a=o(35367);const u=class{constructor(s){(0,r.r)(this,s)}render(){const s=(0,l.b)(this);return(0,r.h)(r.H,{class:(0,a.c)(this.color,{[s]:!0})},(0,r.h)("slot",null))}};u.style=":host(.ion-color){color:var(--ion-color-base)}"},35367:(E,_,o)=>{o.d(_,{c:()=>a,g:()=>u,h:()=>l,o:()=>p});var r=o(73308);const l=(t,n)=>null!==n.closest(t),a=(t,n)=>"string"==typeof t&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},n):n,u=t=>{const n={};return(t=>void 0!==t?(Array.isArray(t)?t:t.split(" ")).filter(e=>null!=e).map(e=>e.trim()).filter(e=>""!==e):[])(t).forEach(e=>n[e]=!0),n},s=/^[a-z][a-z0-9+\-.]*:/,p=function(){var t=(0,r.A)(function*(n,e,f,h){if(null!=n&&"#"!==n[0]&&!s.test(n)){const i=document.querySelector("ion-router");if(i)return e?.preventDefault(),i.push(n,f,h)}return!1});return function(e,f,h,i){return t.apply(this,arguments)}}()}}]);