"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[3378],{93378:(w,P,r)=>{r.r(P),r.d(P,{ItemDetailPageModule:()=>R});var l=r(56610),C=r(37222),i=r(77897),O=r(77575),a=r(73308),n=r(2978),b=r(39316);function h(o,_){if(1&o){const t=n.RV6();n.j41(0,"ion-col",2),n.bIt("click",function(){const u=n.eBV(t).$implicit,E=n.XpG();return n.Njj(E.showDetail(u))}),n.j41(1,"div",3)(2,"ion-card",4),n.nrm(3,"ion-img",5),n.k0s(),n.j41(4,"ion-label",6),n.<PERSON>(5),n.nI1(6,"titlecase"),n.k0s()()()}if(2&o){const t=_.$implicit;n.R7$(3),n.Y8G("src",t.image),n.R7$(2),n.SpI(" ",n.bMT(6,2,t.label)," ")}}let f=(()=>{class o{constructor(t,e){this.modalCtrl=t,this.router=e,this.modalIsOpen=new n.bkB}ngOnInit(){}showDetail(t){this.router.navigate(["/order/product-detail/"+t._id])}addQuantity(t){return(0,a.A)(function*(){})()}static{this.\u0275fac=function(e){return new(e||o)(n.rXU(i.W3),n.rXU(O.Ix))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-items-group"]],inputs:{items:"items"},outputs:{modalIsOpen:"modalIsOpen"},decls:3,vars:1,consts:[[1,"ion-justify-content-between"],["size","auto",3,"click",4,"ngFor","ngForOf"],["size","auto",3,"click"],[1,"card-container","ion-text-center"],[1,"card-content","ion-align-items-center"],[3,"src"],[1,"title"]],template:function(e,c){1&e&&(n.j41(0,"ion-grid")(1,"ion-row",0),n.DNE(2,h,7,4,"ion-col",1),n.k0s()()),2&e&&(n.R7$(2),n.Y8G("ngForOf",c.items))},dependencies:[i.b_,i.hU,i.lO,i.KW,i.he,i.ln,l.Sq,l.PV],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .card-container[_ngcontent-%COMP%]{max-height:300px;margin-bottom:calc(63 * var(--res));width:calc(51 * var(--resW))}ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .card-container[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{box-shadow:none;border-radius:calc(32 * var(--res));padding:calc(60 * var(--res))}ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .card-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{text-align:start;display:block;margin-top:calc(25 * var(--res))}ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .card-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-family:var(--mont-semibold);font-size:calc(40 * var(--res))}ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .card-container[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{color:#aeaeae}ion-grid[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:11em}"]})}}return o})();var m=r(94440),y=r(74657);function d(o,_){if(1&o&&(n.j41(0,"ion-slide"),n.nrm(1,"ion-img",28),n.k0s()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("src",null==t.product?null:t.product.image)}}function s(o,_){1&o&&n.nrm(0,"ion-icon",29),2&o&&n.Y8G("src",_.$implicit)}function g(o,_){if(1&o&&n.nrm(0,"app-items-group",30),2&o){const t=n.XpG();n.Y8G("items",t.products)}}function M(o,_){if(1&o&&(n.j41(0,"ion-text")(1,"strong"),n.EFF(2),n.k0s()()),2&o){const t=_.$implicit;n.R7$(2),n.JRh(t.label)}}function p(o,_){if(1&o&&(n.j41(0,"div",31)(1,"ion-title"),n.EFF(2),n.k0s(),n.j41(3,"ion-text"),n.EFF(4),n.k0s(),n.EFF(5),n.nI1(6,"translate"),n.DNE(7,M,3,1,"ion-text",9),n.j41(8,"ion-text"),n.EFF(9),n.nI1(10,"translate"),n.k0s()()),2&o){const t=n.XpG();n.R7$(2),n.SpI(" ",null==t.product?null:t.product.normLabel," "),n.R7$(2),n.SpI("",null==t.product?null:t.product.description," "),n.R7$(1),n.SpI(" ",n.bMT(6,5,"item-details-page.specifications"),": "),n.R7$(2),n.Y8G("ngForOf",null==t.product?null:t.product.specifications),n.R7$(2),n.JRh(n.bMT(10,7,t.productDescription))}}const v=function(o){return{active:o}},k=[{path:"",component:(()=>{class o{constructor(t,e,c){this.activatedRoute=t,this.productSrv=e,this.location=c,this.slideOpts={initialSlide:0,spaceBetween:16,autoplay:!0},this.tabOption="other",this.usine="USINE DE BONABERI",this.imagesItem=["/assets/images/item-robust.svg","/assets/images/item-robust.svg","/assets/images/item-robust.svg","/assets/images/item-robust.svg","/assets/images/item-robust.svg","/assets/images/item-robust.svg","/assets/images/item-robust.svg"],this.starsIcon=["/assets/icons/star-yellow.svg","/assets/icons/star-yellow.svg","/assets/icons/star-yellow.svg","/assets/icons/star-yellow.svg","/assets/icons/star-grey.svg"],this.orders=[{label:"Ciment Robust",img:"/assets/images/item-multix.svg",weight:50,price:5897,backgoundClass:"background-grey"},{label:"Ciment Hydro",img:"/assets/images/item-robust.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Robust",img:"/assets/images/item-multix.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Hydro",img:"/assets/images/item-robust.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Robust",img:"/assets/images/item-multix.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Hydro",img:"/assets/images/item-robust.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Robust",img:"/assets/images/item-multix.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Hydro",img:"/assets/images/item-robust.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Robust",img:"/assets/images/item-multix.svg",weight:50,price:5897,backgoundClass:""},{label:"Ciment Hydro",img:"/assets/images/item-robust.svg",weight:50,price:5897,backgoundClass:""}]}ngOnInit(){var t=this;return(0,a.A)(function*(){t.getProduct(t.activatedRoute.snapshot.params.id)})()}getProduct(t){var e=this;return(0,a.A)(function*(){e.product=yield e.productSrv.getProduct(t),e.productDescription=yield e.getProductDescription(e.product?.label),e.productApplications=e.getProductApplications(e.product?.label),e.productBenefits=e.getProductBenefits(e.product?.label),e.productFeatures=e.getProductFeatures(e.product?.label),yield e.getOrtherProducts()})()}getProductApplications(t){switch(t?.toLowerCase()){case"amigo":return"item-details-page.amigo-applications";case"camerounaise":return"item-details-page.camerounaise-applications";case"colombe":return"item-details-page.colombe-applications";case"pelican":return"item-details-page.pelican-applications";default:return"item-details-page.default-applications"}}getProductBenefits(t){switch(t?.toLowerCase()){case"amigo":return"item-details-page.amigo-benefits";case"camerounaise":return"item-details-page.camerounaise-benefits";case"colombe":return"item-details-page.colombe-benefits";case"pelican":return"item-details-page.pelican-benefits";default:return"item-details-page.default-benefits"}}getProductFeatures(t){switch(t?.toLowerCase()){case"amigo":return"item-details-page.amigo-features";case"camerounaise":return"item-details-page.camerounaise-features";case"colombe":return"item-details-page.colombe-features";case"pelican":return"item-details-page.pelican-features";default:return"item-details-page.default-features"}}getProductDescription(t){return(0,a.A)(function*(){let e;switch(t?.toLowerCase()){case"camerounaise":e="item-details-page.camerounaise-description";break;case"colombe":e="item-details-page.colombe-description";break;case"pelican":e="item-details-page.pelican-description";break;case"amigo":e="item-details-page.amigo-description";break;default:e="item-details-page.default-description"}return e})()}getOrtherProducts(){var t=this;return(0,a.A)(function*(){t.products=(yield t.productSrv.getProducts()).data,t.products?.filter((e,c)=>{e?._id===t.product?._id&&t.products.splice(c,1)})})()}changeTab(t){this.tabOption=t}back(){this.location.back()}static{this.\u0275fac=function(e){return new(e||o)(n.rXU(O.nX),n.rXU(b.b),n.rXU(l.aZ))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-item-detail"]],decls:71,vars:50,consts:[[1,"header-page"],[1,"div-start"],[1,"logo-tag"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[3,"fullscreen"],["id","container"],[1,"item-detail-focus"],[3,"options"],[4,"ngFor","ngForOf"],[1,"row"],[1,"detail-item"],[1,"stars"],["class","small-stars",3,"src",4,"ngFor","ngForOf"],[1,"small-text"],[1,"btn-group"],[1,"product-information"],[1,"product-information-title"],["value","applications"],["slot","header","color","light"],["slot","content",1,"ion-padding"],["value","benefit"],["value","features"],[1,"other-item"],["slot","top",1,"tab-container","ion-no-border"],[3,"ngClass","click"],[3,"items",4,"ngIf"],["class","description-container",4,"ngIf"],[1,"head-img",3,"src"],[1,"small-stars",3,"src"],[3,"items"],[1,"description-container"]],template:function(e,c){1&e&&(n.j41(0,"ion-header")(1,"ion-toolbar")(2,"div",0)(3,"div",1)(4,"div",2)(5,"ion-img",3),n.bIt("click",function(){return c.back()}),n.k0s()(),n.j41(6,"ion-title",4),n.EFF(7),n.nI1(8,"truncateString"),n.k0s()()()()(),n.j41(9,"ion-content",5)(10,"div",6)(11,"div",7)(12,"ion-item")(13,"ion-slides",8),n.DNE(14,d,2,1,"ion-slide",9),n.k0s()(),n.j41(15,"ion-text"),n.EFF(16),n.k0s(),n.j41(17,"div",10)(18,"div",11)(19,"ion-label",4),n.EFF(20),n.k0s(),n.j41(21,"div",12),n.DNE(22,s,1,1,"ion-icon",13),n.j41(23,"ion-text",14),n.EFF(24," 500 avis "),n.k0s()()(),n.nrm(25,"div",15),n.k0s()(),n.j41(26,"div",16)(27,"ion-title",17),n.EFF(28),n.nI1(29,"translate"),n.k0s(),n.j41(30,"ion-accordion-group")(31,"ion-accordion",18)(32,"ion-item",19)(33,"ion-label"),n.EFF(34),n.nI1(35,"translate"),n.k0s()(),n.j41(36,"div",20),n.EFF(37),n.nI1(38,"translate"),n.k0s()(),n.j41(39,"ion-accordion",21)(40,"ion-item",19)(41,"ion-label"),n.EFF(42),n.nI1(43,"translate"),n.k0s()(),n.j41(44,"div",20),n.EFF(45),n.nI1(46,"translate"),n.k0s()(),n.j41(47,"ion-accordion",22)(48,"ion-item",19)(49,"ion-label"),n.EFF(50),n.nI1(51,"translate"),n.k0s()(),n.j41(52,"div",20),n.EFF(53),n.nI1(54,"translate"),n.k0s()()()(),n.j41(55,"div",23)(56,"ion-tab-bar",24)(57,"ion-tab-button",25),n.bIt("click",function(){return c.changeTab("other")}),n.j41(58,"ion-label",4),n.EFF(59),n.nI1(60,"translate"),n.k0s()(),n.j41(61,"ion-tab-button",25),n.bIt("click",function(){return c.changeTab("description")}),n.j41(62,"ion-label",4),n.EFF(63),n.nI1(64,"translate"),n.k0s()(),n.j41(65,"ion-tab-button",25),n.bIt("click",function(){return c.changeTab("avis")}),n.j41(66,"ion-label",4),n.EFF(67),n.nI1(68,"translate"),n.k0s()()(),n.DNE(69,g,1,1,"app-items-group",26),n.DNE(70,p,11,9,"div",27),n.k0s()()()),2&e&&(n.R7$(7),n.SpI(" ",n.bMT(8,22,null==c.product?null:c.product.label),""),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(4),n.Y8G("options",c.slideOpts),n.R7$(1),n.Y8G("ngForOf",c.imagesItem),n.R7$(2),n.SpI("",null==c.product?null:c.product.description," "),n.R7$(4),n.SpI(" Ref: ",null==c.product?null:c.product.normLabel," "),n.R7$(2),n.Y8G("ngForOf",c.starsIcon),n.R7$(6),n.SpI(" ",n.bMT(29,24,"item-details-page.product-information")," "),n.R7$(6),n.JRh(n.bMT(35,26,"item-details-page.applications")),n.R7$(3),n.JRh(n.bMT(38,28,c.productApplications)),n.R7$(5),n.JRh(n.bMT(43,30,"item-details-page.benefits")),n.R7$(3),n.JRh(n.bMT(46,32,c.productBenefits)),n.R7$(5),n.JRh(n.bMT(51,34,"item-details-page.features")),n.R7$(3),n.JRh(n.bMT(54,36,c.productFeatures)),n.R7$(4),n.Y8G("ngClass",n.eq3(44,v,"other"===c.tabOption)),n.R7$(2),n.JRh(n.bMT(60,38,"item-details-page.others")),n.R7$(2),n.Y8G("ngClass",n.eq3(46,v,"description"===c.tabOption)),n.R7$(2),n.JRh(n.bMT(64,40,"item-details-page.description")),n.R7$(2),n.Y8G("ngClass",n.eq3(48,v,"avis"===c.tabOption)),n.R7$(2),n.JRh(n.bMT(68,42,"item-details-page.reviews")),n.R7$(2),n.Y8G("ngIf","other"===c.tabOption),n.R7$(1),n.Y8G("ngIf","description"===c.tabOption))},dependencies:[l.YU,l.Sq,l.bT,i.xk,i.YH,i.W9,i.eU,i.iq,i.KW,i.uz,i.he,i.q3,i.tR,i.Jq,i.qW,i.IO,i.BC,i.ai,f,m.c,y.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-toolbar[_ngcontent-%COMP%]{--padding-start: var(--space-5);--padding-end: var(--space-5);--padding-top: var(--space-5);--border-color: transparent;--background-color: transparent}ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   .div-start[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--mont-bold);text-align:start;color:var(--clr-primary-900)}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]{background:#f4f4f4}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-family:Mont Regular}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#0b305c}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#000;font-size:calc(45 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{margin:calc(41 * var(--res)) 0;padding:0 calc(41 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]::part(native){color:var(--clr-primary-700);background:var(--clr-primary-0)}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{text-transform:initial}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--border-color: transparent;padding:calc(41 * var(--res)) calc(41 * var(--res)) 0 calc(41 * var(--res));--background: #f4f4f4}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .ion-arrow[_ngcontent-%COMP%]{height:20px;width:25px}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;color:#0b305c}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:calc(31 * var(--res));width:100px}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(60 * var(--res))}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]{text-align:right;position:relative}ion-toolbar[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{position:absolute;right:18%;width:calc(20 * var(--res));height:calc(20 * var(--res));border-radius:50%;background:rgb(173,5,5)}ion-content[_ngcontent-%COMP%]{--background: #eef2f9}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:0;background-color:#fff}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]{padding:0 calc(75 * var(--res));padding-bottom:calc(75 * var(--res));background-color:#fff;border-radius:0px 0px calc(35 * var(--res)) calc(35 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0px;--inner-padding-end: 0px;margin-bottom:.7em}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]{height:calc(36.5 * var(--resH));padding-bottom:1em}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   .swiper-container-horizontal[_ngcontent-%COMP%]   .swiper-pagination-bullets[_ngcontent-%COMP%]{bottom:0!important;left:0;width:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-slides[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:100%;width:100%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{margin-top:.7em;display:flex;align-items:center;justify-content:space-between}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:11px;font-style:normal;font-weight:400;line-height:normal;letter-spacing:-.165px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{margin-top:.25em;display:flex}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:.75em;width:.75em}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{margin-left:.55em}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .small-stars[_ngcontent-%COMP%]{font-size:17px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .small-text[_ngcontent-%COMP%]{font-size:10px;font-style:normal;font-weight:400;line-height:normal;letter-spacing:-.165px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:calc(8 * var(--resH));height:calc(8 * var(--resH));--padding-end: 0;--padding-start: 0;--border-radius: 50%}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .item-detail-focus[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn-border[_ngcontent-%COMP%]{margin-left:.5em;--border-color: #d9d9d9;--border-style: solid;--border-width: 1px;width:40px;height:40px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .usine-change[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]{margin-bottom:calc(25 * var(--res));padding:calc(20 * var(--res)) calc(75 * var(--res)) calc(75 * var(--res)) calc(75 * var(--res));border-radius:8px calc(25 * var(--res)) 0px 0px;background-color:#fff}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .usine-change[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{margin-bottom:.32em;padding-bottom:.32em;text-align:start;border-bottom:2px solid #d6d6d6}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .usine-change[_ngcontent-%COMP%]{margin-top:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .usine-change[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-weight:600}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .add-quantity[_ngcontent-%COMP%]{width:88%;margin-left:auto;margin-right:auto;margin-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]{margin-bottom:0;padding:calc(20 * var(--res)) calc(75 * var(--res)) calc(30 * var(--res)) calc(75 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]   .product-information-title[_ngcontent-%COMP%]{font-size:14px;font-style:normal;font-weight:500;line-height:normal;letter-spacing:-.165px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]   ion-accordion[_ngcontent-%COMP%]{background:#ffffff;border:1px solid rgba(235,235,235,.63);border-radius:calc(25 * var(--res));margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]   ion-accordion[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:700}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .product-information[_ngcontent-%COMP%]   ion-accordion[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:calc(62.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]{padding:calc(50 * var(--res)) calc(75 * var(--res));background-color:#eef2f9}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   ion-tab-bar[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   ion-tab-bar[_ngcontent-%COMP%]   ion-tab-button[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   ion-tab-bar[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#143c5d;border-bottom:3px solid #143c5d}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   ion-tab-bar[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#143c5d;font-weight:700}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   .description-container[_ngcontent-%COMP%]{font-size:15.404px;font-style:normal;font-weight:500;line-height:normal;letter-spacing:-.117px}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   .description-container[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   .description-container[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   .description-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{margin-bottom:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   .description-container[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{text-align:start}ion-content[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]   .other-item[_ngcontent-%COMP%]   .description-container[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{display:block}.telec-btn[_ngcontent-%COMP%]{height:100%}.head-img[_ngcontent-%COMP%]{height:90%!important}"]})}}return o})()}];let x=(()=>{class o{static{this.\u0275fac=function(e){return new(e||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[O.iI.forChild(k),O.iI]})}}return o})();var I=r(93887);let R=(()=>{class o{static{this.\u0275fac=function(e){return new(e||o)}}static{this.\u0275mod=n.$C({type:o})}static{this.\u0275inj=n.G2t({imports:[l.MD,C.YN,i.bv,x,I.G,y.h]})}}return o})()},39316:(w,P,r)=>{r.d(P,{b:()=>f});var l=r(73308),C=r(26409),i=r(94934),O=r(45312),a=r(2978),n=r(82571),b=r(33607),h=r(77897);let f=(()=>{class m{constructor(d,s,g,M){this.http=d,this.commonSrv=s,this.baseUrlService=g,this.toastController=M,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+O.c.basePath+"products"}getProducts(d){var s=this;return(0,l.A)(function*(){try{let g=new C.Nl;return d?.limit&&(g=g.append("limit",d?.limit)),yield(0,i.s)(s.http.get(s.url,{params:g}))}catch(g){const p={message:s.commonSrv.getError("",g).message,color:"danger"};return yield s.commonSrv.showToast(p),g}})()}getProduct(d){var s=this;return(0,l.A)(function*(){try{return yield(0,i.s)(s.http.get(`${s.url}/${d}`))}catch(g){const p={message:s.commonSrv.getError("",g).message,color:"danger"};return yield s.commonSrv.showToast(p),g}})()}static{this.\u0275fac=function(s){return new(s||m)(a.KVO(C.Qq),a.KVO(n.h),a.KVO(b.K),a.KVO(h.K_))}}static{this.\u0275prov=a.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})()},94440:(w,P,r)=>{r.d(P,{c:()=>C});var l=r(2978);let C=(()=>{class i{transform(a,...n){return a?a.length>n[0]?`${a.substring(0,n[0]-3)}...`:a:""}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275pipe=l.EJ8({name:"truncateString",type:i,pure:!0})}}return i})()}}]);