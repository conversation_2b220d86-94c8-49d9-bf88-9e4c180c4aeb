"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6634],{6634:(h,g,o)=>{o.r(g),o.d(g,{UserListPageModule:()=>O});var a=o(56610),d=o(37222),l=o(74657),e=o(77897),s=o(77575),n=o(2978),m=o(58133),u=o(82571);const p=function(){return["/navigation/companies-account"]};function b(t,_){1&t&&(n.j41(0,"a",6),n.nrm(1,"ion-img",7),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s(),n.nrm(5,"ion-icon",8),n.k0s()),2&t&&(n.Y8G("routerLink",n.lJ4(4,p)),n.R7$(3),n.SpI(" ",n.bMT(4,2,"list-user.direct")," "))}const M=function(){return["/navigation/indirect-user"]},C=[{path:"",component:(()=>{class t{constructor(c){this.commonService=c,this.location=(0,n.WQX)(a.aZ),this.userCategory=m.s}ngOnInit(){}back(){this.location.back()}static{this.\u0275fac=function(r){return new(r||t)(n.rXU(u.h))}}static{this.\u0275cmp=n.VBU({type:t,selectors:[["app-user-list"]],decls:15,vars:10,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[3,"fullscreen"],[1,"container"],["class","item",3,"routerLink",4,"ngIf"],[1,"item",3,"routerLink"],["src","/assets/images/man.png"],["slot","end","name","chevron-forward"]],template:function(r,i){1&r&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return i.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content",3)(7,"div",4),n.DNE(8,b,6,5,"a",5),n.j41(9,"a",6),n.nrm(10,"ion-img",7),n.j41(11,"ion-label"),n.EFF(12),n.nI1(13,"translate"),n.k0s(),n.nrm(14,"ion-icon",8),n.k0s()()()),2&r&&(n.R7$(4),n.JRh(n.bMT(5,5,"list-user.title")),n.R7$(2),n.Y8G("fullscreen",!0),n.R7$(2),n.Y8G("ngIf",(null==i.commonService.user?null:i.commonService.user.category)!==i.userCategory.DonutAnimator),n.R7$(1),n.Y8G("routerLink",n.lJ4(9,M)),n.R7$(3),n.SpI(" ",n.bMT(13,7,"list-user.indirect")," "))},dependencies:[a.bT,e.W9,e.eU,e.iq,e.KW,e.he,e.BC,e.ai,e.oY,s.Wk,l.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-button[_ngcontent-%COMP%]{margin:1em 0}ion-title[_ngcontent-%COMP%]{color:#0b305c;font-size:calc(55 * var(--res));text-align:start;font-family:Mont Regular;font-weight:700!important;margin:1em 0}.header[_ngcontent-%COMP%]{--background: #F1F2F4;--border-color: transparent;width:100%;margin-left:13px;margin-top:auto;margin-bottom:auto;padding:auto 0px;display:flex}.header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: transparent}.header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]{--background: #F1F2F4}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{background:white;border-radius:5px;box-shadow:0 4px 4px #00000017;width:calc(100% - 48px);margin:1em;padding:1em;display:flex;justify-content:space-between}ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%], ion-content[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-semibold);text-align:center;margin:auto}ion-content[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:50px;height:50px}ion-content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:1rem;color:#0b305c;font-weight:700}ion-content[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{padding:8px;border-radius:50px;border:1px solid var(--clr-primary-750)}ion-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}"]})}}return t})()}];let P=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[s.iI.forChild(C),s.iI]})}}return t})(),O=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[a.MD,d.YN,e.bv,P,l.h]})}}return t})()}}]);