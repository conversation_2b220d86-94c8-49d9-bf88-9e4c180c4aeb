"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8699],{78699:(Fn,w,c)=>{c.r(w),c.d(w,{ParticularOrdersPageModule:()=>Sn});var p=c(56610),C=c(77575),l=c(99987),n=c(2978),M=c(62049),a=c(77897);function Q(r,g){1&r&&n.nrm(0,"ion-checkbox",10),2&r&&n.Y8G("checked",!0)("disabled",!0)}function L(r,g){if(1&r&&(n.j41(0,"div",11)(1,"ion-label"),n.EFF(2),n.k0s()()),2&r){const t=n.XpG().index;n.R7$(2),n.JRh(t+1)}}function E(r,g){if(1&r&&(n.j41(0,"div",7),n.DNE(1,Q,1,2,"ion-checkbox",8),n.DNE(2,L,3,1,"div",9),n.k0s()),2&r){const t=g.$implicit;n.R7$(1),n.Y8G("ngIf",1===t),n.R7$(1),n.Y8G("ngIf",0===t)}}let G=(()=>{class r{constructor(t,e,o,i){this.router=t,this.location=e,this.translateService=o,this.route=i,this.steps=[0,0,0],this.action="Nouvelle commande",this.stepTitle="",this.lastRoute=""}ngOnInit(){}onOutletLoaded(t){this.route.params.subscribe(e=>{this.action=this.translateService?.currentLang===l.T.French?"Nouvelle commande":"New order";let o={"/order/particular-order/first-step":{step:0,url:"/navigation/home",title:this.translateService?.currentLang===l.T.French?"Nouvelle commande":"New Order",label:this.translateService?.currentLang===l.T.French?"Informations g\xe9n\xe9rales":"general information",description:"",steps:[0,0,0]},"/order/particular-order/second-step":{step:1,title:this.translateService?.currentLang===l.T.French?"S\xe9lection des produits":"Product selection",label:this.translateService?.currentLang===l.T.French?"Rechercher":"search",url:"first-step",description:this.translateService?.currentLang===l.T.French?"S\xe9lectionner un produit pour l\u2019ajouter au panier ":"Select a product to add to cart",steps:[1,0,0]},"/order/particular-order/third-step":{step:2,title:this.translateService?.currentLang===l.T.French?"Finaliser la commande":"Finalise order",url:"first-step",label:this.translateService?.currentLang===l.T.French?"Informations de votre commande":"Information of your order",description:"",steps:[1,1,0]},"/order/particular-order/four-step":{step:3,title:this.translateService?.currentLang===l.T.French?"Op\xe9ration r\xe9ussie":"Successful Operation",url:"third-step",label:"",description:"",steps:[1,1,1]}};this.stepTitle=o[this.router.url]?.title,this.steps=o[this.router.url]?.steps,this.action=o[this.router.url]?.label,this.lastRoute=o[this.router.url]?.url})}back(){if("/order/order-reseller/four-step"===this.router.url)return this.router.navigate(["navigation/home"]);this.location.back()}static{this.\u0275fac=function(e){return new(e||r)(n.rXU(C.Ix),n.rXU(p.aZ),n.rXU(M.E),n.rXU(C.nX))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-particular-orders"]],decls:8,vars:1,consts:[[1,"header"],["slot","start","src","../../../assets/icons/arrow-blue.svg",1,"ion-arrow",3,"click"],[1,"step-container"],[1,"step-content"],["class","step-item",4,"ngFor","ngForOf"],[1,"ion-padding"],[3,"activate"],[1,"step-item"],["mode","ios",3,"checked","disabled",4,"ngIf"],["class","checkbox",4,"ngIf"],["mode","ios",3,"checked","disabled"],[1,"checkbox"]],template:function(e,o){1&e&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return o.back()}),n.k0s(),n.j41(3,"div",2)(4,"div",3),n.DNE(5,E,3,2,"div",4),n.k0s()()()(),n.j41(6,"ion-content",5)(7,"ion-router-outlet",6),n.bIt("activate",function(s){return o.onOutletLoaded(s)}),n.k0s()()),2&e&&(n.R7$(5),n.Y8G("ngForOf",o.steps))},dependencies:[a.eY,a.W9,a.eU,a.KW,a.he,a.ai,a.hB,a.Rg,p.Sq,p.bT],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]{background:#f4f4f4}ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-family:Mont Regular}ion-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#0b305c}ion-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#000;font-size:calc(45 * var(--res))}ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{margin:calc(41 * var(--res)) 0;padding:0 calc(41 * var(--res))}ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]::part(native){color:var(--clr-primary-700);background:var(--clr-primary-0)}ion-header[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}ion-header[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}ion-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{text-transform:initial}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--border-color: transparent;padding:calc(41 * var(--res)) calc(41 * var(--res)) 0 calc(41 * var(--res));--background: #f4f4f4}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .ion-arrow[_ngcontent-%COMP%]{height:20px;width:25px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(46 * var(--res));text-align:start;color:#0b305c}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:calc(31 * var(--res));width:100px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(60 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]{text-align:right;position:relative}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icons_profil[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{position:absolute;right:18%;width:calc(20 * var(--res));height:calc(20 * var(--res));border-radius:50%;background:rgb(173,5,5)}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]{padding-bottom:0;border-bottom-left-radius:calc(50 * var(--res));border-bottom-right-radius:calc(50 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;width:50%;margin-left:auto;margin-right:auto}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{opacity:1!important;font-family:Mont Bold;--background-checked: transparent;--checkmark-color: #419CFB;--border-width: 3px;--checkmark-width: 3px;--border-color-checked: #419CFB !important}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%], ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{width:38px;height:38px}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{background:white;width:38px;height:38px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#8597ad;line-height:normal}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-size:16px}ion-content[_ngcontent-%COMP%]{--background: #f4f4f4}"]})}}return r})();var d=c(73308),V=c(18707),v=c(5141),P=c(82571),y=c(39316),T=c(58133),F=c(73014),S=c(61095),m=c(37222),O=c(74657);const B=["input-number"];function q(r,g){if(1&r&&(n.j41(0,"div",18)(1,"ion-label",19),n.EFF(2),n.nI1(3,"translate"),n.j41(4,"span",20),n.EFF(5),n.k0s()()()),2&r){const t=n.XpG();n.R7$(2),n.SpI(" ",n.bMT(3,2,"order-new-page.planning-modal.remainning-quantity")," : "),n.R7$(3),n.SpI(" ",null==t.scheduleSrv?null:t.scheduleSrv.remainingQtyTonneInStock,"T")}}const D=function(r){return{active:r}};function U(r,g){if(1&r){const t=n.RV6();n.j41(0,"div")(1,"ion-chip",21),n.bIt("click",function(){const i=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.selectQuickChoice(i))}),n.j41(2,"ion-text"),n.EFF(3),n.k0s()()()}if(2&r){const t=g.$implicit,e=n.XpG();n.R7$(1),n.Y8G("ngClass",n.eq3(2,D,e.currentChoice===t)),n.R7$(2),n.SpI(" ",t," ")}}function N(r,g){if(1&r&&(n.j41(0,"div",22)(1,"ion-button",23)(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()()),2&r){const t=n.XpG();n.R7$(1),n.Y8G("disabled",!(null!=t.scheduleSrv&&null!=t.scheduleSrv.scheduleForm&&null!=t.scheduleSrv.scheduleForm.value&&t.scheduleSrv.scheduleForm.value.quantity)),n.R7$(2),n.SpI(" ",n.bMT(4,2,"order-new-page.planning-modal.save-button-label")," ")}}function X(r,g){1&r&&(n.j41(0,"div",22)(1,"ion-button",24)(2,"ion-label"),n.EFF(3),n.k0s()()()),2&r&&(n.R7$(3),n.SpI(" ","Retirer du panier"," "))}let Y=(()=>{class r{constructor(){this.quickChoice=[1,2,3,4,5,6],this.currentChoice=0,this.activeDelete=!1,this.currentDate=new Date,this.quarts=[],this.slideQuartTimeOpts={initialSlide:0,speed:1e3,slidesPerView:3,spaceBetween:8},this.slideTimeOpts={initialSlide:0,speed:400,slidesPerView:4.8,spaceBetween:10},this.statusForFrench={equal:{message:"Votre planification a atteint la quantit\xe9 totale \xe0 retirer",isGoodPlannify:!0},superior:{message:"Votre planification a d\xe9pass\xe9 la quantit\xe9 totale \xe0 retirer veuillez r\xe9duire votre quantit\xe9",isGoodPlannify:!1},inferior:{message:"Votre planification n'a pas atteint la quantit\xe9 totale \xe0 retirer veuillez augmenter votre quantit\xe9",isGoodPlannify:!1}},this.statusForEnglish={equal:{message:"Your planning has reached the total amount to be removed",isGoodPlannify:!0},superior:{message:"Your planning has exceeded the total quantity to be removed please reduce your quantity",isGoodPlannify:!1},inferior:{message:"Your planning has not reached the total quantity to be withdrawn please increase your quantity",isGoodPlannify:!1}},this.commonSrv=(0,n.WQX)(P.h),this.productSrv=(0,n.WQX)(y.b),this.modalCtrl=(0,n.WQX)(a.W3),this.scheduleSrv=(0,n.WQX)(S.l),this.translateService=(0,n.WQX)(M.E),this.onDeleteClick=()=>this.scheduleSrv.scheduleForm.patchValue({quantity:0}),this.addNumberClick=t=>{console.log("quantity : ",t),this.addQuantity({detail:{value:+`${this.scheduleSrv.scheduleForm.value.quantity}${t}`}})}}ngOnInit(){var t=this;return(0,d.A)(function*(){t.unit=t.productSrv.unit,t.ratio=t.productSrv.unit?.ratioToTone,t.isUpdate=t.scheduleSrv?.cartItem?.quantity>0,t.status=t.translateService.currentLang===l.T.French?t.statusForFrench:t.statusForEnglish,t.quarts=t.translateService.currentLang===l.T.French?v.Mn:v.q6,t.resetData(),yield t.scheduleSrv.getRemovalsObject(),t.isUpdate?(t.activeDelete=!0,t.scheduleSrv.scheduleForm?.patchValue({tonneQuantity:t.scheduleSrv?.cartItem?.quantity/t.productSrv?.unit.ratioToTone,quantity:t.scheduleSrv?.cartItem?.quantity}),t.scheduleSrv.remainingQtyTonne=0):t.scheduleSrv.getRemainingQuantityStock()})()}resetData(){this.scheduleSrv.remainingQtyTonneInStock=0,this.scheduleSrv.currentRemoval=new F.J7,this.scheduleSrv.removalObject=new F.iB,this.scheduleSrv.remainingQtyTonne=0,this.scheduleSrv.scheduleForm.reset(),this.scheduleSrv.isGoodPlannify=!1}checkIfDataExist(){(!this.hour||""===this.hour)&&this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===l.T.French?"Veuillez remplir au pr\xe9alable l'heure d'enl\xe8vement":"Please pre-fill the pick-up time"})}checkNbrTruck(t){var e=this;return(0,d.A)(function*(){if(e.nbrTruck=Math.ceil(t.detail.value),e.nbrTruck<e.scheduleSrv.minCapacityToRemove&&e.nbrTruck)return e.nbrTruck=null,yield e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===l.T.French?`Veuillez renseigner une valeur sup\xe9rieure \xe0 ${e.scheduleSrv.minCapacityToRemove}`:`Please enter a value greater than ${e.scheduleSrv.minCapacityToRemove}`});if(e.nbrTonnage*e.nbrTruck>0){let o=e.scheduleSrv.getTotalTonnage(),i=e.scheduleSrv.scheduleForm.get("tonneQuantity").value;if(e.scheduleSrv.isGoodPlannify=e.status[o!==i?"superior":"equal"].isGoodPlannify,e.scheduleSrv.remainingQtyTonne<e.nbrTonnage*e.nbrTruck)return e.nbrTruck=null,yield e.commonSrv.showToast({color:"warning",message:e.status.superior.message});e.scheduleSrv.remainingQtyTonne=i-o,Math.round(100*e.scheduleSrv.remainingQtyTonne)}})()}checkNbrTonnage(t){var e=this;return(0,d.A)(function*(){let o=+t.detail.value;if(o>45)return e.nbrTonnage=null,yield e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===l.T.French?"La capacit\xe9 de tonnage maximum est de 45 tonnes":"The maximum tonnage capacity is 45 tonnes"});if(e.scheduleSrv.remainingQtyTonne<o&&e.scheduleSrv.isGoodPlannify)return e.nbrTonnage=null,yield e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===l.T.French?"Vous avez exc\xe9d\xe9 la quantit\xe9 totale":"You have exceeded the total quantity"});if(e.nbrTonnage=o,e.nbrTruck>0&&e.nbrTonnage){let i=e.scheduleSrv.scheduleForm.get("tonneQuantity").value,s=e.scheduleSrv.getTotalTonnage();e.scheduleSrv.isGoodPlannify=e.status[s!==i?"superior":"equal"].isGoodPlannify,e.scheduleSrv.remainingQtyTonne<i?(yield e.commonSrv.showToast({color:"warning",message:e.status.superior.message}),e.nbrTonnage=null):(e.scheduleSrv.remainingQtyTonne=i-s,Math.round(100*e.scheduleSrv.remainingQtyTonne))}return o<e.scheduleSrv.minCapacityToRemove&&e.nbrTonnage?(e.nbrTonnage=null,yield e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===l.T.French?`Vous ne pouvez pas faire une planification moins de ${e.scheduleSrv.minCapacityToRemove} tonne`:`You can't plan less than ${e.scheduleSrv.minCapacityToRemove} ton`})):void 0})()}selectHour(t){this.SelectedQuart&&""!=this.SelectedQuart?(this.hour=t,this.scheduleSrv.isGoodPlannify=this.hour&&this.selectedQuartHours&&this.scheduleSrv.scheduleForm.valid&&0===this.scheduleSrv.remainingQtyTonne):this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===l.T.French?"Veuillez remplir au pr\xe9alable la quart temps":"Please pre-complete the shift"})}selectQuart(t){this.scheduleSrv.scheduleForm.valid?(this.selectedQuartHours=t?.quartHours,this.SelectedQuart=t.value,this.hour="",this.scheduleSrv.isGoodPlannify=!1):this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===l.T.French?"Veuillez remplir au pr\xe9alable la date d'enl\xe8vement et la quantit\xe9 \xe0 enlever":"Please pre-fill the removal date and quantity to be removed"})}saveSchedule(){var t=this;return(0,d.A)(function*(){let e={...t.scheduleSrv.cartItem};t.isUpdate&&t.activeDelete?e.quantity=-1:(t.scheduleSrv.cartItem.quantity=t.scheduleSrv.scheduleForm.get("quantity").value,e.quantity=t.scheduleSrv.cartItem.quantity),t.scheduleSrv.scheduleForm.reset(),t.modalCtrl.dismiss(e)})()}addTruckWithTonnage(){var t=this;return(0,d.A)(function*(){if(!t.nbrTonnage||!t.nbrTruck)return void(yield t.commonSrv.showToast({color:"warning",message:t.translateService.currentLang===l.T.French?"Veuillez renseigner les quantit\xe9s":"Please fill in the quantities"}));let e=t.scheduleSrv.getTotalTonnage(),o=t.scheduleSrv.scheduleForm.get("tonneQuantity").value;t.scheduleSrv.isGoodPlannify=t.status[e!==o?"superior":"equal"].isGoodPlannify,t.scheduleSrv.remainingQtyTonne>=t.nbrTonnage*t.nbrTruck?t.saveSchedule():yield t.commonSrv.showToast({color:"warning",message:t.status[e>o?"superior":"equal"].message})})()}addQuantity(t){t?.detail?.value<0&&(this.commonSrv.showToast({color:"warning",message:this.translateService.currentLang===l.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),this.scheduleSrv.scheduleForm.patchValue({quantity:null})),this.scheduleSrv.remainingQtyTonne=this.isUpdate&&0===this.scheduleSrv.remainingQtyTonne?this.scheduleSrv.remainingQtyTonne:parseFloat((t.detail.value/this.ratio).toFixed(3)),this.scheduleSrv.scheduleForm.patchValue({tonneQuantity:t.detail.value/this.ratio}),this.activeDelete=this.scheduleSrv.scheduleForm.get("quantity").value<=0||this.scheduleSrv.cartItem?.quantity===this.scheduleSrv.scheduleForm.get("quantity").value,this.scheduleSrv.verifyCanRemoval(this.scheduleSrv.scheduleForm.get("tonneQuantity").value)}addTonneQuantity(t){var e=this;return(0,d.A)(function*(){return t?.detail?.value<0?(yield e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===l.T.French?"Veuillez renseigner une valeur positive":"Please enter a positive value"}),e.scheduleSrv.scheduleForm.patchValue({quantity:null})):(e.scheduleSrv.remainingQtyTonne=e.isUpdate&&0===e.scheduleSrv.remainingQtyTonne?e.scheduleSrv.remainingQtyTonne:t.detail.value,e.scheduleSrv.scheduleForm.patchValue({quantity:t.detail.value*e.ratio}),e.commonSrv?.user?.category===T.s.EmployeeLapasta&&t?.detail?.value>e.commonSrv?.user?.tonnage?.capacityPerYear?(yield e.commonSrv.showToast({color:"warning",message:e.translateService.currentLang===l.T.French?`Vous avez exceder votre capacit\xe9 de tonnage annuelle restante (${e.commonSrv?.user?.tonnage?.capacityPerYear} T)`:`You have exceded your remaining annual tonnage capacity (${e.commonSrv?.user?.tonnage?.capacityPerYear} T)`}),e.scheduleSrv.scheduleForm.patchValue({quantity:null})):e.scheduleSrv.verifyCanRemoval(t.detail.value)?e.scheduleSrv.scheduleForm.patchValue({quantity:null}):void 0)})()}selectQuickChoice(t){this.currentChoice=t,this.scheduleSrv.scheduleForm.patchValue({quantity:t}),this.activeDelete=this.scheduleSrv.cartItem?.quantity===(this.scheduleSrv.scheduleForm.get("quantity").value||0)}static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-form-per-quantity"]],viewQuery:function(e,o){if(1&e&&n.GBs(B,5),2&e){let i;n.mGM(i=n.lsd())&&(o.inputNumber=i.first)}},inputs:{scheduleDates:"scheduleDates"},decls:33,vars:18,consts:[[1,"bottom-sheet-content"],[1,"ion-text-center","ion-padding"],["slot","end",3,"click"],["src","assets/icons/close-btn.svg"],["color","primary",1,"title"],["id","content",3,"formGroup","ngSubmit"],["class","form-group padding-horizontal last-form",4,"ngIf"],[1,"form-group","padding-horizontal"],[1,"card-quantity-title"],["src","assets/icons/card-order.svg"],[1,"mbottom","quantities"],[1,"ion-justify-content-between"],[1,"unit"],[1,"fMedium"],[1,"space-h-v"],[4,"ngFor","ngForOf"],["locale","fr-FR","min","0","formControlName","quantity","type","number","pattern","[0-9]*","step","1","placeholder","0","clearInput","",1,"input-number",3,"readonly","ionChange"],["class","btn-validate",4,"ngIf"],[1,"form-group","padding-horizontal","last-form"],[1,"title"],[1,"primary-text","fMedium","mbottom"],[3,"ngClass","click"],[1,"btn-validate"],["type","submit","color","primary","expand","block",1,"btn--meduim","btn--upper",3,"disabled"],["type","submit","color","danger","expand","block",1,"btn--meduim","btn--upper"]],template:function(e,o){1&e&&(n.j41(0,"div",0)(1,"ion-header")(2,"ion-toolbar",1)(3,"ion-thumbnail",2),n.bIt("click",function(){return o.scheduleSrv.closeForm()}),n.nrm(4,"ion-img",3),n.k0s(),n.j41(5,"ion-title",4),n.EFF(6),n.nI1(7,"translate"),n.k0s()()(),n.j41(8,"ion-content")(9,"form",5),n.bIt("ngSubmit",function(){return o.saveSchedule()}),n.DNE(10,q,6,4,"div",6),n.j41(11,"div",7)(12,"div",8),n.nrm(13,"ion-img",9),n.j41(14,"ion-label"),n.EFF(15),n.nI1(16,"titlecase"),n.k0s()(),n.j41(17,"ion-grid",10)(18,"ion-row",11)(19,"ion-col",12)(20,"ion-label",13),n.EFF(21),n.nI1(22,"translate"),n.k0s(),n.j41(23,"div",14),n.DNE(24,U,4,4,"div",15),n.k0s()()(),n.j41(25,"ion-row",11)(26,"ion-col",12)(27,"ion-label",13),n.EFF(28),n.nI1(29,"translate"),n.k0s(),n.j41(30,"ion-input",16),n.bIt("ionChange",function(s){return o.addQuantity(s)}),n.k0s()()()()(),n.DNE(31,N,5,4,"div",17),n.DNE(32,X,4,1,"div",17),n.k0s()()()),2&e&&(n.R7$(6),n.SpI(" ",n.bMT(7,10,"order-new-page.planning-modal.title-quantity")," "),n.R7$(3),n.Y8G("formGroup",o.scheduleSrv.scheduleForm),n.R7$(1),n.Y8G("ngIf",null==o.scheduleSrv?null:o.scheduleSrv.remainingQtyTonneInStock),n.R7$(5),n.JRh(n.bMT(16,12,null==o.scheduleSrv.cartItem||null==o.scheduleSrv.cartItem.product?null:o.scheduleSrv.cartItem.product.label)+" ("+(null==o.scheduleSrv.cartItem.packaging?null:o.scheduleSrv.cartItem.packaging.label)+")"),n.R7$(6),n.SpI(" ",n.bMT(22,14,"order-new-page.planning-modal.quick-select"),""),n.R7$(3),n.Y8G("ngForOf",o.quickChoice),n.R7$(4),n.SpI(" ",n.bMT(29,16,"order-new-page.planning-modal.label-quantity")," "),n.R7$(2),n.Y8G("readonly",(null==o.scheduleSrv||null==o.scheduleSrv.schedules?null:o.scheduleSrv.schedules.length)>0),n.R7$(1),n.Y8G("ngIf",!(o.isUpdate&&o.activeDelete)),n.R7$(1),n.Y8G("ngIf",o.isUpdate&&o.activeDelete))},dependencies:[m.qT,m.BC,m.cb,m.R_,a.Jm,a.ZB,a.hU,a.W9,a.lO,a.eU,a.KW,a.$w,a.he,a.ln,a.IO,a.Zx,a.BC,a.ai,a.su,p.YU,p.Sq,p.bT,m.j4,m.JD,p.PV,O.D9],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: rgba(208, 227, 246, .61);--color: $color-primary;--padding-top: 1.75rem;--border-width: 0;--padding-bottom: 1rem;padding:0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-family:Mont Bold;color:#143c5d;font-size:calc(44 * var(--res))}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{z-index:10;position:absolute;right:calc(41 * var(--res));height:max-content;width:max-content}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:18px;height:18px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;color:#000;display:flex;flex-direction:column;height:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(44 * var(--res));margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .primary-text[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .card-quantity-title[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#e7eaef;padding-inline:calc(41 * var(--res));padding-block:4px;border-radius:12px;margin-bottom:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .card-quantity-title[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(110 * var(--res));margin-right:calc(25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .card-quantity-title[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont SemiBold;font-weight:600;font-size:calc(37 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{display:flex;white-space:nowrap;overflow-x:scroll;scrollbar-width:none;-webkit-scrollbar-width:none;margin-bottom:calc(50 * var(--res));margin-top:12px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{--background: #F0F0F0;--color: var(--ion-color-primary);border:#E4F1FF .5px solid;padding-block:calc(19.3 * var(--res));padding-inline:calc(60 * var(--res));width:max-content;height:max-content;border-radius:4rem;margin-right:3px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(44 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip.active[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--color: #F0F0F0;border:none}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: 0;color:#143c5d;font-family:Mont Bold;font-weight:700;font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .date-time[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-start: var(--space-4) !important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{border-bottom:2px solid #dedede}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   .tonne[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .quantities[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#1e1e1e!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .ion-justify-content-between[_ngcontent-%COMP%]:last-child   ion-label[_ngcontent-%COMP%]:first-child{padding-top:calc(2.5 * var(--resH))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .ion-justify-content-between[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#0b305c!important;font-size:calc(38 * var(--res))!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-schedule[_ngcontent-%COMP%]{margin-bottom:calc(31.25 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .add-line[_ngcontent-%COMP%]{width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .add-qty-btn[_ngcontent-%COMP%]   .button-inner[_ngcontent-%COMP%]{gap:10px}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .mbottom[_ngcontent-%COMP%]{margin:calc(30 * var(--res)) 0;width:100%}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .select-type[_ngcontent-%COMP%]   .ion-label[_ngcontent-%COMP%]{font-family:Mont Regular!important;font-weight:400!important;font-size:calc(42 * var(--res))!important;color:#0b305c!important}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(42 * var(--res))}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .fMedium[_ngcontent-%COMP%]{font-family:Mont Light}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .no-mbottom[_ngcontent-%COMP%]{margin-bottom:0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-reduce[_ngcontent-%COMP%]{width:calc(50 * var(--res));height:calc(50 * var(--res));--padding-top: 0;--padding-bottom: 0}ion-content[_ngcontent-%COMP%]   #content[_ngcontent-%COMP%]   .btn-reduce[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1em}ion-content[_ngcontent-%COMP%]   .btn-validate[_ngcontent-%COMP%]{margin-top:1em;padding:0 calc(41 * var(--res));width:100%}.numerical-keyboard[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{justify-content:center;align-items:center;background-color:#fff}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:var(--fs-600);width:100%;height:calc(150 * var(--res));border:1px solid #e0e0e0}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:var(--fs-500)}.numerical-keyboard[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{font-size:calc(.7 * var(--fs-300));margin-top:calc(.5 * var(--container-padding))}']})}}return r})();var I=c(43556),R=c(93387),b=c(14599),A=c(56071),k=c(71333);function W(r,g){1&r&&n.nrm(0,"app-progress-spinner")}const j=function(r){return{active:r}};function J(r,g){if(1&r){const t=n.RV6();n.j41(0,"ion-chip",4),n.bIt("click",function(){n.eBV(t);const o=n.XpG().$implicit,i=n.XpG();return n.Njj(i.handleClick(o))}),n.j41(1,"ion-text"),n.EFF(2),n.k0s()()}if(2&r){const t=n.XpG().$implicit,e=n.XpG();n.Y8G("ngClass",n.eq3(2,j,(null==t?null:t.label)===e.selectedPackaging)),n.R7$(2),n.SpI(" ",null==t?null:t.label," ")}}function H(r,g){if(1&r&&(n.j41(0,"div",11),n.DNE(1,J,3,4,"ion-chip",12),n.k0s()),2&r){const t=g.$implicit;n.R7$(1),n.Y8G("ngIf",t&&(null==t?null:t.label))}}function K(r,g){if(1&r){const t=n.RV6();n.j41(0,"div",19),n.bIt("click",function(){n.eBV(t);const o=n.XpG().$implicit,i=n.XpG(2);return n.Njj(i.addQuantity(o))}),n.nrm(1,"ion-img",20),n.k0s()}}function Z(r,g){if(1&r){const t=n.RV6();n.j41(0,"div",15)(1,"div",16),n.bIt("click",function(){const i=n.eBV(t).$implicit,s=n.XpG(2);return n.Njj(!(i.quantity>0)&&s.addQuantity(i))}),n.DNE(2,K,2,0,"div",17),n.nrm(3,"app-product-card",18),n.k0s()()}if(2&r){const t=g.$implicit;n.R7$(2),n.Y8G("ngIf",t.quantity>0),n.R7$(1),n.Y8G("item",t)("isEdit",(null==t?null:t.quantity)>0)}}function nn(r,g){if(1&r&&(n.j41(0,"div",13),n.DNE(1,Z,4,3,"div",14),n.k0s()),2&r){const t=n.XpG();n.R7$(1),n.Y8G("ngForOf",t.productsVariety)("ngForTrackBy",t.trackByFn)}}function tn(r,g){1&r&&(n.j41(0,"div",21),n.nrm(1,"ion-img",22),n.j41(2,"ion-label"),n.EFF(3),n.nI1(4,"translate"),n.k0s()()),2&r&&(n.R7$(3),n.SpI(" ",n.bMT(4,1,"order-new-page.second-step.empty-offer-price")," "))}let en=(()=>{class r{constructor(t,e,o,i,s,_,u,x,kn,xn){this.router=t,this.productSrv=e,this.pricesService=o,this.modalCtrl=i,this.commonService=s,this.shceduleService=_,this.storageService=u,this.alertController=x,this.animationCtrl=kn,this.translateService=xn,this.cartItems=[],this.nbrOfProductSelect=0,this.isLoading=!0,this.selectedPackaging="Tous",this.selectedProducts=[],this.companySrv=(0,n.WQX)(I.B),this.enterAnimation=f=>{const $=f.shadowRoot,wn=this.animationCtrl.create().addElement($.querySelector("ion-backdrop")).fromTo("opacity","0.01","var(--backdrop-opacity)"),Tn=this.animationCtrl.create().addElement($.querySelector(".modal-wrapper")).keyframes([{offset:0,opacity:"0",transform:"scale(0)"},{offset:1,opacity:"0.99",transform:"scale(1)"}]);return this.animationCtrl.create().addElement(f).easing("ease-out").duration(500).addAnimation([wn,Tn])},this.leaveAnimation=f=>this.enterAnimation(f).direction("reverse"),this.storageService.getUserConnected()}ngOnInit(){var t=this;return(0,d.A)(function*(){t.isLoading=!0,t.storageService.remove("removals"),yield t.getPrices(),t.getProducts(),yield t.getPlanification()})()}getPlanification(){var t=this;return(0,d.A)(function*(){return t.isLoading=!0,t.shceduleService.availableRemovals=yield t.shceduleService.getPlanificationByStores({}),t.isLoading=!1})()}disabledCartProduct(t){return!this.shceduleService?.availableRemovals?.find(o=>o?.data?.product?._id===t?.product?._id)}getPrices(){var t=this;return(0,d.A)(function*(){t.isLoading=!0;const e={store:JSON.stringify({storeRef:"L10"}),companyId:t.companySrv.selectedCompanyForSalesOrderProcess?._id};t.prices=yield t.pricesService.getPricesForOrder(e),t.prices&&(t.productSrv.prices=t.prices),t.isLoading=!1})()}showMessageUnAvailablePlanification(){var t=this;return(0,d.A)(function*(){return yield(yield t.alertController.create({header:t.translateService.currentLang===l.T.French?"Indisponible":"Unavailable",message:t.translateService.currentLang===l.T.French?"Aucune planification en production n'est disponible pour ce produit. ":"No production planning is available for this product.",buttons:[{text:"OK",role:"OK",handler:()=>{}}]})).present()})()}trackByFn(t,e){return t}showDetail(){this.router.navigate(["/item-detail"])}addQuantity(t){var e=this;return(0,d.A)(function*(){e.shceduleService.cartItem=t;const o=yield e.modalCtrl.create({component:Y,cssClass:"modal",initialBreakpoint:.6,breakpoints:[0,.75,.5,.35,.9,.95,1],mode:"ios",componentProps:{}});yield o.present();const{data:i}=yield o.onWillDismiss();if(i?.quantity>0){e.nbrOfProductSelect++;const s={...t,quantity:i.quantity};e.productsVariety=e.productsVariety.map(u=>u.product._id===s.product._id&&u?.packaging?._id===s?.packaging?._id?s:u);const _=e.selectedProducts.findIndex(u=>u?.product?._id===s?.product?._id&&u?.packaging?._id===s?.packaging?._id);-1!==_?e.selectedProducts[_]=s:e.selectedProducts.push(s)}-1===i?.quantity&&e.presentAlert(e.shceduleService.cartItem)})()}getProducts(){this.isLoading=!0,this.shceduleService.cart=JSON.parse(this.storageService.load("cart")),this.productSrv.unit=this.productSrv?.prices[0]?.packaging?.unit,this.productSrv.prices?.map(e=>{let o;o=e?.amount,o&&this.cartItems.push({category:e?.product?.category,quantity:0,unitPrice:o,packaging:e.packaging,product:e.product})});const t=JSON.parse(this.storageService.load("stores"));this.availableStores=t?.find(e=>"L10"==e?.storeRef),this.storesPackaging=this.availableStores?.packaging,this.storesPackaging=this.storesPackaging?.sort((e,o)=>e?.unit?.value-o?.unit?.value),this.productsVariety=this.commonService.sortPricesByLabelProduct([...this.cartItems]),this.allCartItems=[...this.productsVariety],this.commonService.cartItems=this.cartItems,this.storageService.store("priceOffers",JSON.stringify(this.cartItems)),this.productsVariety?.length||(this.cartItems=JSON.parse(this.storageService.load("priceOffers"))),this.isLoading=!1}handleClick(t){this.productsVariety=this.allCartItems.filter(e=>e.packaging?._id==t?._id),this.selectedPackaging=t?.label}allProductsDisplay(){this.productsVariety=this.allCartItems,this.selectedPackaging="Tous"}presentAlert(t){var e=this;return(0,d.A)(function*(){const o=yield e.modalCtrl.create({component:V.A,cssClass:"modalClass",componentProps:{cart:t}});yield o.present();const{data:i}=yield o.onWillDismiss();"valider"===i?(e.removeProduct(t),yield o.onWillDismiss()):yield o.onWillDismiss()})()}removeProduct(t){let e=this.productsVariety.findIndex(o=>o===t);t.quantity=0,this.nbrOfProductSelect--,this.productsVariety.splice(e,1,t),this.selectedProducts=this.selectedProducts.filter(o=>o.product._id!==t.product._id)}nextStep(){const t=this.selectedProducts.filter(e=>e.quantity&&e.quantity>0);this.storageService.store("items",JSON.stringify(t)),this.router.navigate(["/order/particular-order/second-step"])}handleInput(t){const e=`${t.target.value}`.toLowerCase();return this.productsVariety=this.allCartItems.filter(o=>o?.product?.label?.toLowerCase().includes(e))?.sort((o,i)=>o.toString().toLowerCase().indexOf(e.toLowerCase())-i.toString().toLowerCase().indexOf(e.toLowerCase()))}openModalQuantity(t){this.addQuantity(t),console.log(t)}static{this.\u0275fac=function(e){return new(e||r)(n.rXU(C.Ix),n.rXU(y.b),n.rXU(R.A),n.rXU(a.W3),n.rXU(P.h),n.rXU(S.l),n.rXU(b.n),n.rXU(a.hG),n.rXU(a.Hx),n.rXU(M.E))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-first-step"]],decls:21,vars:19,consts:[[4,"ngIf"],[1,"title"],[1,"part-search-chip"],[1,"space-h-v","space-all"],[3,"ngClass","click"],["class","space-h-v",4,"ngFor","ngForOf","ngForTrackBy"],["id","container",1,"scroller-container"],["class","products",4,"ngIf"],["class","empty-list",4,"ngIf"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"readonly","disabled","click"],[1,"space-h-v"],[3,"ngClass","click",4,"ngIf"],[1,"products"],["class","item",4,"ngFor","ngForOf","ngForTrackBy"],[1,"item"],[1,"item-block",3,"click"],["class","icon-container",3,"click",4,"ngIf"],[3,"item","isEdit"],[1,"icon-container",3,"click"],["color","primary","src","/assets/icons/edit-white.svg","size","large"],[1,"empty-list"],["src","/assets/icons/Research paper-amico.svg"]],template:function(e,o){1&e&&(n.DNE(0,W,1,0,"app-progress-spinner",0),n.j41(1,"section")(2,"div",1)(3,"ion-label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.j41(6,"div",2)(7,"div",3)(8,"ion-chip",4),n.bIt("click",function(){return o.allProductsDisplay()}),n.j41(9,"ion-text"),n.EFF(10),n.nI1(11,"translate"),n.k0s()()(),n.DNE(12,H,2,1,"div",5),n.k0s()(),n.j41(13,"section",6),n.DNE(14,nn,2,2,"div",7),n.DNE(15,tn,5,3,"div",8),n.j41(16,"div",9)(17,"ion-button",10),n.bIt("click",function(){return o.nextStep()}),n.j41(18,"ion-label"),n.EFF(19),n.nI1(20,"translate"),n.k0s()()()()),2&e&&(n.Y8G("ngIf",o.isLoading),n.R7$(4),n.SpI(" ",n.bMT(5,11,"order-new-page.second-step.title-particular")," "),n.R7$(4),n.Y8G("ngClass",n.eq3(17,j,"Tous"===o.selectedPackaging)),n.R7$(2),n.SpI(" ",n.bMT(11,13,"order-new-page.second-step.all")," "),n.R7$(2),n.Y8G("ngForOf",o.storesPackaging)("ngForTrackBy",o.trackByFn),n.R7$(2),n.Y8G("ngIf",null==o.productsVariety?null:o.productsVariety.length),n.R7$(1),n.Y8G("ngIf",!(null!=o.cartItems&&o.cartItems.length)),n.R7$(2),n.Y8G("readonly",o.nbrOfProductSelect<=0)("disabled",o.nbrOfProductSelect<=0),n.R7$(2),n.SpI(" ",n.bMT(20,15,"order-new-page.second-step.next-button-label")," "))},dependencies:[a.Jm,a.ZB,a.KW,a.he,a.IO,p.YU,p.Sq,p.bT,A.V,k._,O.D9],styles:['@charset "UTF-8";*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;padding-top:0;height:100%;background-color:transparent;border-top-left-radius:calc(50 * var(--res));border-top-right-radius:calc(50 * var(--res));overflow-x:hidden;overflow-y:auto}#container[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{margin-bottom:5px}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));height:max-content;overflow:hidden;display:flex;flex-wrap:wrap;gap:30px;justify-content:center;width:100%}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]{width:max-content;position:relative}#container[_ngcontent-%COMP%]   .products[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-block[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{position:absolute;z-index:10;right:5px;top:5px;height:calc(75 * var(--res));width:calc(75 * var(--res));padding:4px;border-radius:50%;display:flex;justify-content:center;align-self:flex-start;background:#419CFB}#container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column;justify-content:center;height:100%}#container[_ngcontent-%COMP%]   .empty-list[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:50%;padding:1rem 0}.title[_ngcontent-%COMP%]{text-align:center;color:#143c5d;margin-bottom:1em}.title[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:22px;font-weight:700}.header[_ngcontent-%COMP%]{padding:calc(41 * var(--res))}.header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#143c5d;font-family:Mont Bold;font-weight:700;font-size:calc(45 * var(--res));margin-bottom:13%}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res));position:fixed;bottom:6%;width:calc(100% - 10 * var(--res))}.part-search[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]{padding-top:5px;margin-bottom:20px}.part-search[_ngcontent-%COMP%]   .searchbar[_ngcontent-%COMP%]{--background: transparent;height:calc(5 * var(--resH));border:none;--box-shadow: none;border-bottom:1px solid #1e1e1e}.part-search-chip[_ngcontent-%COMP%]{display:flex;white-space:nowrap;overflow-x:scroll;scrollbar-width:none;justify-content:center;-webkit-scrollbar-width:none;margin-bottom:.5em}.part-search-chip[_ngcontent-%COMP%]   .space-all[_ngcontent-%COMP%]{padding:0 0 0 calc(50 * var(--res))}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{--background: #fafafa;--color: var(--ion-color-primary);border:#E4F1FF 1px solid;margin-right:8px;padding:0 12px;width:max-content;height:max-content;border-radius:25px}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-family:var(--mont-bold);font-weight:700;padding-block:.5rem;font-size:11px}.part-search-chip[_ngcontent-%COMP%]   .space-h-v[_ngcontent-%COMP%]   ion-chip.active[_ngcontent-%COMP%]{color:#f0f0f0!important;background:var(--ion-color-primary)!important}']})}}return r})();var on=c(95908),rn=c(23985),cn=c(94440);function an(r,g){1&r&&n.nrm(0,"app-progress-spinner")}function sn(r,g){if(1&r){const t=n.RV6();n.j41(0,"ion-card",4),n.bIt("click",function(){const i=n.eBV(t).$implicit,s=n.XpG();return n.Njj(s.nextStep(i))}),n.j41(1,"div",5)(2,"div",6),n.nrm(3,"ion-img",7),n.k0s(),n.j41(4,"div",8)(5,"label",9),n.EFF(6),n.nI1(7,"truncateString"),n.k0s(),n.j41(8,"p"),n.EFF(9),n.nI1(10,"truncateString"),n.k0s(),n.j41(11,"ion-button",10),n.EFF(12),n.nI1(13,"translate"),n.k0s()(),n.j41(14,"div",11),n.nrm(15,"ion-icon",12),n.k0s()()()}if(2&r){const t=g.$implicit,e=n.XpG();n.R7$(6),n.JRh(n.i5U(7,3,t.name,20)),n.R7$(3),n.JRh(n.i5U(10,6,null==e.commonService||null==e.commonService.user||null==e.commonService.user.address?null:e.commonService.user.address.region,28)),n.R7$(3),n.SpI(" ",n.bMT(13,9,"order-new-page.first-step.choose")," ")}}let ln=(()=>{class r{constructor(t,e,o,i,s,_,u,x){this.router=t,this.storageSrv=e,this.commonService=o,this.companySrv=i,this.priceSrv=s,this.packagingSrv=_,this.scheduleService=u,this.userService=x,this.isLoading=!0,this.isSelectedCompany=!1,this.offset=0,this.limit=20,this.skeletons=[1,2,3,4,5,6],this.filterData={name:"",solToId:0,phone:"",regionCom:""}}getSuppliers(){var t=this;return(0,d.A)(function*(){t.skeletons=[1,2,3,4,5,6],t.isLoading=!0;const e={...t.filterData,offset:t.offset,limit:t.limit,commercialRegion:t.commonService?.user?.address?.commercialRegion},o=t.commonService.user?.associatedSuppliers;if(o&&Array.isArray(o)&&o.length>0)t.suppliers=o;else{const i=yield t.companySrv.getParticularCompanies(e);t.suppliers=i.data}t.offset=t.offset+t.limit,t.isLoading=!1,t.skeletons=[]})()}selectCompany(t){this.selectedCompany=t,this.isSelectedCompany=!0}ngOnInit(){var t=this;return(0,d.A)(function*(){yield t.loadUser(),yield t.getSuppliers(),yield t.getStore()})()}ionViewWillEnter(){var t=this;return(0,d.A)(function*(){t.isLoading=!1})()}getStore(){var t=this;return(0,d.A)(function*(){t.isLoading=!0;const e={companyId:t.companySrv.selectedCompanyForSalesOrderProcess?._id},o=yield t.priceSrv.getStores(e);o&&(t.initData=o,t.storageSrv.store("stores",JSON.stringify(o))),t.isLoading=!1})()}loadUser(){var t=this;return(0,d.A)(function*(){const e=t.commonService.user._id;if(e)try{t.commonService.user=yield t.userService.find(e)}catch(o){console.error("Failed to load user:",o)}})()}nextStep(t){const e=this.storageSrv.load("items"),o=e?JSON.parse(e):[];this.cart={company:t?._id,items:o,amount:null},this.storageSrv.store("cart",JSON.stringify(this.cart)),this.storageSrv.store("supplier",JSON.stringify(t)),this.router.navigate(["order/particular-order/third-step"])}static{this.\u0275fac=function(e){return new(e||r)(n.rXU(C.Ix),n.rXU(b.n),n.rXU(P.h),n.rXU(I.B),n.rXU(R.A),n.rXU(on.L),n.rXU(S.l),n.rXU(rn.D))}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-second-step"]],decls:7,vars:5,consts:[[4,"ngIf"],["id","container"],[1,"title"],["class","custom-card",3,"click",4,"ngFor","ngForOf"],[1,"custom-card",3,"click"],[1,"container"],[1,"image"],["src","../../../../assets/icons/man.png"],[1,"company"],["for",""],["expand","block"],[1,"chevron"],["slot","end","name","chevron-forward-outline","color","primary"]],template:function(e,o){1&e&&(n.DNE(0,an,1,0,"app-progress-spinner",0),n.j41(1,"div",1)(2,"div",2)(3,"label"),n.EFF(4),n.nI1(5,"translate"),n.k0s()(),n.DNE(6,sn,16,11,"ion-card",3),n.k0s()),2&e&&(n.Y8G("ngIf",o.isLoading),n.R7$(4),n.JRh(n.bMT(5,3,"order-new-page.first-step.choose-store")),n.R7$(2),n.Y8G("ngForOf",o.suppliers))},dependencies:[a.Jm,a.b_,a.iq,a.KW,p.Sq,p.bT,k._,cn.c,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}#container[_ngcontent-%COMP%]{padding:calc(41 * var(--res));overflow:auto}.title[_ngcontent-%COMP%]{text-align:center;color:#143c5d;font-size:22px}.custom-card[_ngcontent-%COMP%]{margin:12px 0;border:1px solid #dcdcdc;border-radius:8px}.custom-card[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:126px;padding:calc(41 * var(--res))}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;gap:1em;padding:1em}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border:2px solid #419CFB;border-radius:50%;padding:10px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:43px;width:43px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5em;align-items:baseline;color:#143c5d;width:75%}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:20px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .company[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{color:#fff;border-radius:4px;width:100%;background-color:#143c5d}.custom-card[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .chevron[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}ion-segment[_ngcontent-%COMP%]{--indicator-color: #0078d4;--background: transparent}ion-segment-button[checked][_ngcontent-%COMP%]{background-color:#0078d4;color:#fff}"]})}}return r})();var dn=c(765),gn=c(21635),pn=c(54357),un=c(81559),h=c(88233),z=c(26409),mn=c(94934),hn=c(45312),Cn=c(33607);let Pn=(()=>{class r{constructor(t,e,o,i){this.http=t,this.commonSrv=e,this.baseUrlService=o,this.storageSrv=i,this.url=this.baseUrlService.getOrigin()+hn.c.basePath+"order-supplier"}create(t){var e=this;return(0,d.A)(function*(){try{return yield(0,mn.s)(e.http.post(`${e.url}`,t))}catch(o){const s={message:e.commonSrv.getError("",o).message,color:"danger"};return yield e.commonSrv.showToast(s),o}})()}static{this.\u0275fac=function(e){return new(e||r)(n.KVO(z.Qq),n.KVO(P.h),n.KVO(Cn.K),n.KVO(b.n))}}static{this.\u0275prov=n.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();var _n=c(54648);function On(r,g){1&r&&n.nrm(0,"app-progress-spinner")}const bn=[{path:"method-order",loadChildren:()=>c.e(1501).then(c.bind(c,31501)).then(r=>r.MethodOrderPageModule)},{path:"",component:G,children:[{path:"first-step",component:en},{path:"second-step",component:ln},{path:"third-step",component:(()=>{class r{selectClient(t){this.clientId=t}constructor(){this.itemsLimited=[],this.isValidate=!1,this.modePayment="",this.isLoading=!0,this.promoCodeAction=pn.F,this.clientId=null,this.slideOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:16},this.slideProductOpts={initialSlide:0,speed:400,slidesPerView:4,spaceBetween:10},this.products=[1,2,3,4],this.meansOfPayments=[],this.orderSrv=(0,n.WQX)(un.Q),this.orderParticularSrv=(0,n.WQX)(Pn),this.storageService=(0,n.WQX)(b.n),this.productSrv=(0,n.WQX)(y.b),this.commonSrv=(0,n.WQX)(P.h),this.translateService=(0,n.WQX)(M.E),this.isFrench=this.translateService.currentLang===l.T.French,this.storageService.getUserConnected()}ngOnInit(){this.isLoading=!0}ionViewWillEnter(){var t=this;return(0,d.A)(function*(){t.isLoading=!0,t.cart=JSON.parse(t.storageService.load("cart")),t.supplier=JSON.parse(t.storageService.load("supplier")),console.log(t.supplier),delete t.cart.amount,t.cart.user=t.commonSrv?.user,t.itemsLimited=t.cart?.items?.slice(0,3),t.storageService.store("cart",JSON.stringify(t.cart)),t.isLoading=!1,t.PaymentMode=t.commonSrv?.user?.category==T.s.Particular?"credit_pay":"account_pay"})()}checkPaymentMethod(){this.isFactory=(this.cart.shipping.retrievePoint&&this.cart?.shipping?.retrievePoint?.name)?.toUpperCase()==this.cart?.store?.label?.toUpperCase(),this.meansOfPayments=P.H.filter(t=>!(t.label===h.ru.CREDIT&&1!==this.cart?.renderType||t.label===h.ru.CREDIT&&1===this.cart?.renderType&&!this.isFactory)&&this.commonSrv.user.authorizations.includes(t.label))}selectPaymentMode(t){var e=this;return(0,d.A)(function*(){e.isValidate=!1,e.modePayment=t,e.meansOfPayments[0].label===h.ru.MY_ACCOUNT&&(e.meansOfPayments[0].image=e.meansOfPayments[0].label===h.ru.MY_ACCOUNT?"/assets/logos/cimencam.svg":"/assets/images/cimencam.png");const o=yield e.commonSrv.modalCtrl.create({component:t===h.ru.CREDIT?gn.X:dn.c,initialBreakpoint:t===h.ru.CREDIT?.75:t===h.ru.VISA?1:.7,cssClass:"modal",breakpoints:[0,.8,.85,1],mode:"ios",componentProps:{title:e.isFrench?v.b7[t]?.titleForFrench:v.b7[t]?.titleForEnglish,type:t,cart:e.cart,amount:e.orderPrice?.TTC}});yield o.present(),yield o.onWillDismiss()})()}doPaiement(){var t=this;return(0,d.A)(function*(){t.isLoading=!0,t.user=JSON.parse(t.storageService.load("USER_INFO")),delete t.cart.company,delete t.cart.user;let e={user:t.user,supplier:t.supplier,cart:{...t.cart},qrCodeData:[]};t.orderSrv.response=yield t.orderParticularSrv.create(e),t.isLoading=!1,t.orderSrv.response instanceof z.yz?t.commonSrv.showToast({color:"danger",message:t.orderSrv.response.message}):t.commonSrv.router.navigate(["order/new/four-step"])})()}static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275cmp=n.VBU({type:r,selectors:[["app-third-step"]],decls:17,vars:13,consts:[[4,"ngIf"],[1,"scroll-container"],[3,"cart","orderPrice","itemsLimited","shippingInfo","shipping"],[1,"profile-container"],[1,"avatar"],["name","person-outline"],[1,"info"],[1,"title"],[1,"name"],[1,"btn-validate"],["color","primary","expand","block",1,"btn--meduim","btn--upper",3,"click"]],template:function(e,o){1&e&&(n.DNE(0,On,1,0,"app-progress-spinner",0),n.j41(1,"section",1),n.nrm(2,"app-purchase-summary",2),n.j41(3,"div",3)(4,"div",4),n.nrm(5,"ion-icon",5),n.k0s(),n.j41(6,"div",6)(7,"div",7),n.EFF(8),n.nI1(9,"translate"),n.k0s(),n.j41(10,"div",8),n.EFF(11),n.k0s()()()(),n.j41(12,"div",9)(13,"ion-button",10),n.bIt("click",function(){return o.doPaiement()}),n.j41(14,"ion-label"),n.EFF(15),n.nI1(16,"translate"),n.k0s()()()),2&e&&(n.Y8G("ngIf",o.isLoading),n.R7$(2),n.Y8G("cart",o.cart)("orderPrice",o.orderPrice)("itemsLimited",o.itemsLimited)("shippingInfo",o.shippingInfo)("shipping",o.shipping),n.R7$(6),n.SpI(" ",n.bMT(9,9,"recap-scan.supplier")," "),n.R7$(3),n.JRh(null==o.supplier?null:o.supplier.name),n.R7$(4),n.SpI(" ",n.bMT(16,11,"order-new-page.third-step.next-button-label")," "))},dependencies:[a.Jm,a.iq,a.he,p.bT,_n.N,k._,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.scroll-container[_ngcontent-%COMP%]{height:100%}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]{padding:calc(41 * var(--res)) 0;height:80%;margin:0 calc(41 * var(--res));background-color:#fff;border-radius:calc(30 * var(--res));overflow-x:hidden;overflow-y:auto;box-shadow:-6px 14px 20px 9px #35363633}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fbold[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fextrabold[_ngcontent-%COMP%]{font-family:Mont Bold;font-weight:700;font-size:calc(40.7 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .fMeduim[_ngcontent-%COMP%]{font-family:Mont Light}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:#143c5d!important}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .recap-title[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]{margin-bottom:calc(37.5 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .totalTtc[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding:0 calc(37.5 * var(--res));height:calc(120 * var(--res));display:flex;background-color:#c62f45e0;color:#fff;align-items:center;justify-content:space-between;border-radius:calc(20 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin:1em 0;padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .type-truck[_ngcontent-%COMP%]   ion-toggle[_ngcontent-%COMP%]{color:#0d7d3d;--background-checked: #0d7d3d}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]{padding:0 calc(41 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{display:block}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .padding-horizontal[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:calc(40 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .discount-details[_ngcontent-%COMP%]{padding-top:.5em;padding-bottom:1em;border-top:1px solid rgba(128,128,128,.448)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-family:Mont Bold}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;display:block;font-size:calc(45 * var(--res))}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]{height:calc(12.25 * var(--resH));box-shadow:0 3.08499px 10.7975px #00000016;border:.771248px solid rgba(218,218,218,.47);border-radius:3.85624px;--background: #ffffff}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{--background: var(--ion-color-primary)}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .payment-container[_ngcontent-%COMP%]   ion-slide[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.scroll-container[_ngcontent-%COMP%]   .recap-container[_ngcontent-%COMP%]   .cancel-discount[_ngcontent-%COMP%]{display:inline-block;padding:.6em;color:#fff;background-color:var(--ion-color-secondary);border-radius:5px;margin:0 0 1em}.btn-validate[_ngcontent-%COMP%]{padding:0 calc(25 * var(--res));position:fixed;bottom:3%;width:calc(100% - 10 * var(--res))}.avatar[_ngcontent-%COMP%]{width:40px;height:40px;background:#e1e1e1;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:12px}.avatar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#666}.info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.title[_ngcontent-%COMP%]{font-size:12px;color:#666;margin-bottom:2px}.name[_ngcontent-%COMP%]{font-size:16px;color:#333;font-family:Mont SemiBold}.profile-container[_ngcontent-%COMP%]{display:flex;align-items:center;background:#E4EBF3;padding:12px;border-radius:12px;margin:24px calc(41 * var(--res))}"]})}}return r})()},{path:"foor-step",component:c(51049).I},{path:"",redirectTo:"first-step",pathMatch:"full"},{path:"**",redirectTo:"first-step"}]},{path:"**",redirectTo:""}];let fn=(()=>{class r{static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[C.iI.forChild(bn),C.iI]})}}return r})();var yn=c(93887);let Sn=(()=>{class r{static{this.\u0275fac=function(e){return new(e||r)}}static{this.\u0275mod=n.$C({type:r})}static{this.\u0275inj=n.G2t({imports:[m.YN,a.bv,p.MD,yn.G,O.h,m.X1,fn]})}}return r})()}}]);