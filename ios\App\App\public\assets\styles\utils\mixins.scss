

@mixin truncate-text {
  text-overflow: ellipsis;
  overflow: hidden !important;
  text-wrap: nowrap;
}

@mixin no-style-list {
  list-style-type: none;
  padding-left: 0;
}
@mixin border-status {
  padding: 2px;
  border-radius: 4px;
}

@mixin align-middle {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin v-center {
  display: flex;
  align-items: center;
}

@mixin vh-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin h-center {
  display: flex;
  justify-content: center;
}

@mixin align-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@mixin align-column-between {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@mixin align-column-around {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

@mixin v-align {
  display: flex;
  align-items: center;
}

@mixin v-h-align {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin v-h-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin v-h-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

@mixin v-h-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@mixin h-center {
  margin-left: auto;
  margin-right: auto;
}

@mixin shadow {
  box-shadow: -6px 14px 20px 9px rgba(53, 54, 54, 0.2);
  -webkit-box-shadow: -6px 14px 20px 9px rgba(53, 54, 54, 0.2);
}

@mixin no-padding-margin {
  padding: 0px;
  margin: 0px;
}

@mixin appearance($value: none) {
  -webkit-appearance: $value;
  -moz-appearance: $value;
  -ms-appearance: $value;
  -o-appearance: $value;
  appearance: $value;
}

@mixin rotate-180-left {
  -webkit-transform: scale(-1, 1);
  -moz-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  -o-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

// @mixin bg-primary {
//   --ion-background-color: mixins.$color-primary;
// }
