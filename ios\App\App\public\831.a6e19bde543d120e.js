"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[831],{60831:(g,n,t)=>{t.r(n),t.d(n,{JdeLoadsNotDispatchedPageModule:()=>v});var c=t(77897),h=t(56610),l=t(74657),r=t(76098),m=t(93887),d=t(37222),i=t(77575),e=t(83837),o=t(2978);const u=[{path:"",component:e.O}];let f=(()=>{class s{static{this.\u0275fac=function(a){return new(a||s)}}static{this.\u0275mod=o.$C({type:s})}static{this.\u0275inj=o.G2t({imports:[i.iI.forChild(u),i.iI]})}}return s})(),v=(()=>{class s{static{this.\u0275fac=function(a){return new(a||s)}}static{this.\u0275mod=o.$C({type:s})}static{this.\u0275inj=o.G2t({imports:[d.YN,c.bv,h.MD,m.G,r.Bb,l.h,d.X1,f]})}}return s})()}}]);