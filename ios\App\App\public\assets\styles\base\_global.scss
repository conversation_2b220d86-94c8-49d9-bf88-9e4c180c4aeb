html {
  scroll-behavior: smooth;
  min-width: 280px;
}

ion-tab-button .button-native {
  padding: 0;
  /* Enlever le padding */
}

html {
  font-family: var(--mont-regular);
  // font-size: clamp(1rem, 1vw - 1rem, 10rem);
}

body {
  scroll-behavior: smooth;
  font-display: optional;
}

.container {
  width: 100%;
  padding-left: calc(50% - var(--container) / 2);
  padding-right: calc(50% - var(--container) / 2);
}

.main-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.txt-dark {
  color: var(--clr-dark-400);
}

h1,
h2,
h3,
h4,
p {
  max-width: 86ch;
}

router-outlet {
  flex: 1;
}

.toolbar-container {
  padding: 0 !important;
}