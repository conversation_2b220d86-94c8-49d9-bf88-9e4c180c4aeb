"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1468],{39316:(f,M,c)=>{c.d(M,{b:()=>C});var n=c(73308),p=c(26409),m=c(94934),O=c(45312),l=c(2978),_=c(82571),b=c(33607),v=c(77897);let C=(()=>{class s{constructor(g,r,a,i){this.http=g,this.commonSrv=r,this.baseUrlService=a,this.toastController=i,this.prices=[],this.currentDataProductScan=[],this.dataQrCode=[],this.url=this.baseUrlService.getOrigin()+O.c.basePath+"products"}getProducts(g){var r=this;return(0,n.A)(function*(){try{let a=new p.Nl;return g?.limit&&(a=a.append("limit",g?.limit)),yield(0,m.s)(r.http.get(r.url,{params:a}))}catch(a){const u={message:r.commonSrv.getError("",a).message,color:"danger"};return yield r.commonSrv.showToast(u),a}})()}getProduct(g){var r=this;return(0,n.A)(function*(){try{return yield(0,m.s)(r.http.get(`${r.url}/${g}`))}catch(a){const u={message:r.commonSrv.getError("",a).message,color:"danger"};return yield r.commonSrv.showToast(u),a}})()}static{this.\u0275fac=function(r){return new(r||s)(l.KVO(p.Qq),l.KVO(_.h),l.KVO(b.K),l.KVO(v.K_))}}static{this.\u0275prov=l.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})()},56071:(f,M,c)=>{c.d(M,{V:()=>I});var n=c(2978),p=c(68953),m=c(58133),O=c(82571),l=c(77897),_=c(56610),b=c(74657),v=c(11244);const C=function(o){return{"text-content":o}};function s(o,d){if(1&o&&(n.j41(0,"span",6),n.EFF(1),n.nI1(2,"capitalize"),n.k0s()),2&o){const t=n.XpG();n.Y8G("ngClass",n.eq3(4,C,t.isEdit)),n.R7$(1),n.SpI(" ",n.bMT(2,2,null==t.item||null==t.item.packaging?null:t.item.packaging.label)," ")}}function h(o,d){if(1&o&&(n.j41(0,"ion-label"),n.EFF(1),n.k0s()),2&o){const t=n.XpG(2);n.R7$(1),n.SpI(" ",null==t.item||null==t.item.packaging?null:t.item.packaging.label," ")}}function g(o,d){if(1&o&&(n.j41(0,"ion-label"),n.EFF(1),n.nI1(2,"number"),n.k0s()),2&o){const t=n.XpG(2);n.R7$(1),n.SpI(" ",n.brH(2,1,null==t.item?null:t.item.unitPrice,"","fr-FR")," F ")}}function r(o,d){if(1&o&&(n.j41(0,"ion-label"),n.EFF(1),n.k0s()),2&o){const t=n.XpG(2);n.R7$(1),n.SpI(" ",null==t.item||null==t.item.packaging?null:t.item.packaging.label," ")}}const a=function(o){return{"empty-product":o}},i=function(o){return[o]};function u(o,d){if(1&o&&(n.j41(0,"div",10)(1,"div",11),n.DNE(2,h,2,1,"ion-label",12),n.k0s(),n.j41(3,"div",13),n.DNE(4,g,3,5,"ion-label",12),n.DNE(5,r,2,1,"ion-label",12),n.k0s()()),2&o){const t=n.XpG();n.Y8G("ngClass",n.eq3(4,a,t.isCatalogue)),n.R7$(2),n.Y8G("ngIf",!n.eq3(6,i,t.CompanyCategory.Baker).includes(null==t.commonSrv||null==t.commonSrv.user||null==t.commonSrv.user.company?null:t.commonSrv.user.company.category)),n.R7$(2),n.Y8G("ngIf",!n.eq3(8,i,t.CompanyCategory.Baker).includes(null==t.commonSrv||null==t.commonSrv.user||null==t.commonSrv.user.company?null:t.commonSrv.user.company.category)),n.R7$(1),n.Y8G("ngIf",n.eq3(10,i,t.CompanyCategory.Baker).includes(null==t.commonSrv||null==t.commonSrv.user||null==t.commonSrv.user.company?null:t.commonSrv.user.company.category))}}function y(o,d){if(1&o&&(n.j41(0,"ion-label"),n.EFF(1),n.nI1(2,"translate"),n.nI1(3,"translate"),n.k0s()),2&o){const t=n.XpG(2);n.R7$(1),n.SpI(" ",t.isEdit?n.bMT(3,3,"order-new-page.third-step.modify-button-label"):n.bMT(2,1,"order-new-page.third-step.next-button-label")," ")}}const E=function(o){return{"edit-product":o}};function S(o,d){if(1&o&&(n.j41(0,"div",14)(1,"div",15),n.DNE(2,y,4,5,"ion-label",12),n.k0s()()),2&o){const t=n.XpG();n.R7$(1),n.Y8G("ngClass",n.eq3(2,E,t.isEdit&&(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.category)==t.userCategory.Particular)),n.R7$(1),n.Y8G("ngIf",n.eq3(4,i,t.userCategory.Particular).includes(null==t.commonSrv||null==t.commonSrv.user?null:t.commonSrv.user.category))}}const D=function(o){return{"card-content":o}};let I=(()=>{class o{constructor(t){this.commonSrv=t,this.modalIsOpen=new n.bkB,this.CompanyCategory=p.kJ,this.userCategory=m.s}ngOnInit(){}static{this.\u0275fac=function(P){return new(P||o)(n.rXU(O.h))}}static{this.\u0275cmp=n.VBU({type:o,selectors:[["app-product-card"]],inputs:{item:"item",isEdit:"isEdit",isCatalogue:"isCatalogue"},outputs:{modalIsOpen:"modalIsOpen"},decls:14,vars:18,consts:[[1,"product-card-container"],[1,"containerize",3,"ngClass"],[1,"product-container"],[1,"image-container"],[1,"prod-image"],[3,"src"],[3,"ngClass"],[3,"ngClass",4,"ngIf"],["class","price-container",3,"ngClass",4,"ngIf"],["class","price-container",4,"ngIf"],[1,"price-container",3,"ngClass"],[1,"packaging"],[4,"ngIf"],[1,"price"],[1,"price-container"],[1,"price-particular",3,"ngClass"]],template:function(P,e){1&P&&(n.j41(0,"div",0)(1,"ion-card")(2,"div",1)(3,"div",2)(4,"div",3)(5,"div",4),n.nrm(6,"ion-img",5),n.k0s()(),n.j41(7,"div",6)(8,"ion-label",6),n.EFF(9),n.nI1(10,"capitalize"),n.k0s(),n.DNE(11,s,3,6,"span",7),n.k0s()(),n.DNE(12,u,6,12,"div",8),n.DNE(13,S,3,6,"div",9),n.k0s()()()),2&P&&(n.R7$(2),n.Y8G("ngClass",n.eq3(10,D,e.isEdit&&(null==e.commonSrv||null==e.commonSrv.user?null:e.commonSrv.user.category)!==e.userCategory.Particular)),n.R7$(4),n.Y8G("src",null==e.item||null==e.item.product?null:e.item.product.image),n.R7$(1),n.Y8G("ngClass",(null==e.commonSrv||null==e.commonSrv.user?null:e.commonSrv.user.category)===e.userCategory.Particular?"product-particular":"product-label"),n.R7$(1),n.Y8G("ngClass",n.eq3(12,C,e.isEdit)),n.R7$(1),n.SpI(" ",n.bMT(10,8,null==e.item||null==e.item.product?null:e.item.product.label)," "),n.R7$(2),n.Y8G("ngIf",(null==e.commonSrv||null==e.commonSrv.user?null:e.commonSrv.user.category)===e.userCategory.Particular),n.R7$(1),n.Y8G("ngIf",!n.eq3(14,i,e.userCategory.Particular).includes(null==e.commonSrv||null==e.commonSrv.user?null:e.commonSrv.user.category)),n.R7$(1),n.Y8G("ngIf",n.eq3(16,i,e.userCategory.Particular).includes(null==e.commonSrv||null==e.commonSrv.user?null:e.commonSrv.user.category)))},dependencies:[l.b_,l.KW,l.he,_.YU,_.bT,_.QX,b.D9,v.F],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}.product-card-container[_ngcontent-%COMP%]{height:calc(550 * var(--res));width:calc(420 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100%;border-radius:10px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]{height:100%;background-color:#e7eaef}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize.card-content[_ngcontent-%COMP%]{background-color:#143c5d}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]{height:85%;display:flex;justify-content:center;align-items:center;flex-direction:column;position:relative}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{display:flex;width:100%;height:90%;padding-top:.5em}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .prod-image[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;width:100%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .prod-image[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{height:80%;margin-top:13px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{height:20px;width:25%;display:flex;justify-content:center;align-self:flex-start}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{height:20px;width:20px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]{display:flex;width:100%;color:#0b305c;padding-left:calc(25 * var(--res));margin-bottom:10px}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont SemiBold;font-size:calc(35 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-label[_ngcontent-%COMP%]   ion-label.text-content[_ngcontent-%COMP%]{color:#fff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]{width:100%;display:flex;color:#0b305c;text-align:center;margin-bottom:10px;flex-direction:column}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(35 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .containerize[_ngcontent-%COMP%]   .product-particular[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:10px;color:#6d839d}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]{display:flex;height:20%;width:100%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]{width:48%;display:flex;justify-content:flex-start;align-items:center;padding-inline-start:calc(25 * var(--res))}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(35 * var(--res));font-family:Mont Regular;color:#6d839d;height:75%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{width:70%;color:#fff;border-radius:10px 0 0;background-color:#419cfb;display:flex;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(37 * var(--res));font-family:Mont SemiBold;margin-bottom:10%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-particular[_ngcontent-%COMP%]{width:100%;color:#fff;background-color:#143c5d;display:flex;justify-content:center;align-items:center}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-particular[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:calc(37 * var(--res));font-family:Mont SemiBold;margin-bottom:7%}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .edit-product[_ngcontent-%COMP%]{background-color:#419cfb!important}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]{width:100%;justify-content:center;background-color:#419cfb}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .packaging[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{color:#fff}.product-card-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .empty-product[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{width:0%;visibility:hidden}@media only screen and (max-width: 400px){.product-card-container[_ngcontent-%COMP%]{height:calc(450 * var(--res));width:calc(390 * var(--res))}}"]})}}return o})()}}]);