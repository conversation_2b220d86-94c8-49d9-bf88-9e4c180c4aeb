"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7086],{47086:(v,s,o)=>{o.r(s),o.d(s,{AllOrdersPageModule:()=>u});var e=o(77897),d=o(37222),i=o(56610),l=o(74657),g=o(77575),m=o(73308),p=o(58133),M=o(88233),n=o(2978),O=o(82571),P=o(14599);function C(t,_){1&t&&(n.qSk(),n.joV(),n.j41(0,"ion-card",14)(1,"ion-card-content")(2,"div",5)(3,"div",6),n.qSk(),n.j41(4,"svg",15),n.nrm(5,"path",16)(6,"path",17),n.k0s()(),n.jo<PERSON>(),n.j41(7,"label",10),n.<PERSON><PERSON>(8),n.nI1(9,"translate"),n.k0s()(),n.j41(10,"a",18),n.qSk(),n.j41(11,"svg",11),n.nrm(12,"path",12)(13,"path",9),n.k0s()()()()),2&t&&(n.R7$(8),n.JRh(n.bMT(9,1,"all-orders-page.employees-orders")))}const h=[{path:"",component:(()=>{class t{constructor(c,r,a){this.location=c,this.commonSrv=r,this.storageService=a,this.userCategory=p.s,this.orderAction=M.T3}ngOnInit(){}back(){this.location.back()}ionViewWillEnter(){var c=this;return(0,m.A)(function*(){c.storageService.getUserConnected()})()}static{this.\u0275fac=function(r){return new(r||t)(n.rXU(i.aZ),n.rXU(O.h),n.rXU(P.n))}}static{this.\u0275cmp=n.VBU({type:t,selectors:[["app-all-orders"]],decls:22,vars:7,consts:[[1,"header"],["slot","start","src","/assets/icons/arrow-back.svg",3,"click"],[1,"title"],[1,"ion-padding","ion-content-padding"],["routerLink","/order/history"],[1,"card-flex"],[1,"img-container"],["xmlns","http://www.w3.org/2000/svg","width","24","height","24","viewBox","0 0 24 24",1,"img-container-svg"],["d","M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"],["d","M0 0h24v24H0z","fill","none"],[1,"card-flex-text"],["xmlns","http://www.w3.org/2000/svg","width","30","height","30","viewBox","0 0 24 24",1,"ion-image"],["d","M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"],["routerLink","/order/employees-orders",4,"ngIf"],["routerLink","/order/employees-orders"],["width","40","height","40","xmlns","http://www.w3.org/2000/svg","viewBox","0 0 512 512",1,"img-container-svg"],["d","M459.94 53.25a16.06 16.06 0 00-23.22-.56L424.35 65a8 8 0 000 11.31l11.34 11.32a8 8 0 0011.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38zM399.34 90L218.82 270.2a9 9 0 00-2.31 3.93L208.16 299a3.91 3.91 0 004.86 4.86l24.85-8.35a9 9 0 003.93-2.31L422 112.66a9 9 0 000-12.66l-9.95-10a9 9 0 00-12.71 0z"],["d","M386.34 193.66L264.45 315.79A41.08 41.08 0 01247.58 326l-25.9 8.67a35.92 35.92 0 01-44.33-44.33l8.67-25.9a41.08 41.08 0 0110.19-16.87l122.13-121.91a8 8 0 00-5.65-13.66H104a56 56 0 00-56 56v240a56 56 0 0056 56h240a56 56 0 0056-56V199.31a8 8 0 00-13.66-5.65z"],["routerLink",""]],template:function(r,a){1&r&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-img",1),n.bIt("click",function(){return a.back()}),n.k0s(),n.j41(3,"ion-title",2),n.EFF(4),n.nI1(5,"translate"),n.k0s()()(),n.j41(6,"ion-content",3)(7,"ion-card",4)(8,"ion-card-content")(9,"div",5)(10,"div",6),n.qSk(),n.j41(11,"svg",7),n.nrm(12,"path",8)(13,"path",9),n.k0s()(),n.joV(),n.j41(14,"label",10),n.EFF(15),n.nI1(16,"translate"),n.k0s()(),n.j41(17,"a"),n.qSk(),n.j41(18,"svg",11),n.nrm(19,"path",12)(20,"path",9),n.k0s()()()(),n.DNE(21,C,14,3,"ion-card",13),n.k0s()),2&r&&(n.R7$(4),n.JRh(n.bMT(5,3,"all-orders-page.title")),n.R7$(11),n.JRh(n.bMT(16,5,"all-orders-page.my-orders")),n.R7$(6),n.Y8G("ngIf",null==a.commonSrv||null==a.commonSrv.user?null:a.commonSrv.user.authorizations.includes(a.orderAction.VIEW_EMPLOYEES)))},dependencies:[i.bT,e.b_,e.I9,e.W9,e.eU,e.KW,e.BC,e.ai,e.N7,e.oY,g.Wk,l.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:calc(41 * var(--res));--border-color: transparent;--background: transparent;background:#fff;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-img[_ngcontent-%COMP%]{width:calc(50 * var(--res));margin-right:calc(25 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:calc(55 * var(--res));text-align:start}.ion-content-padding[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:100px;padding:10px;margin-bottom:2rem}.ion-content-padding[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:.5rem}.ion-content-padding[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .card-flex[_ngcontent-%COMP%]{display:flex;align-items:center}.ion-content-padding[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .card-flex[_ngcontent-%COMP%]   .img-container[_ngcontent-%COMP%]{display:flex;justify-content:center;height:60px;width:60px;background-color:#f5f5f5;border-radius:50%}.ion-content-padding[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .card-flex[_ngcontent-%COMP%]   .img-container[_ngcontent-%COMP%]   .img-container-svg[_ngcontent-%COMP%]{height:30px;width:30px;display:block;margin:auto;fill:#143c5d}.ion-content-padding[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .card-flex[_ngcontent-%COMP%]   .card-flex-text[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e;margin-left:10px}.ion-content-padding[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   .ion-image[_ngcontent-%COMP%]{fill:#143c5d}"]})}}return t})()}];let b=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[g.iI.forChild(h),g.iI]})}}return t})(),u=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=n.$C({type:t})}static{this.\u0275inj=n.G2t({imports:[i.MD,d.YN,e.bv,l.h,b]})}}return t})()}}]);