"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[652],{14561:(P,v,o)=>{o.d(v,{a:()=>h,b:()=>s,p:()=>d});const d=(y,...p)=>console.warn(`[Ionic Warning]: ${y}`,...p),s=(y,...p)=>console.error(`[Ionic Error]: ${y}`,...p),h=(y,...p)=>console.error(`<${y.tagName.toLowerCase()}> must be used inside ${p.join(" or ")}.`)},66556:(P,v,o)=>{o.r(v),o.d(v,{ion_refresher:()=>_,ion_refresher_content:()=>W});var d=o(73308),s=o(29814),h=o(42673),y=o(73888),p=o(98717),c=o(46184),T=o(95480),u=o(28775),E=o(89979),w=o(69324),C=o(58121);o(94706);const S=e=>{const t=e.querySelector("ion-spinner"),r=t.shadowRoot.querySelector("circle"),n=e.querySelector(".spinner-arrow-container"),l=e.querySelector(".arrow-container"),f=l?l.querySelector("ion-icon"):null,a=(0,u.c)().duration(1e3).easing("ease-out"),i=(0,u.c)().addElement(n).keyframes([{offset:0,opacity:"0.3"},{offset:.45,opacity:"0.3"},{offset:.55,opacity:"1"},{offset:1,opacity:"1"}]),m=(0,u.c)().addElement(r).keyframes([{offset:0,strokeDasharray:"1px, 200px"},{offset:.2,strokeDasharray:"1px, 200px"},{offset:.55,strokeDasharray:"100px, 200px"},{offset:1,strokeDasharray:"100px, 200px"}]),g=(0,u.c)().addElement(t).keyframes([{offset:0,transform:"rotate(-90deg)"},{offset:1,transform:"rotate(210deg)"}]);if(l&&f){const x=(0,u.c)().addElement(l).keyframes([{offset:0,transform:"rotate(0deg)"},{offset:.3,transform:"rotate(0deg)"},{offset:.55,transform:"rotate(280deg)"},{offset:1,transform:"rotate(400deg)"}]),k=(0,u.c)().addElement(f).keyframes([{offset:0,transform:"translateX(2px) scale(0)"},{offset:.3,transform:"translateX(2px) scale(0)"},{offset:.55,transform:"translateX(-1.5px) scale(1)"},{offset:1,transform:"translateX(-1.5px) scale(1)"}]);a.addAnimation([x,k])}return a.addAnimation([i,m,g])},b=(e,t,r=200)=>{if(!e)return Promise.resolve();const n=(0,c.t)(e,r);return(0,s.c)(()=>{e.style.setProperty("transition",`${r}ms all ease-out`),void 0===t?e.style.removeProperty("transform"):e.style.setProperty("transform",`translate3d(0px, ${t}, 0px)`)}),n},R=function(){var e=(0,d.A)(function*(t,r){const n=t.querySelector("ion-refresher-content");if(!n)return Promise.resolve(!1);yield new Promise(a=>(0,c.c)(n,a));const l=t.querySelector("ion-refresher-content .refresher-pulling ion-spinner"),f=t.querySelector("ion-refresher-content .refresher-refreshing ion-spinner");return null!==l&&null!==f&&("ios"===r&&(0,h.a)("mobile")&&void 0!==t.style.webkitOverflowScrolling||"md"===r)});return function(r,n){return e.apply(this,arguments)}}(),_=class{constructor(e){(0,s.r)(this,e),this.ionRefresh=(0,s.e)(this,"ionRefresh",7),this.ionPull=(0,s.e)(this,"ionPull",7),this.ionStart=(0,s.e)(this,"ionStart",7),this.appliedStyles=!1,this.didStart=!1,this.progress=0,this.pointerDown=!1,this.needsCompletion=!1,this.didRefresh=!1,this.lastVelocityY=0,this.animations=[],this.nativeRefresher=!1,this.state=1,this.pullMin=60,this.pullMax=this.pullMin+60,this.closeDuration="280ms",this.snapbackDuration="280ms",this.pullFactor=1,this.disabled=!1}disabledChanged(){this.gesture&&this.gesture.enable(!this.disabled)}checkNativeRefresher(){var e=this;return(0,d.A)(function*(){const t=yield R(e.el,(0,h.b)(e));if(t&&!e.nativeRefresher){const r=e.el.closest("ion-content");e.setupNativeRefresher(r)}else t||e.destroyNativeRefresher()})()}destroyNativeRefresher(){this.scrollEl&&this.scrollListenerCallback&&(this.scrollEl.removeEventListener("scroll",this.scrollListenerCallback),this.scrollListenerCallback=void 0),this.nativeRefresher=!1}resetNativeRefresher(e,t){var r=this;return(0,d.A)(function*(){r.state=t,"ios"===(0,h.b)(r)?yield b(e,void 0,300):yield(0,c.t)(r.el.querySelector(".refresher-refreshing-icon"),200),r.didRefresh=!1,r.needsCompletion=!1,r.pointerDown=!1,r.animations.forEach(n=>n.destroy()),r.animations=[],r.progress=0,r.state=1})()}setupiOSNativeRefresher(e,t){var r=this;return(0,d.A)(function*(){r.elementToTransform=r.scrollEl;const n=e.shadowRoot.querySelectorAll("svg");let l=.16*r.scrollEl.clientHeight;const f=n.length;(0,s.c)(()=>n.forEach(a=>a.style.setProperty("animation","none"))),r.scrollListenerCallback=()=>{!r.pointerDown&&1===r.state||(0,s.f)(()=>{const a=r.scrollEl.scrollTop,i=r.el.clientHeight;if(a>0){if(8===r.state){const k=(0,c.l)(0,a/(.5*i),1);return void(0,s.c)(()=>((e,t)=>{e.style.setProperty("opacity",t.toString())})(t,1-k))}return}r.pointerDown&&(r.didStart||(r.didStart=!0,r.ionStart.emit()),r.pointerDown&&r.ionPull.emit());const m=r.didStart?30:0,g=r.progress=(0,c.l)(0,(Math.abs(a)-m)/l,1);8===r.state||1===g?(r.pointerDown&&((e,t)=>{(0,s.c)(()=>{e.style.setProperty("--refreshing-rotation-duration",t>=1?"0.5s":"2s"),e.style.setProperty("opacity","1")})})(t,r.lastVelocityY),r.didRefresh||(r.beginRefresh(),r.didRefresh=!0,(0,T.d)({style:"light"}),r.pointerDown||b(r.elementToTransform,`${i}px`))):(r.state=2,((e,t,r)=>{(0,s.c)(()=>{e.forEach((l,f)=>{const a=f*(1/t),g=(0,c.l)(0,(r-a)/(1-a),1);l.style.setProperty("opacity",g.toString())})})})(n,f,g))})},r.scrollEl.addEventListener("scroll",r.scrollListenerCallback),r.gesture=(yield Promise.resolve().then(o.bind(o,53847))).createGesture({el:r.scrollEl,gestureName:"refresher",gesturePriority:31,direction:"y",threshold:5,onStart:()=>{r.pointerDown=!0,r.didRefresh||b(r.elementToTransform,"0px"),0===l&&(l=.16*r.scrollEl.clientHeight)},onMove:a=>{r.lastVelocityY=a.velocityY},onEnd:()=>{r.pointerDown=!1,r.didStart=!1,r.needsCompletion?(r.resetNativeRefresher(r.elementToTransform,32),r.needsCompletion=!1):r.didRefresh&&(0,s.f)(()=>b(r.elementToTransform,`${r.el.clientHeight}px`))}}),r.disabledChanged()})()}setupMDNativeRefresher(e,t,r){var n=this;return(0,d.A)(function*(){const l=(0,c.g)(t).querySelector("circle"),f=n.el.querySelector("ion-refresher-content .refresher-pulling-icon"),a=(0,c.g)(r).querySelector("circle");null!==l&&null!==a&&(0,s.c)(()=>{l.style.setProperty("animation","none"),r.style.setProperty("animation-delay","-655ms"),a.style.setProperty("animation-delay","-655ms")}),n.gesture=(yield Promise.resolve().then(o.bind(o,53847))).createGesture({el:n.scrollEl,gestureName:"refresher",gesturePriority:31,direction:"y",threshold:5,canStart:()=>8!==n.state&&32!==n.state&&0===n.scrollEl.scrollTop,onStart:i=>{n.progress=0,i.data={animation:void 0,didStart:!1,cancelled:!1}},onMove:i=>{if(i.velocityY<0&&0===n.progress&&!i.data.didStart||i.data.cancelled)i.data.cancelled=!0;else{if(!i.data.didStart){i.data.didStart=!0,n.state=2,(0,s.c)(()=>n.scrollEl.style.setProperty("--overflow","hidden"));const m=(e=>{const t=e.previousElementSibling;return null!==t&&"ION-HEADER"===t.tagName?"translate":"scale"})(e),g=((e,t,r)=>"scale"===e?((e,t)=>{const r=t.clientHeight,n=(0,u.c)().addElement(e).keyframes([{offset:0,transform:`scale(0) translateY(-${r}px)`},{offset:1,transform:"scale(1) translateY(100px)"}]);return S(e).addAnimation([n])})(t,r):((e,t)=>{const r=t.clientHeight,n=(0,u.c)().addElement(e).keyframes([{offset:0,transform:`translateY(-${r}px)`},{offset:1,transform:"translateY(100px)"}]);return S(e).addAnimation([n])})(t,r))(m,f,n.el);return i.data.animation=g,g.progressStart(!1,0),n.ionStart.emit(),void n.animations.push(g)}n.progress=(0,c.l)(0,i.deltaY/180*.5,1),i.data.animation.progressStep(n.progress),n.ionPull.emit()}},onEnd:i=>{if(!i.data.didStart)return;if(n.gesture.enable(!1),(0,s.c)(()=>n.scrollEl.style.removeProperty("--overflow")),n.progress<=.4)return void i.data.animation.progressEnd(0,n.progress,500).onFinish(()=>{n.animations.forEach(x=>x.destroy()),n.animations=[],n.gesture.enable(!0),n.state=1});const m=(0,y.g)([0,0],[0,0],[1,1],[1,1],n.progress)[0],g=(e=>(0,u.c)().duration(125).addElement(e).fromTo("transform","translateY(var(--ion-pulling-refresher-translate, 100px))","translateY(0px)"))(f);n.animations.push(g),(0,s.c)((0,d.A)(function*(){f.style.setProperty("--ion-pulling-refresher-translate",100*m+"px"),i.data.animation.progressEnd(),yield g.play(),n.beginRefresh(),i.data.animation.destroy(),n.gesture.enable(!0)}))}}),n.disabledChanged()})()}setupNativeRefresher(e){var t=this;return(0,d.A)(function*(){if(t.scrollListenerCallback||!e||t.nativeRefresher||!t.scrollEl)return;t.setCss(0,"",!1,""),t.nativeRefresher=!0;const r=t.el.querySelector("ion-refresher-content .refresher-pulling ion-spinner"),n=t.el.querySelector("ion-refresher-content .refresher-refreshing ion-spinner");"ios"===(0,h.b)(t)?t.setupiOSNativeRefresher(r,n):t.setupMDNativeRefresher(e,r,n)})()}componentDidUpdate(){this.checkNativeRefresher()}connectedCallback(){var e=this;return(0,d.A)(function*(){if("fixed"!==e.el.getAttribute("slot"))return void console.error('Make sure you use: <ion-refresher slot="fixed">');const t=e.el.closest(p.I);t?(0,c.c)(t,(0,d.A)(function*(){const r=t.querySelector(p.b);e.scrollEl=yield(0,p.g)(r??t),e.backgroundContentEl=yield t.getBackgroundElement(),(yield R(e.el,(0,h.b)(e)))?e.setupNativeRefresher(t):(e.gesture=(yield Promise.resolve().then(o.bind(o,53847))).createGesture({el:t,gestureName:"refresher",gesturePriority:31,direction:"y",threshold:20,passive:!1,canStart:()=>e.canStart(),onStart:()=>e.onStart(),onMove:n=>e.onMove(n),onEnd:()=>e.onEnd()}),e.disabledChanged())})):(0,p.p)(e.el)})()}disconnectedCallback(){this.destroyNativeRefresher(),this.scrollEl=void 0,this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}complete(){var e=this;return(0,d.A)(function*(){e.nativeRefresher?(e.needsCompletion=!0,e.pointerDown||(0,c.r)(()=>(0,c.r)(()=>e.resetNativeRefresher(e.elementToTransform,32)))):e.close(32,"120ms")})()}cancel(){var e=this;return(0,d.A)(function*(){e.nativeRefresher?e.pointerDown||(0,c.r)(()=>(0,c.r)(()=>e.resetNativeRefresher(e.elementToTransform,16))):e.close(16,"")})()}getProgress(){return Promise.resolve(this.progress)}canStart(){return!(!this.scrollEl||1!==this.state||this.scrollEl.scrollTop>0)}onStart(){this.progress=0,this.state=1,this.memoizeOverflowStyle()}onMove(e){if(!this.scrollEl)return;const t=e.event;if(void 0!==t.touches&&t.touches.length>1||56&this.state)return;const r=Number.isNaN(this.pullFactor)||this.pullFactor<0?1:this.pullFactor,n=e.deltaY*r;if(n<=0)return this.progress=0,this.state=1,this.appliedStyles?void this.setCss(0,"",!1,""):void 0;if(1===this.state){if(this.scrollEl.scrollTop>0)return void(this.progress=0);this.state=2}if(t.cancelable&&t.preventDefault(),this.setCss(n,"0ms",!0,""),0===n)return void(this.progress=0);const l=this.pullMin;this.progress=n/l,this.didStart||(this.didStart=!0,this.ionStart.emit()),this.ionPull.emit(),n<l?this.state=2:n>this.pullMax?this.beginRefresh():this.state=4}onEnd(){4===this.state?this.beginRefresh():2===this.state?this.cancel():1===this.state&&this.restoreOverflowStyle()}beginRefresh(){this.state=8,this.setCss(this.pullMin,this.snapbackDuration,!0,""),this.ionRefresh.emit({complete:this.complete.bind(this)})}close(e,t){setTimeout(()=>{this.state=1,this.progress=0,this.didStart=!1,this.setCss(0,"0ms",!1,"",!0)},600),this.state=e,this.setCss(0,this.closeDuration,!0,t)}setCss(e,t,r,n,l=!1){this.nativeRefresher||(this.appliedStyles=e>0,(0,s.c)(()=>{if(this.scrollEl&&this.backgroundContentEl){const f=this.scrollEl.style,a=this.backgroundContentEl.style;f.transform=a.transform=e>0?`translateY(${e}px) translateZ(0px)`:"",f.transitionDuration=a.transitionDuration=t,f.transitionDelay=a.transitionDelay=n,f.overflow=r?"hidden":""}l&&this.restoreOverflowStyle()}))}memoizeOverflowStyle(){if(this.scrollEl){const{overflow:e,overflowX:t,overflowY:r}=this.scrollEl.style;this.overflowStyles={overflow:e??"",overflowX:t??"",overflowY:r??""}}}restoreOverflowStyle(){if(void 0!==this.overflowStyles&&void 0!==this.scrollEl){const{overflow:e,overflowX:t,overflowY:r}=this.overflowStyles;this.scrollEl.style.overflow=e,this.scrollEl.style.overflowX=t,this.scrollEl.style.overflowY=r,this.overflowStyles=void 0}}render(){const e=(0,h.b)(this);return(0,s.h)(s.H,{slot:"fixed",class:{[e]:!0,[`refresher-${e}`]:!0,"refresher-native":this.nativeRefresher,"refresher-active":1!==this.state,"refresher-pulling":2===this.state,"refresher-ready":4===this.state,"refresher-refreshing":8===this.state,"refresher-cancelling":16===this.state,"refresher-completing":32===this.state}})}get el(){return(0,s.i)(this)}static get watchers(){return{disabled:["disabledChanged"]}}};_.style={ios:"ion-refresher{left:0;top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}[dir=rtl] ion-refresher,:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}[dir=rtl] .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-ios .refresher-pulling-icon,.refresher-ios .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-ios .refresher-pulling-text,.refresher-ios .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-lines-ios line,.refresher-ios .refresher-refreshing .spinner-lines-small-ios line,.refresher-ios .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-bubbles circle,.refresher-ios .refresher-refreshing .spinner-circles circle,.refresher-ios .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{margin-left:auto;margin-right:auto;margin-top:0;margin-bottom:0}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){ion-refresher.refresher-native ion-spinner{margin-left:unset;margin-right:unset;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}}.refresher-native .refresher-refreshing ion-spinner{--refreshing-rotation-duration:2s;display:none;-webkit-animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards;animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards}.refresher-native .refresher-refreshing{display:none;-webkit-animation:250ms linear refresher-pop forwards;animation:250ms linear refresher-pop forwards}.refresher-native ion-spinner{width:32px;height:32px;color:var(--ion-color-step-450, #747577)}.refresher-native.refresher-refreshing .refresher-pulling ion-spinner,.refresher-native.refresher-completing .refresher-pulling ion-spinner{display:none}.refresher-native.refresher-refreshing .refresher-refreshing ion-spinner,.refresher-native.refresher-completing .refresher-refreshing ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-pulling ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-refreshing ion-spinner{display:none}.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0) rotate(180deg);transform:scale(0) rotate(180deg);-webkit-transition:300ms;transition:300ms}@-webkit-keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}",md:"ion-refresher{left:0;top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}[dir=rtl] ion-refresher,:host-context([dir=rtl]) ion-refresher{left:unset;right:unset;right:0}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}[dir=rtl] .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-md .refresher-pulling-icon,.refresher-md .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-md .refresher-pulling-text,.refresher-md .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-lines-md line,.refresher-md .refresher-refreshing .spinner-lines-small-md line,.refresher-md .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-bubbles circle,.refresher-md .refresher-refreshing .spinner-circles circle,.refresher-md .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{margin-left:auto;margin-right:auto;margin-top:0;margin-bottom:0;width:24px;height:24px;color:var(--ion-color-primary, #3880ff)}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){ion-refresher.refresher-native ion-spinner{margin-left:unset;margin-right:unset;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}}ion-refresher.refresher-native .spinner-arrow-container{display:inherit}ion-refresher.refresher-native .arrow-container{display:block;position:absolute;width:24px;height:24px}ion-refresher.refresher-native .arrow-container ion-icon{margin-left:auto;margin-right:auto;margin-top:0;margin-bottom:0;left:0;right:0;bottom:-4px;position:absolute;color:var(--ion-color-primary, #3880ff);font-size:12px}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){ion-refresher.refresher-native .arrow-container ion-icon{margin-left:unset;margin-right:unset;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}}ion-refresher.refresher-native.refresher-pulling ion-refresher-content .refresher-pulling,ion-refresher.refresher-native.refresher-ready ion-refresher-content .refresher-pulling{display:-ms-flexbox;display:flex}ion-refresher.refresher-native.refresher-refreshing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-cancelling ion-refresher-content .refresher-refreshing{display:-ms-flexbox;display:flex}ion-refresher.refresher-native .refresher-pulling-icon{-webkit-transform:translateY(calc(-100% - 10px));transform:translateY(calc(-100% - 10px))}ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{margin-left:auto;margin-right:auto;margin-top:0;margin-bottom:0;border-radius:100%;padding-left:8px;padding-right:8px;padding-top:8px;padding-bottom:8px;display:-ms-flexbox;display:flex;border:1px solid var(--ion-color-step-200, #ececec);background:var(--ion-color-step-250, #ffffff);-webkit-box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1);box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1)}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{margin-left:unset;margin-right:unset;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto}}@supports ((-webkit-margin-start: 0) or (margin-inline-start: 0)) or (-webkit-margin-start: 0){ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{padding-left:unset;padding-right:unset;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px}}"};const W=class{constructor(e){(0,s.r)(this,e),this.customHTMLEnabled=h.c.get("innerHTMLTemplatesEnabled",w.E)}componentWillLoad(){if(void 0===this.pullingIcon){const e=(0,h.b)(this),t=void 0!==this.el.style.webkitOverflowScrolling?"lines":E.i;this.pullingIcon=h.c.get("refreshingIcon","ios"===e&&(0,h.a)("mobile")?h.c.get("spinner",t):"circular")}if(void 0===this.refreshingSpinner){const e=(0,h.b)(this);this.refreshingSpinner=h.c.get("refreshingSpinner",h.c.get("spinner","ios"===e?"lines":"circular"))}}renderPullingText(){const{customHTMLEnabled:e,pullingText:t}=this;return e?(0,s.h)("div",{class:"refresher-pulling-text",innerHTML:(0,w.a)(t)}):(0,s.h)("div",{class:"refresher-pulling-text"},t)}renderRefreshingText(){const{customHTMLEnabled:e,refreshingText:t}=this;return e?(0,s.h)("div",{class:"refresher-refreshing-text",innerHTML:(0,w.a)(t)}):(0,s.h)("div",{class:"refresher-refreshing-text"},t)}render(){const e=this.pullingIcon,t=null!=e&&void 0!==C.S[e],r=(0,h.b)(this);return(0,s.h)(s.H,{class:r},(0,s.h)("div",{class:"refresher-pulling"},this.pullingIcon&&t&&(0,s.h)("div",{class:"refresher-pulling-icon"},(0,s.h)("div",{class:"spinner-arrow-container"},(0,s.h)("ion-spinner",{name:this.pullingIcon,paused:!0}),"md"===r&&"circular"===this.pullingIcon&&(0,s.h)("div",{class:"arrow-container"},(0,s.h)("ion-icon",{icon:E.h})))),this.pullingIcon&&!t&&(0,s.h)("div",{class:"refresher-pulling-icon"},(0,s.h)("ion-icon",{icon:this.pullingIcon,lazy:!1})),void 0!==this.pullingText&&this.renderPullingText()),(0,s.h)("div",{class:"refresher-refreshing"},this.refreshingSpinner&&(0,s.h)("div",{class:"refresher-refreshing-icon"},(0,s.h)("ion-spinner",{name:this.refreshingSpinner})),void 0!==this.refreshingText&&this.renderRefreshingText()))}get el(){return(0,s.i)(this)}}}}]);