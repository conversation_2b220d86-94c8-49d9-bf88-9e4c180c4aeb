"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[6470],{66470:(w,d,a)=>{a.r(d),a.d(d,{ReportingSalesEvolutionsPageModule:()=>E});var g=a(56610),u=a(37222),i=a(77897),p=a(77575),c=a(73308),f=a(20120),m=a(58133),_=a(35025),M=a.n(_),n=a(2978),h=a(93368),b=a(82571),C=a(71604),P=a(60787),v=a(11244),O=a(74657);function y(e,l){if(1&e&&(n.j41(0,"ion-label",25),n.EFF(1),n.k0s()),2&e){const t=n.XpG();n.R7$(1),n.SpI(" ",t.commonSrv.getDisplayValue(t.total)," ")}}function x(e,l){if(1&e&&(n.j41(0,"ion-label",26),n.EFF(1),n.k0s()),2&e){const t=n.XpG();n.R7$(1),n.SpI(" ",t.getDate(t.date,"day")," ")}}function D(e,l){if(1&e&&(n.j41(0,"ion-item",27)(1,"ion-label",3),n.EFF(2),n.k0s(),n.j41(3,"ion-label",28),n.EFF(4),n.k0s()()),2&e){const t=l.$implicit,r=l.index,o=n.XpG();n.R7$(2),n.JRh(o.months[r]),n.R7$(2),n.JRh(o.commonSrv.getDisplayValue(t))}}const S=[{path:"",component:(()=>{class e{constructor(t,r,o){this.reportingSrv=t,this.commonSrv=r,this.modalCtrl=o,this.filterData={userId:"",companyId:"",startDate:new Date((new Date).getFullYear(),0,1),endDate:new Date,product:""},this.months=["Janvier","Fevrier","Mars","Avril","Mai","Juin","Juillet","Aout","Septembre","Octobre","Novembre","Decembre"],this.date=new Date}ngOnInit(){var t=this;return(0,c.A)(function*(){yield t.loadData(),t.filterData.userId=t.commonSrv.user.category==m.s.Commercial?null:t.commonSrv.user?._id,t.commonSrv.user.category==m.s.CompanyUser&&(t.filterData.companyId=t.commonSrv.user?.company?._id)})()}getDate(t,r){return"day"===r?M()(t).format("DD-MM-YYYY"):M()(t).format("HH:mm")}ionViewWillEnter(){var t=this;return(0,c.A)(function*(){yield t.loadData()})()}loadData(){var t=this;return(0,c.A)(function*(){yield t.chartData()})()}getEvolutionsSalesPayment(){var t=this;return(0,c.A)(function*(){t.isLoading=!0,t.reportingSrv.dataSaleEvolutions=yield t.reportingSrv.getSalesEvolutionForMonthSale({...t.filterData}),t.isLoading=!1})()}chartData(){var t=this;return(0,c.A)(function*(){t.isLoading=!0,t.total=t.reportingSrv?.dataSaleEvolutions?.reduce((o,s)=>o+s),t.dataSaleEvolutionsBar={labels:["J","F","M","A","M","J","J","A","S","O","N","D"],datasets:[{data:t.reportingSrv?.dataSaleEvolutions,backgroundColor:"#419CFB",borderColor:"#419CFB",borderRadius:10}]},t.basicOptions={plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!1,ticks:{callback:function(o){return(o=>o>=1e9?(o/1e9).toFixed(1)+"B":o>=1e6?(o/1e6).toFixed(1)+"M":o>=1e3?(o/1e3).toFixed(1)+"k":o)(o)},color:"none"},grid:{display:!0,color:"#dadada",drawBorder:!1}},x:{ticks:{color:"none"},grid:{display:!1,drawBorder:!1}}}},t.isLoading=!1})()}generateHexColor(){const t=Math.floor(16777215*Math.random()).toString(16);return"#"+"0".repeat(6-t.length)+t}doRefresh(t){var r=this;return(0,c.A)(function*(){r.filterData=r.filterData,yield r.loadData(),t.target.complete()})()}resetFilter(){var t=this;return(0,c.A)(function*(){t.filterData=t.filterData,yield t.loadData()})()}showFilter(){var t=this;return(0,c.A)(function*(){const r=yield t.modalCtrl.create({component:f.$,initialBreakpoint:.6,cssClass:"modal",breakpoints:[0,.6,.7],mode:"ios",componentProps:{filterData:t.filterData,products:Object.keys(t.reportingSrv.dataStateProduct)}});r.present();const o=(yield r.onWillDismiss()).data;t.filterData={...t.filterData,...o},t.filterData&&(yield t.loadData())})()}static{this.\u0275fac=function(r){return new(r||e)(n.rXU(h.k),n.rXU(b.h),n.rXU(i.W3))}}static{this.\u0275cmp=n.VBU({type:e,selectors:[["app-reporting-sales-evolutions"]],decls:38,vars:20,consts:[[1,"header"],["mode","md","slot","start","text",""],["defaultHref","./"],[1,"title"],[1,"ion-padding"],[1,"media-padding"],[1,"illustration-container"],[1,"details-balance"],[1,"value"],["class","balance","color","primary",4,"ngIf"],["class","value date",4,"ngIf"],["slot","fixed",3,"ionRefresh"],["pullingIcon","chevron-down-circle-outline","refreshingSpinner","circles",3,"pullingText","refreshingText"],[1,"ion-content"],[1,"p-card"],[1,"p-card-header"],[1,"total"],[1,"filter"],["src","/assets/icons/refresh-green.png",1,"img-refresh",3,"click"],["expand","block","fill","outline","color","primary","size","small",3,"click"],["slot","start","src","/assets/icons/filtre-icon.png",1,"img-filter"],["type","bar",1,"p-chart",3,"data","options","height"],[1,"amount-card"],[1,"detail"],["class","custom-list",4,"ngFor","ngForOf"],["color","primary",1,"balance"],[1,"value","date"],[1,"custom-list"],["slot","end",1,"value"]],template:function(r,o){1&r&&(n.j41(0,"ion-header")(1,"ion-toolbar",0)(2,"ion-buttons",1),n.nrm(3,"ion-back-button",2),n.k0s(),n.j41(4,"h1",3),n.EFF(5),n.nI1(6,"translate"),n.k0s()()(),n.j41(7,"ion-content",4)(8,"div",5)(9,"div",6)(10,"div",7)(11,"ion-label",8),n.EFF(12,"Total"),n.k0s(),n.DNE(13,y,2,1,"ion-label",9),n.DNE(14,x,2,1,"ion-label",10),n.k0s()(),n.j41(15,"ion-refresher",11),n.bIt("ionRefresh",function(R){return o.doRefresh(R)}),n.nrm(16,"ion-refresher-content",12),n.nI1(17,"translate"),n.nI1(18,"translate"),n.k0s(),n.j41(19,"div",13)(20,"p-card",14)(21,"div",15),n.nrm(22,"div",16),n.j41(23,"div",17)(24,"ion-img",18),n.bIt("click",function(){return o.resetFilter()}),n.k0s(),n.j41(25,"ion-button",19),n.bIt("click",function(){return o.showFilter()}),n.nrm(26,"ion-img",20),n.EFF(27),n.nI1(28,"capitalize"),n.nI1(29,"translate"),n.k0s()()(),n.nrm(30,"p-chart",21),n.k0s(),n.j41(31,"div",22)(32,"p",23),n.EFF(33,"Details"),n.k0s(),n.j41(34,"p",16),n.EFF(35,"Total"),n.k0s(),n.j41(36,"ion-list"),n.DNE(37,D,5,2,"ion-item",24),n.k0s()()()()()),2&r&&(n.R7$(5),n.JRh(n.bMT(6,10,"reporting.evolution_sale")),n.R7$(8),n.Y8G("ngIf",null!=o.total),n.R7$(1),n.Y8G("ngIf",o.date),n.R7$(2),n.FS9("pullingText",n.bMT(17,12,"refresher.pull")),n.Mz_("refreshingText","",n.bMT(18,14,"refresher.refreshing"),"..."),n.R7$(11),n.SpI(" ",n.bMT(28,16,n.bMT(29,18,"reporting.btn-filter"))," "),n.R7$(3),n.Y8G("data",o.dataSaleEvolutionsBar)("options",o.basicOptions)("height","12rem"),n.R7$(7),n.Y8G("ngForOf",o.reportingSrv.dataSaleEvolutions))},dependencies:[g.Sq,g.bT,i.el,i.Jm,i.QW,i.W9,i.eU,i.KW,i.uz,i.he,i.nf,i.To,i.Ki,i.ai,i.tY,C.Z,P.X,v.F,O.D9],styles:["*[_ngcontent-%COMP%]{margin:0;padding:0}.header-md[_ngcontent-%COMP%]:after{background-image:none!important}.modal[_ngcontent-%COMP%]{--border-radius: calc(60 * var(--res))}.input-group[_ngcontent-%COMP%]{margin-top:calc(75 * var(--res))}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 0;margin-bottom:calc(50 * var(--res));--placeholder-color: $color-six}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{font-family:Mont Regular;font-weight:400;font-size:calc(42 * var(--res));color:#1e1e1e}.input-group[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{padding-top:calc(25 * var(--res));padding-bottom:calc(25 * var(--res))}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]{margin-bottom:calc(50 * var(--res));display:flex;align-items:center;justify-content:space-between;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));border-bottom-width:.55px;border-bottom-style:solid}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{width:90%;margin-bottom:0;--border-color: transparent;--inner-border-width: 0px 0px 0px 0px;--border-style: none}.input-group[_ngcontent-%COMP%]   .password-input[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-top:5%}.bg-primary[_ngcontent-%COMP%]{color:#143c5d}.message[_ngcontent-%COMP%]{margin-bottom:calc(75 * var(--res))}.message[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-weight:800;font-size:calc(70 * var(--res));padding-bottom:calc(25 * var(--res))}.message[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-weight:400;color:#757474;display:block;line-height:1.5em!important}.btn--meduim[_ngcontent-%COMP%]{height:calc(130 * var(--res));font-size:calc(50 * var(--res));font-family:var(--mont-bold)}.btn--meduim[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:var(--mont-bold)}.btn--small[_ngcontent-%COMP%]{height:calc(100 * var(--res))}.btn--round[_ngcontent-%COMP%]{--border-radius: 50%;padding-inline-start:0;padding-inline-end:0;--padding-start: 0;--padding-end: 0}.btn--border-primary[_ngcontent-%COMP%]{--background: transparent;--background-activated: #c4e6d0;--border-color: var(--ion-color-primary);--border-style: solid;--border-width: 2px}.btn--border-secondary[_ngcontent-%COMP%]{--border-width: 2px;--background: transparent;--background-activated: #ddabb2;--border-color: var(--ion-color-secondary);--border-style: solid}.btn--upper[_ngcontent-%COMP%]{text-transform:uppercase}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{overflow:hidden}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-track{box-shadow:inset 0 0 5px #546e8d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#143c5d;border-radius:10px}.scroller-container[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#143c5d}.bottom-sheet-content[_ngcontent-%COMP%]{background:#fdfdfe;height:100%}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-thumbnail[_ngcontent-%COMP%]{margin-left:calc(31.25 * var(--res));background-color:#1e1e1e;--border-radius: 50%;--size: 23}.bottom-sheet-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:calc(45 * var(--res));text-align:center;color:#757474}.bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%], .bottom-sheet-content[_ngcontent-%COMP%]   ion-container[_ngcontent-%COMP%]   #container[_ngcontent-%COMP%]{padding:calc(75 * var(--res))}.font-size-5[_ngcontent-%COMP%]{font-size:calc(42 * var(--res))}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{--border-color: transparent;display:flex;align-items:center}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{width:4rem}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--mont-semibold);font-size:calc(55 * var(--res));text-align:start}ion-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .color-primary[_ngcontent-%COMP%]{color:#143c5d}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]{display:flex;align-items:flex-start;flex-direction:column;gap:1em;height:5em;padding:var(--container-padding);background-position:center;background-size:cover;border-radius:16px;width:100%;background-image:url(/assets/images/Total-card.png)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]{display:flex;flex-direction:column;font-size:1em;font-family:Mont Regular}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%]{font-size:var(--fs-32-px);color:var(--clr-tertiary-600);font-family:Mont Bold}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-size:var(--fs-15-px);color:var(--clr-tertiary-600)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-size:var(--fs-10-px)}ion-content[_ngcontent-%COMP%]   .media-padding[_ngcontent-%COMP%]   .illustration-container[_ngcontent-%COMP%]   .details-balance[_ngcontent-%COMP%]   .icon-home[_ngcontent-%COMP%]{height:48px;width:48px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]{width:100%;margin:20px .5em;background-color:#d9d9d9}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:0 0 calc(25 * var(--res)) 0;font-family:var(--mont-semibold);font-size:calc(42 * var(--res));color:#143c5d}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .total[_ngcontent-%COMP%]{flex-grow:1}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{display:flex;align-items:center;gap:calc(50 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   .img-refresh[_ngcontent-%COMP%]{width:calc(50 * var(--res));cursor:pointer}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{text-transform:capitalize;--padding-top: calc(35 * var(--res));--padding-bottom: calc(35 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-card-header[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .img-filter[_ngcontent-%COMP%]{margin-right:calc(15 * var(--res));width:calc(41 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .p-card[_ngcontent-%COMP%]   .p-chart[_ngcontent-%COMP%]{height:12rem}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .amount-card[_ngcontent-%COMP%]{color:var(--ion-color-primary)}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .amount-card[_ngcontent-%COMP%]   .detail[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:18px;line-height:6px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .amount-card[_ngcontent-%COMP%]   .total[_ngcontent-%COMP%]{font-family:Mont Regular;font-size:12px;text-align:right;padding:0 1.5em}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]{--background: #CEE5FE;--border-radius: 10px;margin-bottom:.5em}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:14px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   .amount-card[_ngcontent-%COMP%]   .custom-list[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-family:Mont Bold;font-size:14px}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{padding-bottom:10px;margin-bottom:10px;border-bottom:1px solid hsl(0,0%,85%);font-size:calc(30 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .row-title[_ngcontent-%COMP%]{font-size:calc(42 * var(--res));font-weight:700;font-size:calc(45 * var(--res))}ion-content[_ngcontent-%COMP%]   .ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{font-size:1.25rem}"]})}}return e})()}];let F=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[p.iI.forChild(S),p.iI]})}}return e})();var k=a(93887);let E=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=n.$C({type:e})}static{this.\u0275inj=n.G2t({imports:[g.MD,u.YN,i.bv,C.D,P.F,k.G,O.h,F]})}}return e})()}}]);